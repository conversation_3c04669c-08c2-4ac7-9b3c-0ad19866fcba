// @flow strict

export type StandardLonghandProperties<TLength = string | 0, TTime = string> = {|
  accentColor?: Property$AccentColor,
  alignContent?: Property$AlignContent,
  alignItems?: Property$AlignItems,
  alignSelf?: Property$AlignSelf,
  alignTracks?: Property$AlignTracks,
  animationComposition?: Property$AnimationComposition,
  animationDelay?: Property$AnimationDelay<TTime>,
  animationDirection?: Property$AnimationDirection,
  animationDuration?: Property$AnimationDuration<TTime>,
  animationFillMode?: Property$AnimationFillMode,
  animationIterationCount?: Property$AnimationIterationCount,
  animationName?: Property$AnimationName,
  animationPlayState?: Property$AnimationPlayState,
  animationRangeEnd?: Property$AnimationRangeEnd<TLength>,
  animationRangeStart?: Property$AnimationRangeStart<TLength>,
  animationTimeline?: Property$AnimationTimeline,
  animationTimingFunction?: Property$AnimationTimingFunction,
  appearance?: Property$Appearance,
  aspectRatio?: Property$AspectRatio,
  backdropFilter?: Property$BackdropFilter,
  backfaceVisibility?: Property$BackfaceVisibility,
  backgroundAttachment?: Property$BackgroundAttachment,
  backgroundBlendMode?: Property$BackgroundBlendMode,
  backgroundClip?: Property$BackgroundClip,
  backgroundColor?: Property$BackgroundColor,
  backgroundImage?: Property$BackgroundImage,
  backgroundOrigin?: Property$BackgroundOrigin,
  backgroundPositionX?: Property$BackgroundPositionX<TLength>,
  backgroundPositionY?: Property$BackgroundPositionY<TLength>,
  backgroundRepeat?: Property$BackgroundRepeat,
  backgroundSize?: Property$BackgroundSize<TLength>,
  blockOverflow?: Property$BlockOverflow,
  blockSize?: Property$BlockSize<TLength>,
  borderBlockColor?: Property$BorderBlockColor,
  borderBlockEndColor?: Property$BorderBlockEndColor,
  borderBlockEndStyle?: Property$BorderBlockEndStyle,
  borderBlockEndWidth?: Property$BorderBlockEndWidth<TLength>,
  borderBlockStartColor?: Property$BorderBlockStartColor,
  borderBlockStartStyle?: Property$BorderBlockStartStyle,
  borderBlockStartWidth?: Property$BorderBlockStartWidth<TLength>,
  borderBlockStyle?: Property$BorderBlockStyle,
  borderBlockWidth?: Property$BorderBlockWidth<TLength>,
  borderBottomColor?: Property$BorderBottomColor,
  borderBottomLeftRadius?: Property$BorderBottomLeftRadius<TLength>,
  borderBottomRightRadius?: Property$BorderBottomRightRadius<TLength>,
  borderBottomStyle?: Property$BorderBottomStyle,
  borderBottomWidth?: Property$BorderBottomWidth<TLength>,
  borderCollapse?: Property$BorderCollapse,
  borderEndEndRadius?: Property$BorderEndEndRadius<TLength>,
  borderEndStartRadius?: Property$BorderEndStartRadius<TLength>,
  borderImageOutset?: Property$BorderImageOutset<TLength>,
  borderImageRepeat?: Property$BorderImageRepeat,
  borderImageSlice?: Property$BorderImageSlice,
  borderImageSource?: Property$BorderImageSource,
  borderImageWidth?: Property$BorderImageWidth<TLength>,
  borderInlineColor?: Property$BorderInlineColor,
  borderInlineEndColor?: Property$BorderInlineEndColor,
  borderInlineEndStyle?: Property$BorderInlineEndStyle,
  borderInlineEndWidth?: Property$BorderInlineEndWidth<TLength>,
  borderInlineStartColor?: Property$BorderInlineStartColor,
  borderInlineStartStyle?: Property$BorderInlineStartStyle,
  borderInlineStartWidth?: Property$BorderInlineStartWidth<TLength>,
  borderInlineStyle?: Property$BorderInlineStyle,
  borderInlineWidth?: Property$BorderInlineWidth<TLength>,
  borderLeftColor?: Property$BorderLeftColor,
  borderLeftStyle?: Property$BorderLeftStyle,
  borderLeftWidth?: Property$BorderLeftWidth<TLength>,
  borderRightColor?: Property$BorderRightColor,
  borderRightStyle?: Property$BorderRightStyle,
  borderRightWidth?: Property$BorderRightWidth<TLength>,
  borderSpacing?: Property$BorderSpacing<TLength>,
  borderStartEndRadius?: Property$BorderStartEndRadius<TLength>,
  borderStartStartRadius?: Property$BorderStartStartRadius<TLength>,
  borderTopColor?: Property$BorderTopColor,
  borderTopLeftRadius?: Property$BorderTopLeftRadius<TLength>,
  borderTopRightRadius?: Property$BorderTopRightRadius<TLength>,
  borderTopStyle?: Property$BorderTopStyle,
  borderTopWidth?: Property$BorderTopWidth<TLength>,
  bottom?: Property$Bottom<TLength>,
  boxDecorationBreak?: Property$BoxDecorationBreak,
  boxShadow?: Property$BoxShadow,
  boxSizing?: Property$BoxSizing,
  breakAfter?: Property$BreakAfter,
  breakBefore?: Property$BreakBefore,
  breakInside?: Property$BreakInside,
  captionSide?: Property$CaptionSide,
  caretColor?: Property$CaretColor,
  caretShape?: Property$CaretShape,
  clear?: Property$Clear,
  clipPath?: Property$ClipPath,
  color?: Property$Color,
  colorAdjust?: Property$PrintColorAdjust,
  colorScheme?: Property$ColorScheme,
  columnCount?: Property$ColumnCount,
  columnFill?: Property$ColumnFill,
  columnGap?: Property$ColumnGap<TLength>,
  columnRuleColor?: Property$ColumnRuleColor,
  columnRuleStyle?: Property$ColumnRuleStyle,
  columnRuleWidth?: Property$ColumnRuleWidth<TLength>,
  columnSpan?: Property$ColumnSpan,
  columnWidth?: Property$ColumnWidth<TLength>,
  contain?: Property$Contain,
  containIntrinsicBlockSize?: Property$ContainIntrinsicBlockSize<TLength>,
  containIntrinsicHeight?: Property$ContainIntrinsicHeight<TLength>,
  containIntrinsicInlineSize?: Property$ContainIntrinsicInlineSize<TLength>,
  containIntrinsicWidth?: Property$ContainIntrinsicWidth<TLength>,
  containerName?: Property$ContainerName,
  containerType?: Property$ContainerType,
  content?: Property$Content,
  contentVisibility?: Property$ContentVisibility,
  counterIncrement?: Property$CounterIncrement,
  counterReset?: Property$CounterReset,
  counterSet?: Property$CounterSet,
  cursor?: Property$Cursor,
  direction?: Property$Direction,
  display?: Property$Display,
  emptyCells?: Property$EmptyCells,
  filter?: Property$Filter,
  flexBasis?: Property$FlexBasis<TLength>,
  flexDirection?: Property$FlexDirection,
  flexGrow?: Property$FlexGrow,
  flexShrink?: Property$FlexShrink,
  flexWrap?: Property$FlexWrap,
  float?: Property$Float,
  fontFamily?: Property$FontFamily,
  fontFeatureSettings?: Property$FontFeatureSettings,
  fontKerning?: Property$FontKerning,
  fontLanguageOverride?: Property$FontLanguageOverride,
  fontOpticalSizing?: Property$FontOpticalSizing,
  fontPalette?: Property$FontPalette,
  fontSize?: Property$FontSize<TLength>,
  fontSizeAdjust?: Property$FontSizeAdjust,
  fontSmooth?: Property$FontSmooth<TLength>,
  fontStretch?: Property$FontStretch,
  fontStyle?: Property$FontStyle,
  fontSynthesis?: Property$FontSynthesis,
  fontSynthesisPosition?: Property$FontSynthesisPosition,
  fontSynthesisSmallCaps?: Property$FontSynthesisSmallCaps,
  fontSynthesisStyle?: Property$FontSynthesisStyle,
  fontSynthesisWeight?: Property$FontSynthesisWeight,
  fontVariant?: Property$FontVariant,
  fontVariantAlternates?: Property$FontVariantAlternates,
  fontVariantCaps?: Property$FontVariantCaps,
  fontVariantEastAsian?: Property$FontVariantEastAsian,
  fontVariantEmoji?: Property$FontVariantEmoji,
  fontVariantLigatures?: Property$FontVariantLigatures,
  fontVariantNumeric?: Property$FontVariantNumeric,
  fontVariantPosition?: Property$FontVariantPosition,
  fontVariationSettings?: Property$FontVariationSettings,
  fontWeight?: Property$FontWeight,
  forcedColorAdjust?: Property$ForcedColorAdjust,
  gridAutoColumns?: Property$GridAutoColumns<TLength>,
  gridAutoFlow?: Property$GridAutoFlow,
  gridAutoRows?: Property$GridAutoRows<TLength>,
  gridColumnEnd?: Property$GridColumnEnd,
  gridColumnStart?: Property$GridColumnStart,
  gridRowEnd?: Property$GridRowEnd,
  gridRowStart?: Property$GridRowStart,
  gridTemplateAreas?: Property$GridTemplateAreas,
  gridTemplateColumns?: Property$GridTemplateColumns<TLength>,
  gridTemplateRows?: Property$GridTemplateRows<TLength>,
  hangingPunctuation?: Property$HangingPunctuation,
  height?: Property$Height<TLength>,
  hyphenateCharacter?: Property$HyphenateCharacter,
  hyphenateLimitChars?: Property$HyphenateLimitChars,
  hyphens?: Property$Hyphens,
  imageOrientation?: Property$ImageOrientation,
  imageRendering?: Property$ImageRendering,
  imageResolution?: Property$ImageResolution,
  initialLetter?: Property$InitialLetter,
  inlineSize?: Property$InlineSize<TLength>,
  inputSecurity?: Property$InputSecurity,
  insetBlockEnd?: Property$InsetBlockEnd<TLength>,
  insetBlockStart?: Property$InsetBlockStart<TLength>,
  insetInlineEnd?: Property$InsetInlineEnd<TLength>,
  insetInlineStart?: Property$InsetInlineStart<TLength>,
  isolation?: Property$Isolation,
  justifyContent?: Property$JustifyContent,
  justifyItems?: Property$JustifyItems,
  justifySelf?: Property$JustifySelf,
  justifyTracks?: Property$JustifyTracks,
  left?: Property$Left<TLength>,
  letterSpacing?: Property$LetterSpacing<TLength>,
  lineBreak?: Property$LineBreak,
  lineHeight?: Property$LineHeight<TLength>,
  lineHeightStep?: Property$LineHeightStep<TLength>,
  listStyleImage?: Property$ListStyleImage,
  listStylePosition?: Property$ListStylePosition,
  listStyleType?: Property$ListStyleType,
  marginBlockEnd?: Property$MarginBlockEnd<TLength>,
  marginBlockStart?: Property$MarginBlockStart<TLength>,
  marginBottom?: Property$MarginBottom<TLength>,
  marginInlineEnd?: Property$MarginInlineEnd<TLength>,
  marginInlineStart?: Property$MarginInlineStart<TLength>,
  marginLeft?: Property$MarginLeft<TLength>,
  marginRight?: Property$MarginRight<TLength>,
  marginTop?: Property$MarginTop<TLength>,
  marginTrim?: Property$MarginTrim,
  maskBorderMode?: Property$MaskBorderMode,
  maskBorderOutset?: Property$MaskBorderOutset<TLength>,
  maskBorderRepeat?: Property$MaskBorderRepeat,
  maskBorderSlice?: Property$MaskBorderSlice,
  maskBorderSource?: Property$MaskBorderSource,
  maskBorderWidth?: Property$MaskBorderWidth<TLength>,
  maskClip?: Property$MaskClip,
  maskComposite?: Property$MaskComposite,
  maskImage?: Property$MaskImage,
  maskMode?: Property$MaskMode,
  maskOrigin?: Property$MaskOrigin,
  maskPosition?: Property$MaskPosition<TLength>,
  maskRepeat?: Property$MaskRepeat,
  maskSize?: Property$MaskSize<TLength>,
  maskType?: Property$MaskType,
  masonryAutoFlow?: Property$MasonryAutoFlow,
  mathDepth?: Property$MathDepth,
  mathShift?: Property$MathShift,
  mathStyle?: Property$MathStyle,
  maxBlockSize?: Property$MaxBlockSize<TLength>,
  maxHeight?: Property$MaxHeight<TLength>,
  maxInlineSize?: Property$MaxInlineSize<TLength>,
  maxLines?: Property$MaxLines,
  maxWidth?: Property$MaxWidth<TLength>,
  minBlockSize?: Property$MinBlockSize<TLength>,
  minHeight?: Property$MinHeight<TLength>,
  minInlineSize?: Property$MinInlineSize<TLength>,
  minWidth?: Property$MinWidth<TLength>,
  mixBlendMode?: Property$MixBlendMode,
  motionDistance?: Property$OffsetDistance<TLength>,
  motionPath?: Property$OffsetPath,
  motionRotation?: Property$OffsetRotate,
  objectFit?: Property$ObjectFit,
  objectPosition?: Property$ObjectPosition<TLength>,
  offsetAnchor?: Property$OffsetAnchor<TLength>,
  offsetDistance?: Property$OffsetDistance<TLength>,
  offsetPath?: Property$OffsetPath,
  offsetPosition?: Property$OffsetPosition<TLength>,
  offsetRotate?: Property$OffsetRotate,
  offsetRotation?: Property$OffsetRotate,
  opacity?: Property$Opacity,
  order?: Property$Order,
  orphans?: Property$Orphans,
  outlineColor?: Property$OutlineColor,
  outlineOffset?: Property$OutlineOffset<TLength>,
  outlineStyle?: Property$OutlineStyle,
  outlineWidth?: Property$OutlineWidth<TLength>,
  overflowAnchor?: Property$OverflowAnchor,
  overflowBlock?: Property$OverflowBlock,
  overflowClipBox?: Property$OverflowClipBox,
  overflowClipMargin?: Property$OverflowClipMargin<TLength>,
  overflowInline?: Property$OverflowInline,
  overflowWrap?: Property$OverflowWrap,
  overflowX?: Property$OverflowX,
  overflowY?: Property$OverflowY,
  overlay?: Property$Overlay,
  overscrollBehaviorBlock?: Property$OverscrollBehaviorBlock,
  overscrollBehaviorInline?: Property$OverscrollBehaviorInline,
  overscrollBehaviorX?: Property$OverscrollBehaviorX,
  overscrollBehaviorY?: Property$OverscrollBehaviorY,
  paddingBlockEnd?: Property$PaddingBlockEnd<TLength>,
  paddingBlockStart?: Property$PaddingBlockStart<TLength>,
  paddingBottom?: Property$PaddingBottom<TLength>,
  paddingInlineEnd?: Property$PaddingInlineEnd<TLength>,
  paddingInlineStart?: Property$PaddingInlineStart<TLength>,
  paddingLeft?: Property$PaddingLeft<TLength>,
  paddingRight?: Property$PaddingRight<TLength>,
  paddingTop?: Property$PaddingTop<TLength>,
  page?: Property$Page,
  pageBreakAfter?: Property$PageBreakAfter,
  pageBreakBefore?: Property$PageBreakBefore,
  pageBreakInside?: Property$PageBreakInside,
  paintOrder?: Property$PaintOrder,
  perspective?: Property$Perspective<TLength>,
  perspectiveOrigin?: Property$PerspectiveOrigin<TLength>,
  pointerEvents?: Property$PointerEvents,
  position?: Property$Position,
  printColorAdjust?: Property$PrintColorAdjust,
  quotes?: Property$Quotes,
  resize?: Property$Resize,
  right?: Property$Right<TLength>,
  rotate?: Property$Rotate,
  rowGap?: Property$RowGap<TLength>,
  rubyAlign?: Property$RubyAlign,
  rubyMerge?: Property$RubyMerge,
  rubyPosition?: Property$RubyPosition,
  scale?: Property$Scale,
  scrollBehavior?: Property$ScrollBehavior,
  scrollMarginBlockEnd?: Property$ScrollMarginBlockEnd<TLength>,
  scrollMarginBlockStart?: Property$ScrollMarginBlockStart<TLength>,
  scrollMarginBottom?: Property$ScrollMarginBottom<TLength>,
  scrollMarginInlineEnd?: Property$ScrollMarginInlineEnd<TLength>,
  scrollMarginInlineStart?: Property$ScrollMarginInlineStart<TLength>,
  scrollMarginLeft?: Property$ScrollMarginLeft<TLength>,
  scrollMarginRight?: Property$ScrollMarginRight<TLength>,
  scrollMarginTop?: Property$ScrollMarginTop<TLength>,
  scrollPaddingBlockEnd?: Property$ScrollPaddingBlockEnd<TLength>,
  scrollPaddingBlockStart?: Property$ScrollPaddingBlockStart<TLength>,
  scrollPaddingBottom?: Property$ScrollPaddingBottom<TLength>,
  scrollPaddingInlineEnd?: Property$ScrollPaddingInlineEnd<TLength>,
  scrollPaddingInlineStart?: Property$ScrollPaddingInlineStart<TLength>,
  scrollPaddingLeft?: Property$ScrollPaddingLeft<TLength>,
  scrollPaddingRight?: Property$ScrollPaddingRight<TLength>,
  scrollPaddingTop?: Property$ScrollPaddingTop<TLength>,
  scrollSnapAlign?: Property$ScrollSnapAlign,
  scrollSnapMarginBottom?: Property$ScrollMarginBottom<TLength>,
  scrollSnapMarginLeft?: Property$ScrollMarginLeft<TLength>,
  scrollSnapMarginRight?: Property$ScrollMarginRight<TLength>,
  scrollSnapMarginTop?: Property$ScrollMarginTop<TLength>,
  scrollSnapStop?: Property$ScrollSnapStop,
  scrollSnapType?: Property$ScrollSnapType,
  scrollTimelineAxis?: Property$ScrollTimelineAxis,
  scrollTimelineName?: Property$ScrollTimelineName,
  scrollbarColor?: Property$ScrollbarColor,
  scrollbarGutter?: Property$ScrollbarGutter,
  scrollbarWidth?: Property$ScrollbarWidth,
  shapeImageThreshold?: Property$ShapeImageThreshold,
  shapeMargin?: Property$ShapeMargin<TLength>,
  shapeOutside?: Property$ShapeOutside,
  tabSize?: Property$TabSize<TLength>,
  tableLayout?: Property$TableLayout,
  textAlign?: Property$TextAlign,
  textAlignLast?: Property$TextAlignLast,
  textCombineUpright?: Property$TextCombineUpright,
  textDecorationColor?: Property$TextDecorationColor,
  textDecorationLine?: Property$TextDecorationLine,
  textDecorationSkip?: Property$TextDecorationSkip,
  textDecorationSkipInk?: Property$TextDecorationSkipInk,
  textDecorationStyle?: Property$TextDecorationStyle,
  textDecorationThickness?: Property$TextDecorationThickness<TLength>,
  textEmphasisColor?: Property$TextEmphasisColor,
  textEmphasisPosition?: Property$TextEmphasisPosition,
  textEmphasisStyle?: Property$TextEmphasisStyle,
  textIndent?: Property$TextIndent<TLength>,
  textJustify?: Property$TextJustify,
  textOrientation?: Property$TextOrientation,
  textOverflow?: Property$TextOverflow,
  textRendering?: Property$TextRendering,
  textShadow?: Property$TextShadow,
  textSizeAdjust?: Property$TextSizeAdjust,
  textTransform?: Property$TextTransform,
  textUnderlineOffset?: Property$TextUnderlineOffset<TLength>,
  textUnderlinePosition?: Property$TextUnderlinePosition,
  textWrap?: Property$TextWrap,
  timelineScope?: Property$TimelineScope,
  top?: Property$Top<TLength>,
  touchAction?: Property$TouchAction,
  transform?: Property$Transform,
  transformBox?: Property$TransformBox,
  transformOrigin?: Property$TransformOrigin<TLength>,
  transformStyle?: Property$TransformStyle,
  transitionBehavior?: Property$TransitionBehavior,
  transitionDelay?: Property$TransitionDelay<TTime>,
  transitionDuration?: Property$TransitionDuration<TTime>,
  transitionProperty?: Property$TransitionProperty,
  transitionTimingFunction?: Property$TransitionTimingFunction,
  translate?: Property$Translate<TLength>,
  unicodeBidi?: Property$UnicodeBidi,
  userSelect?: Property$UserSelect,
  verticalAlign?: Property$VerticalAlign<TLength>,
  viewTimelineAxis?: Property$ViewTimelineAxis,
  viewTimelineInset?: Property$ViewTimelineInset<TLength>,
  viewTimelineName?: Property$ViewTimelineName,
  viewTransitionName?: Property$ViewTransitionName,
  visibility?: Property$Visibility,
  whiteSpace?: Property$WhiteSpace,
  whiteSpaceCollapse?: Property$WhiteSpaceCollapse,
  whiteSpaceTrim?: Property$WhiteSpaceTrim,
  widows?: Property$Widows,
  width?: Property$Width<TLength>,
  willChange?: Property$WillChange,
  wordBreak?: Property$WordBreak,
  wordSpacing?: Property$WordSpacing<TLength>,
  wordWrap?: Property$WordWrap,
  writingMode?: Property$WritingMode,
  zIndex?: Property$ZIndex,
  zoom?: Property$Zoom,
|};

export type StandardShorthandProperties<TLength = string | 0, TTime = string> = {|
  all?: Property$All,
  animation?: Property$Animation<TTime>,
  animationRange?: Property$AnimationRange<TLength>,
  background?: Property$Background<TLength>,
  backgroundPosition?: Property$BackgroundPosition<TLength>,
  border?: Property$Border<TLength>,
  borderBlock?: Property$BorderBlock<TLength>,
  borderBlockEnd?: Property$BorderBlockEnd<TLength>,
  borderBlockStart?: Property$BorderBlockStart<TLength>,
  borderBottom?: Property$BorderBottom<TLength>,
  borderColor?: Property$BorderColor,
  borderImage?: Property$BorderImage,
  borderInline?: Property$BorderInline<TLength>,
  borderInlineEnd?: Property$BorderInlineEnd<TLength>,
  borderInlineStart?: Property$BorderInlineStart<TLength>,
  borderLeft?: Property$BorderLeft<TLength>,
  borderRadius?: Property$BorderRadius<TLength>,
  borderRight?: Property$BorderRight<TLength>,
  borderStyle?: Property$BorderStyle,
  borderTop?: Property$BorderTop<TLength>,
  borderWidth?: Property$BorderWidth<TLength>,
  caret?: Property$Caret,
  columnRule?: Property$ColumnRule<TLength>,
  columns?: Property$Columns<TLength>,
  containIntrinsicSize?: Property$ContainIntrinsicSize<TLength>,
  container?: Property$Container,
  flex?: Property$Flex<TLength>,
  flexFlow?: Property$FlexFlow,
  font?: Property$Font,
  gap?: Property$Gap<TLength>,
  grid?: Property$Grid,
  gridArea?: Property$GridArea,
  gridColumn?: Property$GridColumn,
  gridRow?: Property$GridRow,
  gridTemplate?: Property$GridTemplate,
  inset?: Property$Inset<TLength>,
  insetBlock?: Property$InsetBlock<TLength>,
  insetInline?: Property$InsetInline<TLength>,
  lineClamp?: Property$LineClamp,
  listStyle?: Property$ListStyle,
  margin?: Property$Margin<TLength>,
  marginBlock?: Property$MarginBlock<TLength>,
  marginInline?: Property$MarginInline<TLength>,
  mask?: Property$Mask<TLength>,
  maskBorder?: Property$MaskBorder,
  motion?: Property$Offset<TLength>,
  offset?: Property$Offset<TLength>,
  outline?: Property$Outline<TLength>,
  overflow?: Property$Overflow,
  overscrollBehavior?: Property$OverscrollBehavior,
  padding?: Property$Padding<TLength>,
  paddingBlock?: Property$PaddingBlock<TLength>,
  paddingInline?: Property$PaddingInline<TLength>,
  placeContent?: Property$PlaceContent,
  placeItems?: Property$PlaceItems,
  placeSelf?: Property$PlaceSelf,
  scrollMargin?: Property$ScrollMargin<TLength>,
  scrollMarginBlock?: Property$ScrollMarginBlock<TLength>,
  scrollMarginInline?: Property$ScrollMarginInline<TLength>,
  scrollPadding?: Property$ScrollPadding<TLength>,
  scrollPaddingBlock?: Property$ScrollPaddingBlock<TLength>,
  scrollPaddingInline?: Property$ScrollPaddingInline<TLength>,
  scrollSnapMargin?: Property$ScrollMargin<TLength>,
  scrollTimeline?: Property$ScrollTimeline,
  textDecoration?: Property$TextDecoration<TLength>,
  textEmphasis?: Property$TextEmphasis,
  transition?: Property$Transition<TTime>,
  viewTimeline?: Property$ViewTimeline,
|};

export type StandardProperties<TLength = string | 0, TTime = string> = {| ...StandardLonghandProperties<TLength, TTime>, ...StandardShorthandProperties<TLength, TTime> |};

export type VendorLonghandProperties<TLength = string | 0, TTime = string> = {|
  MozAnimationDelay?: Property$AnimationDelay<TTime>,
  MozAnimationDirection?: Property$AnimationDirection,
  MozAnimationDuration?: Property$AnimationDuration<TTime>,
  MozAnimationFillMode?: Property$AnimationFillMode,
  MozAnimationIterationCount?: Property$AnimationIterationCount,
  MozAnimationName?: Property$AnimationName,
  MozAnimationPlayState?: Property$AnimationPlayState,
  MozAnimationTimingFunction?: Property$AnimationTimingFunction,
  MozAppearance?: Property$MozAppearance,
  MozBinding?: Property$MozBinding,
  MozBorderBottomColors?: Property$MozBorderBottomColors,
  MozBorderEndColor?: Property$BorderInlineEndColor,
  MozBorderEndStyle?: Property$BorderInlineEndStyle,
  MozBorderEndWidth?: Property$BorderInlineEndWidth<TLength>,
  MozBorderLeftColors?: Property$MozBorderLeftColors,
  MozBorderRightColors?: Property$MozBorderRightColors,
  MozBorderStartColor?: Property$BorderInlineStartColor,
  MozBorderStartStyle?: Property$BorderInlineStartStyle,
  MozBorderTopColors?: Property$MozBorderTopColors,
  MozBoxSizing?: Property$BoxSizing,
  MozColumnCount?: Property$ColumnCount,
  MozColumnFill?: Property$ColumnFill,
  MozColumnRuleColor?: Property$ColumnRuleColor,
  MozColumnRuleStyle?: Property$ColumnRuleStyle,
  MozColumnRuleWidth?: Property$ColumnRuleWidth<TLength>,
  MozColumnWidth?: Property$ColumnWidth<TLength>,
  MozContextProperties?: Property$MozContextProperties,
  MozFontFeatureSettings?: Property$FontFeatureSettings,
  MozFontLanguageOverride?: Property$FontLanguageOverride,
  MozHyphens?: Property$Hyphens,
  MozImageRegion?: Property$MozImageRegion,
  MozMarginEnd?: Property$MarginInlineEnd<TLength>,
  MozMarginStart?: Property$MarginInlineStart<TLength>,
  MozOrient?: Property$MozOrient,
  MozOsxFontSmoothing?: Property$FontSmooth<TLength>,
  MozOutlineRadiusBottomleft?: Property$MozOutlineRadiusBottomleft<TLength>,
  MozOutlineRadiusBottomright?: Property$MozOutlineRadiusBottomright<TLength>,
  MozOutlineRadiusTopleft?: Property$MozOutlineRadiusTopleft<TLength>,
  MozOutlineRadiusTopright?: Property$MozOutlineRadiusTopright<TLength>,
  MozPaddingEnd?: Property$PaddingInlineEnd<TLength>,
  MozPaddingStart?: Property$PaddingInlineStart<TLength>,
  MozStackSizing?: Property$MozStackSizing,
  MozTabSize?: Property$TabSize<TLength>,
  MozTextBlink?: Property$MozTextBlink,
  MozTextSizeAdjust?: Property$TextSizeAdjust,
  MozUserFocus?: Property$MozUserFocus,
  MozUserModify?: Property$MozUserModify,
  MozUserSelect?: Property$UserSelect,
  MozWindowDragging?: Property$MozWindowDragging,
  MozWindowShadow?: Property$MozWindowShadow,
  msAccelerator?: Property$MsAccelerator,
  msBlockProgression?: Property$MsBlockProgression,
  msContentZoomChaining?: Property$MsContentZoomChaining,
  msContentZoomLimitMax?: Property$MsContentZoomLimitMax,
  msContentZoomLimitMin?: Property$MsContentZoomLimitMin,
  msContentZoomSnapPoints?: Property$MsContentZoomSnapPoints,
  msContentZoomSnapType?: Property$MsContentZoomSnapType,
  msContentZooming?: Property$MsContentZooming,
  msFilter?: Property$MsFilter,
  msFlexDirection?: Property$FlexDirection,
  msFlexPositive?: Property$FlexGrow,
  msFlowFrom?: Property$MsFlowFrom,
  msFlowInto?: Property$MsFlowInto,
  msGridColumns?: Property$MsGridColumns<TLength>,
  msGridRows?: Property$MsGridRows<TLength>,
  msHighContrastAdjust?: Property$MsHighContrastAdjust,
  msHyphenateLimitChars?: Property$MsHyphenateLimitChars,
  msHyphenateLimitLines?: Property$MsHyphenateLimitLines,
  msHyphenateLimitZone?: Property$MsHyphenateLimitZone<TLength>,
  msHyphens?: Property$Hyphens,
  msImeAlign?: Property$MsImeAlign,
  msLineBreak?: Property$LineBreak,
  msOrder?: Property$Order,
  msOverflowStyle?: Property$MsOverflowStyle,
  msOverflowX?: Property$OverflowX,
  msOverflowY?: Property$OverflowY,
  msScrollChaining?: Property$MsScrollChaining,
  msScrollLimitXMax?: Property$MsScrollLimitXMax<TLength>,
  msScrollLimitXMin?: Property$MsScrollLimitXMin<TLength>,
  msScrollLimitYMax?: Property$MsScrollLimitYMax<TLength>,
  msScrollLimitYMin?: Property$MsScrollLimitYMin<TLength>,
  msScrollRails?: Property$MsScrollRails,
  msScrollSnapPointsX?: Property$MsScrollSnapPointsX,
  msScrollSnapPointsY?: Property$MsScrollSnapPointsY,
  msScrollSnapType?: Property$MsScrollSnapType,
  msScrollTranslation?: Property$MsScrollTranslation,
  msScrollbar3dlightColor?: Property$MsScrollbar3dlightColor,
  msScrollbarArrowColor?: Property$MsScrollbarArrowColor,
  msScrollbarBaseColor?: Property$MsScrollbarBaseColor,
  msScrollbarDarkshadowColor?: Property$MsScrollbarDarkshadowColor,
  msScrollbarFaceColor?: Property$MsScrollbarFaceColor,
  msScrollbarHighlightColor?: Property$MsScrollbarHighlightColor,
  msScrollbarShadowColor?: Property$MsScrollbarShadowColor,
  msScrollbarTrackColor?: Property$MsScrollbarTrackColor,
  msTextAutospace?: Property$MsTextAutospace,
  msTextCombineHorizontal?: Property$TextCombineUpright,
  msTextOverflow?: Property$TextOverflow,
  msTouchAction?: Property$TouchAction,
  msTouchSelect?: Property$MsTouchSelect,
  msTransform?: Property$Transform,
  msTransformOrigin?: Property$TransformOrigin<TLength>,
  msTransitionDelay?: Property$TransitionDelay<TTime>,
  msTransitionDuration?: Property$TransitionDuration<TTime>,
  msTransitionProperty?: Property$TransitionProperty,
  msTransitionTimingFunction?: Property$TransitionTimingFunction,
  msUserSelect?: Property$MsUserSelect,
  msWordBreak?: Property$WordBreak,
  msWrapFlow?: Property$MsWrapFlow,
  msWrapMargin?: Property$MsWrapMargin<TLength>,
  msWrapThrough?: Property$MsWrapThrough,
  msWritingMode?: Property$WritingMode,
  WebkitAlignContent?: Property$AlignContent,
  WebkitAlignItems?: Property$AlignItems,
  WebkitAlignSelf?: Property$AlignSelf,
  WebkitAnimationDelay?: Property$AnimationDelay<TTime>,
  WebkitAnimationDirection?: Property$AnimationDirection,
  WebkitAnimationDuration?: Property$AnimationDuration<TTime>,
  WebkitAnimationFillMode?: Property$AnimationFillMode,
  WebkitAnimationIterationCount?: Property$AnimationIterationCount,
  WebkitAnimationName?: Property$AnimationName,
  WebkitAnimationPlayState?: Property$AnimationPlayState,
  WebkitAnimationTimingFunction?: Property$AnimationTimingFunction,
  WebkitAppearance?: Property$WebkitAppearance,
  WebkitBackdropFilter?: Property$BackdropFilter,
  WebkitBackfaceVisibility?: Property$BackfaceVisibility,
  WebkitBackgroundClip?: Property$BackgroundClip,
  WebkitBackgroundOrigin?: Property$BackgroundOrigin,
  WebkitBackgroundSize?: Property$BackgroundSize<TLength>,
  WebkitBorderBeforeColor?: Property$WebkitBorderBeforeColor,
  WebkitBorderBeforeStyle?: Property$WebkitBorderBeforeStyle,
  WebkitBorderBeforeWidth?: Property$WebkitBorderBeforeWidth<TLength>,
  WebkitBorderBottomLeftRadius?: Property$BorderBottomLeftRadius<TLength>,
  WebkitBorderBottomRightRadius?: Property$BorderBottomRightRadius<TLength>,
  WebkitBorderImageSlice?: Property$BorderImageSlice,
  WebkitBorderTopLeftRadius?: Property$BorderTopLeftRadius<TLength>,
  WebkitBorderTopRightRadius?: Property$BorderTopRightRadius<TLength>,
  WebkitBoxDecorationBreak?: Property$BoxDecorationBreak,
  WebkitBoxReflect?: Property$WebkitBoxReflect<TLength>,
  WebkitBoxShadow?: Property$BoxShadow,
  WebkitBoxSizing?: Property$BoxSizing,
  WebkitClipPath?: Property$ClipPath,
  WebkitColumnCount?: Property$ColumnCount,
  WebkitColumnFill?: Property$ColumnFill,
  WebkitColumnRuleColor?: Property$ColumnRuleColor,
  WebkitColumnRuleStyle?: Property$ColumnRuleStyle,
  WebkitColumnRuleWidth?: Property$ColumnRuleWidth<TLength>,
  WebkitColumnSpan?: Property$ColumnSpan,
  WebkitColumnWidth?: Property$ColumnWidth<TLength>,
  WebkitFilter?: Property$Filter,
  WebkitFlexBasis?: Property$FlexBasis<TLength>,
  WebkitFlexDirection?: Property$FlexDirection,
  WebkitFlexGrow?: Property$FlexGrow,
  WebkitFlexShrink?: Property$FlexShrink,
  WebkitFlexWrap?: Property$FlexWrap,
  WebkitFontFeatureSettings?: Property$FontFeatureSettings,
  WebkitFontKerning?: Property$FontKerning,
  WebkitFontSmoothing?: Property$FontSmooth<TLength>,
  WebkitFontVariantLigatures?: Property$FontVariantLigatures,
  WebkitHyphenateCharacter?: Property$HyphenateCharacter,
  WebkitHyphens?: Property$Hyphens,
  WebkitInitialLetter?: Property$InitialLetter,
  WebkitJustifyContent?: Property$JustifyContent,
  WebkitLineBreak?: Property$LineBreak,
  WebkitLineClamp?: Property$WebkitLineClamp,
  WebkitMarginEnd?: Property$MarginInlineEnd<TLength>,
  WebkitMarginStart?: Property$MarginInlineStart<TLength>,
  WebkitMaskAttachment?: Property$WebkitMaskAttachment,
  WebkitMaskBoxImageOutset?: Property$MaskBorderOutset<TLength>,
  WebkitMaskBoxImageRepeat?: Property$MaskBorderRepeat,
  WebkitMaskBoxImageSlice?: Property$MaskBorderSlice,
  WebkitMaskBoxImageSource?: Property$MaskBorderSource,
  WebkitMaskBoxImageWidth?: Property$MaskBorderWidth<TLength>,
  WebkitMaskClip?: Property$WebkitMaskClip,
  WebkitMaskComposite?: Property$WebkitMaskComposite,
  WebkitMaskImage?: Property$WebkitMaskImage,
  WebkitMaskOrigin?: Property$WebkitMaskOrigin,
  WebkitMaskPosition?: Property$WebkitMaskPosition<TLength>,
  WebkitMaskPositionX?: Property$WebkitMaskPositionX<TLength>,
  WebkitMaskPositionY?: Property$WebkitMaskPositionY<TLength>,
  WebkitMaskRepeat?: Property$WebkitMaskRepeat,
  WebkitMaskRepeatX?: Property$WebkitMaskRepeatX,
  WebkitMaskRepeatY?: Property$WebkitMaskRepeatY,
  WebkitMaskSize?: Property$WebkitMaskSize<TLength>,
  WebkitMaxInlineSize?: Property$MaxInlineSize<TLength>,
  WebkitOrder?: Property$Order,
  WebkitOverflowScrolling?: Property$WebkitOverflowScrolling,
  WebkitPaddingEnd?: Property$PaddingInlineEnd<TLength>,
  WebkitPaddingStart?: Property$PaddingInlineStart<TLength>,
  WebkitPerspective?: Property$Perspective<TLength>,
  WebkitPerspectiveOrigin?: Property$PerspectiveOrigin<TLength>,
  WebkitPrintColorAdjust?: Property$PrintColorAdjust,
  WebkitRubyPosition?: Property$RubyPosition,
  WebkitScrollSnapType?: Property$ScrollSnapType,
  WebkitShapeMargin?: Property$ShapeMargin<TLength>,
  WebkitTapHighlightColor?: Property$WebkitTapHighlightColor,
  WebkitTextCombine?: Property$TextCombineUpright,
  WebkitTextDecorationColor?: Property$TextDecorationColor,
  WebkitTextDecorationLine?: Property$TextDecorationLine,
  WebkitTextDecorationSkip?: Property$TextDecorationSkip,
  WebkitTextDecorationStyle?: Property$TextDecorationStyle,
  WebkitTextEmphasisColor?: Property$TextEmphasisColor,
  WebkitTextEmphasisPosition?: Property$TextEmphasisPosition,
  WebkitTextEmphasisStyle?: Property$TextEmphasisStyle,
  WebkitTextFillColor?: Property$WebkitTextFillColor,
  WebkitTextOrientation?: Property$TextOrientation,
  WebkitTextSizeAdjust?: Property$TextSizeAdjust,
  WebkitTextStrokeColor?: Property$WebkitTextStrokeColor,
  WebkitTextStrokeWidth?: Property$WebkitTextStrokeWidth<TLength>,
  WebkitTextUnderlinePosition?: Property$TextUnderlinePosition,
  WebkitTouchCallout?: Property$WebkitTouchCallout,
  WebkitTransform?: Property$Transform,
  WebkitTransformOrigin?: Property$TransformOrigin<TLength>,
  WebkitTransformStyle?: Property$TransformStyle,
  WebkitTransitionDelay?: Property$TransitionDelay<TTime>,
  WebkitTransitionDuration?: Property$TransitionDuration<TTime>,
  WebkitTransitionProperty?: Property$TransitionProperty,
  WebkitTransitionTimingFunction?: Property$TransitionTimingFunction,
  WebkitUserModify?: Property$WebkitUserModify,
  WebkitUserSelect?: Property$UserSelect,
  WebkitWritingMode?: Property$WritingMode,
|};

export type VendorShorthandProperties<TLength = string | 0, TTime = string> = {|
  MozAnimation?: Property$Animation<TTime>,
  MozBorderImage?: Property$BorderImage,
  MozColumnRule?: Property$ColumnRule<TLength>,
  MozColumns?: Property$Columns<TLength>,
  MozOutlineRadius?: Property$MozOutlineRadius<TLength>,
  msContentZoomLimit?: Property$MsContentZoomLimit,
  msContentZoomSnap?: Property$MsContentZoomSnap,
  msFlex?: Property$Flex<TLength>,
  msScrollLimit?: Property$MsScrollLimit,
  msScrollSnapX?: Property$MsScrollSnapX,
  msScrollSnapY?: Property$MsScrollSnapY,
  msTransition?: Property$Transition<TTime>,
  WebkitAnimation?: Property$Animation<TTime>,
  WebkitBorderBefore?: Property$WebkitBorderBefore<TLength>,
  WebkitBorderImage?: Property$BorderImage,
  WebkitBorderRadius?: Property$BorderRadius<TLength>,
  WebkitColumnRule?: Property$ColumnRule<TLength>,
  WebkitColumns?: Property$Columns<TLength>,
  WebkitFlex?: Property$Flex<TLength>,
  WebkitFlexFlow?: Property$FlexFlow,
  WebkitMask?: Property$WebkitMask<TLength>,
  WebkitMaskBoxImage?: Property$MaskBorder,
  WebkitTextEmphasis?: Property$TextEmphasis,
  WebkitTextStroke?: Property$WebkitTextStroke<TLength>,
  WebkitTransition?: Property$Transition<TTime>,
|};

export type VendorProperties<TLength = string | 0, TTime = string> = {| ...VendorLonghandProperties<TLength, TTime>, ...VendorShorthandProperties<TLength, TTime> |};

export type ObsoleteProperties<TLength = string | 0, TTime = string> = {|
  azimuth?: Property$Azimuth,
  boxAlign?: Property$BoxAlign,
  boxDirection?: Property$BoxDirection,
  boxFlex?: Property$BoxFlex,
  boxFlexGroup?: Property$BoxFlexGroup,
  boxLines?: Property$BoxLines,
  boxOrdinalGroup?: Property$BoxOrdinalGroup,
  boxOrient?: Property$BoxOrient,
  boxPack?: Property$BoxPack,
  clip?: Property$Clip,
  gridColumnGap?: Property$GridColumnGap<TLength>,
  gridGap?: Property$GridGap<TLength>,
  gridRowGap?: Property$GridRowGap<TLength>,
  imeMode?: Property$ImeMode,
  offsetBlock?: Property$InsetBlock<TLength>,
  offsetBlockEnd?: Property$InsetBlockEnd<TLength>,
  offsetBlockStart?: Property$InsetBlockStart<TLength>,
  offsetInline?: Property$InsetInline<TLength>,
  offsetInlineEnd?: Property$InsetInlineEnd<TLength>,
  offsetInlineStart?: Property$InsetInlineStart<TLength>,
  scrollSnapCoordinate?: Property$ScrollSnapCoordinate<TLength>,
  scrollSnapDestination?: Property$ScrollSnapDestination<TLength>,
  scrollSnapPointsX?: Property$ScrollSnapPointsX,
  scrollSnapPointsY?: Property$ScrollSnapPointsY,
  scrollSnapTypeX?: Property$ScrollSnapTypeX,
  scrollSnapTypeY?: Property$ScrollSnapTypeY,
  KhtmlBoxAlign?: Property$BoxAlign,
  KhtmlBoxDirection?: Property$BoxDirection,
  KhtmlBoxFlex?: Property$BoxFlex,
  KhtmlBoxFlexGroup?: Property$BoxFlexGroup,
  KhtmlBoxLines?: Property$BoxLines,
  KhtmlBoxOrdinalGroup?: Property$BoxOrdinalGroup,
  KhtmlBoxOrient?: Property$BoxOrient,
  KhtmlBoxPack?: Property$BoxPack,
  KhtmlLineBreak?: Property$LineBreak,
  KhtmlOpacity?: Property$Opacity,
  KhtmlUserSelect?: Property$UserSelect,
  MozBackfaceVisibility?: Property$BackfaceVisibility,
  MozBackgroundClip?: Property$BackgroundClip,
  MozBackgroundInlinePolicy?: Property$BoxDecorationBreak,
  MozBackgroundOrigin?: Property$BackgroundOrigin,
  MozBackgroundSize?: Property$BackgroundSize<TLength>,
  MozBorderRadius?: Property$BorderRadius<TLength>,
  MozBorderRadiusBottomleft?: Property$BorderBottomLeftRadius<TLength>,
  MozBorderRadiusBottomright?: Property$BorderBottomRightRadius<TLength>,
  MozBorderRadiusTopleft?: Property$BorderTopLeftRadius<TLength>,
  MozBorderRadiusTopright?: Property$BorderTopRightRadius<TLength>,
  MozBoxAlign?: Property$BoxAlign,
  MozBoxDirection?: Property$BoxDirection,
  MozBoxFlex?: Property$BoxFlex,
  MozBoxOrdinalGroup?: Property$BoxOrdinalGroup,
  MozBoxOrient?: Property$BoxOrient,
  MozBoxPack?: Property$BoxPack,
  MozBoxShadow?: Property$BoxShadow,
  MozFloatEdge?: Property$MozFloatEdge,
  MozForceBrokenImageIcon?: Property$MozForceBrokenImageIcon,
  MozOpacity?: Property$Opacity,
  MozOutline?: Property$Outline<TLength>,
  MozOutlineColor?: Property$OutlineColor,
  MozOutlineStyle?: Property$OutlineStyle,
  MozOutlineWidth?: Property$OutlineWidth<TLength>,
  MozPerspective?: Property$Perspective<TLength>,
  MozPerspectiveOrigin?: Property$PerspectiveOrigin<TLength>,
  MozTextAlignLast?: Property$TextAlignLast,
  MozTextDecorationColor?: Property$TextDecorationColor,
  MozTextDecorationLine?: Property$TextDecorationLine,
  MozTextDecorationStyle?: Property$TextDecorationStyle,
  MozTransform?: Property$Transform,
  MozTransformOrigin?: Property$TransformOrigin<TLength>,
  MozTransformStyle?: Property$TransformStyle,
  MozTransition?: Property$Transition<TTime>,
  MozTransitionDelay?: Property$TransitionDelay<TTime>,
  MozTransitionDuration?: Property$TransitionDuration<TTime>,
  MozTransitionProperty?: Property$TransitionProperty,
  MozTransitionTimingFunction?: Property$TransitionTimingFunction,
  MozUserInput?: Property$MozUserInput,
  msImeMode?: Property$ImeMode,
  OAnimation?: Property$Animation<TTime>,
  OAnimationDelay?: Property$AnimationDelay<TTime>,
  OAnimationDirection?: Property$AnimationDirection,
  OAnimationDuration?: Property$AnimationDuration<TTime>,
  OAnimationFillMode?: Property$AnimationFillMode,
  OAnimationIterationCount?: Property$AnimationIterationCount,
  OAnimationName?: Property$AnimationName,
  OAnimationPlayState?: Property$AnimationPlayState,
  OAnimationTimingFunction?: Property$AnimationTimingFunction,
  OBackgroundSize?: Property$BackgroundSize<TLength>,
  OBorderImage?: Property$BorderImage,
  OObjectFit?: Property$ObjectFit,
  OObjectPosition?: Property$ObjectPosition<TLength>,
  OTabSize?: Property$TabSize<TLength>,
  OTextOverflow?: Property$TextOverflow,
  OTransform?: Property$Transform,
  OTransformOrigin?: Property$TransformOrigin<TLength>,
  OTransition?: Property$Transition<TTime>,
  OTransitionDelay?: Property$TransitionDelay<TTime>,
  OTransitionDuration?: Property$TransitionDuration<TTime>,
  OTransitionProperty?: Property$TransitionProperty,
  OTransitionTimingFunction?: Property$TransitionTimingFunction,
  WebkitBoxAlign?: Property$BoxAlign,
  WebkitBoxDirection?: Property$BoxDirection,
  WebkitBoxFlex?: Property$BoxFlex,
  WebkitBoxFlexGroup?: Property$BoxFlexGroup,
  WebkitBoxLines?: Property$BoxLines,
  WebkitBoxOrdinalGroup?: Property$BoxOrdinalGroup,
  WebkitBoxOrient?: Property$BoxOrient,
  WebkitBoxPack?: Property$BoxPack,
|};

export type SvgProperties<TLength = string | 0, TTime = string> = {|
  alignmentBaseline?: Property$AlignmentBaseline,
  baselineShift?: Property$BaselineShift<TLength>,
  clip?: Property$Clip,
  clipPath?: Property$ClipPath,
  clipRule?: Property$ClipRule,
  color?: Property$Color,
  colorInterpolation?: Property$ColorInterpolation,
  colorRendering?: Property$ColorRendering,
  cursor?: Property$Cursor,
  direction?: Property$Direction,
  display?: Property$Display,
  dominantBaseline?: Property$DominantBaseline,
  fill?: Property$Fill,
  fillOpacity?: Property$FillOpacity,
  fillRule?: Property$FillRule,
  filter?: Property$Filter,
  floodColor?: Property$FloodColor,
  floodOpacity?: Property$FloodOpacity,
  font?: Property$Font,
  fontFamily?: Property$FontFamily,
  fontSize?: Property$FontSize<TLength>,
  fontSizeAdjust?: Property$FontSizeAdjust,
  fontStretch?: Property$FontStretch,
  fontStyle?: Property$FontStyle,
  fontVariant?: Property$FontVariant,
  fontWeight?: Property$FontWeight,
  glyphOrientationVertical?: Property$GlyphOrientationVertical,
  imageRendering?: Property$ImageRendering,
  letterSpacing?: Property$LetterSpacing<TLength>,
  lightingColor?: Property$LightingColor,
  lineHeight?: Property$LineHeight<TLength>,
  marker?: Property$Marker,
  markerEnd?: Property$MarkerEnd,
  markerMid?: Property$MarkerMid,
  markerStart?: Property$MarkerStart,
  mask?: Property$Mask<TLength>,
  opacity?: Property$Opacity,
  overflow?: Property$Overflow,
  paintOrder?: Property$PaintOrder,
  pointerEvents?: Property$PointerEvents,
  shapeRendering?: Property$ShapeRendering,
  stopColor?: Property$StopColor,
  stopOpacity?: Property$StopOpacity,
  stroke?: Property$Stroke,
  strokeDasharray?: Property$StrokeDasharray<TLength>,
  strokeDashoffset?: Property$StrokeDashoffset<TLength>,
  strokeLinecap?: Property$StrokeLinecap,
  strokeLinejoin?: Property$StrokeLinejoin,
  strokeMiterlimit?: Property$StrokeMiterlimit,
  strokeOpacity?: Property$StrokeOpacity,
  strokeWidth?: Property$StrokeWidth<TLength>,
  textAnchor?: Property$TextAnchor,
  textDecoration?: Property$TextDecoration<TLength>,
  textRendering?: Property$TextRendering,
  unicodeBidi?: Property$UnicodeBidi,
  vectorEffect?: Property$VectorEffect,
  visibility?: Property$Visibility,
  whiteSpace?: Property$WhiteSpace,
  wordSpacing?: Property$WordSpacing<TLength>,
  writingMode?: Property$WritingMode,
|};

export type Properties<TLength = string | 0, TTime = string> = {|
  ...StandardProperties<TLength, TTime>,
  ...VendorProperties<TLength, TTime>,
  ...ObsoleteProperties<TLength, TTime>,
  ...SvgProperties<TLength, TTime>,
|};

export type StandardLonghandPropertiesHyphen<TLength = string | 0, TTime = string> = {|
  "accent-color"?: Property$AccentColor,
  "align-content"?: Property$AlignContent,
  "align-items"?: Property$AlignItems,
  "align-self"?: Property$AlignSelf,
  "align-tracks"?: Property$AlignTracks,
  "animation-composition"?: Property$AnimationComposition,
  "animation-delay"?: Property$AnimationDelay<TTime>,
  "animation-direction"?: Property$AnimationDirection,
  "animation-duration"?: Property$AnimationDuration<TTime>,
  "animation-fill-mode"?: Property$AnimationFillMode,
  "animation-iteration-count"?: Property$AnimationIterationCount,
  "animation-name"?: Property$AnimationName,
  "animation-play-state"?: Property$AnimationPlayState,
  "animation-range-end"?: Property$AnimationRangeEnd<TLength>,
  "animation-range-start"?: Property$AnimationRangeStart<TLength>,
  "animation-timeline"?: Property$AnimationTimeline,
  "animation-timing-function"?: Property$AnimationTimingFunction,
  appearance?: Property$Appearance,
  "aspect-ratio"?: Property$AspectRatio,
  "backdrop-filter"?: Property$BackdropFilter,
  "backface-visibility"?: Property$BackfaceVisibility,
  "background-attachment"?: Property$BackgroundAttachment,
  "background-blend-mode"?: Property$BackgroundBlendMode,
  "background-clip"?: Property$BackgroundClip,
  "background-color"?: Property$BackgroundColor,
  "background-image"?: Property$BackgroundImage,
  "background-origin"?: Property$BackgroundOrigin,
  "background-position-x"?: Property$BackgroundPositionX<TLength>,
  "background-position-y"?: Property$BackgroundPositionY<TLength>,
  "background-repeat"?: Property$BackgroundRepeat,
  "background-size"?: Property$BackgroundSize<TLength>,
  "block-overflow"?: Property$BlockOverflow,
  "block-size"?: Property$BlockSize<TLength>,
  "border-block-color"?: Property$BorderBlockColor,
  "border-block-end-color"?: Property$BorderBlockEndColor,
  "border-block-end-style"?: Property$BorderBlockEndStyle,
  "border-block-end-width"?: Property$BorderBlockEndWidth<TLength>,
  "border-block-start-color"?: Property$BorderBlockStartColor,
  "border-block-start-style"?: Property$BorderBlockStartStyle,
  "border-block-start-width"?: Property$BorderBlockStartWidth<TLength>,
  "border-block-style"?: Property$BorderBlockStyle,
  "border-block-width"?: Property$BorderBlockWidth<TLength>,
  "border-bottom-color"?: Property$BorderBottomColor,
  "border-bottom-left-radius"?: Property$BorderBottomLeftRadius<TLength>,
  "border-bottom-right-radius"?: Property$BorderBottomRightRadius<TLength>,
  "border-bottom-style"?: Property$BorderBottomStyle,
  "border-bottom-width"?: Property$BorderBottomWidth<TLength>,
  "border-collapse"?: Property$BorderCollapse,
  "border-end-end-radius"?: Property$BorderEndEndRadius<TLength>,
  "border-end-start-radius"?: Property$BorderEndStartRadius<TLength>,
  "border-image-outset"?: Property$BorderImageOutset<TLength>,
  "border-image-repeat"?: Property$BorderImageRepeat,
  "border-image-slice"?: Property$BorderImageSlice,
  "border-image-source"?: Property$BorderImageSource,
  "border-image-width"?: Property$BorderImageWidth<TLength>,
  "border-inline-color"?: Property$BorderInlineColor,
  "border-inline-end-color"?: Property$BorderInlineEndColor,
  "border-inline-end-style"?: Property$BorderInlineEndStyle,
  "border-inline-end-width"?: Property$BorderInlineEndWidth<TLength>,
  "border-inline-start-color"?: Property$BorderInlineStartColor,
  "border-inline-start-style"?: Property$BorderInlineStartStyle,
  "border-inline-start-width"?: Property$BorderInlineStartWidth<TLength>,
  "border-inline-style"?: Property$BorderInlineStyle,
  "border-inline-width"?: Property$BorderInlineWidth<TLength>,
  "border-left-color"?: Property$BorderLeftColor,
  "border-left-style"?: Property$BorderLeftStyle,
  "border-left-width"?: Property$BorderLeftWidth<TLength>,
  "border-right-color"?: Property$BorderRightColor,
  "border-right-style"?: Property$BorderRightStyle,
  "border-right-width"?: Property$BorderRightWidth<TLength>,
  "border-spacing"?: Property$BorderSpacing<TLength>,
  "border-start-end-radius"?: Property$BorderStartEndRadius<TLength>,
  "border-start-start-radius"?: Property$BorderStartStartRadius<TLength>,
  "border-top-color"?: Property$BorderTopColor,
  "border-top-left-radius"?: Property$BorderTopLeftRadius<TLength>,
  "border-top-right-radius"?: Property$BorderTopRightRadius<TLength>,
  "border-top-style"?: Property$BorderTopStyle,
  "border-top-width"?: Property$BorderTopWidth<TLength>,
  bottom?: Property$Bottom<TLength>,
  "box-decoration-break"?: Property$BoxDecorationBreak,
  "box-shadow"?: Property$BoxShadow,
  "box-sizing"?: Property$BoxSizing,
  "break-after"?: Property$BreakAfter,
  "break-before"?: Property$BreakBefore,
  "break-inside"?: Property$BreakInside,
  "caption-side"?: Property$CaptionSide,
  "caret-color"?: Property$CaretColor,
  "caret-shape"?: Property$CaretShape,
  clear?: Property$Clear,
  "clip-path"?: Property$ClipPath,
  color?: Property$Color,
  "color-adjust"?: Property$PrintColorAdjust,
  "color-scheme"?: Property$ColorScheme,
  "column-count"?: Property$ColumnCount,
  "column-fill"?: Property$ColumnFill,
  "column-gap"?: Property$ColumnGap<TLength>,
  "column-rule-color"?: Property$ColumnRuleColor,
  "column-rule-style"?: Property$ColumnRuleStyle,
  "column-rule-width"?: Property$ColumnRuleWidth<TLength>,
  "column-span"?: Property$ColumnSpan,
  "column-width"?: Property$ColumnWidth<TLength>,
  contain?: Property$Contain,
  "contain-intrinsic-block-size"?: Property$ContainIntrinsicBlockSize<TLength>,
  "contain-intrinsic-height"?: Property$ContainIntrinsicHeight<TLength>,
  "contain-intrinsic-inline-size"?: Property$ContainIntrinsicInlineSize<TLength>,
  "contain-intrinsic-width"?: Property$ContainIntrinsicWidth<TLength>,
  "container-name"?: Property$ContainerName,
  "container-type"?: Property$ContainerType,
  content?: Property$Content,
  "content-visibility"?: Property$ContentVisibility,
  "counter-increment"?: Property$CounterIncrement,
  "counter-reset"?: Property$CounterReset,
  "counter-set"?: Property$CounterSet,
  cursor?: Property$Cursor,
  direction?: Property$Direction,
  display?: Property$Display,
  "empty-cells"?: Property$EmptyCells,
  filter?: Property$Filter,
  "flex-basis"?: Property$FlexBasis<TLength>,
  "flex-direction"?: Property$FlexDirection,
  "flex-grow"?: Property$FlexGrow,
  "flex-shrink"?: Property$FlexShrink,
  "flex-wrap"?: Property$FlexWrap,
  float?: Property$Float,
  "font-family"?: Property$FontFamily,
  "font-feature-settings"?: Property$FontFeatureSettings,
  "font-kerning"?: Property$FontKerning,
  "font-language-override"?: Property$FontLanguageOverride,
  "font-optical-sizing"?: Property$FontOpticalSizing,
  "font-palette"?: Property$FontPalette,
  "font-size"?: Property$FontSize<TLength>,
  "font-size-adjust"?: Property$FontSizeAdjust,
  "font-smooth"?: Property$FontSmooth<TLength>,
  "font-stretch"?: Property$FontStretch,
  "font-style"?: Property$FontStyle,
  "font-synthesis"?: Property$FontSynthesis,
  "font-synthesis-position"?: Property$FontSynthesisPosition,
  "font-synthesis-small-caps"?: Property$FontSynthesisSmallCaps,
  "font-synthesis-style"?: Property$FontSynthesisStyle,
  "font-synthesis-weight"?: Property$FontSynthesisWeight,
  "font-variant"?: Property$FontVariant,
  "font-variant-alternates"?: Property$FontVariantAlternates,
  "font-variant-caps"?: Property$FontVariantCaps,
  "font-variant-east-asian"?: Property$FontVariantEastAsian,
  "font-variant-emoji"?: Property$FontVariantEmoji,
  "font-variant-ligatures"?: Property$FontVariantLigatures,
  "font-variant-numeric"?: Property$FontVariantNumeric,
  "font-variant-position"?: Property$FontVariantPosition,
  "font-variation-settings"?: Property$FontVariationSettings,
  "font-weight"?: Property$FontWeight,
  "forced-color-adjust"?: Property$ForcedColorAdjust,
  "grid-auto-columns"?: Property$GridAutoColumns<TLength>,
  "grid-auto-flow"?: Property$GridAutoFlow,
  "grid-auto-rows"?: Property$GridAutoRows<TLength>,
  "grid-column-end"?: Property$GridColumnEnd,
  "grid-column-start"?: Property$GridColumnStart,
  "grid-row-end"?: Property$GridRowEnd,
  "grid-row-start"?: Property$GridRowStart,
  "grid-template-areas"?: Property$GridTemplateAreas,
  "grid-template-columns"?: Property$GridTemplateColumns<TLength>,
  "grid-template-rows"?: Property$GridTemplateRows<TLength>,
  "hanging-punctuation"?: Property$HangingPunctuation,
  height?: Property$Height<TLength>,
  "hyphenate-character"?: Property$HyphenateCharacter,
  "hyphenate-limit-chars"?: Property$HyphenateLimitChars,
  hyphens?: Property$Hyphens,
  "image-orientation"?: Property$ImageOrientation,
  "image-rendering"?: Property$ImageRendering,
  "image-resolution"?: Property$ImageResolution,
  "initial-letter"?: Property$InitialLetter,
  "inline-size"?: Property$InlineSize<TLength>,
  "input-security"?: Property$InputSecurity,
  "inset-block-end"?: Property$InsetBlockEnd<TLength>,
  "inset-block-start"?: Property$InsetBlockStart<TLength>,
  "inset-inline-end"?: Property$InsetInlineEnd<TLength>,
  "inset-inline-start"?: Property$InsetInlineStart<TLength>,
  isolation?: Property$Isolation,
  "justify-content"?: Property$JustifyContent,
  "justify-items"?: Property$JustifyItems,
  "justify-self"?: Property$JustifySelf,
  "justify-tracks"?: Property$JustifyTracks,
  left?: Property$Left<TLength>,
  "letter-spacing"?: Property$LetterSpacing<TLength>,
  "line-break"?: Property$LineBreak,
  "line-height"?: Property$LineHeight<TLength>,
  "line-height-step"?: Property$LineHeightStep<TLength>,
  "list-style-image"?: Property$ListStyleImage,
  "list-style-position"?: Property$ListStylePosition,
  "list-style-type"?: Property$ListStyleType,
  "margin-block-end"?: Property$MarginBlockEnd<TLength>,
  "margin-block-start"?: Property$MarginBlockStart<TLength>,
  "margin-bottom"?: Property$MarginBottom<TLength>,
  "margin-inline-end"?: Property$MarginInlineEnd<TLength>,
  "margin-inline-start"?: Property$MarginInlineStart<TLength>,
  "margin-left"?: Property$MarginLeft<TLength>,
  "margin-right"?: Property$MarginRight<TLength>,
  "margin-top"?: Property$MarginTop<TLength>,
  "margin-trim"?: Property$MarginTrim,
  "mask-border-mode"?: Property$MaskBorderMode,
  "mask-border-outset"?: Property$MaskBorderOutset<TLength>,
  "mask-border-repeat"?: Property$MaskBorderRepeat,
  "mask-border-slice"?: Property$MaskBorderSlice,
  "mask-border-source"?: Property$MaskBorderSource,
  "mask-border-width"?: Property$MaskBorderWidth<TLength>,
  "mask-clip"?: Property$MaskClip,
  "mask-composite"?: Property$MaskComposite,
  "mask-image"?: Property$MaskImage,
  "mask-mode"?: Property$MaskMode,
  "mask-origin"?: Property$MaskOrigin,
  "mask-position"?: Property$MaskPosition<TLength>,
  "mask-repeat"?: Property$MaskRepeat,
  "mask-size"?: Property$MaskSize<TLength>,
  "mask-type"?: Property$MaskType,
  "masonry-auto-flow"?: Property$MasonryAutoFlow,
  "math-depth"?: Property$MathDepth,
  "math-shift"?: Property$MathShift,
  "math-style"?: Property$MathStyle,
  "max-block-size"?: Property$MaxBlockSize<TLength>,
  "max-height"?: Property$MaxHeight<TLength>,
  "max-inline-size"?: Property$MaxInlineSize<TLength>,
  "max-lines"?: Property$MaxLines,
  "max-width"?: Property$MaxWidth<TLength>,
  "min-block-size"?: Property$MinBlockSize<TLength>,
  "min-height"?: Property$MinHeight<TLength>,
  "min-inline-size"?: Property$MinInlineSize<TLength>,
  "min-width"?: Property$MinWidth<TLength>,
  "mix-blend-mode"?: Property$MixBlendMode,
  "motion-distance"?: Property$OffsetDistance<TLength>,
  "motion-path"?: Property$OffsetPath,
  "motion-rotation"?: Property$OffsetRotate,
  "object-fit"?: Property$ObjectFit,
  "object-position"?: Property$ObjectPosition<TLength>,
  "offset-anchor"?: Property$OffsetAnchor<TLength>,
  "offset-distance"?: Property$OffsetDistance<TLength>,
  "offset-path"?: Property$OffsetPath,
  "offset-position"?: Property$OffsetPosition<TLength>,
  "offset-rotate"?: Property$OffsetRotate,
  "offset-rotation"?: Property$OffsetRotate,
  opacity?: Property$Opacity,
  order?: Property$Order,
  orphans?: Property$Orphans,
  "outline-color"?: Property$OutlineColor,
  "outline-offset"?: Property$OutlineOffset<TLength>,
  "outline-style"?: Property$OutlineStyle,
  "outline-width"?: Property$OutlineWidth<TLength>,
  "overflow-anchor"?: Property$OverflowAnchor,
  "overflow-block"?: Property$OverflowBlock,
  "overflow-clip-box"?: Property$OverflowClipBox,
  "overflow-clip-margin"?: Property$OverflowClipMargin<TLength>,
  "overflow-inline"?: Property$OverflowInline,
  "overflow-wrap"?: Property$OverflowWrap,
  "overflow-x"?: Property$OverflowX,
  "overflow-y"?: Property$OverflowY,
  overlay?: Property$Overlay,
  "overscroll-behavior-block"?: Property$OverscrollBehaviorBlock,
  "overscroll-behavior-inline"?: Property$OverscrollBehaviorInline,
  "overscroll-behavior-x"?: Property$OverscrollBehaviorX,
  "overscroll-behavior-y"?: Property$OverscrollBehaviorY,
  "padding-block-end"?: Property$PaddingBlockEnd<TLength>,
  "padding-block-start"?: Property$PaddingBlockStart<TLength>,
  "padding-bottom"?: Property$PaddingBottom<TLength>,
  "padding-inline-end"?: Property$PaddingInlineEnd<TLength>,
  "padding-inline-start"?: Property$PaddingInlineStart<TLength>,
  "padding-left"?: Property$PaddingLeft<TLength>,
  "padding-right"?: Property$PaddingRight<TLength>,
  "padding-top"?: Property$PaddingTop<TLength>,
  page?: Property$Page,
  "page-break-after"?: Property$PageBreakAfter,
  "page-break-before"?: Property$PageBreakBefore,
  "page-break-inside"?: Property$PageBreakInside,
  "paint-order"?: Property$PaintOrder,
  perspective?: Property$Perspective<TLength>,
  "perspective-origin"?: Property$PerspectiveOrigin<TLength>,
  "pointer-events"?: Property$PointerEvents,
  position?: Property$Position,
  "print-color-adjust"?: Property$PrintColorAdjust,
  quotes?: Property$Quotes,
  resize?: Property$Resize,
  right?: Property$Right<TLength>,
  rotate?: Property$Rotate,
  "row-gap"?: Property$RowGap<TLength>,
  "ruby-align"?: Property$RubyAlign,
  "ruby-merge"?: Property$RubyMerge,
  "ruby-position"?: Property$RubyPosition,
  scale?: Property$Scale,
  "scroll-behavior"?: Property$ScrollBehavior,
  "scroll-margin-block-end"?: Property$ScrollMarginBlockEnd<TLength>,
  "scroll-margin-block-start"?: Property$ScrollMarginBlockStart<TLength>,
  "scroll-margin-bottom"?: Property$ScrollMarginBottom<TLength>,
  "scroll-margin-inline-end"?: Property$ScrollMarginInlineEnd<TLength>,
  "scroll-margin-inline-start"?: Property$ScrollMarginInlineStart<TLength>,
  "scroll-margin-left"?: Property$ScrollMarginLeft<TLength>,
  "scroll-margin-right"?: Property$ScrollMarginRight<TLength>,
  "scroll-margin-top"?: Property$ScrollMarginTop<TLength>,
  "scroll-padding-block-end"?: Property$ScrollPaddingBlockEnd<TLength>,
  "scroll-padding-block-start"?: Property$ScrollPaddingBlockStart<TLength>,
  "scroll-padding-bottom"?: Property$ScrollPaddingBottom<TLength>,
  "scroll-padding-inline-end"?: Property$ScrollPaddingInlineEnd<TLength>,
  "scroll-padding-inline-start"?: Property$ScrollPaddingInlineStart<TLength>,
  "scroll-padding-left"?: Property$ScrollPaddingLeft<TLength>,
  "scroll-padding-right"?: Property$ScrollPaddingRight<TLength>,
  "scroll-padding-top"?: Property$ScrollPaddingTop<TLength>,
  "scroll-snap-align"?: Property$ScrollSnapAlign,
  "scroll-snap-margin-bottom"?: Property$ScrollMarginBottom<TLength>,
  "scroll-snap-margin-left"?: Property$ScrollMarginLeft<TLength>,
  "scroll-snap-margin-right"?: Property$ScrollMarginRight<TLength>,
  "scroll-snap-margin-top"?: Property$ScrollMarginTop<TLength>,
  "scroll-snap-stop"?: Property$ScrollSnapStop,
  "scroll-snap-type"?: Property$ScrollSnapType,
  "scroll-timeline-axis"?: Property$ScrollTimelineAxis,
  "scroll-timeline-name"?: Property$ScrollTimelineName,
  "scrollbar-color"?: Property$ScrollbarColor,
  "scrollbar-gutter"?: Property$ScrollbarGutter,
  "scrollbar-width"?: Property$ScrollbarWidth,
  "shape-image-threshold"?: Property$ShapeImageThreshold,
  "shape-margin"?: Property$ShapeMargin<TLength>,
  "shape-outside"?: Property$ShapeOutside,
  "tab-size"?: Property$TabSize<TLength>,
  "table-layout"?: Property$TableLayout,
  "text-align"?: Property$TextAlign,
  "text-align-last"?: Property$TextAlignLast,
  "text-combine-upright"?: Property$TextCombineUpright,
  "text-decoration-color"?: Property$TextDecorationColor,
  "text-decoration-line"?: Property$TextDecorationLine,
  "text-decoration-skip"?: Property$TextDecorationSkip,
  "text-decoration-skip-ink"?: Property$TextDecorationSkipInk,
  "text-decoration-style"?: Property$TextDecorationStyle,
  "text-decoration-thickness"?: Property$TextDecorationThickness<TLength>,
  "text-emphasis-color"?: Property$TextEmphasisColor,
  "text-emphasis-position"?: Property$TextEmphasisPosition,
  "text-emphasis-style"?: Property$TextEmphasisStyle,
  "text-indent"?: Property$TextIndent<TLength>,
  "text-justify"?: Property$TextJustify,
  "text-orientation"?: Property$TextOrientation,
  "text-overflow"?: Property$TextOverflow,
  "text-rendering"?: Property$TextRendering,
  "text-shadow"?: Property$TextShadow,
  "text-size-adjust"?: Property$TextSizeAdjust,
  "text-transform"?: Property$TextTransform,
  "text-underline-offset"?: Property$TextUnderlineOffset<TLength>,
  "text-underline-position"?: Property$TextUnderlinePosition,
  "text-wrap"?: Property$TextWrap,
  "timeline-scope"?: Property$TimelineScope,
  top?: Property$Top<TLength>,
  "touch-action"?: Property$TouchAction,
  transform?: Property$Transform,
  "transform-box"?: Property$TransformBox,
  "transform-origin"?: Property$TransformOrigin<TLength>,
  "transform-style"?: Property$TransformStyle,
  "transition-behavior"?: Property$TransitionBehavior,
  "transition-delay"?: Property$TransitionDelay<TTime>,
  "transition-duration"?: Property$TransitionDuration<TTime>,
  "transition-property"?: Property$TransitionProperty,
  "transition-timing-function"?: Property$TransitionTimingFunction,
  translate?: Property$Translate<TLength>,
  "unicode-bidi"?: Property$UnicodeBidi,
  "user-select"?: Property$UserSelect,
  "vertical-align"?: Property$VerticalAlign<TLength>,
  "view-timeline-axis"?: Property$ViewTimelineAxis,
  "view-timeline-inset"?: Property$ViewTimelineInset<TLength>,
  "view-timeline-name"?: Property$ViewTimelineName,
  "view-transition-name"?: Property$ViewTransitionName,
  visibility?: Property$Visibility,
  "white-space"?: Property$WhiteSpace,
  "white-space-collapse"?: Property$WhiteSpaceCollapse,
  "white-space-trim"?: Property$WhiteSpaceTrim,
  widows?: Property$Widows,
  width?: Property$Width<TLength>,
  "will-change"?: Property$WillChange,
  "word-break"?: Property$WordBreak,
  "word-spacing"?: Property$WordSpacing<TLength>,
  "word-wrap"?: Property$WordWrap,
  "writing-mode"?: Property$WritingMode,
  "z-index"?: Property$ZIndex,
  zoom?: Property$Zoom,
|};

export type StandardShorthandPropertiesHyphen<TLength = string | 0, TTime = string> = {|
  all?: Property$All,
  animation?: Property$Animation<TTime>,
  "animation-range"?: Property$AnimationRange<TLength>,
  background?: Property$Background<TLength>,
  "background-position"?: Property$BackgroundPosition<TLength>,
  border?: Property$Border<TLength>,
  "border-block"?: Property$BorderBlock<TLength>,
  "border-block-end"?: Property$BorderBlockEnd<TLength>,
  "border-block-start"?: Property$BorderBlockStart<TLength>,
  "border-bottom"?: Property$BorderBottom<TLength>,
  "border-color"?: Property$BorderColor,
  "border-image"?: Property$BorderImage,
  "border-inline"?: Property$BorderInline<TLength>,
  "border-inline-end"?: Property$BorderInlineEnd<TLength>,
  "border-inline-start"?: Property$BorderInlineStart<TLength>,
  "border-left"?: Property$BorderLeft<TLength>,
  "border-radius"?: Property$BorderRadius<TLength>,
  "border-right"?: Property$BorderRight<TLength>,
  "border-style"?: Property$BorderStyle,
  "border-top"?: Property$BorderTop<TLength>,
  "border-width"?: Property$BorderWidth<TLength>,
  caret?: Property$Caret,
  "column-rule"?: Property$ColumnRule<TLength>,
  columns?: Property$Columns<TLength>,
  "contain-intrinsic-size"?: Property$ContainIntrinsicSize<TLength>,
  container?: Property$Container,
  flex?: Property$Flex<TLength>,
  "flex-flow"?: Property$FlexFlow,
  font?: Property$Font,
  gap?: Property$Gap<TLength>,
  grid?: Property$Grid,
  "grid-area"?: Property$GridArea,
  "grid-column"?: Property$GridColumn,
  "grid-row"?: Property$GridRow,
  "grid-template"?: Property$GridTemplate,
  inset?: Property$Inset<TLength>,
  "inset-block"?: Property$InsetBlock<TLength>,
  "inset-inline"?: Property$InsetInline<TLength>,
  "line-clamp"?: Property$LineClamp,
  "list-style"?: Property$ListStyle,
  margin?: Property$Margin<TLength>,
  "margin-block"?: Property$MarginBlock<TLength>,
  "margin-inline"?: Property$MarginInline<TLength>,
  mask?: Property$Mask<TLength>,
  "mask-border"?: Property$MaskBorder,
  motion?: Property$Offset<TLength>,
  offset?: Property$Offset<TLength>,
  outline?: Property$Outline<TLength>,
  overflow?: Property$Overflow,
  "overscroll-behavior"?: Property$OverscrollBehavior,
  padding?: Property$Padding<TLength>,
  "padding-block"?: Property$PaddingBlock<TLength>,
  "padding-inline"?: Property$PaddingInline<TLength>,
  "place-content"?: Property$PlaceContent,
  "place-items"?: Property$PlaceItems,
  "place-self"?: Property$PlaceSelf,
  "scroll-margin"?: Property$ScrollMargin<TLength>,
  "scroll-margin-block"?: Property$ScrollMarginBlock<TLength>,
  "scroll-margin-inline"?: Property$ScrollMarginInline<TLength>,
  "scroll-padding"?: Property$ScrollPadding<TLength>,
  "scroll-padding-block"?: Property$ScrollPaddingBlock<TLength>,
  "scroll-padding-inline"?: Property$ScrollPaddingInline<TLength>,
  "scroll-snap-margin"?: Property$ScrollMargin<TLength>,
  "scroll-timeline"?: Property$ScrollTimeline,
  "text-decoration"?: Property$TextDecoration<TLength>,
  "text-emphasis"?: Property$TextEmphasis,
  transition?: Property$Transition<TTime>,
  "view-timeline"?: Property$ViewTimeline,
|};

export type StandardPropertiesHyphen<TLength = string | 0, TTime = string> = {|
  ...StandardLonghandPropertiesHyphen<TLength, TTime>,
  ...StandardShorthandPropertiesHyphen<TLength, TTime>,
|};

export type VendorLonghandPropertiesHyphen<TLength = string | 0, TTime = string> = {|
  "-moz-animation-delay"?: Property$AnimationDelay<TTime>,
  "-moz-animation-direction"?: Property$AnimationDirection,
  "-moz-animation-duration"?: Property$AnimationDuration<TTime>,
  "-moz-animation-fill-mode"?: Property$AnimationFillMode,
  "-moz-animation-iteration-count"?: Property$AnimationIterationCount,
  "-moz-animation-name"?: Property$AnimationName,
  "-moz-animation-play-state"?: Property$AnimationPlayState,
  "-moz-animation-timing-function"?: Property$AnimationTimingFunction,
  "-moz-appearance"?: Property$MozAppearance,
  "-moz-binding"?: Property$MozBinding,
  "-moz-border-bottom-colors"?: Property$MozBorderBottomColors,
  "-moz-border-end-color"?: Property$BorderInlineEndColor,
  "-moz-border-end-style"?: Property$BorderInlineEndStyle,
  "-moz-border-end-width"?: Property$BorderInlineEndWidth<TLength>,
  "-moz-border-left-colors"?: Property$MozBorderLeftColors,
  "-moz-border-right-colors"?: Property$MozBorderRightColors,
  "-moz-border-start-color"?: Property$BorderInlineStartColor,
  "-moz-border-start-style"?: Property$BorderInlineStartStyle,
  "-moz-border-top-colors"?: Property$MozBorderTopColors,
  "-moz-box-sizing"?: Property$BoxSizing,
  "-moz-column-count"?: Property$ColumnCount,
  "-moz-column-fill"?: Property$ColumnFill,
  "-moz-column-rule-color"?: Property$ColumnRuleColor,
  "-moz-column-rule-style"?: Property$ColumnRuleStyle,
  "-moz-column-rule-width"?: Property$ColumnRuleWidth<TLength>,
  "-moz-column-width"?: Property$ColumnWidth<TLength>,
  "-moz-context-properties"?: Property$MozContextProperties,
  "-moz-font-feature-settings"?: Property$FontFeatureSettings,
  "-moz-font-language-override"?: Property$FontLanguageOverride,
  "-moz-hyphens"?: Property$Hyphens,
  "-moz-image-region"?: Property$MozImageRegion,
  "-moz-margin-end"?: Property$MarginInlineEnd<TLength>,
  "-moz-margin-start"?: Property$MarginInlineStart<TLength>,
  "-moz-orient"?: Property$MozOrient,
  "-moz-osx-font-smoothing"?: Property$FontSmooth<TLength>,
  "-moz-outline-radius-bottomleft"?: Property$MozOutlineRadiusBottomleft<TLength>,
  "-moz-outline-radius-bottomright"?: Property$MozOutlineRadiusBottomright<TLength>,
  "-moz-outline-radius-topleft"?: Property$MozOutlineRadiusTopleft<TLength>,
  "-moz-outline-radius-topright"?: Property$MozOutlineRadiusTopright<TLength>,
  "-moz-padding-end"?: Property$PaddingInlineEnd<TLength>,
  "-moz-padding-start"?: Property$PaddingInlineStart<TLength>,
  "-moz-stack-sizing"?: Property$MozStackSizing,
  "-moz-tab-size"?: Property$TabSize<TLength>,
  "-moz-text-blink"?: Property$MozTextBlink,
  "-moz-text-size-adjust"?: Property$TextSizeAdjust,
  "-moz-user-focus"?: Property$MozUserFocus,
  "-moz-user-modify"?: Property$MozUserModify,
  "-moz-user-select"?: Property$UserSelect,
  "-moz-window-dragging"?: Property$MozWindowDragging,
  "-moz-window-shadow"?: Property$MozWindowShadow,
  "-ms-accelerator"?: Property$MsAccelerator,
  "-ms-block-progression"?: Property$MsBlockProgression,
  "-ms-content-zoom-chaining"?: Property$MsContentZoomChaining,
  "-ms-content-zoom-limit-max"?: Property$MsContentZoomLimitMax,
  "-ms-content-zoom-limit-min"?: Property$MsContentZoomLimitMin,
  "-ms-content-zoom-snap-points"?: Property$MsContentZoomSnapPoints,
  "-ms-content-zoom-snap-type"?: Property$MsContentZoomSnapType,
  "-ms-content-zooming"?: Property$MsContentZooming,
  "-ms-filter"?: Property$MsFilter,
  "-ms-flex-direction"?: Property$FlexDirection,
  "-ms-flex-positive"?: Property$FlexGrow,
  "-ms-flow-from"?: Property$MsFlowFrom,
  "-ms-flow-into"?: Property$MsFlowInto,
  "-ms-grid-columns"?: Property$MsGridColumns<TLength>,
  "-ms-grid-rows"?: Property$MsGridRows<TLength>,
  "-ms-high-contrast-adjust"?: Property$MsHighContrastAdjust,
  "-ms-hyphenate-limit-chars"?: Property$MsHyphenateLimitChars,
  "-ms-hyphenate-limit-lines"?: Property$MsHyphenateLimitLines,
  "-ms-hyphenate-limit-zone"?: Property$MsHyphenateLimitZone<TLength>,
  "-ms-hyphens"?: Property$Hyphens,
  "-ms-ime-align"?: Property$MsImeAlign,
  "-ms-line-break"?: Property$LineBreak,
  "-ms-order"?: Property$Order,
  "-ms-overflow-style"?: Property$MsOverflowStyle,
  "-ms-overflow-x"?: Property$OverflowX,
  "-ms-overflow-y"?: Property$OverflowY,
  "-ms-scroll-chaining"?: Property$MsScrollChaining,
  "-ms-scroll-limit-x-max"?: Property$MsScrollLimitXMax<TLength>,
  "-ms-scroll-limit-x-min"?: Property$MsScrollLimitXMin<TLength>,
  "-ms-scroll-limit-y-max"?: Property$MsScrollLimitYMax<TLength>,
  "-ms-scroll-limit-y-min"?: Property$MsScrollLimitYMin<TLength>,
  "-ms-scroll-rails"?: Property$MsScrollRails,
  "-ms-scroll-snap-points-x"?: Property$MsScrollSnapPointsX,
  "-ms-scroll-snap-points-y"?: Property$MsScrollSnapPointsY,
  "-ms-scroll-snap-type"?: Property$MsScrollSnapType,
  "-ms-scroll-translation"?: Property$MsScrollTranslation,
  "-ms-scrollbar-3dlight-color"?: Property$MsScrollbar3dlightColor,
  "-ms-scrollbar-arrow-color"?: Property$MsScrollbarArrowColor,
  "-ms-scrollbar-base-color"?: Property$MsScrollbarBaseColor,
  "-ms-scrollbar-darkshadow-color"?: Property$MsScrollbarDarkshadowColor,
  "-ms-scrollbar-face-color"?: Property$MsScrollbarFaceColor,
  "-ms-scrollbar-highlight-color"?: Property$MsScrollbarHighlightColor,
  "-ms-scrollbar-shadow-color"?: Property$MsScrollbarShadowColor,
  "-ms-scrollbar-track-color"?: Property$MsScrollbarTrackColor,
  "-ms-text-autospace"?: Property$MsTextAutospace,
  "-ms-text-combine-horizontal"?: Property$TextCombineUpright,
  "-ms-text-overflow"?: Property$TextOverflow,
  "-ms-touch-action"?: Property$TouchAction,
  "-ms-touch-select"?: Property$MsTouchSelect,
  "-ms-transform"?: Property$Transform,
  "-ms-transform-origin"?: Property$TransformOrigin<TLength>,
  "-ms-transition-delay"?: Property$TransitionDelay<TTime>,
  "-ms-transition-duration"?: Property$TransitionDuration<TTime>,
  "-ms-transition-property"?: Property$TransitionProperty,
  "-ms-transition-timing-function"?: Property$TransitionTimingFunction,
  "-ms-user-select"?: Property$MsUserSelect,
  "-ms-word-break"?: Property$WordBreak,
  "-ms-wrap-flow"?: Property$MsWrapFlow,
  "-ms-wrap-margin"?: Property$MsWrapMargin<TLength>,
  "-ms-wrap-through"?: Property$MsWrapThrough,
  "-ms-writing-mode"?: Property$WritingMode,
  "-webkit-align-content"?: Property$AlignContent,
  "-webkit-align-items"?: Property$AlignItems,
  "-webkit-align-self"?: Property$AlignSelf,
  "-webkit-animation-delay"?: Property$AnimationDelay<TTime>,
  "-webkit-animation-direction"?: Property$AnimationDirection,
  "-webkit-animation-duration"?: Property$AnimationDuration<TTime>,
  "-webkit-animation-fill-mode"?: Property$AnimationFillMode,
  "-webkit-animation-iteration-count"?: Property$AnimationIterationCount,
  "-webkit-animation-name"?: Property$AnimationName,
  "-webkit-animation-play-state"?: Property$AnimationPlayState,
  "-webkit-animation-timing-function"?: Property$AnimationTimingFunction,
  "-webkit-appearance"?: Property$WebkitAppearance,
  "-webkit-backdrop-filter"?: Property$BackdropFilter,
  "-webkit-backface-visibility"?: Property$BackfaceVisibility,
  "-webkit-background-clip"?: Property$BackgroundClip,
  "-webkit-background-origin"?: Property$BackgroundOrigin,
  "-webkit-background-size"?: Property$BackgroundSize<TLength>,
  "-webkit-border-before-color"?: Property$WebkitBorderBeforeColor,
  "-webkit-border-before-style"?: Property$WebkitBorderBeforeStyle,
  "-webkit-border-before-width"?: Property$WebkitBorderBeforeWidth<TLength>,
  "-webkit-border-bottom-left-radius"?: Property$BorderBottomLeftRadius<TLength>,
  "-webkit-border-bottom-right-radius"?: Property$BorderBottomRightRadius<TLength>,
  "-webkit-border-image-slice"?: Property$BorderImageSlice,
  "-webkit-border-top-left-radius"?: Property$BorderTopLeftRadius<TLength>,
  "-webkit-border-top-right-radius"?: Property$BorderTopRightRadius<TLength>,
  "-webkit-box-decoration-break"?: Property$BoxDecorationBreak,
  "-webkit-box-reflect"?: Property$WebkitBoxReflect<TLength>,
  "-webkit-box-shadow"?: Property$BoxShadow,
  "-webkit-box-sizing"?: Property$BoxSizing,
  "-webkit-clip-path"?: Property$ClipPath,
  "-webkit-column-count"?: Property$ColumnCount,
  "-webkit-column-fill"?: Property$ColumnFill,
  "-webkit-column-rule-color"?: Property$ColumnRuleColor,
  "-webkit-column-rule-style"?: Property$ColumnRuleStyle,
  "-webkit-column-rule-width"?: Property$ColumnRuleWidth<TLength>,
  "-webkit-column-span"?: Property$ColumnSpan,
  "-webkit-column-width"?: Property$ColumnWidth<TLength>,
  "-webkit-filter"?: Property$Filter,
  "-webkit-flex-basis"?: Property$FlexBasis<TLength>,
  "-webkit-flex-direction"?: Property$FlexDirection,
  "-webkit-flex-grow"?: Property$FlexGrow,
  "-webkit-flex-shrink"?: Property$FlexShrink,
  "-webkit-flex-wrap"?: Property$FlexWrap,
  "-webkit-font-feature-settings"?: Property$FontFeatureSettings,
  "-webkit-font-kerning"?: Property$FontKerning,
  "-webkit-font-smoothing"?: Property$FontSmooth<TLength>,
  "-webkit-font-variant-ligatures"?: Property$FontVariantLigatures,
  "-webkit-hyphenate-character"?: Property$HyphenateCharacter,
  "-webkit-hyphens"?: Property$Hyphens,
  "-webkit-initial-letter"?: Property$InitialLetter,
  "-webkit-justify-content"?: Property$JustifyContent,
  "-webkit-line-break"?: Property$LineBreak,
  "-webkit-line-clamp"?: Property$WebkitLineClamp,
  "-webkit-margin-end"?: Property$MarginInlineEnd<TLength>,
  "-webkit-margin-start"?: Property$MarginInlineStart<TLength>,
  "-webkit-mask-attachment"?: Property$WebkitMaskAttachment,
  "-webkit-mask-box-image-outset"?: Property$MaskBorderOutset<TLength>,
  "-webkit-mask-box-image-repeat"?: Property$MaskBorderRepeat,
  "-webkit-mask-box-image-slice"?: Property$MaskBorderSlice,
  "-webkit-mask-box-image-source"?: Property$MaskBorderSource,
  "-webkit-mask-box-image-width"?: Property$MaskBorderWidth<TLength>,
  "-webkit-mask-clip"?: Property$WebkitMaskClip,
  "-webkit-mask-composite"?: Property$WebkitMaskComposite,
  "-webkit-mask-image"?: Property$WebkitMaskImage,
  "-webkit-mask-origin"?: Property$WebkitMaskOrigin,
  "-webkit-mask-position"?: Property$WebkitMaskPosition<TLength>,
  "-webkit-mask-position-x"?: Property$WebkitMaskPositionX<TLength>,
  "-webkit-mask-position-y"?: Property$WebkitMaskPositionY<TLength>,
  "-webkit-mask-repeat"?: Property$WebkitMaskRepeat,
  "-webkit-mask-repeat-x"?: Property$WebkitMaskRepeatX,
  "-webkit-mask-repeat-y"?: Property$WebkitMaskRepeatY,
  "-webkit-mask-size"?: Property$WebkitMaskSize<TLength>,
  "-webkit-max-inline-size"?: Property$MaxInlineSize<TLength>,
  "-webkit-order"?: Property$Order,
  "-webkit-overflow-scrolling"?: Property$WebkitOverflowScrolling,
  "-webkit-padding-end"?: Property$PaddingInlineEnd<TLength>,
  "-webkit-padding-start"?: Property$PaddingInlineStart<TLength>,
  "-webkit-perspective"?: Property$Perspective<TLength>,
  "-webkit-perspective-origin"?: Property$PerspectiveOrigin<TLength>,
  "-webkit-print-color-adjust"?: Property$PrintColorAdjust,
  "-webkit-ruby-position"?: Property$RubyPosition,
  "-webkit-scroll-snap-type"?: Property$ScrollSnapType,
  "-webkit-shape-margin"?: Property$ShapeMargin<TLength>,
  "-webkit-tap-highlight-color"?: Property$WebkitTapHighlightColor,
  "-webkit-text-combine"?: Property$TextCombineUpright,
  "-webkit-text-decoration-color"?: Property$TextDecorationColor,
  "-webkit-text-decoration-line"?: Property$TextDecorationLine,
  "-webkit-text-decoration-skip"?: Property$TextDecorationSkip,
  "-webkit-text-decoration-style"?: Property$TextDecorationStyle,
  "-webkit-text-emphasis-color"?: Property$TextEmphasisColor,
  "-webkit-text-emphasis-position"?: Property$TextEmphasisPosition,
  "-webkit-text-emphasis-style"?: Property$TextEmphasisStyle,
  "-webkit-text-fill-color"?: Property$WebkitTextFillColor,
  "-webkit-text-orientation"?: Property$TextOrientation,
  "-webkit-text-size-adjust"?: Property$TextSizeAdjust,
  "-webkit-text-stroke-color"?: Property$WebkitTextStrokeColor,
  "-webkit-text-stroke-width"?: Property$WebkitTextStrokeWidth<TLength>,
  "-webkit-text-underline-position"?: Property$TextUnderlinePosition,
  "-webkit-touch-callout"?: Property$WebkitTouchCallout,
  "-webkit-transform"?: Property$Transform,
  "-webkit-transform-origin"?: Property$TransformOrigin<TLength>,
  "-webkit-transform-style"?: Property$TransformStyle,
  "-webkit-transition-delay"?: Property$TransitionDelay<TTime>,
  "-webkit-transition-duration"?: Property$TransitionDuration<TTime>,
  "-webkit-transition-property"?: Property$TransitionProperty,
  "-webkit-transition-timing-function"?: Property$TransitionTimingFunction,
  "-webkit-user-modify"?: Property$WebkitUserModify,
  "-webkit-user-select"?: Property$UserSelect,
  "-webkit-writing-mode"?: Property$WritingMode,
|};

export type VendorShorthandPropertiesHyphen<TLength = string | 0, TTime = string> = {|
  "-moz-animation"?: Property$Animation<TTime>,
  "-moz-border-image"?: Property$BorderImage,
  "-moz-column-rule"?: Property$ColumnRule<TLength>,
  "-moz-columns"?: Property$Columns<TLength>,
  "-moz-outline-radius"?: Property$MozOutlineRadius<TLength>,
  "-ms-content-zoom-limit"?: Property$MsContentZoomLimit,
  "-ms-content-zoom-snap"?: Property$MsContentZoomSnap,
  "-ms-flex"?: Property$Flex<TLength>,
  "-ms-scroll-limit"?: Property$MsScrollLimit,
  "-ms-scroll-snap-x"?: Property$MsScrollSnapX,
  "-ms-scroll-snap-y"?: Property$MsScrollSnapY,
  "-ms-transition"?: Property$Transition<TTime>,
  "-webkit-animation"?: Property$Animation<TTime>,
  "-webkit-border-before"?: Property$WebkitBorderBefore<TLength>,
  "-webkit-border-image"?: Property$BorderImage,
  "-webkit-border-radius"?: Property$BorderRadius<TLength>,
  "-webkit-column-rule"?: Property$ColumnRule<TLength>,
  "-webkit-columns"?: Property$Columns<TLength>,
  "-webkit-flex"?: Property$Flex<TLength>,
  "-webkit-flex-flow"?: Property$FlexFlow,
  "-webkit-mask"?: Property$WebkitMask<TLength>,
  "-webkit-mask-box-image"?: Property$MaskBorder,
  "-webkit-text-emphasis"?: Property$TextEmphasis,
  "-webkit-text-stroke"?: Property$WebkitTextStroke<TLength>,
  "-webkit-transition"?: Property$Transition<TTime>,
|};

export type VendorPropertiesHyphen<TLength = string | 0, TTime = string> = {|
  ...VendorLonghandPropertiesHyphen<TLength, TTime>,
  ...VendorShorthandPropertiesHyphen<TLength, TTime>,
|};

export type ObsoletePropertiesHyphen<TLength = string | 0, TTime = string> = {|
  azimuth?: Property$Azimuth,
  "box-align"?: Property$BoxAlign,
  "box-direction"?: Property$BoxDirection,
  "box-flex"?: Property$BoxFlex,
  "box-flex-group"?: Property$BoxFlexGroup,
  "box-lines"?: Property$BoxLines,
  "box-ordinal-group"?: Property$BoxOrdinalGroup,
  "box-orient"?: Property$BoxOrient,
  "box-pack"?: Property$BoxPack,
  clip?: Property$Clip,
  "grid-column-gap"?: Property$GridColumnGap<TLength>,
  "grid-gap"?: Property$GridGap<TLength>,
  "grid-row-gap"?: Property$GridRowGap<TLength>,
  "ime-mode"?: Property$ImeMode,
  "offset-block"?: Property$InsetBlock<TLength>,
  "offset-block-end"?: Property$InsetBlockEnd<TLength>,
  "offset-block-start"?: Property$InsetBlockStart<TLength>,
  "offset-inline"?: Property$InsetInline<TLength>,
  "offset-inline-end"?: Property$InsetInlineEnd<TLength>,
  "offset-inline-start"?: Property$InsetInlineStart<TLength>,
  "scroll-snap-coordinate"?: Property$ScrollSnapCoordinate<TLength>,
  "scroll-snap-destination"?: Property$ScrollSnapDestination<TLength>,
  "scroll-snap-points-x"?: Property$ScrollSnapPointsX,
  "scroll-snap-points-y"?: Property$ScrollSnapPointsY,
  "scroll-snap-type-x"?: Property$ScrollSnapTypeX,
  "scroll-snap-type-y"?: Property$ScrollSnapTypeY,
  "-khtml-box-align"?: Property$BoxAlign,
  "-khtml-box-direction"?: Property$BoxDirection,
  "-khtml-box-flex"?: Property$BoxFlex,
  "-khtml-box-flex-group"?: Property$BoxFlexGroup,
  "-khtml-box-lines"?: Property$BoxLines,
  "-khtml-box-ordinal-group"?: Property$BoxOrdinalGroup,
  "-khtml-box-orient"?: Property$BoxOrient,
  "-khtml-box-pack"?: Property$BoxPack,
  "-khtml-line-break"?: Property$LineBreak,
  "-khtml-opacity"?: Property$Opacity,
  "-khtml-user-select"?: Property$UserSelect,
  "-moz-backface-visibility"?: Property$BackfaceVisibility,
  "-moz-background-clip"?: Property$BackgroundClip,
  "-moz-background-inline-policy"?: Property$BoxDecorationBreak,
  "-moz-background-origin"?: Property$BackgroundOrigin,
  "-moz-background-size"?: Property$BackgroundSize<TLength>,
  "-moz-border-radius"?: Property$BorderRadius<TLength>,
  "-moz-border-radius-bottomleft"?: Property$BorderBottomLeftRadius<TLength>,
  "-moz-border-radius-bottomright"?: Property$BorderBottomRightRadius<TLength>,
  "-moz-border-radius-topleft"?: Property$BorderTopLeftRadius<TLength>,
  "-moz-border-radius-topright"?: Property$BorderTopRightRadius<TLength>,
  "-moz-box-align"?: Property$BoxAlign,
  "-moz-box-direction"?: Property$BoxDirection,
  "-moz-box-flex"?: Property$BoxFlex,
  "-moz-box-ordinal-group"?: Property$BoxOrdinalGroup,
  "-moz-box-orient"?: Property$BoxOrient,
  "-moz-box-pack"?: Property$BoxPack,
  "-moz-box-shadow"?: Property$BoxShadow,
  "-moz-float-edge"?: Property$MozFloatEdge,
  "-moz-force-broken-image-icon"?: Property$MozForceBrokenImageIcon,
  "-moz-opacity"?: Property$Opacity,
  "-moz-outline"?: Property$Outline<TLength>,
  "-moz-outline-color"?: Property$OutlineColor,
  "-moz-outline-style"?: Property$OutlineStyle,
  "-moz-outline-width"?: Property$OutlineWidth<TLength>,
  "-moz-perspective"?: Property$Perspective<TLength>,
  "-moz-perspective-origin"?: Property$PerspectiveOrigin<TLength>,
  "-moz-text-align-last"?: Property$TextAlignLast,
  "-moz-text-decoration-color"?: Property$TextDecorationColor,
  "-moz-text-decoration-line"?: Property$TextDecorationLine,
  "-moz-text-decoration-style"?: Property$TextDecorationStyle,
  "-moz-transform"?: Property$Transform,
  "-moz-transform-origin"?: Property$TransformOrigin<TLength>,
  "-moz-transform-style"?: Property$TransformStyle,
  "-moz-transition"?: Property$Transition<TTime>,
  "-moz-transition-delay"?: Property$TransitionDelay<TTime>,
  "-moz-transition-duration"?: Property$TransitionDuration<TTime>,
  "-moz-transition-property"?: Property$TransitionProperty,
  "-moz-transition-timing-function"?: Property$TransitionTimingFunction,
  "-moz-user-input"?: Property$MozUserInput,
  "-ms-ime-mode"?: Property$ImeMode,
  "-o-animation"?: Property$Animation<TTime>,
  "-o-animation-delay"?: Property$AnimationDelay<TTime>,
  "-o-animation-direction"?: Property$AnimationDirection,
  "-o-animation-duration"?: Property$AnimationDuration<TTime>,
  "-o-animation-fill-mode"?: Property$AnimationFillMode,
  "-o-animation-iteration-count"?: Property$AnimationIterationCount,
  "-o-animation-name"?: Property$AnimationName,
  "-o-animation-play-state"?: Property$AnimationPlayState,
  "-o-animation-timing-function"?: Property$AnimationTimingFunction,
  "-o-background-size"?: Property$BackgroundSize<TLength>,
  "-o-border-image"?: Property$BorderImage,
  "-o-object-fit"?: Property$ObjectFit,
  "-o-object-position"?: Property$ObjectPosition<TLength>,
  "-o-tab-size"?: Property$TabSize<TLength>,
  "-o-text-overflow"?: Property$TextOverflow,
  "-o-transform"?: Property$Transform,
  "-o-transform-origin"?: Property$TransformOrigin<TLength>,
  "-o-transition"?: Property$Transition<TTime>,
  "-o-transition-delay"?: Property$TransitionDelay<TTime>,
  "-o-transition-duration"?: Property$TransitionDuration<TTime>,
  "-o-transition-property"?: Property$TransitionProperty,
  "-o-transition-timing-function"?: Property$TransitionTimingFunction,
  "-webkit-box-align"?: Property$BoxAlign,
  "-webkit-box-direction"?: Property$BoxDirection,
  "-webkit-box-flex"?: Property$BoxFlex,
  "-webkit-box-flex-group"?: Property$BoxFlexGroup,
  "-webkit-box-lines"?: Property$BoxLines,
  "-webkit-box-ordinal-group"?: Property$BoxOrdinalGroup,
  "-webkit-box-orient"?: Property$BoxOrient,
  "-webkit-box-pack"?: Property$BoxPack,
|};

export type SvgPropertiesHyphen<TLength = string | 0, TTime = string> = {|
  "alignment-baseline"?: Property$AlignmentBaseline,
  "baseline-shift"?: Property$BaselineShift<TLength>,
  clip?: Property$Clip,
  "clip-path"?: Property$ClipPath,
  "clip-rule"?: Property$ClipRule,
  color?: Property$Color,
  "color-interpolation"?: Property$ColorInterpolation,
  "color-rendering"?: Property$ColorRendering,
  cursor?: Property$Cursor,
  direction?: Property$Direction,
  display?: Property$Display,
  "dominant-baseline"?: Property$DominantBaseline,
  fill?: Property$Fill,
  "fill-opacity"?: Property$FillOpacity,
  "fill-rule"?: Property$FillRule,
  filter?: Property$Filter,
  "flood-color"?: Property$FloodColor,
  "flood-opacity"?: Property$FloodOpacity,
  font?: Property$Font,
  "font-family"?: Property$FontFamily,
  "font-size"?: Property$FontSize<TLength>,
  "font-size-adjust"?: Property$FontSizeAdjust,
  "font-stretch"?: Property$FontStretch,
  "font-style"?: Property$FontStyle,
  "font-variant"?: Property$FontVariant,
  "font-weight"?: Property$FontWeight,
  "glyph-orientation-vertical"?: Property$GlyphOrientationVertical,
  "image-rendering"?: Property$ImageRendering,
  "letter-spacing"?: Property$LetterSpacing<TLength>,
  "lighting-color"?: Property$LightingColor,
  "line-height"?: Property$LineHeight<TLength>,
  marker?: Property$Marker,
  "marker-end"?: Property$MarkerEnd,
  "marker-mid"?: Property$MarkerMid,
  "marker-start"?: Property$MarkerStart,
  mask?: Property$Mask<TLength>,
  opacity?: Property$Opacity,
  overflow?: Property$Overflow,
  "paint-order"?: Property$PaintOrder,
  "pointer-events"?: Property$PointerEvents,
  "shape-rendering"?: Property$ShapeRendering,
  "stop-color"?: Property$StopColor,
  "stop-opacity"?: Property$StopOpacity,
  stroke?: Property$Stroke,
  "stroke-dasharray"?: Property$StrokeDasharray<TLength>,
  "stroke-dashoffset"?: Property$StrokeDashoffset<TLength>,
  "stroke-linecap"?: Property$StrokeLinecap,
  "stroke-linejoin"?: Property$StrokeLinejoin,
  "stroke-miterlimit"?: Property$StrokeMiterlimit,
  "stroke-opacity"?: Property$StrokeOpacity,
  "stroke-width"?: Property$StrokeWidth<TLength>,
  "text-anchor"?: Property$TextAnchor,
  "text-decoration"?: Property$TextDecoration<TLength>,
  "text-rendering"?: Property$TextRendering,
  "unicode-bidi"?: Property$UnicodeBidi,
  "vector-effect"?: Property$VectorEffect,
  visibility?: Property$Visibility,
  "white-space"?: Property$WhiteSpace,
  "word-spacing"?: Property$WordSpacing<TLength>,
  "writing-mode"?: Property$WritingMode,
|};

export type PropertiesHyphen<TLength = string | 0, TTime = string> = {|
  ...StandardPropertiesHyphen<TLength, TTime>,
  ...VendorPropertiesHyphen<TLength, TTime>,
  ...ObsoletePropertiesHyphen<TLength, TTime>,
  ...SvgPropertiesHyphen<TLength, TTime>,
|};

export type StandardLonghandPropertiesFallback<TLength = string | 0, TTime = string> = {|
  accentColor?: Property$AccentColor | Array<Property$AccentColor>,
  alignContent?: Property$AlignContent | Array<Property$AlignContent>,
  alignItems?: Property$AlignItems | Array<Property$AlignItems>,
  alignSelf?: Property$AlignSelf | Array<Property$AlignSelf>,
  alignTracks?: Property$AlignTracks | Array<Property$AlignTracks>,
  animationComposition?: Property$AnimationComposition | Array<Property$AnimationComposition>,
  animationDelay?: Property$AnimationDelay<TTime> | Array<Property$AnimationDelay<TTime>>,
  animationDirection?: Property$AnimationDirection | Array<Property$AnimationDirection>,
  animationDuration?: Property$AnimationDuration<TTime> | Array<Property$AnimationDuration<TTime>>,
  animationFillMode?: Property$AnimationFillMode | Array<Property$AnimationFillMode>,
  animationIterationCount?: Property$AnimationIterationCount | Array<Property$AnimationIterationCount>,
  animationName?: Property$AnimationName | Array<Property$AnimationName>,
  animationPlayState?: Property$AnimationPlayState | Array<Property$AnimationPlayState>,
  animationRangeEnd?: Property$AnimationRangeEnd<TLength> | Array<Property$AnimationRangeEnd<TLength>>,
  animationRangeStart?: Property$AnimationRangeStart<TLength> | Array<Property$AnimationRangeStart<TLength>>,
  animationTimeline?: Property$AnimationTimeline | Array<Property$AnimationTimeline>,
  animationTimingFunction?: Property$AnimationTimingFunction | Array<Property$AnimationTimingFunction>,
  appearance?: Property$Appearance | Array<Property$Appearance>,
  aspectRatio?: Property$AspectRatio | Array<Property$AspectRatio>,
  backdropFilter?: Property$BackdropFilter | Array<Property$BackdropFilter>,
  backfaceVisibility?: Property$BackfaceVisibility | Array<Property$BackfaceVisibility>,
  backgroundAttachment?: Property$BackgroundAttachment | Array<Property$BackgroundAttachment>,
  backgroundBlendMode?: Property$BackgroundBlendMode | Array<Property$BackgroundBlendMode>,
  backgroundClip?: Property$BackgroundClip | Array<Property$BackgroundClip>,
  backgroundColor?: Property$BackgroundColor | Array<Property$BackgroundColor>,
  backgroundImage?: Property$BackgroundImage | Array<Property$BackgroundImage>,
  backgroundOrigin?: Property$BackgroundOrigin | Array<Property$BackgroundOrigin>,
  backgroundPositionX?: Property$BackgroundPositionX<TLength> | Array<Property$BackgroundPositionX<TLength>>,
  backgroundPositionY?: Property$BackgroundPositionY<TLength> | Array<Property$BackgroundPositionY<TLength>>,
  backgroundRepeat?: Property$BackgroundRepeat | Array<Property$BackgroundRepeat>,
  backgroundSize?: Property$BackgroundSize<TLength> | Array<Property$BackgroundSize<TLength>>,
  blockOverflow?: Property$BlockOverflow | Array<Property$BlockOverflow>,
  blockSize?: Property$BlockSize<TLength> | Array<Property$BlockSize<TLength>>,
  borderBlockColor?: Property$BorderBlockColor | Array<Property$BorderBlockColor>,
  borderBlockEndColor?: Property$BorderBlockEndColor | Array<Property$BorderBlockEndColor>,
  borderBlockEndStyle?: Property$BorderBlockEndStyle | Array<Property$BorderBlockEndStyle>,
  borderBlockEndWidth?: Property$BorderBlockEndWidth<TLength> | Array<Property$BorderBlockEndWidth<TLength>>,
  borderBlockStartColor?: Property$BorderBlockStartColor | Array<Property$BorderBlockStartColor>,
  borderBlockStartStyle?: Property$BorderBlockStartStyle | Array<Property$BorderBlockStartStyle>,
  borderBlockStartWidth?: Property$BorderBlockStartWidth<TLength> | Array<Property$BorderBlockStartWidth<TLength>>,
  borderBlockStyle?: Property$BorderBlockStyle | Array<Property$BorderBlockStyle>,
  borderBlockWidth?: Property$BorderBlockWidth<TLength> | Array<Property$BorderBlockWidth<TLength>>,
  borderBottomColor?: Property$BorderBottomColor | Array<Property$BorderBottomColor>,
  borderBottomLeftRadius?: Property$BorderBottomLeftRadius<TLength> | Array<Property$BorderBottomLeftRadius<TLength>>,
  borderBottomRightRadius?: Property$BorderBottomRightRadius<TLength> | Array<Property$BorderBottomRightRadius<TLength>>,
  borderBottomStyle?: Property$BorderBottomStyle | Array<Property$BorderBottomStyle>,
  borderBottomWidth?: Property$BorderBottomWidth<TLength> | Array<Property$BorderBottomWidth<TLength>>,
  borderCollapse?: Property$BorderCollapse | Array<Property$BorderCollapse>,
  borderEndEndRadius?: Property$BorderEndEndRadius<TLength> | Array<Property$BorderEndEndRadius<TLength>>,
  borderEndStartRadius?: Property$BorderEndStartRadius<TLength> | Array<Property$BorderEndStartRadius<TLength>>,
  borderImageOutset?: Property$BorderImageOutset<TLength> | Array<Property$BorderImageOutset<TLength>>,
  borderImageRepeat?: Property$BorderImageRepeat | Array<Property$BorderImageRepeat>,
  borderImageSlice?: Property$BorderImageSlice | Array<Property$BorderImageSlice>,
  borderImageSource?: Property$BorderImageSource | Array<Property$BorderImageSource>,
  borderImageWidth?: Property$BorderImageWidth<TLength> | Array<Property$BorderImageWidth<TLength>>,
  borderInlineColor?: Property$BorderInlineColor | Array<Property$BorderInlineColor>,
  borderInlineEndColor?: Property$BorderInlineEndColor | Array<Property$BorderInlineEndColor>,
  borderInlineEndStyle?: Property$BorderInlineEndStyle | Array<Property$BorderInlineEndStyle>,
  borderInlineEndWidth?: Property$BorderInlineEndWidth<TLength> | Array<Property$BorderInlineEndWidth<TLength>>,
  borderInlineStartColor?: Property$BorderInlineStartColor | Array<Property$BorderInlineStartColor>,
  borderInlineStartStyle?: Property$BorderInlineStartStyle | Array<Property$BorderInlineStartStyle>,
  borderInlineStartWidth?: Property$BorderInlineStartWidth<TLength> | Array<Property$BorderInlineStartWidth<TLength>>,
  borderInlineStyle?: Property$BorderInlineStyle | Array<Property$BorderInlineStyle>,
  borderInlineWidth?: Property$BorderInlineWidth<TLength> | Array<Property$BorderInlineWidth<TLength>>,
  borderLeftColor?: Property$BorderLeftColor | Array<Property$BorderLeftColor>,
  borderLeftStyle?: Property$BorderLeftStyle | Array<Property$BorderLeftStyle>,
  borderLeftWidth?: Property$BorderLeftWidth<TLength> | Array<Property$BorderLeftWidth<TLength>>,
  borderRightColor?: Property$BorderRightColor | Array<Property$BorderRightColor>,
  borderRightStyle?: Property$BorderRightStyle | Array<Property$BorderRightStyle>,
  borderRightWidth?: Property$BorderRightWidth<TLength> | Array<Property$BorderRightWidth<TLength>>,
  borderSpacing?: Property$BorderSpacing<TLength> | Array<Property$BorderSpacing<TLength>>,
  borderStartEndRadius?: Property$BorderStartEndRadius<TLength> | Array<Property$BorderStartEndRadius<TLength>>,
  borderStartStartRadius?: Property$BorderStartStartRadius<TLength> | Array<Property$BorderStartStartRadius<TLength>>,
  borderTopColor?: Property$BorderTopColor | Array<Property$BorderTopColor>,
  borderTopLeftRadius?: Property$BorderTopLeftRadius<TLength> | Array<Property$BorderTopLeftRadius<TLength>>,
  borderTopRightRadius?: Property$BorderTopRightRadius<TLength> | Array<Property$BorderTopRightRadius<TLength>>,
  borderTopStyle?: Property$BorderTopStyle | Array<Property$BorderTopStyle>,
  borderTopWidth?: Property$BorderTopWidth<TLength> | Array<Property$BorderTopWidth<TLength>>,
  bottom?: Property$Bottom<TLength> | Array<Property$Bottom<TLength>>,
  boxDecorationBreak?: Property$BoxDecorationBreak | Array<Property$BoxDecorationBreak>,
  boxShadow?: Property$BoxShadow | Array<Property$BoxShadow>,
  boxSizing?: Property$BoxSizing | Array<Property$BoxSizing>,
  breakAfter?: Property$BreakAfter | Array<Property$BreakAfter>,
  breakBefore?: Property$BreakBefore | Array<Property$BreakBefore>,
  breakInside?: Property$BreakInside | Array<Property$BreakInside>,
  captionSide?: Property$CaptionSide | Array<Property$CaptionSide>,
  caretColor?: Property$CaretColor | Array<Property$CaretColor>,
  caretShape?: Property$CaretShape | Array<Property$CaretShape>,
  clear?: Property$Clear | Array<Property$Clear>,
  clipPath?: Property$ClipPath | Array<Property$ClipPath>,
  color?: Property$Color | Array<Property$Color>,
  colorAdjust?: Property$PrintColorAdjust | Array<Property$PrintColorAdjust>,
  colorScheme?: Property$ColorScheme | Array<Property$ColorScheme>,
  columnCount?: Property$ColumnCount | Array<Property$ColumnCount>,
  columnFill?: Property$ColumnFill | Array<Property$ColumnFill>,
  columnGap?: Property$ColumnGap<TLength> | Array<Property$ColumnGap<TLength>>,
  columnRuleColor?: Property$ColumnRuleColor | Array<Property$ColumnRuleColor>,
  columnRuleStyle?: Property$ColumnRuleStyle | Array<Property$ColumnRuleStyle>,
  columnRuleWidth?: Property$ColumnRuleWidth<TLength> | Array<Property$ColumnRuleWidth<TLength>>,
  columnSpan?: Property$ColumnSpan | Array<Property$ColumnSpan>,
  columnWidth?: Property$ColumnWidth<TLength> | Array<Property$ColumnWidth<TLength>>,
  contain?: Property$Contain | Array<Property$Contain>,
  containIntrinsicBlockSize?: Property$ContainIntrinsicBlockSize<TLength> | Array<Property$ContainIntrinsicBlockSize<TLength>>,
  containIntrinsicHeight?: Property$ContainIntrinsicHeight<TLength> | Array<Property$ContainIntrinsicHeight<TLength>>,
  containIntrinsicInlineSize?: Property$ContainIntrinsicInlineSize<TLength> | Array<Property$ContainIntrinsicInlineSize<TLength>>,
  containIntrinsicWidth?: Property$ContainIntrinsicWidth<TLength> | Array<Property$ContainIntrinsicWidth<TLength>>,
  containerName?: Property$ContainerName | Array<Property$ContainerName>,
  containerType?: Property$ContainerType | Array<Property$ContainerType>,
  content?: Property$Content | Array<Property$Content>,
  contentVisibility?: Property$ContentVisibility | Array<Property$ContentVisibility>,
  counterIncrement?: Property$CounterIncrement | Array<Property$CounterIncrement>,
  counterReset?: Property$CounterReset | Array<Property$CounterReset>,
  counterSet?: Property$CounterSet | Array<Property$CounterSet>,
  cursor?: Property$Cursor | Array<Property$Cursor>,
  direction?: Property$Direction | Array<Property$Direction>,
  display?: Property$Display | Array<Property$Display>,
  emptyCells?: Property$EmptyCells | Array<Property$EmptyCells>,
  filter?: Property$Filter | Array<Property$Filter>,
  flexBasis?: Property$FlexBasis<TLength> | Array<Property$FlexBasis<TLength>>,
  flexDirection?: Property$FlexDirection | Array<Property$FlexDirection>,
  flexGrow?: Property$FlexGrow | Array<Property$FlexGrow>,
  flexShrink?: Property$FlexShrink | Array<Property$FlexShrink>,
  flexWrap?: Property$FlexWrap | Array<Property$FlexWrap>,
  float?: Property$Float | Array<Property$Float>,
  fontFamily?: Property$FontFamily | Array<Property$FontFamily>,
  fontFeatureSettings?: Property$FontFeatureSettings | Array<Property$FontFeatureSettings>,
  fontKerning?: Property$FontKerning | Array<Property$FontKerning>,
  fontLanguageOverride?: Property$FontLanguageOverride | Array<Property$FontLanguageOverride>,
  fontOpticalSizing?: Property$FontOpticalSizing | Array<Property$FontOpticalSizing>,
  fontPalette?: Property$FontPalette | Array<Property$FontPalette>,
  fontSize?: Property$FontSize<TLength> | Array<Property$FontSize<TLength>>,
  fontSizeAdjust?: Property$FontSizeAdjust | Array<Property$FontSizeAdjust>,
  fontSmooth?: Property$FontSmooth<TLength> | Array<Property$FontSmooth<TLength>>,
  fontStretch?: Property$FontStretch | Array<Property$FontStretch>,
  fontStyle?: Property$FontStyle | Array<Property$FontStyle>,
  fontSynthesis?: Property$FontSynthesis | Array<Property$FontSynthesis>,
  fontSynthesisPosition?: Property$FontSynthesisPosition | Array<Property$FontSynthesisPosition>,
  fontSynthesisSmallCaps?: Property$FontSynthesisSmallCaps | Array<Property$FontSynthesisSmallCaps>,
  fontSynthesisStyle?: Property$FontSynthesisStyle | Array<Property$FontSynthesisStyle>,
  fontSynthesisWeight?: Property$FontSynthesisWeight | Array<Property$FontSynthesisWeight>,
  fontVariant?: Property$FontVariant | Array<Property$FontVariant>,
  fontVariantAlternates?: Property$FontVariantAlternates | Array<Property$FontVariantAlternates>,
  fontVariantCaps?: Property$FontVariantCaps | Array<Property$FontVariantCaps>,
  fontVariantEastAsian?: Property$FontVariantEastAsian | Array<Property$FontVariantEastAsian>,
  fontVariantEmoji?: Property$FontVariantEmoji | Array<Property$FontVariantEmoji>,
  fontVariantLigatures?: Property$FontVariantLigatures | Array<Property$FontVariantLigatures>,
  fontVariantNumeric?: Property$FontVariantNumeric | Array<Property$FontVariantNumeric>,
  fontVariantPosition?: Property$FontVariantPosition | Array<Property$FontVariantPosition>,
  fontVariationSettings?: Property$FontVariationSettings | Array<Property$FontVariationSettings>,
  fontWeight?: Property$FontWeight | Array<Property$FontWeight>,
  forcedColorAdjust?: Property$ForcedColorAdjust | Array<Property$ForcedColorAdjust>,
  gridAutoColumns?: Property$GridAutoColumns<TLength> | Array<Property$GridAutoColumns<TLength>>,
  gridAutoFlow?: Property$GridAutoFlow | Array<Property$GridAutoFlow>,
  gridAutoRows?: Property$GridAutoRows<TLength> | Array<Property$GridAutoRows<TLength>>,
  gridColumnEnd?: Property$GridColumnEnd | Array<Property$GridColumnEnd>,
  gridColumnStart?: Property$GridColumnStart | Array<Property$GridColumnStart>,
  gridRowEnd?: Property$GridRowEnd | Array<Property$GridRowEnd>,
  gridRowStart?: Property$GridRowStart | Array<Property$GridRowStart>,
  gridTemplateAreas?: Property$GridTemplateAreas | Array<Property$GridTemplateAreas>,
  gridTemplateColumns?: Property$GridTemplateColumns<TLength> | Array<Property$GridTemplateColumns<TLength>>,
  gridTemplateRows?: Property$GridTemplateRows<TLength> | Array<Property$GridTemplateRows<TLength>>,
  hangingPunctuation?: Property$HangingPunctuation | Array<Property$HangingPunctuation>,
  height?: Property$Height<TLength> | Array<Property$Height<TLength>>,
  hyphenateCharacter?: Property$HyphenateCharacter | Array<Property$HyphenateCharacter>,
  hyphenateLimitChars?: Property$HyphenateLimitChars | Array<Property$HyphenateLimitChars>,
  hyphens?: Property$Hyphens | Array<Property$Hyphens>,
  imageOrientation?: Property$ImageOrientation | Array<Property$ImageOrientation>,
  imageRendering?: Property$ImageRendering | Array<Property$ImageRendering>,
  imageResolution?: Property$ImageResolution | Array<Property$ImageResolution>,
  initialLetter?: Property$InitialLetter | Array<Property$InitialLetter>,
  inlineSize?: Property$InlineSize<TLength> | Array<Property$InlineSize<TLength>>,
  inputSecurity?: Property$InputSecurity | Array<Property$InputSecurity>,
  insetBlockEnd?: Property$InsetBlockEnd<TLength> | Array<Property$InsetBlockEnd<TLength>>,
  insetBlockStart?: Property$InsetBlockStart<TLength> | Array<Property$InsetBlockStart<TLength>>,
  insetInlineEnd?: Property$InsetInlineEnd<TLength> | Array<Property$InsetInlineEnd<TLength>>,
  insetInlineStart?: Property$InsetInlineStart<TLength> | Array<Property$InsetInlineStart<TLength>>,
  isolation?: Property$Isolation | Array<Property$Isolation>,
  justifyContent?: Property$JustifyContent | Array<Property$JustifyContent>,
  justifyItems?: Property$JustifyItems | Array<Property$JustifyItems>,
  justifySelf?: Property$JustifySelf | Array<Property$JustifySelf>,
  justifyTracks?: Property$JustifyTracks | Array<Property$JustifyTracks>,
  left?: Property$Left<TLength> | Array<Property$Left<TLength>>,
  letterSpacing?: Property$LetterSpacing<TLength> | Array<Property$LetterSpacing<TLength>>,
  lineBreak?: Property$LineBreak | Array<Property$LineBreak>,
  lineHeight?: Property$LineHeight<TLength> | Array<Property$LineHeight<TLength>>,
  lineHeightStep?: Property$LineHeightStep<TLength> | Array<Property$LineHeightStep<TLength>>,
  listStyleImage?: Property$ListStyleImage | Array<Property$ListStyleImage>,
  listStylePosition?: Property$ListStylePosition | Array<Property$ListStylePosition>,
  listStyleType?: Property$ListStyleType | Array<Property$ListStyleType>,
  marginBlockEnd?: Property$MarginBlockEnd<TLength> | Array<Property$MarginBlockEnd<TLength>>,
  marginBlockStart?: Property$MarginBlockStart<TLength> | Array<Property$MarginBlockStart<TLength>>,
  marginBottom?: Property$MarginBottom<TLength> | Array<Property$MarginBottom<TLength>>,
  marginInlineEnd?: Property$MarginInlineEnd<TLength> | Array<Property$MarginInlineEnd<TLength>>,
  marginInlineStart?: Property$MarginInlineStart<TLength> | Array<Property$MarginInlineStart<TLength>>,
  marginLeft?: Property$MarginLeft<TLength> | Array<Property$MarginLeft<TLength>>,
  marginRight?: Property$MarginRight<TLength> | Array<Property$MarginRight<TLength>>,
  marginTop?: Property$MarginTop<TLength> | Array<Property$MarginTop<TLength>>,
  marginTrim?: Property$MarginTrim | Array<Property$MarginTrim>,
  maskBorderMode?: Property$MaskBorderMode | Array<Property$MaskBorderMode>,
  maskBorderOutset?: Property$MaskBorderOutset<TLength> | Array<Property$MaskBorderOutset<TLength>>,
  maskBorderRepeat?: Property$MaskBorderRepeat | Array<Property$MaskBorderRepeat>,
  maskBorderSlice?: Property$MaskBorderSlice | Array<Property$MaskBorderSlice>,
  maskBorderSource?: Property$MaskBorderSource | Array<Property$MaskBorderSource>,
  maskBorderWidth?: Property$MaskBorderWidth<TLength> | Array<Property$MaskBorderWidth<TLength>>,
  maskClip?: Property$MaskClip | Array<Property$MaskClip>,
  maskComposite?: Property$MaskComposite | Array<Property$MaskComposite>,
  maskImage?: Property$MaskImage | Array<Property$MaskImage>,
  maskMode?: Property$MaskMode | Array<Property$MaskMode>,
  maskOrigin?: Property$MaskOrigin | Array<Property$MaskOrigin>,
  maskPosition?: Property$MaskPosition<TLength> | Array<Property$MaskPosition<TLength>>,
  maskRepeat?: Property$MaskRepeat | Array<Property$MaskRepeat>,
  maskSize?: Property$MaskSize<TLength> | Array<Property$MaskSize<TLength>>,
  maskType?: Property$MaskType | Array<Property$MaskType>,
  masonryAutoFlow?: Property$MasonryAutoFlow | Array<Property$MasonryAutoFlow>,
  mathDepth?: Property$MathDepth | Array<Property$MathDepth>,
  mathShift?: Property$MathShift | Array<Property$MathShift>,
  mathStyle?: Property$MathStyle | Array<Property$MathStyle>,
  maxBlockSize?: Property$MaxBlockSize<TLength> | Array<Property$MaxBlockSize<TLength>>,
  maxHeight?: Property$MaxHeight<TLength> | Array<Property$MaxHeight<TLength>>,
  maxInlineSize?: Property$MaxInlineSize<TLength> | Array<Property$MaxInlineSize<TLength>>,
  maxLines?: Property$MaxLines | Array<Property$MaxLines>,
  maxWidth?: Property$MaxWidth<TLength> | Array<Property$MaxWidth<TLength>>,
  minBlockSize?: Property$MinBlockSize<TLength> | Array<Property$MinBlockSize<TLength>>,
  minHeight?: Property$MinHeight<TLength> | Array<Property$MinHeight<TLength>>,
  minInlineSize?: Property$MinInlineSize<TLength> | Array<Property$MinInlineSize<TLength>>,
  minWidth?: Property$MinWidth<TLength> | Array<Property$MinWidth<TLength>>,
  mixBlendMode?: Property$MixBlendMode | Array<Property$MixBlendMode>,
  motionDistance?: Property$OffsetDistance<TLength> | Array<Property$OffsetDistance<TLength>>,
  motionPath?: Property$OffsetPath | Array<Property$OffsetPath>,
  motionRotation?: Property$OffsetRotate | Array<Property$OffsetRotate>,
  objectFit?: Property$ObjectFit | Array<Property$ObjectFit>,
  objectPosition?: Property$ObjectPosition<TLength> | Array<Property$ObjectPosition<TLength>>,
  offsetAnchor?: Property$OffsetAnchor<TLength> | Array<Property$OffsetAnchor<TLength>>,
  offsetDistance?: Property$OffsetDistance<TLength> | Array<Property$OffsetDistance<TLength>>,
  offsetPath?: Property$OffsetPath | Array<Property$OffsetPath>,
  offsetPosition?: Property$OffsetPosition<TLength> | Array<Property$OffsetPosition<TLength>>,
  offsetRotate?: Property$OffsetRotate | Array<Property$OffsetRotate>,
  offsetRotation?: Property$OffsetRotate | Array<Property$OffsetRotate>,
  opacity?: Property$Opacity | Array<Property$Opacity>,
  order?: Property$Order | Array<Property$Order>,
  orphans?: Property$Orphans | Array<Property$Orphans>,
  outlineColor?: Property$OutlineColor | Array<Property$OutlineColor>,
  outlineOffset?: Property$OutlineOffset<TLength> | Array<Property$OutlineOffset<TLength>>,
  outlineStyle?: Property$OutlineStyle | Array<Property$OutlineStyle>,
  outlineWidth?: Property$OutlineWidth<TLength> | Array<Property$OutlineWidth<TLength>>,
  overflowAnchor?: Property$OverflowAnchor | Array<Property$OverflowAnchor>,
  overflowBlock?: Property$OverflowBlock | Array<Property$OverflowBlock>,
  overflowClipBox?: Property$OverflowClipBox | Array<Property$OverflowClipBox>,
  overflowClipMargin?: Property$OverflowClipMargin<TLength> | Array<Property$OverflowClipMargin<TLength>>,
  overflowInline?: Property$OverflowInline | Array<Property$OverflowInline>,
  overflowWrap?: Property$OverflowWrap | Array<Property$OverflowWrap>,
  overflowX?: Property$OverflowX | Array<Property$OverflowX>,
  overflowY?: Property$OverflowY | Array<Property$OverflowY>,
  overlay?: Property$Overlay | Array<Property$Overlay>,
  overscrollBehaviorBlock?: Property$OverscrollBehaviorBlock | Array<Property$OverscrollBehaviorBlock>,
  overscrollBehaviorInline?: Property$OverscrollBehaviorInline | Array<Property$OverscrollBehaviorInline>,
  overscrollBehaviorX?: Property$OverscrollBehaviorX | Array<Property$OverscrollBehaviorX>,
  overscrollBehaviorY?: Property$OverscrollBehaviorY | Array<Property$OverscrollBehaviorY>,
  paddingBlockEnd?: Property$PaddingBlockEnd<TLength> | Array<Property$PaddingBlockEnd<TLength>>,
  paddingBlockStart?: Property$PaddingBlockStart<TLength> | Array<Property$PaddingBlockStart<TLength>>,
  paddingBottom?: Property$PaddingBottom<TLength> | Array<Property$PaddingBottom<TLength>>,
  paddingInlineEnd?: Property$PaddingInlineEnd<TLength> | Array<Property$PaddingInlineEnd<TLength>>,
  paddingInlineStart?: Property$PaddingInlineStart<TLength> | Array<Property$PaddingInlineStart<TLength>>,
  paddingLeft?: Property$PaddingLeft<TLength> | Array<Property$PaddingLeft<TLength>>,
  paddingRight?: Property$PaddingRight<TLength> | Array<Property$PaddingRight<TLength>>,
  paddingTop?: Property$PaddingTop<TLength> | Array<Property$PaddingTop<TLength>>,
  page?: Property$Page | Array<Property$Page>,
  pageBreakAfter?: Property$PageBreakAfter | Array<Property$PageBreakAfter>,
  pageBreakBefore?: Property$PageBreakBefore | Array<Property$PageBreakBefore>,
  pageBreakInside?: Property$PageBreakInside | Array<Property$PageBreakInside>,
  paintOrder?: Property$PaintOrder | Array<Property$PaintOrder>,
  perspective?: Property$Perspective<TLength> | Array<Property$Perspective<TLength>>,
  perspectiveOrigin?: Property$PerspectiveOrigin<TLength> | Array<Property$PerspectiveOrigin<TLength>>,
  pointerEvents?: Property$PointerEvents | Array<Property$PointerEvents>,
  position?: Property$Position | Array<Property$Position>,
  printColorAdjust?: Property$PrintColorAdjust | Array<Property$PrintColorAdjust>,
  quotes?: Property$Quotes | Array<Property$Quotes>,
  resize?: Property$Resize | Array<Property$Resize>,
  right?: Property$Right<TLength> | Array<Property$Right<TLength>>,
  rotate?: Property$Rotate | Array<Property$Rotate>,
  rowGap?: Property$RowGap<TLength> | Array<Property$RowGap<TLength>>,
  rubyAlign?: Property$RubyAlign | Array<Property$RubyAlign>,
  rubyMerge?: Property$RubyMerge | Array<Property$RubyMerge>,
  rubyPosition?: Property$RubyPosition | Array<Property$RubyPosition>,
  scale?: Property$Scale | Array<Property$Scale>,
  scrollBehavior?: Property$ScrollBehavior | Array<Property$ScrollBehavior>,
  scrollMarginBlockEnd?: Property$ScrollMarginBlockEnd<TLength> | Array<Property$ScrollMarginBlockEnd<TLength>>,
  scrollMarginBlockStart?: Property$ScrollMarginBlockStart<TLength> | Array<Property$ScrollMarginBlockStart<TLength>>,
  scrollMarginBottom?: Property$ScrollMarginBottom<TLength> | Array<Property$ScrollMarginBottom<TLength>>,
  scrollMarginInlineEnd?: Property$ScrollMarginInlineEnd<TLength> | Array<Property$ScrollMarginInlineEnd<TLength>>,
  scrollMarginInlineStart?: Property$ScrollMarginInlineStart<TLength> | Array<Property$ScrollMarginInlineStart<TLength>>,
  scrollMarginLeft?: Property$ScrollMarginLeft<TLength> | Array<Property$ScrollMarginLeft<TLength>>,
  scrollMarginRight?: Property$ScrollMarginRight<TLength> | Array<Property$ScrollMarginRight<TLength>>,
  scrollMarginTop?: Property$ScrollMarginTop<TLength> | Array<Property$ScrollMarginTop<TLength>>,
  scrollPaddingBlockEnd?: Property$ScrollPaddingBlockEnd<TLength> | Array<Property$ScrollPaddingBlockEnd<TLength>>,
  scrollPaddingBlockStart?: Property$ScrollPaddingBlockStart<TLength> | Array<Property$ScrollPaddingBlockStart<TLength>>,
  scrollPaddingBottom?: Property$ScrollPaddingBottom<TLength> | Array<Property$ScrollPaddingBottom<TLength>>,
  scrollPaddingInlineEnd?: Property$ScrollPaddingInlineEnd<TLength> | Array<Property$ScrollPaddingInlineEnd<TLength>>,
  scrollPaddingInlineStart?: Property$ScrollPaddingInlineStart<TLength> | Array<Property$ScrollPaddingInlineStart<TLength>>,
  scrollPaddingLeft?: Property$ScrollPaddingLeft<TLength> | Array<Property$ScrollPaddingLeft<TLength>>,
  scrollPaddingRight?: Property$ScrollPaddingRight<TLength> | Array<Property$ScrollPaddingRight<TLength>>,
  scrollPaddingTop?: Property$ScrollPaddingTop<TLength> | Array<Property$ScrollPaddingTop<TLength>>,
  scrollSnapAlign?: Property$ScrollSnapAlign | Array<Property$ScrollSnapAlign>,
  scrollSnapMarginBottom?: Property$ScrollMarginBottom<TLength> | Array<Property$ScrollMarginBottom<TLength>>,
  scrollSnapMarginLeft?: Property$ScrollMarginLeft<TLength> | Array<Property$ScrollMarginLeft<TLength>>,
  scrollSnapMarginRight?: Property$ScrollMarginRight<TLength> | Array<Property$ScrollMarginRight<TLength>>,
  scrollSnapMarginTop?: Property$ScrollMarginTop<TLength> | Array<Property$ScrollMarginTop<TLength>>,
  scrollSnapStop?: Property$ScrollSnapStop | Array<Property$ScrollSnapStop>,
  scrollSnapType?: Property$ScrollSnapType | Array<Property$ScrollSnapType>,
  scrollTimelineAxis?: Property$ScrollTimelineAxis | Array<Property$ScrollTimelineAxis>,
  scrollTimelineName?: Property$ScrollTimelineName | Array<Property$ScrollTimelineName>,
  scrollbarColor?: Property$ScrollbarColor | Array<Property$ScrollbarColor>,
  scrollbarGutter?: Property$ScrollbarGutter | Array<Property$ScrollbarGutter>,
  scrollbarWidth?: Property$ScrollbarWidth | Array<Property$ScrollbarWidth>,
  shapeImageThreshold?: Property$ShapeImageThreshold | Array<Property$ShapeImageThreshold>,
  shapeMargin?: Property$ShapeMargin<TLength> | Array<Property$ShapeMargin<TLength>>,
  shapeOutside?: Property$ShapeOutside | Array<Property$ShapeOutside>,
  tabSize?: Property$TabSize<TLength> | Array<Property$TabSize<TLength>>,
  tableLayout?: Property$TableLayout | Array<Property$TableLayout>,
  textAlign?: Property$TextAlign | Array<Property$TextAlign>,
  textAlignLast?: Property$TextAlignLast | Array<Property$TextAlignLast>,
  textCombineUpright?: Property$TextCombineUpright | Array<Property$TextCombineUpright>,
  textDecorationColor?: Property$TextDecorationColor | Array<Property$TextDecorationColor>,
  textDecorationLine?: Property$TextDecorationLine | Array<Property$TextDecorationLine>,
  textDecorationSkip?: Property$TextDecorationSkip | Array<Property$TextDecorationSkip>,
  textDecorationSkipInk?: Property$TextDecorationSkipInk | Array<Property$TextDecorationSkipInk>,
  textDecorationStyle?: Property$TextDecorationStyle | Array<Property$TextDecorationStyle>,
  textDecorationThickness?: Property$TextDecorationThickness<TLength> | Array<Property$TextDecorationThickness<TLength>>,
  textEmphasisColor?: Property$TextEmphasisColor | Array<Property$TextEmphasisColor>,
  textEmphasisPosition?: Property$TextEmphasisPosition | Array<Property$TextEmphasisPosition>,
  textEmphasisStyle?: Property$TextEmphasisStyle | Array<Property$TextEmphasisStyle>,
  textIndent?: Property$TextIndent<TLength> | Array<Property$TextIndent<TLength>>,
  textJustify?: Property$TextJustify | Array<Property$TextJustify>,
  textOrientation?: Property$TextOrientation | Array<Property$TextOrientation>,
  textOverflow?: Property$TextOverflow | Array<Property$TextOverflow>,
  textRendering?: Property$TextRendering | Array<Property$TextRendering>,
  textShadow?: Property$TextShadow | Array<Property$TextShadow>,
  textSizeAdjust?: Property$TextSizeAdjust | Array<Property$TextSizeAdjust>,
  textTransform?: Property$TextTransform | Array<Property$TextTransform>,
  textUnderlineOffset?: Property$TextUnderlineOffset<TLength> | Array<Property$TextUnderlineOffset<TLength>>,
  textUnderlinePosition?: Property$TextUnderlinePosition | Array<Property$TextUnderlinePosition>,
  textWrap?: Property$TextWrap | Array<Property$TextWrap>,
  timelineScope?: Property$TimelineScope | Array<Property$TimelineScope>,
  top?: Property$Top<TLength> | Array<Property$Top<TLength>>,
  touchAction?: Property$TouchAction | Array<Property$TouchAction>,
  transform?: Property$Transform | Array<Property$Transform>,
  transformBox?: Property$TransformBox | Array<Property$TransformBox>,
  transformOrigin?: Property$TransformOrigin<TLength> | Array<Property$TransformOrigin<TLength>>,
  transformStyle?: Property$TransformStyle | Array<Property$TransformStyle>,
  transitionBehavior?: Property$TransitionBehavior | Array<Property$TransitionBehavior>,
  transitionDelay?: Property$TransitionDelay<TTime> | Array<Property$TransitionDelay<TTime>>,
  transitionDuration?: Property$TransitionDuration<TTime> | Array<Property$TransitionDuration<TTime>>,
  transitionProperty?: Property$TransitionProperty | Array<Property$TransitionProperty>,
  transitionTimingFunction?: Property$TransitionTimingFunction | Array<Property$TransitionTimingFunction>,
  translate?: Property$Translate<TLength> | Array<Property$Translate<TLength>>,
  unicodeBidi?: Property$UnicodeBidi | Array<Property$UnicodeBidi>,
  userSelect?: Property$UserSelect | Array<Property$UserSelect>,
  verticalAlign?: Property$VerticalAlign<TLength> | Array<Property$VerticalAlign<TLength>>,
  viewTimelineAxis?: Property$ViewTimelineAxis | Array<Property$ViewTimelineAxis>,
  viewTimelineInset?: Property$ViewTimelineInset<TLength> | Array<Property$ViewTimelineInset<TLength>>,
  viewTimelineName?: Property$ViewTimelineName | Array<Property$ViewTimelineName>,
  viewTransitionName?: Property$ViewTransitionName | Array<Property$ViewTransitionName>,
  visibility?: Property$Visibility | Array<Property$Visibility>,
  whiteSpace?: Property$WhiteSpace | Array<Property$WhiteSpace>,
  whiteSpaceCollapse?: Property$WhiteSpaceCollapse | Array<Property$WhiteSpaceCollapse>,
  whiteSpaceTrim?: Property$WhiteSpaceTrim | Array<Property$WhiteSpaceTrim>,
  widows?: Property$Widows | Array<Property$Widows>,
  width?: Property$Width<TLength> | Array<Property$Width<TLength>>,
  willChange?: Property$WillChange | Array<Property$WillChange>,
  wordBreak?: Property$WordBreak | Array<Property$WordBreak>,
  wordSpacing?: Property$WordSpacing<TLength> | Array<Property$WordSpacing<TLength>>,
  wordWrap?: Property$WordWrap | Array<Property$WordWrap>,
  writingMode?: Property$WritingMode | Array<Property$WritingMode>,
  zIndex?: Property$ZIndex | Array<Property$ZIndex>,
  zoom?: Property$Zoom | Array<Property$Zoom>,
|};

export type StandardShorthandPropertiesFallback<TLength = string | 0, TTime = string> = {|
  all?: Property$All | Array<Property$All>,
  animation?: Property$Animation<TTime> | Array<Property$Animation<TTime>>,
  animationRange?: Property$AnimationRange<TLength> | Array<Property$AnimationRange<TLength>>,
  background?: Property$Background<TLength> | Array<Property$Background<TLength>>,
  backgroundPosition?: Property$BackgroundPosition<TLength> | Array<Property$BackgroundPosition<TLength>>,
  border?: Property$Border<TLength> | Array<Property$Border<TLength>>,
  borderBlock?: Property$BorderBlock<TLength> | Array<Property$BorderBlock<TLength>>,
  borderBlockEnd?: Property$BorderBlockEnd<TLength> | Array<Property$BorderBlockEnd<TLength>>,
  borderBlockStart?: Property$BorderBlockStart<TLength> | Array<Property$BorderBlockStart<TLength>>,
  borderBottom?: Property$BorderBottom<TLength> | Array<Property$BorderBottom<TLength>>,
  borderColor?: Property$BorderColor | Array<Property$BorderColor>,
  borderImage?: Property$BorderImage | Array<Property$BorderImage>,
  borderInline?: Property$BorderInline<TLength> | Array<Property$BorderInline<TLength>>,
  borderInlineEnd?: Property$BorderInlineEnd<TLength> | Array<Property$BorderInlineEnd<TLength>>,
  borderInlineStart?: Property$BorderInlineStart<TLength> | Array<Property$BorderInlineStart<TLength>>,
  borderLeft?: Property$BorderLeft<TLength> | Array<Property$BorderLeft<TLength>>,
  borderRadius?: Property$BorderRadius<TLength> | Array<Property$BorderRadius<TLength>>,
  borderRight?: Property$BorderRight<TLength> | Array<Property$BorderRight<TLength>>,
  borderStyle?: Property$BorderStyle | Array<Property$BorderStyle>,
  borderTop?: Property$BorderTop<TLength> | Array<Property$BorderTop<TLength>>,
  borderWidth?: Property$BorderWidth<TLength> | Array<Property$BorderWidth<TLength>>,
  caret?: Property$Caret | Array<Property$Caret>,
  columnRule?: Property$ColumnRule<TLength> | Array<Property$ColumnRule<TLength>>,
  columns?: Property$Columns<TLength> | Array<Property$Columns<TLength>>,
  containIntrinsicSize?: Property$ContainIntrinsicSize<TLength> | Array<Property$ContainIntrinsicSize<TLength>>,
  container?: Property$Container | Array<Property$Container>,
  flex?: Property$Flex<TLength> | Array<Property$Flex<TLength>>,
  flexFlow?: Property$FlexFlow | Array<Property$FlexFlow>,
  font?: Property$Font | Array<Property$Font>,
  gap?: Property$Gap<TLength> | Array<Property$Gap<TLength>>,
  grid?: Property$Grid | Array<Property$Grid>,
  gridArea?: Property$GridArea | Array<Property$GridArea>,
  gridColumn?: Property$GridColumn | Array<Property$GridColumn>,
  gridRow?: Property$GridRow | Array<Property$GridRow>,
  gridTemplate?: Property$GridTemplate | Array<Property$GridTemplate>,
  inset?: Property$Inset<TLength> | Array<Property$Inset<TLength>>,
  insetBlock?: Property$InsetBlock<TLength> | Array<Property$InsetBlock<TLength>>,
  insetInline?: Property$InsetInline<TLength> | Array<Property$InsetInline<TLength>>,
  lineClamp?: Property$LineClamp | Array<Property$LineClamp>,
  listStyle?: Property$ListStyle | Array<Property$ListStyle>,
  margin?: Property$Margin<TLength> | Array<Property$Margin<TLength>>,
  marginBlock?: Property$MarginBlock<TLength> | Array<Property$MarginBlock<TLength>>,
  marginInline?: Property$MarginInline<TLength> | Array<Property$MarginInline<TLength>>,
  mask?: Property$Mask<TLength> | Array<Property$Mask<TLength>>,
  maskBorder?: Property$MaskBorder | Array<Property$MaskBorder>,
  motion?: Property$Offset<TLength> | Array<Property$Offset<TLength>>,
  offset?: Property$Offset<TLength> | Array<Property$Offset<TLength>>,
  outline?: Property$Outline<TLength> | Array<Property$Outline<TLength>>,
  overflow?: Property$Overflow | Array<Property$Overflow>,
  overscrollBehavior?: Property$OverscrollBehavior | Array<Property$OverscrollBehavior>,
  padding?: Property$Padding<TLength> | Array<Property$Padding<TLength>>,
  paddingBlock?: Property$PaddingBlock<TLength> | Array<Property$PaddingBlock<TLength>>,
  paddingInline?: Property$PaddingInline<TLength> | Array<Property$PaddingInline<TLength>>,
  placeContent?: Property$PlaceContent | Array<Property$PlaceContent>,
  placeItems?: Property$PlaceItems | Array<Property$PlaceItems>,
  placeSelf?: Property$PlaceSelf | Array<Property$PlaceSelf>,
  scrollMargin?: Property$ScrollMargin<TLength> | Array<Property$ScrollMargin<TLength>>,
  scrollMarginBlock?: Property$ScrollMarginBlock<TLength> | Array<Property$ScrollMarginBlock<TLength>>,
  scrollMarginInline?: Property$ScrollMarginInline<TLength> | Array<Property$ScrollMarginInline<TLength>>,
  scrollPadding?: Property$ScrollPadding<TLength> | Array<Property$ScrollPadding<TLength>>,
  scrollPaddingBlock?: Property$ScrollPaddingBlock<TLength> | Array<Property$ScrollPaddingBlock<TLength>>,
  scrollPaddingInline?: Property$ScrollPaddingInline<TLength> | Array<Property$ScrollPaddingInline<TLength>>,
  scrollSnapMargin?: Property$ScrollMargin<TLength> | Array<Property$ScrollMargin<TLength>>,
  scrollTimeline?: Property$ScrollTimeline | Array<Property$ScrollTimeline>,
  textDecoration?: Property$TextDecoration<TLength> | Array<Property$TextDecoration<TLength>>,
  textEmphasis?: Property$TextEmphasis | Array<Property$TextEmphasis>,
  transition?: Property$Transition<TTime> | Array<Property$Transition<TTime>>,
  viewTimeline?: Property$ViewTimeline | Array<Property$ViewTimeline>,
|};

export type StandardPropertiesFallback<TLength = string | 0, TTime = string> = {|
  ...StandardLonghandPropertiesFallback<TLength, TTime>,
  ...StandardShorthandPropertiesFallback<TLength, TTime>,
|};

export type VendorLonghandPropertiesFallback<TLength = string | 0, TTime = string> = {|
  MozAnimationDelay?: Property$AnimationDelay<TTime> | Array<Property$AnimationDelay<TTime>>,
  MozAnimationDirection?: Property$AnimationDirection | Array<Property$AnimationDirection>,
  MozAnimationDuration?: Property$AnimationDuration<TTime> | Array<Property$AnimationDuration<TTime>>,
  MozAnimationFillMode?: Property$AnimationFillMode | Array<Property$AnimationFillMode>,
  MozAnimationIterationCount?: Property$AnimationIterationCount | Array<Property$AnimationIterationCount>,
  MozAnimationName?: Property$AnimationName | Array<Property$AnimationName>,
  MozAnimationPlayState?: Property$AnimationPlayState | Array<Property$AnimationPlayState>,
  MozAnimationTimingFunction?: Property$AnimationTimingFunction | Array<Property$AnimationTimingFunction>,
  MozAppearance?: Property$MozAppearance | Array<Property$MozAppearance>,
  MozBinding?: Property$MozBinding | Array<Property$MozBinding>,
  MozBorderBottomColors?: Property$MozBorderBottomColors | Array<Property$MozBorderBottomColors>,
  MozBorderEndColor?: Property$BorderInlineEndColor | Array<Property$BorderInlineEndColor>,
  MozBorderEndStyle?: Property$BorderInlineEndStyle | Array<Property$BorderInlineEndStyle>,
  MozBorderEndWidth?: Property$BorderInlineEndWidth<TLength> | Array<Property$BorderInlineEndWidth<TLength>>,
  MozBorderLeftColors?: Property$MozBorderLeftColors | Array<Property$MozBorderLeftColors>,
  MozBorderRightColors?: Property$MozBorderRightColors | Array<Property$MozBorderRightColors>,
  MozBorderStartColor?: Property$BorderInlineStartColor | Array<Property$BorderInlineStartColor>,
  MozBorderStartStyle?: Property$BorderInlineStartStyle | Array<Property$BorderInlineStartStyle>,
  MozBorderTopColors?: Property$MozBorderTopColors | Array<Property$MozBorderTopColors>,
  MozBoxSizing?: Property$BoxSizing | Array<Property$BoxSizing>,
  MozColumnCount?: Property$ColumnCount | Array<Property$ColumnCount>,
  MozColumnFill?: Property$ColumnFill | Array<Property$ColumnFill>,
  MozColumnRuleColor?: Property$ColumnRuleColor | Array<Property$ColumnRuleColor>,
  MozColumnRuleStyle?: Property$ColumnRuleStyle | Array<Property$ColumnRuleStyle>,
  MozColumnRuleWidth?: Property$ColumnRuleWidth<TLength> | Array<Property$ColumnRuleWidth<TLength>>,
  MozColumnWidth?: Property$ColumnWidth<TLength> | Array<Property$ColumnWidth<TLength>>,
  MozContextProperties?: Property$MozContextProperties | Array<Property$MozContextProperties>,
  MozFontFeatureSettings?: Property$FontFeatureSettings | Array<Property$FontFeatureSettings>,
  MozFontLanguageOverride?: Property$FontLanguageOverride | Array<Property$FontLanguageOverride>,
  MozHyphens?: Property$Hyphens | Array<Property$Hyphens>,
  MozImageRegion?: Property$MozImageRegion | Array<Property$MozImageRegion>,
  MozMarginEnd?: Property$MarginInlineEnd<TLength> | Array<Property$MarginInlineEnd<TLength>>,
  MozMarginStart?: Property$MarginInlineStart<TLength> | Array<Property$MarginInlineStart<TLength>>,
  MozOrient?: Property$MozOrient | Array<Property$MozOrient>,
  MozOsxFontSmoothing?: Property$FontSmooth<TLength> | Array<Property$FontSmooth<TLength>>,
  MozOutlineRadiusBottomleft?: Property$MozOutlineRadiusBottomleft<TLength> | Array<Property$MozOutlineRadiusBottomleft<TLength>>,
  MozOutlineRadiusBottomright?: Property$MozOutlineRadiusBottomright<TLength> | Array<Property$MozOutlineRadiusBottomright<TLength>>,
  MozOutlineRadiusTopleft?: Property$MozOutlineRadiusTopleft<TLength> | Array<Property$MozOutlineRadiusTopleft<TLength>>,
  MozOutlineRadiusTopright?: Property$MozOutlineRadiusTopright<TLength> | Array<Property$MozOutlineRadiusTopright<TLength>>,
  MozPaddingEnd?: Property$PaddingInlineEnd<TLength> | Array<Property$PaddingInlineEnd<TLength>>,
  MozPaddingStart?: Property$PaddingInlineStart<TLength> | Array<Property$PaddingInlineStart<TLength>>,
  MozStackSizing?: Property$MozStackSizing | Array<Property$MozStackSizing>,
  MozTabSize?: Property$TabSize<TLength> | Array<Property$TabSize<TLength>>,
  MozTextBlink?: Property$MozTextBlink | Array<Property$MozTextBlink>,
  MozTextSizeAdjust?: Property$TextSizeAdjust | Array<Property$TextSizeAdjust>,
  MozUserFocus?: Property$MozUserFocus | Array<Property$MozUserFocus>,
  MozUserModify?: Property$MozUserModify | Array<Property$MozUserModify>,
  MozUserSelect?: Property$UserSelect | Array<Property$UserSelect>,
  MozWindowDragging?: Property$MozWindowDragging | Array<Property$MozWindowDragging>,
  MozWindowShadow?: Property$MozWindowShadow | Array<Property$MozWindowShadow>,
  msAccelerator?: Property$MsAccelerator | Array<Property$MsAccelerator>,
  msBlockProgression?: Property$MsBlockProgression | Array<Property$MsBlockProgression>,
  msContentZoomChaining?: Property$MsContentZoomChaining | Array<Property$MsContentZoomChaining>,
  msContentZoomLimitMax?: Property$MsContentZoomLimitMax | Array<Property$MsContentZoomLimitMax>,
  msContentZoomLimitMin?: Property$MsContentZoomLimitMin | Array<Property$MsContentZoomLimitMin>,
  msContentZoomSnapPoints?: Property$MsContentZoomSnapPoints | Array<Property$MsContentZoomSnapPoints>,
  msContentZoomSnapType?: Property$MsContentZoomSnapType | Array<Property$MsContentZoomSnapType>,
  msContentZooming?: Property$MsContentZooming | Array<Property$MsContentZooming>,
  msFilter?: Property$MsFilter | Array<Property$MsFilter>,
  msFlexDirection?: Property$FlexDirection | Array<Property$FlexDirection>,
  msFlexPositive?: Property$FlexGrow | Array<Property$FlexGrow>,
  msFlowFrom?: Property$MsFlowFrom | Array<Property$MsFlowFrom>,
  msFlowInto?: Property$MsFlowInto | Array<Property$MsFlowInto>,
  msGridColumns?: Property$MsGridColumns<TLength> | Array<Property$MsGridColumns<TLength>>,
  msGridRows?: Property$MsGridRows<TLength> | Array<Property$MsGridRows<TLength>>,
  msHighContrastAdjust?: Property$MsHighContrastAdjust | Array<Property$MsHighContrastAdjust>,
  msHyphenateLimitChars?: Property$MsHyphenateLimitChars | Array<Property$MsHyphenateLimitChars>,
  msHyphenateLimitLines?: Property$MsHyphenateLimitLines | Array<Property$MsHyphenateLimitLines>,
  msHyphenateLimitZone?: Property$MsHyphenateLimitZone<TLength> | Array<Property$MsHyphenateLimitZone<TLength>>,
  msHyphens?: Property$Hyphens | Array<Property$Hyphens>,
  msImeAlign?: Property$MsImeAlign | Array<Property$MsImeAlign>,
  msLineBreak?: Property$LineBreak | Array<Property$LineBreak>,
  msOrder?: Property$Order | Array<Property$Order>,
  msOverflowStyle?: Property$MsOverflowStyle | Array<Property$MsOverflowStyle>,
  msOverflowX?: Property$OverflowX | Array<Property$OverflowX>,
  msOverflowY?: Property$OverflowY | Array<Property$OverflowY>,
  msScrollChaining?: Property$MsScrollChaining | Array<Property$MsScrollChaining>,
  msScrollLimitXMax?: Property$MsScrollLimitXMax<TLength> | Array<Property$MsScrollLimitXMax<TLength>>,
  msScrollLimitXMin?: Property$MsScrollLimitXMin<TLength> | Array<Property$MsScrollLimitXMin<TLength>>,
  msScrollLimitYMax?: Property$MsScrollLimitYMax<TLength> | Array<Property$MsScrollLimitYMax<TLength>>,
  msScrollLimitYMin?: Property$MsScrollLimitYMin<TLength> | Array<Property$MsScrollLimitYMin<TLength>>,
  msScrollRails?: Property$MsScrollRails | Array<Property$MsScrollRails>,
  msScrollSnapPointsX?: Property$MsScrollSnapPointsX | Array<Property$MsScrollSnapPointsX>,
  msScrollSnapPointsY?: Property$MsScrollSnapPointsY | Array<Property$MsScrollSnapPointsY>,
  msScrollSnapType?: Property$MsScrollSnapType | Array<Property$MsScrollSnapType>,
  msScrollTranslation?: Property$MsScrollTranslation | Array<Property$MsScrollTranslation>,
  msScrollbar3dlightColor?: Property$MsScrollbar3dlightColor | Array<Property$MsScrollbar3dlightColor>,
  msScrollbarArrowColor?: Property$MsScrollbarArrowColor | Array<Property$MsScrollbarArrowColor>,
  msScrollbarBaseColor?: Property$MsScrollbarBaseColor | Array<Property$MsScrollbarBaseColor>,
  msScrollbarDarkshadowColor?: Property$MsScrollbarDarkshadowColor | Array<Property$MsScrollbarDarkshadowColor>,
  msScrollbarFaceColor?: Property$MsScrollbarFaceColor | Array<Property$MsScrollbarFaceColor>,
  msScrollbarHighlightColor?: Property$MsScrollbarHighlightColor | Array<Property$MsScrollbarHighlightColor>,
  msScrollbarShadowColor?: Property$MsScrollbarShadowColor | Array<Property$MsScrollbarShadowColor>,
  msScrollbarTrackColor?: Property$MsScrollbarTrackColor | Array<Property$MsScrollbarTrackColor>,
  msTextAutospace?: Property$MsTextAutospace | Array<Property$MsTextAutospace>,
  msTextCombineHorizontal?: Property$TextCombineUpright | Array<Property$TextCombineUpright>,
  msTextOverflow?: Property$TextOverflow | Array<Property$TextOverflow>,
  msTouchAction?: Property$TouchAction | Array<Property$TouchAction>,
  msTouchSelect?: Property$MsTouchSelect | Array<Property$MsTouchSelect>,
  msTransform?: Property$Transform | Array<Property$Transform>,
  msTransformOrigin?: Property$TransformOrigin<TLength> | Array<Property$TransformOrigin<TLength>>,
  msTransitionDelay?: Property$TransitionDelay<TTime> | Array<Property$TransitionDelay<TTime>>,
  msTransitionDuration?: Property$TransitionDuration<TTime> | Array<Property$TransitionDuration<TTime>>,
  msTransitionProperty?: Property$TransitionProperty | Array<Property$TransitionProperty>,
  msTransitionTimingFunction?: Property$TransitionTimingFunction | Array<Property$TransitionTimingFunction>,
  msUserSelect?: Property$MsUserSelect | Array<Property$MsUserSelect>,
  msWordBreak?: Property$WordBreak | Array<Property$WordBreak>,
  msWrapFlow?: Property$MsWrapFlow | Array<Property$MsWrapFlow>,
  msWrapMargin?: Property$MsWrapMargin<TLength> | Array<Property$MsWrapMargin<TLength>>,
  msWrapThrough?: Property$MsWrapThrough | Array<Property$MsWrapThrough>,
  msWritingMode?: Property$WritingMode | Array<Property$WritingMode>,
  WebkitAlignContent?: Property$AlignContent | Array<Property$AlignContent>,
  WebkitAlignItems?: Property$AlignItems | Array<Property$AlignItems>,
  WebkitAlignSelf?: Property$AlignSelf | Array<Property$AlignSelf>,
  WebkitAnimationDelay?: Property$AnimationDelay<TTime> | Array<Property$AnimationDelay<TTime>>,
  WebkitAnimationDirection?: Property$AnimationDirection | Array<Property$AnimationDirection>,
  WebkitAnimationDuration?: Property$AnimationDuration<TTime> | Array<Property$AnimationDuration<TTime>>,
  WebkitAnimationFillMode?: Property$AnimationFillMode | Array<Property$AnimationFillMode>,
  WebkitAnimationIterationCount?: Property$AnimationIterationCount | Array<Property$AnimationIterationCount>,
  WebkitAnimationName?: Property$AnimationName | Array<Property$AnimationName>,
  WebkitAnimationPlayState?: Property$AnimationPlayState | Array<Property$AnimationPlayState>,
  WebkitAnimationTimingFunction?: Property$AnimationTimingFunction | Array<Property$AnimationTimingFunction>,
  WebkitAppearance?: Property$WebkitAppearance | Array<Property$WebkitAppearance>,
  WebkitBackdropFilter?: Property$BackdropFilter | Array<Property$BackdropFilter>,
  WebkitBackfaceVisibility?: Property$BackfaceVisibility | Array<Property$BackfaceVisibility>,
  WebkitBackgroundClip?: Property$BackgroundClip | Array<Property$BackgroundClip>,
  WebkitBackgroundOrigin?: Property$BackgroundOrigin | Array<Property$BackgroundOrigin>,
  WebkitBackgroundSize?: Property$BackgroundSize<TLength> | Array<Property$BackgroundSize<TLength>>,
  WebkitBorderBeforeColor?: Property$WebkitBorderBeforeColor | Array<Property$WebkitBorderBeforeColor>,
  WebkitBorderBeforeStyle?: Property$WebkitBorderBeforeStyle | Array<Property$WebkitBorderBeforeStyle>,
  WebkitBorderBeforeWidth?: Property$WebkitBorderBeforeWidth<TLength> | Array<Property$WebkitBorderBeforeWidth<TLength>>,
  WebkitBorderBottomLeftRadius?: Property$BorderBottomLeftRadius<TLength> | Array<Property$BorderBottomLeftRadius<TLength>>,
  WebkitBorderBottomRightRadius?: Property$BorderBottomRightRadius<TLength> | Array<Property$BorderBottomRightRadius<TLength>>,
  WebkitBorderImageSlice?: Property$BorderImageSlice | Array<Property$BorderImageSlice>,
  WebkitBorderTopLeftRadius?: Property$BorderTopLeftRadius<TLength> | Array<Property$BorderTopLeftRadius<TLength>>,
  WebkitBorderTopRightRadius?: Property$BorderTopRightRadius<TLength> | Array<Property$BorderTopRightRadius<TLength>>,
  WebkitBoxDecorationBreak?: Property$BoxDecorationBreak | Array<Property$BoxDecorationBreak>,
  WebkitBoxReflect?: Property$WebkitBoxReflect<TLength> | Array<Property$WebkitBoxReflect<TLength>>,
  WebkitBoxShadow?: Property$BoxShadow | Array<Property$BoxShadow>,
  WebkitBoxSizing?: Property$BoxSizing | Array<Property$BoxSizing>,
  WebkitClipPath?: Property$ClipPath | Array<Property$ClipPath>,
  WebkitColumnCount?: Property$ColumnCount | Array<Property$ColumnCount>,
  WebkitColumnFill?: Property$ColumnFill | Array<Property$ColumnFill>,
  WebkitColumnRuleColor?: Property$ColumnRuleColor | Array<Property$ColumnRuleColor>,
  WebkitColumnRuleStyle?: Property$ColumnRuleStyle | Array<Property$ColumnRuleStyle>,
  WebkitColumnRuleWidth?: Property$ColumnRuleWidth<TLength> | Array<Property$ColumnRuleWidth<TLength>>,
  WebkitColumnSpan?: Property$ColumnSpan | Array<Property$ColumnSpan>,
  WebkitColumnWidth?: Property$ColumnWidth<TLength> | Array<Property$ColumnWidth<TLength>>,
  WebkitFilter?: Property$Filter | Array<Property$Filter>,
  WebkitFlexBasis?: Property$FlexBasis<TLength> | Array<Property$FlexBasis<TLength>>,
  WebkitFlexDirection?: Property$FlexDirection | Array<Property$FlexDirection>,
  WebkitFlexGrow?: Property$FlexGrow | Array<Property$FlexGrow>,
  WebkitFlexShrink?: Property$FlexShrink | Array<Property$FlexShrink>,
  WebkitFlexWrap?: Property$FlexWrap | Array<Property$FlexWrap>,
  WebkitFontFeatureSettings?: Property$FontFeatureSettings | Array<Property$FontFeatureSettings>,
  WebkitFontKerning?: Property$FontKerning | Array<Property$FontKerning>,
  WebkitFontSmoothing?: Property$FontSmooth<TLength> | Array<Property$FontSmooth<TLength>>,
  WebkitFontVariantLigatures?: Property$FontVariantLigatures | Array<Property$FontVariantLigatures>,
  WebkitHyphenateCharacter?: Property$HyphenateCharacter | Array<Property$HyphenateCharacter>,
  WebkitHyphens?: Property$Hyphens | Array<Property$Hyphens>,
  WebkitInitialLetter?: Property$InitialLetter | Array<Property$InitialLetter>,
  WebkitJustifyContent?: Property$JustifyContent | Array<Property$JustifyContent>,
  WebkitLineBreak?: Property$LineBreak | Array<Property$LineBreak>,
  WebkitLineClamp?: Property$WebkitLineClamp | Array<Property$WebkitLineClamp>,
  WebkitMarginEnd?: Property$MarginInlineEnd<TLength> | Array<Property$MarginInlineEnd<TLength>>,
  WebkitMarginStart?: Property$MarginInlineStart<TLength> | Array<Property$MarginInlineStart<TLength>>,
  WebkitMaskAttachment?: Property$WebkitMaskAttachment | Array<Property$WebkitMaskAttachment>,
  WebkitMaskBoxImageOutset?: Property$MaskBorderOutset<TLength> | Array<Property$MaskBorderOutset<TLength>>,
  WebkitMaskBoxImageRepeat?: Property$MaskBorderRepeat | Array<Property$MaskBorderRepeat>,
  WebkitMaskBoxImageSlice?: Property$MaskBorderSlice | Array<Property$MaskBorderSlice>,
  WebkitMaskBoxImageSource?: Property$MaskBorderSource | Array<Property$MaskBorderSource>,
  WebkitMaskBoxImageWidth?: Property$MaskBorderWidth<TLength> | Array<Property$MaskBorderWidth<TLength>>,
  WebkitMaskClip?: Property$WebkitMaskClip | Array<Property$WebkitMaskClip>,
  WebkitMaskComposite?: Property$WebkitMaskComposite | Array<Property$WebkitMaskComposite>,
  WebkitMaskImage?: Property$WebkitMaskImage | Array<Property$WebkitMaskImage>,
  WebkitMaskOrigin?: Property$WebkitMaskOrigin | Array<Property$WebkitMaskOrigin>,
  WebkitMaskPosition?: Property$WebkitMaskPosition<TLength> | Array<Property$WebkitMaskPosition<TLength>>,
  WebkitMaskPositionX?: Property$WebkitMaskPositionX<TLength> | Array<Property$WebkitMaskPositionX<TLength>>,
  WebkitMaskPositionY?: Property$WebkitMaskPositionY<TLength> | Array<Property$WebkitMaskPositionY<TLength>>,
  WebkitMaskRepeat?: Property$WebkitMaskRepeat | Array<Property$WebkitMaskRepeat>,
  WebkitMaskRepeatX?: Property$WebkitMaskRepeatX | Array<Property$WebkitMaskRepeatX>,
  WebkitMaskRepeatY?: Property$WebkitMaskRepeatY | Array<Property$WebkitMaskRepeatY>,
  WebkitMaskSize?: Property$WebkitMaskSize<TLength> | Array<Property$WebkitMaskSize<TLength>>,
  WebkitMaxInlineSize?: Property$MaxInlineSize<TLength> | Array<Property$MaxInlineSize<TLength>>,
  WebkitOrder?: Property$Order | Array<Property$Order>,
  WebkitOverflowScrolling?: Property$WebkitOverflowScrolling | Array<Property$WebkitOverflowScrolling>,
  WebkitPaddingEnd?: Property$PaddingInlineEnd<TLength> | Array<Property$PaddingInlineEnd<TLength>>,
  WebkitPaddingStart?: Property$PaddingInlineStart<TLength> | Array<Property$PaddingInlineStart<TLength>>,
  WebkitPerspective?: Property$Perspective<TLength> | Array<Property$Perspective<TLength>>,
  WebkitPerspectiveOrigin?: Property$PerspectiveOrigin<TLength> | Array<Property$PerspectiveOrigin<TLength>>,
  WebkitPrintColorAdjust?: Property$PrintColorAdjust | Array<Property$PrintColorAdjust>,
  WebkitRubyPosition?: Property$RubyPosition | Array<Property$RubyPosition>,
  WebkitScrollSnapType?: Property$ScrollSnapType | Array<Property$ScrollSnapType>,
  WebkitShapeMargin?: Property$ShapeMargin<TLength> | Array<Property$ShapeMargin<TLength>>,
  WebkitTapHighlightColor?: Property$WebkitTapHighlightColor | Array<Property$WebkitTapHighlightColor>,
  WebkitTextCombine?: Property$TextCombineUpright | Array<Property$TextCombineUpright>,
  WebkitTextDecorationColor?: Property$TextDecorationColor | Array<Property$TextDecorationColor>,
  WebkitTextDecorationLine?: Property$TextDecorationLine | Array<Property$TextDecorationLine>,
  WebkitTextDecorationSkip?: Property$TextDecorationSkip | Array<Property$TextDecorationSkip>,
  WebkitTextDecorationStyle?: Property$TextDecorationStyle | Array<Property$TextDecorationStyle>,
  WebkitTextEmphasisColor?: Property$TextEmphasisColor | Array<Property$TextEmphasisColor>,
  WebkitTextEmphasisPosition?: Property$TextEmphasisPosition | Array<Property$TextEmphasisPosition>,
  WebkitTextEmphasisStyle?: Property$TextEmphasisStyle | Array<Property$TextEmphasisStyle>,
  WebkitTextFillColor?: Property$WebkitTextFillColor | Array<Property$WebkitTextFillColor>,
  WebkitTextOrientation?: Property$TextOrientation | Array<Property$TextOrientation>,
  WebkitTextSizeAdjust?: Property$TextSizeAdjust | Array<Property$TextSizeAdjust>,
  WebkitTextStrokeColor?: Property$WebkitTextStrokeColor | Array<Property$WebkitTextStrokeColor>,
  WebkitTextStrokeWidth?: Property$WebkitTextStrokeWidth<TLength> | Array<Property$WebkitTextStrokeWidth<TLength>>,
  WebkitTextUnderlinePosition?: Property$TextUnderlinePosition | Array<Property$TextUnderlinePosition>,
  WebkitTouchCallout?: Property$WebkitTouchCallout | Array<Property$WebkitTouchCallout>,
  WebkitTransform?: Property$Transform | Array<Property$Transform>,
  WebkitTransformOrigin?: Property$TransformOrigin<TLength> | Array<Property$TransformOrigin<TLength>>,
  WebkitTransformStyle?: Property$TransformStyle | Array<Property$TransformStyle>,
  WebkitTransitionDelay?: Property$TransitionDelay<TTime> | Array<Property$TransitionDelay<TTime>>,
  WebkitTransitionDuration?: Property$TransitionDuration<TTime> | Array<Property$TransitionDuration<TTime>>,
  WebkitTransitionProperty?: Property$TransitionProperty | Array<Property$TransitionProperty>,
  WebkitTransitionTimingFunction?: Property$TransitionTimingFunction | Array<Property$TransitionTimingFunction>,
  WebkitUserModify?: Property$WebkitUserModify | Array<Property$WebkitUserModify>,
  WebkitUserSelect?: Property$UserSelect | Array<Property$UserSelect>,
  WebkitWritingMode?: Property$WritingMode | Array<Property$WritingMode>,
|};

export type VendorShorthandPropertiesFallback<TLength = string | 0, TTime = string> = {|
  MozAnimation?: Property$Animation<TTime> | Array<Property$Animation<TTime>>,
  MozBorderImage?: Property$BorderImage | Array<Property$BorderImage>,
  MozColumnRule?: Property$ColumnRule<TLength> | Array<Property$ColumnRule<TLength>>,
  MozColumns?: Property$Columns<TLength> | Array<Property$Columns<TLength>>,
  MozOutlineRadius?: Property$MozOutlineRadius<TLength> | Array<Property$MozOutlineRadius<TLength>>,
  msContentZoomLimit?: Property$MsContentZoomLimit | Array<Property$MsContentZoomLimit>,
  msContentZoomSnap?: Property$MsContentZoomSnap | Array<Property$MsContentZoomSnap>,
  msFlex?: Property$Flex<TLength> | Array<Property$Flex<TLength>>,
  msScrollLimit?: Property$MsScrollLimit | Array<Property$MsScrollLimit>,
  msScrollSnapX?: Property$MsScrollSnapX | Array<Property$MsScrollSnapX>,
  msScrollSnapY?: Property$MsScrollSnapY | Array<Property$MsScrollSnapY>,
  msTransition?: Property$Transition<TTime> | Array<Property$Transition<TTime>>,
  WebkitAnimation?: Property$Animation<TTime> | Array<Property$Animation<TTime>>,
  WebkitBorderBefore?: Property$WebkitBorderBefore<TLength> | Array<Property$WebkitBorderBefore<TLength>>,
  WebkitBorderImage?: Property$BorderImage | Array<Property$BorderImage>,
  WebkitBorderRadius?: Property$BorderRadius<TLength> | Array<Property$BorderRadius<TLength>>,
  WebkitColumnRule?: Property$ColumnRule<TLength> | Array<Property$ColumnRule<TLength>>,
  WebkitColumns?: Property$Columns<TLength> | Array<Property$Columns<TLength>>,
  WebkitFlex?: Property$Flex<TLength> | Array<Property$Flex<TLength>>,
  WebkitFlexFlow?: Property$FlexFlow | Array<Property$FlexFlow>,
  WebkitMask?: Property$WebkitMask<TLength> | Array<Property$WebkitMask<TLength>>,
  WebkitMaskBoxImage?: Property$MaskBorder | Array<Property$MaskBorder>,
  WebkitTextEmphasis?: Property$TextEmphasis | Array<Property$TextEmphasis>,
  WebkitTextStroke?: Property$WebkitTextStroke<TLength> | Array<Property$WebkitTextStroke<TLength>>,
  WebkitTransition?: Property$Transition<TTime> | Array<Property$Transition<TTime>>,
|};

export type VendorPropertiesFallback<TLength = string | 0, TTime = string> = {|
  ...VendorLonghandPropertiesFallback<TLength, TTime>,
  ...VendorShorthandPropertiesFallback<TLength, TTime>,
|};

export type ObsoletePropertiesFallback<TLength = string | 0, TTime = string> = {|
  azimuth?: Property$Azimuth | Array<Property$Azimuth>,
  boxAlign?: Property$BoxAlign | Array<Property$BoxAlign>,
  boxDirection?: Property$BoxDirection | Array<Property$BoxDirection>,
  boxFlex?: Property$BoxFlex | Array<Property$BoxFlex>,
  boxFlexGroup?: Property$BoxFlexGroup | Array<Property$BoxFlexGroup>,
  boxLines?: Property$BoxLines | Array<Property$BoxLines>,
  boxOrdinalGroup?: Property$BoxOrdinalGroup | Array<Property$BoxOrdinalGroup>,
  boxOrient?: Property$BoxOrient | Array<Property$BoxOrient>,
  boxPack?: Property$BoxPack | Array<Property$BoxPack>,
  clip?: Property$Clip | Array<Property$Clip>,
  gridColumnGap?: Property$GridColumnGap<TLength> | Array<Property$GridColumnGap<TLength>>,
  gridGap?: Property$GridGap<TLength> | Array<Property$GridGap<TLength>>,
  gridRowGap?: Property$GridRowGap<TLength> | Array<Property$GridRowGap<TLength>>,
  imeMode?: Property$ImeMode | Array<Property$ImeMode>,
  offsetBlock?: Property$InsetBlock<TLength> | Array<Property$InsetBlock<TLength>>,
  offsetBlockEnd?: Property$InsetBlockEnd<TLength> | Array<Property$InsetBlockEnd<TLength>>,
  offsetBlockStart?: Property$InsetBlockStart<TLength> | Array<Property$InsetBlockStart<TLength>>,
  offsetInline?: Property$InsetInline<TLength> | Array<Property$InsetInline<TLength>>,
  offsetInlineEnd?: Property$InsetInlineEnd<TLength> | Array<Property$InsetInlineEnd<TLength>>,
  offsetInlineStart?: Property$InsetInlineStart<TLength> | Array<Property$InsetInlineStart<TLength>>,
  scrollSnapCoordinate?: Property$ScrollSnapCoordinate<TLength> | Array<Property$ScrollSnapCoordinate<TLength>>,
  scrollSnapDestination?: Property$ScrollSnapDestination<TLength> | Array<Property$ScrollSnapDestination<TLength>>,
  scrollSnapPointsX?: Property$ScrollSnapPointsX | Array<Property$ScrollSnapPointsX>,
  scrollSnapPointsY?: Property$ScrollSnapPointsY | Array<Property$ScrollSnapPointsY>,
  scrollSnapTypeX?: Property$ScrollSnapTypeX | Array<Property$ScrollSnapTypeX>,
  scrollSnapTypeY?: Property$ScrollSnapTypeY | Array<Property$ScrollSnapTypeY>,
  KhtmlBoxAlign?: Property$BoxAlign | Array<Property$BoxAlign>,
  KhtmlBoxDirection?: Property$BoxDirection | Array<Property$BoxDirection>,
  KhtmlBoxFlex?: Property$BoxFlex | Array<Property$BoxFlex>,
  KhtmlBoxFlexGroup?: Property$BoxFlexGroup | Array<Property$BoxFlexGroup>,
  KhtmlBoxLines?: Property$BoxLines | Array<Property$BoxLines>,
  KhtmlBoxOrdinalGroup?: Property$BoxOrdinalGroup | Array<Property$BoxOrdinalGroup>,
  KhtmlBoxOrient?: Property$BoxOrient | Array<Property$BoxOrient>,
  KhtmlBoxPack?: Property$BoxPack | Array<Property$BoxPack>,
  KhtmlLineBreak?: Property$LineBreak | Array<Property$LineBreak>,
  KhtmlOpacity?: Property$Opacity | Array<Property$Opacity>,
  KhtmlUserSelect?: Property$UserSelect | Array<Property$UserSelect>,
  MozBackfaceVisibility?: Property$BackfaceVisibility | Array<Property$BackfaceVisibility>,
  MozBackgroundClip?: Property$BackgroundClip | Array<Property$BackgroundClip>,
  MozBackgroundInlinePolicy?: Property$BoxDecorationBreak | Array<Property$BoxDecorationBreak>,
  MozBackgroundOrigin?: Property$BackgroundOrigin | Array<Property$BackgroundOrigin>,
  MozBackgroundSize?: Property$BackgroundSize<TLength> | Array<Property$BackgroundSize<TLength>>,
  MozBorderRadius?: Property$BorderRadius<TLength> | Array<Property$BorderRadius<TLength>>,
  MozBorderRadiusBottomleft?: Property$BorderBottomLeftRadius<TLength> | Array<Property$BorderBottomLeftRadius<TLength>>,
  MozBorderRadiusBottomright?: Property$BorderBottomRightRadius<TLength> | Array<Property$BorderBottomRightRadius<TLength>>,
  MozBorderRadiusTopleft?: Property$BorderTopLeftRadius<TLength> | Array<Property$BorderTopLeftRadius<TLength>>,
  MozBorderRadiusTopright?: Property$BorderTopRightRadius<TLength> | Array<Property$BorderTopRightRadius<TLength>>,
  MozBoxAlign?: Property$BoxAlign | Array<Property$BoxAlign>,
  MozBoxDirection?: Property$BoxDirection | Array<Property$BoxDirection>,
  MozBoxFlex?: Property$BoxFlex | Array<Property$BoxFlex>,
  MozBoxOrdinalGroup?: Property$BoxOrdinalGroup | Array<Property$BoxOrdinalGroup>,
  MozBoxOrient?: Property$BoxOrient | Array<Property$BoxOrient>,
  MozBoxPack?: Property$BoxPack | Array<Property$BoxPack>,
  MozBoxShadow?: Property$BoxShadow | Array<Property$BoxShadow>,
  MozFloatEdge?: Property$MozFloatEdge | Array<Property$MozFloatEdge>,
  MozForceBrokenImageIcon?: Property$MozForceBrokenImageIcon | Array<Property$MozForceBrokenImageIcon>,
  MozOpacity?: Property$Opacity | Array<Property$Opacity>,
  MozOutline?: Property$Outline<TLength> | Array<Property$Outline<TLength>>,
  MozOutlineColor?: Property$OutlineColor | Array<Property$OutlineColor>,
  MozOutlineStyle?: Property$OutlineStyle | Array<Property$OutlineStyle>,
  MozOutlineWidth?: Property$OutlineWidth<TLength> | Array<Property$OutlineWidth<TLength>>,
  MozPerspective?: Property$Perspective<TLength> | Array<Property$Perspective<TLength>>,
  MozPerspectiveOrigin?: Property$PerspectiveOrigin<TLength> | Array<Property$PerspectiveOrigin<TLength>>,
  MozTextAlignLast?: Property$TextAlignLast | Array<Property$TextAlignLast>,
  MozTextDecorationColor?: Property$TextDecorationColor | Array<Property$TextDecorationColor>,
  MozTextDecorationLine?: Property$TextDecorationLine | Array<Property$TextDecorationLine>,
  MozTextDecorationStyle?: Property$TextDecorationStyle | Array<Property$TextDecorationStyle>,
  MozTransform?: Property$Transform | Array<Property$Transform>,
  MozTransformOrigin?: Property$TransformOrigin<TLength> | Array<Property$TransformOrigin<TLength>>,
  MozTransformStyle?: Property$TransformStyle | Array<Property$TransformStyle>,
  MozTransition?: Property$Transition<TTime> | Array<Property$Transition<TTime>>,
  MozTransitionDelay?: Property$TransitionDelay<TTime> | Array<Property$TransitionDelay<TTime>>,
  MozTransitionDuration?: Property$TransitionDuration<TTime> | Array<Property$TransitionDuration<TTime>>,
  MozTransitionProperty?: Property$TransitionProperty | Array<Property$TransitionProperty>,
  MozTransitionTimingFunction?: Property$TransitionTimingFunction | Array<Property$TransitionTimingFunction>,
  MozUserInput?: Property$MozUserInput | Array<Property$MozUserInput>,
  msImeMode?: Property$ImeMode | Array<Property$ImeMode>,
  OAnimation?: Property$Animation<TTime> | Array<Property$Animation<TTime>>,
  OAnimationDelay?: Property$AnimationDelay<TTime> | Array<Property$AnimationDelay<TTime>>,
  OAnimationDirection?: Property$AnimationDirection | Array<Property$AnimationDirection>,
  OAnimationDuration?: Property$AnimationDuration<TTime> | Array<Property$AnimationDuration<TTime>>,
  OAnimationFillMode?: Property$AnimationFillMode | Array<Property$AnimationFillMode>,
  OAnimationIterationCount?: Property$AnimationIterationCount | Array<Property$AnimationIterationCount>,
  OAnimationName?: Property$AnimationName | Array<Property$AnimationName>,
  OAnimationPlayState?: Property$AnimationPlayState | Array<Property$AnimationPlayState>,
  OAnimationTimingFunction?: Property$AnimationTimingFunction | Array<Property$AnimationTimingFunction>,
  OBackgroundSize?: Property$BackgroundSize<TLength> | Array<Property$BackgroundSize<TLength>>,
  OBorderImage?: Property$BorderImage | Array<Property$BorderImage>,
  OObjectFit?: Property$ObjectFit | Array<Property$ObjectFit>,
  OObjectPosition?: Property$ObjectPosition<TLength> | Array<Property$ObjectPosition<TLength>>,
  OTabSize?: Property$TabSize<TLength> | Array<Property$TabSize<TLength>>,
  OTextOverflow?: Property$TextOverflow | Array<Property$TextOverflow>,
  OTransform?: Property$Transform | Array<Property$Transform>,
  OTransformOrigin?: Property$TransformOrigin<TLength> | Array<Property$TransformOrigin<TLength>>,
  OTransition?: Property$Transition<TTime> | Array<Property$Transition<TTime>>,
  OTransitionDelay?: Property$TransitionDelay<TTime> | Array<Property$TransitionDelay<TTime>>,
  OTransitionDuration?: Property$TransitionDuration<TTime> | Array<Property$TransitionDuration<TTime>>,
  OTransitionProperty?: Property$TransitionProperty | Array<Property$TransitionProperty>,
  OTransitionTimingFunction?: Property$TransitionTimingFunction | Array<Property$TransitionTimingFunction>,
  WebkitBoxAlign?: Property$BoxAlign | Array<Property$BoxAlign>,
  WebkitBoxDirection?: Property$BoxDirection | Array<Property$BoxDirection>,
  WebkitBoxFlex?: Property$BoxFlex | Array<Property$BoxFlex>,
  WebkitBoxFlexGroup?: Property$BoxFlexGroup | Array<Property$BoxFlexGroup>,
  WebkitBoxLines?: Property$BoxLines | Array<Property$BoxLines>,
  WebkitBoxOrdinalGroup?: Property$BoxOrdinalGroup | Array<Property$BoxOrdinalGroup>,
  WebkitBoxOrient?: Property$BoxOrient | Array<Property$BoxOrient>,
  WebkitBoxPack?: Property$BoxPack | Array<Property$BoxPack>,
|};

export type SvgPropertiesFallback<TLength = string | 0, TTime = string> = {|
  alignmentBaseline?: Property$AlignmentBaseline | Array<Property$AlignmentBaseline>,
  baselineShift?: Property$BaselineShift<TLength> | Array<Property$BaselineShift<TLength>>,
  clip?: Property$Clip | Array<Property$Clip>,
  clipPath?: Property$ClipPath | Array<Property$ClipPath>,
  clipRule?: Property$ClipRule | Array<Property$ClipRule>,
  color?: Property$Color | Array<Property$Color>,
  colorInterpolation?: Property$ColorInterpolation | Array<Property$ColorInterpolation>,
  colorRendering?: Property$ColorRendering | Array<Property$ColorRendering>,
  cursor?: Property$Cursor | Array<Property$Cursor>,
  direction?: Property$Direction | Array<Property$Direction>,
  display?: Property$Display | Array<Property$Display>,
  dominantBaseline?: Property$DominantBaseline | Array<Property$DominantBaseline>,
  fill?: Property$Fill | Array<Property$Fill>,
  fillOpacity?: Property$FillOpacity | Array<Property$FillOpacity>,
  fillRule?: Property$FillRule | Array<Property$FillRule>,
  filter?: Property$Filter | Array<Property$Filter>,
  floodColor?: Property$FloodColor | Array<Property$FloodColor>,
  floodOpacity?: Property$FloodOpacity | Array<Property$FloodOpacity>,
  font?: Property$Font | Array<Property$Font>,
  fontFamily?: Property$FontFamily | Array<Property$FontFamily>,
  fontSize?: Property$FontSize<TLength> | Array<Property$FontSize<TLength>>,
  fontSizeAdjust?: Property$FontSizeAdjust | Array<Property$FontSizeAdjust>,
  fontStretch?: Property$FontStretch | Array<Property$FontStretch>,
  fontStyle?: Property$FontStyle | Array<Property$FontStyle>,
  fontVariant?: Property$FontVariant | Array<Property$FontVariant>,
  fontWeight?: Property$FontWeight | Array<Property$FontWeight>,
  glyphOrientationVertical?: Property$GlyphOrientationVertical | Array<Property$GlyphOrientationVertical>,
  imageRendering?: Property$ImageRendering | Array<Property$ImageRendering>,
  letterSpacing?: Property$LetterSpacing<TLength> | Array<Property$LetterSpacing<TLength>>,
  lightingColor?: Property$LightingColor | Array<Property$LightingColor>,
  lineHeight?: Property$LineHeight<TLength> | Array<Property$LineHeight<TLength>>,
  marker?: Property$Marker | Array<Property$Marker>,
  markerEnd?: Property$MarkerEnd | Array<Property$MarkerEnd>,
  markerMid?: Property$MarkerMid | Array<Property$MarkerMid>,
  markerStart?: Property$MarkerStart | Array<Property$MarkerStart>,
  mask?: Property$Mask<TLength> | Array<Property$Mask<TLength>>,
  opacity?: Property$Opacity | Array<Property$Opacity>,
  overflow?: Property$Overflow | Array<Property$Overflow>,
  paintOrder?: Property$PaintOrder | Array<Property$PaintOrder>,
  pointerEvents?: Property$PointerEvents | Array<Property$PointerEvents>,
  shapeRendering?: Property$ShapeRendering | Array<Property$ShapeRendering>,
  stopColor?: Property$StopColor | Array<Property$StopColor>,
  stopOpacity?: Property$StopOpacity | Array<Property$StopOpacity>,
  stroke?: Property$Stroke | Array<Property$Stroke>,
  strokeDasharray?: Property$StrokeDasharray<TLength> | Array<Property$StrokeDasharray<TLength>>,
  strokeDashoffset?: Property$StrokeDashoffset<TLength> | Array<Property$StrokeDashoffset<TLength>>,
  strokeLinecap?: Property$StrokeLinecap | Array<Property$StrokeLinecap>,
  strokeLinejoin?: Property$StrokeLinejoin | Array<Property$StrokeLinejoin>,
  strokeMiterlimit?: Property$StrokeMiterlimit | Array<Property$StrokeMiterlimit>,
  strokeOpacity?: Property$StrokeOpacity | Array<Property$StrokeOpacity>,
  strokeWidth?: Property$StrokeWidth<TLength> | Array<Property$StrokeWidth<TLength>>,
  textAnchor?: Property$TextAnchor | Array<Property$TextAnchor>,
  textDecoration?: Property$TextDecoration<TLength> | Array<Property$TextDecoration<TLength>>,
  textRendering?: Property$TextRendering | Array<Property$TextRendering>,
  unicodeBidi?: Property$UnicodeBidi | Array<Property$UnicodeBidi>,
  vectorEffect?: Property$VectorEffect | Array<Property$VectorEffect>,
  visibility?: Property$Visibility | Array<Property$Visibility>,
  whiteSpace?: Property$WhiteSpace | Array<Property$WhiteSpace>,
  wordSpacing?: Property$WordSpacing<TLength> | Array<Property$WordSpacing<TLength>>,
  writingMode?: Property$WritingMode | Array<Property$WritingMode>,
|};

export type PropertiesFallback<TLength = string | 0, TTime = string> = {|
  ...StandardPropertiesFallback<TLength, TTime>,
  ...VendorPropertiesFallback<TLength, TTime>,
  ...ObsoletePropertiesFallback<TLength, TTime>,
  ...SvgPropertiesFallback<TLength, TTime>,
|};

export type StandardLonghandPropertiesHyphenFallback<TLength = string | 0, TTime = string> = {|
  "accent-color"?: Property$AccentColor | Array<Property$AccentColor>,
  "align-content"?: Property$AlignContent | Array<Property$AlignContent>,
  "align-items"?: Property$AlignItems | Array<Property$AlignItems>,
  "align-self"?: Property$AlignSelf | Array<Property$AlignSelf>,
  "align-tracks"?: Property$AlignTracks | Array<Property$AlignTracks>,
  "animation-composition"?: Property$AnimationComposition | Array<Property$AnimationComposition>,
  "animation-delay"?: Property$AnimationDelay<TTime> | Array<Property$AnimationDelay<TTime>>,
  "animation-direction"?: Property$AnimationDirection | Array<Property$AnimationDirection>,
  "animation-duration"?: Property$AnimationDuration<TTime> | Array<Property$AnimationDuration<TTime>>,
  "animation-fill-mode"?: Property$AnimationFillMode | Array<Property$AnimationFillMode>,
  "animation-iteration-count"?: Property$AnimationIterationCount | Array<Property$AnimationIterationCount>,
  "animation-name"?: Property$AnimationName | Array<Property$AnimationName>,
  "animation-play-state"?: Property$AnimationPlayState | Array<Property$AnimationPlayState>,
  "animation-range-end"?: Property$AnimationRangeEnd<TLength> | Array<Property$AnimationRangeEnd<TLength>>,
  "animation-range-start"?: Property$AnimationRangeStart<TLength> | Array<Property$AnimationRangeStart<TLength>>,
  "animation-timeline"?: Property$AnimationTimeline | Array<Property$AnimationTimeline>,
  "animation-timing-function"?: Property$AnimationTimingFunction | Array<Property$AnimationTimingFunction>,
  appearance?: Property$Appearance | Array<Property$Appearance>,
  "aspect-ratio"?: Property$AspectRatio | Array<Property$AspectRatio>,
  "backdrop-filter"?: Property$BackdropFilter | Array<Property$BackdropFilter>,
  "backface-visibility"?: Property$BackfaceVisibility | Array<Property$BackfaceVisibility>,
  "background-attachment"?: Property$BackgroundAttachment | Array<Property$BackgroundAttachment>,
  "background-blend-mode"?: Property$BackgroundBlendMode | Array<Property$BackgroundBlendMode>,
  "background-clip"?: Property$BackgroundClip | Array<Property$BackgroundClip>,
  "background-color"?: Property$BackgroundColor | Array<Property$BackgroundColor>,
  "background-image"?: Property$BackgroundImage | Array<Property$BackgroundImage>,
  "background-origin"?: Property$BackgroundOrigin | Array<Property$BackgroundOrigin>,
  "background-position-x"?: Property$BackgroundPositionX<TLength> | Array<Property$BackgroundPositionX<TLength>>,
  "background-position-y"?: Property$BackgroundPositionY<TLength> | Array<Property$BackgroundPositionY<TLength>>,
  "background-repeat"?: Property$BackgroundRepeat | Array<Property$BackgroundRepeat>,
  "background-size"?: Property$BackgroundSize<TLength> | Array<Property$BackgroundSize<TLength>>,
  "block-overflow"?: Property$BlockOverflow | Array<Property$BlockOverflow>,
  "block-size"?: Property$BlockSize<TLength> | Array<Property$BlockSize<TLength>>,
  "border-block-color"?: Property$BorderBlockColor | Array<Property$BorderBlockColor>,
  "border-block-end-color"?: Property$BorderBlockEndColor | Array<Property$BorderBlockEndColor>,
  "border-block-end-style"?: Property$BorderBlockEndStyle | Array<Property$BorderBlockEndStyle>,
  "border-block-end-width"?: Property$BorderBlockEndWidth<TLength> | Array<Property$BorderBlockEndWidth<TLength>>,
  "border-block-start-color"?: Property$BorderBlockStartColor | Array<Property$BorderBlockStartColor>,
  "border-block-start-style"?: Property$BorderBlockStartStyle | Array<Property$BorderBlockStartStyle>,
  "border-block-start-width"?: Property$BorderBlockStartWidth<TLength> | Array<Property$BorderBlockStartWidth<TLength>>,
  "border-block-style"?: Property$BorderBlockStyle | Array<Property$BorderBlockStyle>,
  "border-block-width"?: Property$BorderBlockWidth<TLength> | Array<Property$BorderBlockWidth<TLength>>,
  "border-bottom-color"?: Property$BorderBottomColor | Array<Property$BorderBottomColor>,
  "border-bottom-left-radius"?: Property$BorderBottomLeftRadius<TLength> | Array<Property$BorderBottomLeftRadius<TLength>>,
  "border-bottom-right-radius"?: Property$BorderBottomRightRadius<TLength> | Array<Property$BorderBottomRightRadius<TLength>>,
  "border-bottom-style"?: Property$BorderBottomStyle | Array<Property$BorderBottomStyle>,
  "border-bottom-width"?: Property$BorderBottomWidth<TLength> | Array<Property$BorderBottomWidth<TLength>>,
  "border-collapse"?: Property$BorderCollapse | Array<Property$BorderCollapse>,
  "border-end-end-radius"?: Property$BorderEndEndRadius<TLength> | Array<Property$BorderEndEndRadius<TLength>>,
  "border-end-start-radius"?: Property$BorderEndStartRadius<TLength> | Array<Property$BorderEndStartRadius<TLength>>,
  "border-image-outset"?: Property$BorderImageOutset<TLength> | Array<Property$BorderImageOutset<TLength>>,
  "border-image-repeat"?: Property$BorderImageRepeat | Array<Property$BorderImageRepeat>,
  "border-image-slice"?: Property$BorderImageSlice | Array<Property$BorderImageSlice>,
  "border-image-source"?: Property$BorderImageSource | Array<Property$BorderImageSource>,
  "border-image-width"?: Property$BorderImageWidth<TLength> | Array<Property$BorderImageWidth<TLength>>,
  "border-inline-color"?: Property$BorderInlineColor | Array<Property$BorderInlineColor>,
  "border-inline-end-color"?: Property$BorderInlineEndColor | Array<Property$BorderInlineEndColor>,
  "border-inline-end-style"?: Property$BorderInlineEndStyle | Array<Property$BorderInlineEndStyle>,
  "border-inline-end-width"?: Property$BorderInlineEndWidth<TLength> | Array<Property$BorderInlineEndWidth<TLength>>,
  "border-inline-start-color"?: Property$BorderInlineStartColor | Array<Property$BorderInlineStartColor>,
  "border-inline-start-style"?: Property$BorderInlineStartStyle | Array<Property$BorderInlineStartStyle>,
  "border-inline-start-width"?: Property$BorderInlineStartWidth<TLength> | Array<Property$BorderInlineStartWidth<TLength>>,
  "border-inline-style"?: Property$BorderInlineStyle | Array<Property$BorderInlineStyle>,
  "border-inline-width"?: Property$BorderInlineWidth<TLength> | Array<Property$BorderInlineWidth<TLength>>,
  "border-left-color"?: Property$BorderLeftColor | Array<Property$BorderLeftColor>,
  "border-left-style"?: Property$BorderLeftStyle | Array<Property$BorderLeftStyle>,
  "border-left-width"?: Property$BorderLeftWidth<TLength> | Array<Property$BorderLeftWidth<TLength>>,
  "border-right-color"?: Property$BorderRightColor | Array<Property$BorderRightColor>,
  "border-right-style"?: Property$BorderRightStyle | Array<Property$BorderRightStyle>,
  "border-right-width"?: Property$BorderRightWidth<TLength> | Array<Property$BorderRightWidth<TLength>>,
  "border-spacing"?: Property$BorderSpacing<TLength> | Array<Property$BorderSpacing<TLength>>,
  "border-start-end-radius"?: Property$BorderStartEndRadius<TLength> | Array<Property$BorderStartEndRadius<TLength>>,
  "border-start-start-radius"?: Property$BorderStartStartRadius<TLength> | Array<Property$BorderStartStartRadius<TLength>>,
  "border-top-color"?: Property$BorderTopColor | Array<Property$BorderTopColor>,
  "border-top-left-radius"?: Property$BorderTopLeftRadius<TLength> | Array<Property$BorderTopLeftRadius<TLength>>,
  "border-top-right-radius"?: Property$BorderTopRightRadius<TLength> | Array<Property$BorderTopRightRadius<TLength>>,
  "border-top-style"?: Property$BorderTopStyle | Array<Property$BorderTopStyle>,
  "border-top-width"?: Property$BorderTopWidth<TLength> | Array<Property$BorderTopWidth<TLength>>,
  bottom?: Property$Bottom<TLength> | Array<Property$Bottom<TLength>>,
  "box-decoration-break"?: Property$BoxDecorationBreak | Array<Property$BoxDecorationBreak>,
  "box-shadow"?: Property$BoxShadow | Array<Property$BoxShadow>,
  "box-sizing"?: Property$BoxSizing | Array<Property$BoxSizing>,
  "break-after"?: Property$BreakAfter | Array<Property$BreakAfter>,
  "break-before"?: Property$BreakBefore | Array<Property$BreakBefore>,
  "break-inside"?: Property$BreakInside | Array<Property$BreakInside>,
  "caption-side"?: Property$CaptionSide | Array<Property$CaptionSide>,
  "caret-color"?: Property$CaretColor | Array<Property$CaretColor>,
  "caret-shape"?: Property$CaretShape | Array<Property$CaretShape>,
  clear?: Property$Clear | Array<Property$Clear>,
  "clip-path"?: Property$ClipPath | Array<Property$ClipPath>,
  color?: Property$Color | Array<Property$Color>,
  "color-adjust"?: Property$PrintColorAdjust | Array<Property$PrintColorAdjust>,
  "color-scheme"?: Property$ColorScheme | Array<Property$ColorScheme>,
  "column-count"?: Property$ColumnCount | Array<Property$ColumnCount>,
  "column-fill"?: Property$ColumnFill | Array<Property$ColumnFill>,
  "column-gap"?: Property$ColumnGap<TLength> | Array<Property$ColumnGap<TLength>>,
  "column-rule-color"?: Property$ColumnRuleColor | Array<Property$ColumnRuleColor>,
  "column-rule-style"?: Property$ColumnRuleStyle | Array<Property$ColumnRuleStyle>,
  "column-rule-width"?: Property$ColumnRuleWidth<TLength> | Array<Property$ColumnRuleWidth<TLength>>,
  "column-span"?: Property$ColumnSpan | Array<Property$ColumnSpan>,
  "column-width"?: Property$ColumnWidth<TLength> | Array<Property$ColumnWidth<TLength>>,
  contain?: Property$Contain | Array<Property$Contain>,
  "contain-intrinsic-block-size"?: Property$ContainIntrinsicBlockSize<TLength> | Array<Property$ContainIntrinsicBlockSize<TLength>>,
  "contain-intrinsic-height"?: Property$ContainIntrinsicHeight<TLength> | Array<Property$ContainIntrinsicHeight<TLength>>,
  "contain-intrinsic-inline-size"?: Property$ContainIntrinsicInlineSize<TLength> | Array<Property$ContainIntrinsicInlineSize<TLength>>,
  "contain-intrinsic-width"?: Property$ContainIntrinsicWidth<TLength> | Array<Property$ContainIntrinsicWidth<TLength>>,
  "container-name"?: Property$ContainerName | Array<Property$ContainerName>,
  "container-type"?: Property$ContainerType | Array<Property$ContainerType>,
  content?: Property$Content | Array<Property$Content>,
  "content-visibility"?: Property$ContentVisibility | Array<Property$ContentVisibility>,
  "counter-increment"?: Property$CounterIncrement | Array<Property$CounterIncrement>,
  "counter-reset"?: Property$CounterReset | Array<Property$CounterReset>,
  "counter-set"?: Property$CounterSet | Array<Property$CounterSet>,
  cursor?: Property$Cursor | Array<Property$Cursor>,
  direction?: Property$Direction | Array<Property$Direction>,
  display?: Property$Display | Array<Property$Display>,
  "empty-cells"?: Property$EmptyCells | Array<Property$EmptyCells>,
  filter?: Property$Filter | Array<Property$Filter>,
  "flex-basis"?: Property$FlexBasis<TLength> | Array<Property$FlexBasis<TLength>>,
  "flex-direction"?: Property$FlexDirection | Array<Property$FlexDirection>,
  "flex-grow"?: Property$FlexGrow | Array<Property$FlexGrow>,
  "flex-shrink"?: Property$FlexShrink | Array<Property$FlexShrink>,
  "flex-wrap"?: Property$FlexWrap | Array<Property$FlexWrap>,
  float?: Property$Float | Array<Property$Float>,
  "font-family"?: Property$FontFamily | Array<Property$FontFamily>,
  "font-feature-settings"?: Property$FontFeatureSettings | Array<Property$FontFeatureSettings>,
  "font-kerning"?: Property$FontKerning | Array<Property$FontKerning>,
  "font-language-override"?: Property$FontLanguageOverride | Array<Property$FontLanguageOverride>,
  "font-optical-sizing"?: Property$FontOpticalSizing | Array<Property$FontOpticalSizing>,
  "font-palette"?: Property$FontPalette | Array<Property$FontPalette>,
  "font-size"?: Property$FontSize<TLength> | Array<Property$FontSize<TLength>>,
  "font-size-adjust"?: Property$FontSizeAdjust | Array<Property$FontSizeAdjust>,
  "font-smooth"?: Property$FontSmooth<TLength> | Array<Property$FontSmooth<TLength>>,
  "font-stretch"?: Property$FontStretch | Array<Property$FontStretch>,
  "font-style"?: Property$FontStyle | Array<Property$FontStyle>,
  "font-synthesis"?: Property$FontSynthesis | Array<Property$FontSynthesis>,
  "font-synthesis-position"?: Property$FontSynthesisPosition | Array<Property$FontSynthesisPosition>,
  "font-synthesis-small-caps"?: Property$FontSynthesisSmallCaps | Array<Property$FontSynthesisSmallCaps>,
  "font-synthesis-style"?: Property$FontSynthesisStyle | Array<Property$FontSynthesisStyle>,
  "font-synthesis-weight"?: Property$FontSynthesisWeight | Array<Property$FontSynthesisWeight>,
  "font-variant"?: Property$FontVariant | Array<Property$FontVariant>,
  "font-variant-alternates"?: Property$FontVariantAlternates | Array<Property$FontVariantAlternates>,
  "font-variant-caps"?: Property$FontVariantCaps | Array<Property$FontVariantCaps>,
  "font-variant-east-asian"?: Property$FontVariantEastAsian | Array<Property$FontVariantEastAsian>,
  "font-variant-emoji"?: Property$FontVariantEmoji | Array<Property$FontVariantEmoji>,
  "font-variant-ligatures"?: Property$FontVariantLigatures | Array<Property$FontVariantLigatures>,
  "font-variant-numeric"?: Property$FontVariantNumeric | Array<Property$FontVariantNumeric>,
  "font-variant-position"?: Property$FontVariantPosition | Array<Property$FontVariantPosition>,
  "font-variation-settings"?: Property$FontVariationSettings | Array<Property$FontVariationSettings>,
  "font-weight"?: Property$FontWeight | Array<Property$FontWeight>,
  "forced-color-adjust"?: Property$ForcedColorAdjust | Array<Property$ForcedColorAdjust>,
  "grid-auto-columns"?: Property$GridAutoColumns<TLength> | Array<Property$GridAutoColumns<TLength>>,
  "grid-auto-flow"?: Property$GridAutoFlow | Array<Property$GridAutoFlow>,
  "grid-auto-rows"?: Property$GridAutoRows<TLength> | Array<Property$GridAutoRows<TLength>>,
  "grid-column-end"?: Property$GridColumnEnd | Array<Property$GridColumnEnd>,
  "grid-column-start"?: Property$GridColumnStart | Array<Property$GridColumnStart>,
  "grid-row-end"?: Property$GridRowEnd | Array<Property$GridRowEnd>,
  "grid-row-start"?: Property$GridRowStart | Array<Property$GridRowStart>,
  "grid-template-areas"?: Property$GridTemplateAreas | Array<Property$GridTemplateAreas>,
  "grid-template-columns"?: Property$GridTemplateColumns<TLength> | Array<Property$GridTemplateColumns<TLength>>,
  "grid-template-rows"?: Property$GridTemplateRows<TLength> | Array<Property$GridTemplateRows<TLength>>,
  "hanging-punctuation"?: Property$HangingPunctuation | Array<Property$HangingPunctuation>,
  height?: Property$Height<TLength> | Array<Property$Height<TLength>>,
  "hyphenate-character"?: Property$HyphenateCharacter | Array<Property$HyphenateCharacter>,
  "hyphenate-limit-chars"?: Property$HyphenateLimitChars | Array<Property$HyphenateLimitChars>,
  hyphens?: Property$Hyphens | Array<Property$Hyphens>,
  "image-orientation"?: Property$ImageOrientation | Array<Property$ImageOrientation>,
  "image-rendering"?: Property$ImageRendering | Array<Property$ImageRendering>,
  "image-resolution"?: Property$ImageResolution | Array<Property$ImageResolution>,
  "initial-letter"?: Property$InitialLetter | Array<Property$InitialLetter>,
  "inline-size"?: Property$InlineSize<TLength> | Array<Property$InlineSize<TLength>>,
  "input-security"?: Property$InputSecurity | Array<Property$InputSecurity>,
  "inset-block-end"?: Property$InsetBlockEnd<TLength> | Array<Property$InsetBlockEnd<TLength>>,
  "inset-block-start"?: Property$InsetBlockStart<TLength> | Array<Property$InsetBlockStart<TLength>>,
  "inset-inline-end"?: Property$InsetInlineEnd<TLength> | Array<Property$InsetInlineEnd<TLength>>,
  "inset-inline-start"?: Property$InsetInlineStart<TLength> | Array<Property$InsetInlineStart<TLength>>,
  isolation?: Property$Isolation | Array<Property$Isolation>,
  "justify-content"?: Property$JustifyContent | Array<Property$JustifyContent>,
  "justify-items"?: Property$JustifyItems | Array<Property$JustifyItems>,
  "justify-self"?: Property$JustifySelf | Array<Property$JustifySelf>,
  "justify-tracks"?: Property$JustifyTracks | Array<Property$JustifyTracks>,
  left?: Property$Left<TLength> | Array<Property$Left<TLength>>,
  "letter-spacing"?: Property$LetterSpacing<TLength> | Array<Property$LetterSpacing<TLength>>,
  "line-break"?: Property$LineBreak | Array<Property$LineBreak>,
  "line-height"?: Property$LineHeight<TLength> | Array<Property$LineHeight<TLength>>,
  "line-height-step"?: Property$LineHeightStep<TLength> | Array<Property$LineHeightStep<TLength>>,
  "list-style-image"?: Property$ListStyleImage | Array<Property$ListStyleImage>,
  "list-style-position"?: Property$ListStylePosition | Array<Property$ListStylePosition>,
  "list-style-type"?: Property$ListStyleType | Array<Property$ListStyleType>,
  "margin-block-end"?: Property$MarginBlockEnd<TLength> | Array<Property$MarginBlockEnd<TLength>>,
  "margin-block-start"?: Property$MarginBlockStart<TLength> | Array<Property$MarginBlockStart<TLength>>,
  "margin-bottom"?: Property$MarginBottom<TLength> | Array<Property$MarginBottom<TLength>>,
  "margin-inline-end"?: Property$MarginInlineEnd<TLength> | Array<Property$MarginInlineEnd<TLength>>,
  "margin-inline-start"?: Property$MarginInlineStart<TLength> | Array<Property$MarginInlineStart<TLength>>,
  "margin-left"?: Property$MarginLeft<TLength> | Array<Property$MarginLeft<TLength>>,
  "margin-right"?: Property$MarginRight<TLength> | Array<Property$MarginRight<TLength>>,
  "margin-top"?: Property$MarginTop<TLength> | Array<Property$MarginTop<TLength>>,
  "margin-trim"?: Property$MarginTrim | Array<Property$MarginTrim>,
  "mask-border-mode"?: Property$MaskBorderMode | Array<Property$MaskBorderMode>,
  "mask-border-outset"?: Property$MaskBorderOutset<TLength> | Array<Property$MaskBorderOutset<TLength>>,
  "mask-border-repeat"?: Property$MaskBorderRepeat | Array<Property$MaskBorderRepeat>,
  "mask-border-slice"?: Property$MaskBorderSlice | Array<Property$MaskBorderSlice>,
  "mask-border-source"?: Property$MaskBorderSource | Array<Property$MaskBorderSource>,
  "mask-border-width"?: Property$MaskBorderWidth<TLength> | Array<Property$MaskBorderWidth<TLength>>,
  "mask-clip"?: Property$MaskClip | Array<Property$MaskClip>,
  "mask-composite"?: Property$MaskComposite | Array<Property$MaskComposite>,
  "mask-image"?: Property$MaskImage | Array<Property$MaskImage>,
  "mask-mode"?: Property$MaskMode | Array<Property$MaskMode>,
  "mask-origin"?: Property$MaskOrigin | Array<Property$MaskOrigin>,
  "mask-position"?: Property$MaskPosition<TLength> | Array<Property$MaskPosition<TLength>>,
  "mask-repeat"?: Property$MaskRepeat | Array<Property$MaskRepeat>,
  "mask-size"?: Property$MaskSize<TLength> | Array<Property$MaskSize<TLength>>,
  "mask-type"?: Property$MaskType | Array<Property$MaskType>,
  "masonry-auto-flow"?: Property$MasonryAutoFlow | Array<Property$MasonryAutoFlow>,
  "math-depth"?: Property$MathDepth | Array<Property$MathDepth>,
  "math-shift"?: Property$MathShift | Array<Property$MathShift>,
  "math-style"?: Property$MathStyle | Array<Property$MathStyle>,
  "max-block-size"?: Property$MaxBlockSize<TLength> | Array<Property$MaxBlockSize<TLength>>,
  "max-height"?: Property$MaxHeight<TLength> | Array<Property$MaxHeight<TLength>>,
  "max-inline-size"?: Property$MaxInlineSize<TLength> | Array<Property$MaxInlineSize<TLength>>,
  "max-lines"?: Property$MaxLines | Array<Property$MaxLines>,
  "max-width"?: Property$MaxWidth<TLength> | Array<Property$MaxWidth<TLength>>,
  "min-block-size"?: Property$MinBlockSize<TLength> | Array<Property$MinBlockSize<TLength>>,
  "min-height"?: Property$MinHeight<TLength> | Array<Property$MinHeight<TLength>>,
  "min-inline-size"?: Property$MinInlineSize<TLength> | Array<Property$MinInlineSize<TLength>>,
  "min-width"?: Property$MinWidth<TLength> | Array<Property$MinWidth<TLength>>,
  "mix-blend-mode"?: Property$MixBlendMode | Array<Property$MixBlendMode>,
  "motion-distance"?: Property$OffsetDistance<TLength> | Array<Property$OffsetDistance<TLength>>,
  "motion-path"?: Property$OffsetPath | Array<Property$OffsetPath>,
  "motion-rotation"?: Property$OffsetRotate | Array<Property$OffsetRotate>,
  "object-fit"?: Property$ObjectFit | Array<Property$ObjectFit>,
  "object-position"?: Property$ObjectPosition<TLength> | Array<Property$ObjectPosition<TLength>>,
  "offset-anchor"?: Property$OffsetAnchor<TLength> | Array<Property$OffsetAnchor<TLength>>,
  "offset-distance"?: Property$OffsetDistance<TLength> | Array<Property$OffsetDistance<TLength>>,
  "offset-path"?: Property$OffsetPath | Array<Property$OffsetPath>,
  "offset-position"?: Property$OffsetPosition<TLength> | Array<Property$OffsetPosition<TLength>>,
  "offset-rotate"?: Property$OffsetRotate | Array<Property$OffsetRotate>,
  "offset-rotation"?: Property$OffsetRotate | Array<Property$OffsetRotate>,
  opacity?: Property$Opacity | Array<Property$Opacity>,
  order?: Property$Order | Array<Property$Order>,
  orphans?: Property$Orphans | Array<Property$Orphans>,
  "outline-color"?: Property$OutlineColor | Array<Property$OutlineColor>,
  "outline-offset"?: Property$OutlineOffset<TLength> | Array<Property$OutlineOffset<TLength>>,
  "outline-style"?: Property$OutlineStyle | Array<Property$OutlineStyle>,
  "outline-width"?: Property$OutlineWidth<TLength> | Array<Property$OutlineWidth<TLength>>,
  "overflow-anchor"?: Property$OverflowAnchor | Array<Property$OverflowAnchor>,
  "overflow-block"?: Property$OverflowBlock | Array<Property$OverflowBlock>,
  "overflow-clip-box"?: Property$OverflowClipBox | Array<Property$OverflowClipBox>,
  "overflow-clip-margin"?: Property$OverflowClipMargin<TLength> | Array<Property$OverflowClipMargin<TLength>>,
  "overflow-inline"?: Property$OverflowInline | Array<Property$OverflowInline>,
  "overflow-wrap"?: Property$OverflowWrap | Array<Property$OverflowWrap>,
  "overflow-x"?: Property$OverflowX | Array<Property$OverflowX>,
  "overflow-y"?: Property$OverflowY | Array<Property$OverflowY>,
  overlay?: Property$Overlay | Array<Property$Overlay>,
  "overscroll-behavior-block"?: Property$OverscrollBehaviorBlock | Array<Property$OverscrollBehaviorBlock>,
  "overscroll-behavior-inline"?: Property$OverscrollBehaviorInline | Array<Property$OverscrollBehaviorInline>,
  "overscroll-behavior-x"?: Property$OverscrollBehaviorX | Array<Property$OverscrollBehaviorX>,
  "overscroll-behavior-y"?: Property$OverscrollBehaviorY | Array<Property$OverscrollBehaviorY>,
  "padding-block-end"?: Property$PaddingBlockEnd<TLength> | Array<Property$PaddingBlockEnd<TLength>>,
  "padding-block-start"?: Property$PaddingBlockStart<TLength> | Array<Property$PaddingBlockStart<TLength>>,
  "padding-bottom"?: Property$PaddingBottom<TLength> | Array<Property$PaddingBottom<TLength>>,
  "padding-inline-end"?: Property$PaddingInlineEnd<TLength> | Array<Property$PaddingInlineEnd<TLength>>,
  "padding-inline-start"?: Property$PaddingInlineStart<TLength> | Array<Property$PaddingInlineStart<TLength>>,
  "padding-left"?: Property$PaddingLeft<TLength> | Array<Property$PaddingLeft<TLength>>,
  "padding-right"?: Property$PaddingRight<TLength> | Array<Property$PaddingRight<TLength>>,
  "padding-top"?: Property$PaddingTop<TLength> | Array<Property$PaddingTop<TLength>>,
  page?: Property$Page | Array<Property$Page>,
  "page-break-after"?: Property$PageBreakAfter | Array<Property$PageBreakAfter>,
  "page-break-before"?: Property$PageBreakBefore | Array<Property$PageBreakBefore>,
  "page-break-inside"?: Property$PageBreakInside | Array<Property$PageBreakInside>,
  "paint-order"?: Property$PaintOrder | Array<Property$PaintOrder>,
  perspective?: Property$Perspective<TLength> | Array<Property$Perspective<TLength>>,
  "perspective-origin"?: Property$PerspectiveOrigin<TLength> | Array<Property$PerspectiveOrigin<TLength>>,
  "pointer-events"?: Property$PointerEvents | Array<Property$PointerEvents>,
  position?: Property$Position | Array<Property$Position>,
  "print-color-adjust"?: Property$PrintColorAdjust | Array<Property$PrintColorAdjust>,
  quotes?: Property$Quotes | Array<Property$Quotes>,
  resize?: Property$Resize | Array<Property$Resize>,
  right?: Property$Right<TLength> | Array<Property$Right<TLength>>,
  rotate?: Property$Rotate | Array<Property$Rotate>,
  "row-gap"?: Property$RowGap<TLength> | Array<Property$RowGap<TLength>>,
  "ruby-align"?: Property$RubyAlign | Array<Property$RubyAlign>,
  "ruby-merge"?: Property$RubyMerge | Array<Property$RubyMerge>,
  "ruby-position"?: Property$RubyPosition | Array<Property$RubyPosition>,
  scale?: Property$Scale | Array<Property$Scale>,
  "scroll-behavior"?: Property$ScrollBehavior | Array<Property$ScrollBehavior>,
  "scroll-margin-block-end"?: Property$ScrollMarginBlockEnd<TLength> | Array<Property$ScrollMarginBlockEnd<TLength>>,
  "scroll-margin-block-start"?: Property$ScrollMarginBlockStart<TLength> | Array<Property$ScrollMarginBlockStart<TLength>>,
  "scroll-margin-bottom"?: Property$ScrollMarginBottom<TLength> | Array<Property$ScrollMarginBottom<TLength>>,
  "scroll-margin-inline-end"?: Property$ScrollMarginInlineEnd<TLength> | Array<Property$ScrollMarginInlineEnd<TLength>>,
  "scroll-margin-inline-start"?: Property$ScrollMarginInlineStart<TLength> | Array<Property$ScrollMarginInlineStart<TLength>>,
  "scroll-margin-left"?: Property$ScrollMarginLeft<TLength> | Array<Property$ScrollMarginLeft<TLength>>,
  "scroll-margin-right"?: Property$ScrollMarginRight<TLength> | Array<Property$ScrollMarginRight<TLength>>,
  "scroll-margin-top"?: Property$ScrollMarginTop<TLength> | Array<Property$ScrollMarginTop<TLength>>,
  "scroll-padding-block-end"?: Property$ScrollPaddingBlockEnd<TLength> | Array<Property$ScrollPaddingBlockEnd<TLength>>,
  "scroll-padding-block-start"?: Property$ScrollPaddingBlockStart<TLength> | Array<Property$ScrollPaddingBlockStart<TLength>>,
  "scroll-padding-bottom"?: Property$ScrollPaddingBottom<TLength> | Array<Property$ScrollPaddingBottom<TLength>>,
  "scroll-padding-inline-end"?: Property$ScrollPaddingInlineEnd<TLength> | Array<Property$ScrollPaddingInlineEnd<TLength>>,
  "scroll-padding-inline-start"?: Property$ScrollPaddingInlineStart<TLength> | Array<Property$ScrollPaddingInlineStart<TLength>>,
  "scroll-padding-left"?: Property$ScrollPaddingLeft<TLength> | Array<Property$ScrollPaddingLeft<TLength>>,
  "scroll-padding-right"?: Property$ScrollPaddingRight<TLength> | Array<Property$ScrollPaddingRight<TLength>>,
  "scroll-padding-top"?: Property$ScrollPaddingTop<TLength> | Array<Property$ScrollPaddingTop<TLength>>,
  "scroll-snap-align"?: Property$ScrollSnapAlign | Array<Property$ScrollSnapAlign>,
  "scroll-snap-margin-bottom"?: Property$ScrollMarginBottom<TLength> | Array<Property$ScrollMarginBottom<TLength>>,
  "scroll-snap-margin-left"?: Property$ScrollMarginLeft<TLength> | Array<Property$ScrollMarginLeft<TLength>>,
  "scroll-snap-margin-right"?: Property$ScrollMarginRight<TLength> | Array<Property$ScrollMarginRight<TLength>>,
  "scroll-snap-margin-top"?: Property$ScrollMarginTop<TLength> | Array<Property$ScrollMarginTop<TLength>>,
  "scroll-snap-stop"?: Property$ScrollSnapStop | Array<Property$ScrollSnapStop>,
  "scroll-snap-type"?: Property$ScrollSnapType | Array<Property$ScrollSnapType>,
  "scroll-timeline-axis"?: Property$ScrollTimelineAxis | Array<Property$ScrollTimelineAxis>,
  "scroll-timeline-name"?: Property$ScrollTimelineName | Array<Property$ScrollTimelineName>,
  "scrollbar-color"?: Property$ScrollbarColor | Array<Property$ScrollbarColor>,
  "scrollbar-gutter"?: Property$ScrollbarGutter | Array<Property$ScrollbarGutter>,
  "scrollbar-width"?: Property$ScrollbarWidth | Array<Property$ScrollbarWidth>,
  "shape-image-threshold"?: Property$ShapeImageThreshold | Array<Property$ShapeImageThreshold>,
  "shape-margin"?: Property$ShapeMargin<TLength> | Array<Property$ShapeMargin<TLength>>,
  "shape-outside"?: Property$ShapeOutside | Array<Property$ShapeOutside>,
  "tab-size"?: Property$TabSize<TLength> | Array<Property$TabSize<TLength>>,
  "table-layout"?: Property$TableLayout | Array<Property$TableLayout>,
  "text-align"?: Property$TextAlign | Array<Property$TextAlign>,
  "text-align-last"?: Property$TextAlignLast | Array<Property$TextAlignLast>,
  "text-combine-upright"?: Property$TextCombineUpright | Array<Property$TextCombineUpright>,
  "text-decoration-color"?: Property$TextDecorationColor | Array<Property$TextDecorationColor>,
  "text-decoration-line"?: Property$TextDecorationLine | Array<Property$TextDecorationLine>,
  "text-decoration-skip"?: Property$TextDecorationSkip | Array<Property$TextDecorationSkip>,
  "text-decoration-skip-ink"?: Property$TextDecorationSkipInk | Array<Property$TextDecorationSkipInk>,
  "text-decoration-style"?: Property$TextDecorationStyle | Array<Property$TextDecorationStyle>,
  "text-decoration-thickness"?: Property$TextDecorationThickness<TLength> | Array<Property$TextDecorationThickness<TLength>>,
  "text-emphasis-color"?: Property$TextEmphasisColor | Array<Property$TextEmphasisColor>,
  "text-emphasis-position"?: Property$TextEmphasisPosition | Array<Property$TextEmphasisPosition>,
  "text-emphasis-style"?: Property$TextEmphasisStyle | Array<Property$TextEmphasisStyle>,
  "text-indent"?: Property$TextIndent<TLength> | Array<Property$TextIndent<TLength>>,
  "text-justify"?: Property$TextJustify | Array<Property$TextJustify>,
  "text-orientation"?: Property$TextOrientation | Array<Property$TextOrientation>,
  "text-overflow"?: Property$TextOverflow | Array<Property$TextOverflow>,
  "text-rendering"?: Property$TextRendering | Array<Property$TextRendering>,
  "text-shadow"?: Property$TextShadow | Array<Property$TextShadow>,
  "text-size-adjust"?: Property$TextSizeAdjust | Array<Property$TextSizeAdjust>,
  "text-transform"?: Property$TextTransform | Array<Property$TextTransform>,
  "text-underline-offset"?: Property$TextUnderlineOffset<TLength> | Array<Property$TextUnderlineOffset<TLength>>,
  "text-underline-position"?: Property$TextUnderlinePosition | Array<Property$TextUnderlinePosition>,
  "text-wrap"?: Property$TextWrap | Array<Property$TextWrap>,
  "timeline-scope"?: Property$TimelineScope | Array<Property$TimelineScope>,
  top?: Property$Top<TLength> | Array<Property$Top<TLength>>,
  "touch-action"?: Property$TouchAction | Array<Property$TouchAction>,
  transform?: Property$Transform | Array<Property$Transform>,
  "transform-box"?: Property$TransformBox | Array<Property$TransformBox>,
  "transform-origin"?: Property$TransformOrigin<TLength> | Array<Property$TransformOrigin<TLength>>,
  "transform-style"?: Property$TransformStyle | Array<Property$TransformStyle>,
  "transition-behavior"?: Property$TransitionBehavior | Array<Property$TransitionBehavior>,
  "transition-delay"?: Property$TransitionDelay<TTime> | Array<Property$TransitionDelay<TTime>>,
  "transition-duration"?: Property$TransitionDuration<TTime> | Array<Property$TransitionDuration<TTime>>,
  "transition-property"?: Property$TransitionProperty | Array<Property$TransitionProperty>,
  "transition-timing-function"?: Property$TransitionTimingFunction | Array<Property$TransitionTimingFunction>,
  translate?: Property$Translate<TLength> | Array<Property$Translate<TLength>>,
  "unicode-bidi"?: Property$UnicodeBidi | Array<Property$UnicodeBidi>,
  "user-select"?: Property$UserSelect | Array<Property$UserSelect>,
  "vertical-align"?: Property$VerticalAlign<TLength> | Array<Property$VerticalAlign<TLength>>,
  "view-timeline-axis"?: Property$ViewTimelineAxis | Array<Property$ViewTimelineAxis>,
  "view-timeline-inset"?: Property$ViewTimelineInset<TLength> | Array<Property$ViewTimelineInset<TLength>>,
  "view-timeline-name"?: Property$ViewTimelineName | Array<Property$ViewTimelineName>,
  "view-transition-name"?: Property$ViewTransitionName | Array<Property$ViewTransitionName>,
  visibility?: Property$Visibility | Array<Property$Visibility>,
  "white-space"?: Property$WhiteSpace | Array<Property$WhiteSpace>,
  "white-space-collapse"?: Property$WhiteSpaceCollapse | Array<Property$WhiteSpaceCollapse>,
  "white-space-trim"?: Property$WhiteSpaceTrim | Array<Property$WhiteSpaceTrim>,
  widows?: Property$Widows | Array<Property$Widows>,
  width?: Property$Width<TLength> | Array<Property$Width<TLength>>,
  "will-change"?: Property$WillChange | Array<Property$WillChange>,
  "word-break"?: Property$WordBreak | Array<Property$WordBreak>,
  "word-spacing"?: Property$WordSpacing<TLength> | Array<Property$WordSpacing<TLength>>,
  "word-wrap"?: Property$WordWrap | Array<Property$WordWrap>,
  "writing-mode"?: Property$WritingMode | Array<Property$WritingMode>,
  "z-index"?: Property$ZIndex | Array<Property$ZIndex>,
  zoom?: Property$Zoom | Array<Property$Zoom>,
|};

export type StandardShorthandPropertiesHyphenFallback<TLength = string | 0, TTime = string> = {|
  all?: Property$All | Array<Property$All>,
  animation?: Property$Animation<TTime> | Array<Property$Animation<TTime>>,
  "animation-range"?: Property$AnimationRange<TLength> | Array<Property$AnimationRange<TLength>>,
  background?: Property$Background<TLength> | Array<Property$Background<TLength>>,
  "background-position"?: Property$BackgroundPosition<TLength> | Array<Property$BackgroundPosition<TLength>>,
  border?: Property$Border<TLength> | Array<Property$Border<TLength>>,
  "border-block"?: Property$BorderBlock<TLength> | Array<Property$BorderBlock<TLength>>,
  "border-block-end"?: Property$BorderBlockEnd<TLength> | Array<Property$BorderBlockEnd<TLength>>,
  "border-block-start"?: Property$BorderBlockStart<TLength> | Array<Property$BorderBlockStart<TLength>>,
  "border-bottom"?: Property$BorderBottom<TLength> | Array<Property$BorderBottom<TLength>>,
  "border-color"?: Property$BorderColor | Array<Property$BorderColor>,
  "border-image"?: Property$BorderImage | Array<Property$BorderImage>,
  "border-inline"?: Property$BorderInline<TLength> | Array<Property$BorderInline<TLength>>,
  "border-inline-end"?: Property$BorderInlineEnd<TLength> | Array<Property$BorderInlineEnd<TLength>>,
  "border-inline-start"?: Property$BorderInlineStart<TLength> | Array<Property$BorderInlineStart<TLength>>,
  "border-left"?: Property$BorderLeft<TLength> | Array<Property$BorderLeft<TLength>>,
  "border-radius"?: Property$BorderRadius<TLength> | Array<Property$BorderRadius<TLength>>,
  "border-right"?: Property$BorderRight<TLength> | Array<Property$BorderRight<TLength>>,
  "border-style"?: Property$BorderStyle | Array<Property$BorderStyle>,
  "border-top"?: Property$BorderTop<TLength> | Array<Property$BorderTop<TLength>>,
  "border-width"?: Property$BorderWidth<TLength> | Array<Property$BorderWidth<TLength>>,
  caret?: Property$Caret | Array<Property$Caret>,
  "column-rule"?: Property$ColumnRule<TLength> | Array<Property$ColumnRule<TLength>>,
  columns?: Property$Columns<TLength> | Array<Property$Columns<TLength>>,
  "contain-intrinsic-size"?: Property$ContainIntrinsicSize<TLength> | Array<Property$ContainIntrinsicSize<TLength>>,
  container?: Property$Container | Array<Property$Container>,
  flex?: Property$Flex<TLength> | Array<Property$Flex<TLength>>,
  "flex-flow"?: Property$FlexFlow | Array<Property$FlexFlow>,
  font?: Property$Font | Array<Property$Font>,
  gap?: Property$Gap<TLength> | Array<Property$Gap<TLength>>,
  grid?: Property$Grid | Array<Property$Grid>,
  "grid-area"?: Property$GridArea | Array<Property$GridArea>,
  "grid-column"?: Property$GridColumn | Array<Property$GridColumn>,
  "grid-row"?: Property$GridRow | Array<Property$GridRow>,
  "grid-template"?: Property$GridTemplate | Array<Property$GridTemplate>,
  inset?: Property$Inset<TLength> | Array<Property$Inset<TLength>>,
  "inset-block"?: Property$InsetBlock<TLength> | Array<Property$InsetBlock<TLength>>,
  "inset-inline"?: Property$InsetInline<TLength> | Array<Property$InsetInline<TLength>>,
  "line-clamp"?: Property$LineClamp | Array<Property$LineClamp>,
  "list-style"?: Property$ListStyle | Array<Property$ListStyle>,
  margin?: Property$Margin<TLength> | Array<Property$Margin<TLength>>,
  "margin-block"?: Property$MarginBlock<TLength> | Array<Property$MarginBlock<TLength>>,
  "margin-inline"?: Property$MarginInline<TLength> | Array<Property$MarginInline<TLength>>,
  mask?: Property$Mask<TLength> | Array<Property$Mask<TLength>>,
  "mask-border"?: Property$MaskBorder | Array<Property$MaskBorder>,
  motion?: Property$Offset<TLength> | Array<Property$Offset<TLength>>,
  offset?: Property$Offset<TLength> | Array<Property$Offset<TLength>>,
  outline?: Property$Outline<TLength> | Array<Property$Outline<TLength>>,
  overflow?: Property$Overflow | Array<Property$Overflow>,
  "overscroll-behavior"?: Property$OverscrollBehavior | Array<Property$OverscrollBehavior>,
  padding?: Property$Padding<TLength> | Array<Property$Padding<TLength>>,
  "padding-block"?: Property$PaddingBlock<TLength> | Array<Property$PaddingBlock<TLength>>,
  "padding-inline"?: Property$PaddingInline<TLength> | Array<Property$PaddingInline<TLength>>,
  "place-content"?: Property$PlaceContent | Array<Property$PlaceContent>,
  "place-items"?: Property$PlaceItems | Array<Property$PlaceItems>,
  "place-self"?: Property$PlaceSelf | Array<Property$PlaceSelf>,
  "scroll-margin"?: Property$ScrollMargin<TLength> | Array<Property$ScrollMargin<TLength>>,
  "scroll-margin-block"?: Property$ScrollMarginBlock<TLength> | Array<Property$ScrollMarginBlock<TLength>>,
  "scroll-margin-inline"?: Property$ScrollMarginInline<TLength> | Array<Property$ScrollMarginInline<TLength>>,
  "scroll-padding"?: Property$ScrollPadding<TLength> | Array<Property$ScrollPadding<TLength>>,
  "scroll-padding-block"?: Property$ScrollPaddingBlock<TLength> | Array<Property$ScrollPaddingBlock<TLength>>,
  "scroll-padding-inline"?: Property$ScrollPaddingInline<TLength> | Array<Property$ScrollPaddingInline<TLength>>,
  "scroll-snap-margin"?: Property$ScrollMargin<TLength> | Array<Property$ScrollMargin<TLength>>,
  "scroll-timeline"?: Property$ScrollTimeline | Array<Property$ScrollTimeline>,
  "text-decoration"?: Property$TextDecoration<TLength> | Array<Property$TextDecoration<TLength>>,
  "text-emphasis"?: Property$TextEmphasis | Array<Property$TextEmphasis>,
  transition?: Property$Transition<TTime> | Array<Property$Transition<TTime>>,
  "view-timeline"?: Property$ViewTimeline | Array<Property$ViewTimeline>,
|};

export type StandardPropertiesHyphenFallback<TLength = string | 0, TTime = string> = {|
  ...StandardLonghandPropertiesHyphenFallback<TLength, TTime>,
  ...StandardShorthandPropertiesHyphenFallback<TLength, TTime>,
|};

export type VendorLonghandPropertiesHyphenFallback<TLength = string | 0, TTime = string> = {|
  "-moz-animation-delay"?: Property$AnimationDelay<TTime> | Array<Property$AnimationDelay<TTime>>,
  "-moz-animation-direction"?: Property$AnimationDirection | Array<Property$AnimationDirection>,
  "-moz-animation-duration"?: Property$AnimationDuration<TTime> | Array<Property$AnimationDuration<TTime>>,
  "-moz-animation-fill-mode"?: Property$AnimationFillMode | Array<Property$AnimationFillMode>,
  "-moz-animation-iteration-count"?: Property$AnimationIterationCount | Array<Property$AnimationIterationCount>,
  "-moz-animation-name"?: Property$AnimationName | Array<Property$AnimationName>,
  "-moz-animation-play-state"?: Property$AnimationPlayState | Array<Property$AnimationPlayState>,
  "-moz-animation-timing-function"?: Property$AnimationTimingFunction | Array<Property$AnimationTimingFunction>,
  "-moz-appearance"?: Property$MozAppearance | Array<Property$MozAppearance>,
  "-moz-binding"?: Property$MozBinding | Array<Property$MozBinding>,
  "-moz-border-bottom-colors"?: Property$MozBorderBottomColors | Array<Property$MozBorderBottomColors>,
  "-moz-border-end-color"?: Property$BorderInlineEndColor | Array<Property$BorderInlineEndColor>,
  "-moz-border-end-style"?: Property$BorderInlineEndStyle | Array<Property$BorderInlineEndStyle>,
  "-moz-border-end-width"?: Property$BorderInlineEndWidth<TLength> | Array<Property$BorderInlineEndWidth<TLength>>,
  "-moz-border-left-colors"?: Property$MozBorderLeftColors | Array<Property$MozBorderLeftColors>,
  "-moz-border-right-colors"?: Property$MozBorderRightColors | Array<Property$MozBorderRightColors>,
  "-moz-border-start-color"?: Property$BorderInlineStartColor | Array<Property$BorderInlineStartColor>,
  "-moz-border-start-style"?: Property$BorderInlineStartStyle | Array<Property$BorderInlineStartStyle>,
  "-moz-border-top-colors"?: Property$MozBorderTopColors | Array<Property$MozBorderTopColors>,
  "-moz-box-sizing"?: Property$BoxSizing | Array<Property$BoxSizing>,
  "-moz-column-count"?: Property$ColumnCount | Array<Property$ColumnCount>,
  "-moz-column-fill"?: Property$ColumnFill | Array<Property$ColumnFill>,
  "-moz-column-rule-color"?: Property$ColumnRuleColor | Array<Property$ColumnRuleColor>,
  "-moz-column-rule-style"?: Property$ColumnRuleStyle | Array<Property$ColumnRuleStyle>,
  "-moz-column-rule-width"?: Property$ColumnRuleWidth<TLength> | Array<Property$ColumnRuleWidth<TLength>>,
  "-moz-column-width"?: Property$ColumnWidth<TLength> | Array<Property$ColumnWidth<TLength>>,
  "-moz-context-properties"?: Property$MozContextProperties | Array<Property$MozContextProperties>,
  "-moz-font-feature-settings"?: Property$FontFeatureSettings | Array<Property$FontFeatureSettings>,
  "-moz-font-language-override"?: Property$FontLanguageOverride | Array<Property$FontLanguageOverride>,
  "-moz-hyphens"?: Property$Hyphens | Array<Property$Hyphens>,
  "-moz-image-region"?: Property$MozImageRegion | Array<Property$MozImageRegion>,
  "-moz-margin-end"?: Property$MarginInlineEnd<TLength> | Array<Property$MarginInlineEnd<TLength>>,
  "-moz-margin-start"?: Property$MarginInlineStart<TLength> | Array<Property$MarginInlineStart<TLength>>,
  "-moz-orient"?: Property$MozOrient | Array<Property$MozOrient>,
  "-moz-osx-font-smoothing"?: Property$FontSmooth<TLength> | Array<Property$FontSmooth<TLength>>,
  "-moz-outline-radius-bottomleft"?: Property$MozOutlineRadiusBottomleft<TLength> | Array<Property$MozOutlineRadiusBottomleft<TLength>>,
  "-moz-outline-radius-bottomright"?: Property$MozOutlineRadiusBottomright<TLength> | Array<Property$MozOutlineRadiusBottomright<TLength>>,
  "-moz-outline-radius-topleft"?: Property$MozOutlineRadiusTopleft<TLength> | Array<Property$MozOutlineRadiusTopleft<TLength>>,
  "-moz-outline-radius-topright"?: Property$MozOutlineRadiusTopright<TLength> | Array<Property$MozOutlineRadiusTopright<TLength>>,
  "-moz-padding-end"?: Property$PaddingInlineEnd<TLength> | Array<Property$PaddingInlineEnd<TLength>>,
  "-moz-padding-start"?: Property$PaddingInlineStart<TLength> | Array<Property$PaddingInlineStart<TLength>>,
  "-moz-stack-sizing"?: Property$MozStackSizing | Array<Property$MozStackSizing>,
  "-moz-tab-size"?: Property$TabSize<TLength> | Array<Property$TabSize<TLength>>,
  "-moz-text-blink"?: Property$MozTextBlink | Array<Property$MozTextBlink>,
  "-moz-text-size-adjust"?: Property$TextSizeAdjust | Array<Property$TextSizeAdjust>,
  "-moz-user-focus"?: Property$MozUserFocus | Array<Property$MozUserFocus>,
  "-moz-user-modify"?: Property$MozUserModify | Array<Property$MozUserModify>,
  "-moz-user-select"?: Property$UserSelect | Array<Property$UserSelect>,
  "-moz-window-dragging"?: Property$MozWindowDragging | Array<Property$MozWindowDragging>,
  "-moz-window-shadow"?: Property$MozWindowShadow | Array<Property$MozWindowShadow>,
  "-ms-accelerator"?: Property$MsAccelerator | Array<Property$MsAccelerator>,
  "-ms-block-progression"?: Property$MsBlockProgression | Array<Property$MsBlockProgression>,
  "-ms-content-zoom-chaining"?: Property$MsContentZoomChaining | Array<Property$MsContentZoomChaining>,
  "-ms-content-zoom-limit-max"?: Property$MsContentZoomLimitMax | Array<Property$MsContentZoomLimitMax>,
  "-ms-content-zoom-limit-min"?: Property$MsContentZoomLimitMin | Array<Property$MsContentZoomLimitMin>,
  "-ms-content-zoom-snap-points"?: Property$MsContentZoomSnapPoints | Array<Property$MsContentZoomSnapPoints>,
  "-ms-content-zoom-snap-type"?: Property$MsContentZoomSnapType | Array<Property$MsContentZoomSnapType>,
  "-ms-content-zooming"?: Property$MsContentZooming | Array<Property$MsContentZooming>,
  "-ms-filter"?: Property$MsFilter | Array<Property$MsFilter>,
  "-ms-flex-direction"?: Property$FlexDirection | Array<Property$FlexDirection>,
  "-ms-flex-positive"?: Property$FlexGrow | Array<Property$FlexGrow>,
  "-ms-flow-from"?: Property$MsFlowFrom | Array<Property$MsFlowFrom>,
  "-ms-flow-into"?: Property$MsFlowInto | Array<Property$MsFlowInto>,
  "-ms-grid-columns"?: Property$MsGridColumns<TLength> | Array<Property$MsGridColumns<TLength>>,
  "-ms-grid-rows"?: Property$MsGridRows<TLength> | Array<Property$MsGridRows<TLength>>,
  "-ms-high-contrast-adjust"?: Property$MsHighContrastAdjust | Array<Property$MsHighContrastAdjust>,
  "-ms-hyphenate-limit-chars"?: Property$MsHyphenateLimitChars | Array<Property$MsHyphenateLimitChars>,
  "-ms-hyphenate-limit-lines"?: Property$MsHyphenateLimitLines | Array<Property$MsHyphenateLimitLines>,
  "-ms-hyphenate-limit-zone"?: Property$MsHyphenateLimitZone<TLength> | Array<Property$MsHyphenateLimitZone<TLength>>,
  "-ms-hyphens"?: Property$Hyphens | Array<Property$Hyphens>,
  "-ms-ime-align"?: Property$MsImeAlign | Array<Property$MsImeAlign>,
  "-ms-line-break"?: Property$LineBreak | Array<Property$LineBreak>,
  "-ms-order"?: Property$Order | Array<Property$Order>,
  "-ms-overflow-style"?: Property$MsOverflowStyle | Array<Property$MsOverflowStyle>,
  "-ms-overflow-x"?: Property$OverflowX | Array<Property$OverflowX>,
  "-ms-overflow-y"?: Property$OverflowY | Array<Property$OverflowY>,
  "-ms-scroll-chaining"?: Property$MsScrollChaining | Array<Property$MsScrollChaining>,
  "-ms-scroll-limit-x-max"?: Property$MsScrollLimitXMax<TLength> | Array<Property$MsScrollLimitXMax<TLength>>,
  "-ms-scroll-limit-x-min"?: Property$MsScrollLimitXMin<TLength> | Array<Property$MsScrollLimitXMin<TLength>>,
  "-ms-scroll-limit-y-max"?: Property$MsScrollLimitYMax<TLength> | Array<Property$MsScrollLimitYMax<TLength>>,
  "-ms-scroll-limit-y-min"?: Property$MsScrollLimitYMin<TLength> | Array<Property$MsScrollLimitYMin<TLength>>,
  "-ms-scroll-rails"?: Property$MsScrollRails | Array<Property$MsScrollRails>,
  "-ms-scroll-snap-points-x"?: Property$MsScrollSnapPointsX | Array<Property$MsScrollSnapPointsX>,
  "-ms-scroll-snap-points-y"?: Property$MsScrollSnapPointsY | Array<Property$MsScrollSnapPointsY>,
  "-ms-scroll-snap-type"?: Property$MsScrollSnapType | Array<Property$MsScrollSnapType>,
  "-ms-scroll-translation"?: Property$MsScrollTranslation | Array<Property$MsScrollTranslation>,
  "-ms-scrollbar-3dlight-color"?: Property$MsScrollbar3dlightColor | Array<Property$MsScrollbar3dlightColor>,
  "-ms-scrollbar-arrow-color"?: Property$MsScrollbarArrowColor | Array<Property$MsScrollbarArrowColor>,
  "-ms-scrollbar-base-color"?: Property$MsScrollbarBaseColor | Array<Property$MsScrollbarBaseColor>,
  "-ms-scrollbar-darkshadow-color"?: Property$MsScrollbarDarkshadowColor | Array<Property$MsScrollbarDarkshadowColor>,
  "-ms-scrollbar-face-color"?: Property$MsScrollbarFaceColor | Array<Property$MsScrollbarFaceColor>,
  "-ms-scrollbar-highlight-color"?: Property$MsScrollbarHighlightColor | Array<Property$MsScrollbarHighlightColor>,
  "-ms-scrollbar-shadow-color"?: Property$MsScrollbarShadowColor | Array<Property$MsScrollbarShadowColor>,
  "-ms-scrollbar-track-color"?: Property$MsScrollbarTrackColor | Array<Property$MsScrollbarTrackColor>,
  "-ms-text-autospace"?: Property$MsTextAutospace | Array<Property$MsTextAutospace>,
  "-ms-text-combine-horizontal"?: Property$TextCombineUpright | Array<Property$TextCombineUpright>,
  "-ms-text-overflow"?: Property$TextOverflow | Array<Property$TextOverflow>,
  "-ms-touch-action"?: Property$TouchAction | Array<Property$TouchAction>,
  "-ms-touch-select"?: Property$MsTouchSelect | Array<Property$MsTouchSelect>,
  "-ms-transform"?: Property$Transform | Array<Property$Transform>,
  "-ms-transform-origin"?: Property$TransformOrigin<TLength> | Array<Property$TransformOrigin<TLength>>,
  "-ms-transition-delay"?: Property$TransitionDelay<TTime> | Array<Property$TransitionDelay<TTime>>,
  "-ms-transition-duration"?: Property$TransitionDuration<TTime> | Array<Property$TransitionDuration<TTime>>,
  "-ms-transition-property"?: Property$TransitionProperty | Array<Property$TransitionProperty>,
  "-ms-transition-timing-function"?: Property$TransitionTimingFunction | Array<Property$TransitionTimingFunction>,
  "-ms-user-select"?: Property$MsUserSelect | Array<Property$MsUserSelect>,
  "-ms-word-break"?: Property$WordBreak | Array<Property$WordBreak>,
  "-ms-wrap-flow"?: Property$MsWrapFlow | Array<Property$MsWrapFlow>,
  "-ms-wrap-margin"?: Property$MsWrapMargin<TLength> | Array<Property$MsWrapMargin<TLength>>,
  "-ms-wrap-through"?: Property$MsWrapThrough | Array<Property$MsWrapThrough>,
  "-ms-writing-mode"?: Property$WritingMode | Array<Property$WritingMode>,
  "-webkit-align-content"?: Property$AlignContent | Array<Property$AlignContent>,
  "-webkit-align-items"?: Property$AlignItems | Array<Property$AlignItems>,
  "-webkit-align-self"?: Property$AlignSelf | Array<Property$AlignSelf>,
  "-webkit-animation-delay"?: Property$AnimationDelay<TTime> | Array<Property$AnimationDelay<TTime>>,
  "-webkit-animation-direction"?: Property$AnimationDirection | Array<Property$AnimationDirection>,
  "-webkit-animation-duration"?: Property$AnimationDuration<TTime> | Array<Property$AnimationDuration<TTime>>,
  "-webkit-animation-fill-mode"?: Property$AnimationFillMode | Array<Property$AnimationFillMode>,
  "-webkit-animation-iteration-count"?: Property$AnimationIterationCount | Array<Property$AnimationIterationCount>,
  "-webkit-animation-name"?: Property$AnimationName | Array<Property$AnimationName>,
  "-webkit-animation-play-state"?: Property$AnimationPlayState | Array<Property$AnimationPlayState>,
  "-webkit-animation-timing-function"?: Property$AnimationTimingFunction | Array<Property$AnimationTimingFunction>,
  "-webkit-appearance"?: Property$WebkitAppearance | Array<Property$WebkitAppearance>,
  "-webkit-backdrop-filter"?: Property$BackdropFilter | Array<Property$BackdropFilter>,
  "-webkit-backface-visibility"?: Property$BackfaceVisibility | Array<Property$BackfaceVisibility>,
  "-webkit-background-clip"?: Property$BackgroundClip | Array<Property$BackgroundClip>,
  "-webkit-background-origin"?: Property$BackgroundOrigin | Array<Property$BackgroundOrigin>,
  "-webkit-background-size"?: Property$BackgroundSize<TLength> | Array<Property$BackgroundSize<TLength>>,
  "-webkit-border-before-color"?: Property$WebkitBorderBeforeColor | Array<Property$WebkitBorderBeforeColor>,
  "-webkit-border-before-style"?: Property$WebkitBorderBeforeStyle | Array<Property$WebkitBorderBeforeStyle>,
  "-webkit-border-before-width"?: Property$WebkitBorderBeforeWidth<TLength> | Array<Property$WebkitBorderBeforeWidth<TLength>>,
  "-webkit-border-bottom-left-radius"?: Property$BorderBottomLeftRadius<TLength> | Array<Property$BorderBottomLeftRadius<TLength>>,
  "-webkit-border-bottom-right-radius"?: Property$BorderBottomRightRadius<TLength> | Array<Property$BorderBottomRightRadius<TLength>>,
  "-webkit-border-image-slice"?: Property$BorderImageSlice | Array<Property$BorderImageSlice>,
  "-webkit-border-top-left-radius"?: Property$BorderTopLeftRadius<TLength> | Array<Property$BorderTopLeftRadius<TLength>>,
  "-webkit-border-top-right-radius"?: Property$BorderTopRightRadius<TLength> | Array<Property$BorderTopRightRadius<TLength>>,
  "-webkit-box-decoration-break"?: Property$BoxDecorationBreak | Array<Property$BoxDecorationBreak>,
  "-webkit-box-reflect"?: Property$WebkitBoxReflect<TLength> | Array<Property$WebkitBoxReflect<TLength>>,
  "-webkit-box-shadow"?: Property$BoxShadow | Array<Property$BoxShadow>,
  "-webkit-box-sizing"?: Property$BoxSizing | Array<Property$BoxSizing>,
  "-webkit-clip-path"?: Property$ClipPath | Array<Property$ClipPath>,
  "-webkit-column-count"?: Property$ColumnCount | Array<Property$ColumnCount>,
  "-webkit-column-fill"?: Property$ColumnFill | Array<Property$ColumnFill>,
  "-webkit-column-rule-color"?: Property$ColumnRuleColor | Array<Property$ColumnRuleColor>,
  "-webkit-column-rule-style"?: Property$ColumnRuleStyle | Array<Property$ColumnRuleStyle>,
  "-webkit-column-rule-width"?: Property$ColumnRuleWidth<TLength> | Array<Property$ColumnRuleWidth<TLength>>,
  "-webkit-column-span"?: Property$ColumnSpan | Array<Property$ColumnSpan>,
  "-webkit-column-width"?: Property$ColumnWidth<TLength> | Array<Property$ColumnWidth<TLength>>,
  "-webkit-filter"?: Property$Filter | Array<Property$Filter>,
  "-webkit-flex-basis"?: Property$FlexBasis<TLength> | Array<Property$FlexBasis<TLength>>,
  "-webkit-flex-direction"?: Property$FlexDirection | Array<Property$FlexDirection>,
  "-webkit-flex-grow"?: Property$FlexGrow | Array<Property$FlexGrow>,
  "-webkit-flex-shrink"?: Property$FlexShrink | Array<Property$FlexShrink>,
  "-webkit-flex-wrap"?: Property$FlexWrap | Array<Property$FlexWrap>,
  "-webkit-font-feature-settings"?: Property$FontFeatureSettings | Array<Property$FontFeatureSettings>,
  "-webkit-font-kerning"?: Property$FontKerning | Array<Property$FontKerning>,
  "-webkit-font-smoothing"?: Property$FontSmooth<TLength> | Array<Property$FontSmooth<TLength>>,
  "-webkit-font-variant-ligatures"?: Property$FontVariantLigatures | Array<Property$FontVariantLigatures>,
  "-webkit-hyphenate-character"?: Property$HyphenateCharacter | Array<Property$HyphenateCharacter>,
  "-webkit-hyphens"?: Property$Hyphens | Array<Property$Hyphens>,
  "-webkit-initial-letter"?: Property$InitialLetter | Array<Property$InitialLetter>,
  "-webkit-justify-content"?: Property$JustifyContent | Array<Property$JustifyContent>,
  "-webkit-line-break"?: Property$LineBreak | Array<Property$LineBreak>,
  "-webkit-line-clamp"?: Property$WebkitLineClamp | Array<Property$WebkitLineClamp>,
  "-webkit-margin-end"?: Property$MarginInlineEnd<TLength> | Array<Property$MarginInlineEnd<TLength>>,
  "-webkit-margin-start"?: Property$MarginInlineStart<TLength> | Array<Property$MarginInlineStart<TLength>>,
  "-webkit-mask-attachment"?: Property$WebkitMaskAttachment | Array<Property$WebkitMaskAttachment>,
  "-webkit-mask-box-image-outset"?: Property$MaskBorderOutset<TLength> | Array<Property$MaskBorderOutset<TLength>>,
  "-webkit-mask-box-image-repeat"?: Property$MaskBorderRepeat | Array<Property$MaskBorderRepeat>,
  "-webkit-mask-box-image-slice"?: Property$MaskBorderSlice | Array<Property$MaskBorderSlice>,
  "-webkit-mask-box-image-source"?: Property$MaskBorderSource | Array<Property$MaskBorderSource>,
  "-webkit-mask-box-image-width"?: Property$MaskBorderWidth<TLength> | Array<Property$MaskBorderWidth<TLength>>,
  "-webkit-mask-clip"?: Property$WebkitMaskClip | Array<Property$WebkitMaskClip>,
  "-webkit-mask-composite"?: Property$WebkitMaskComposite | Array<Property$WebkitMaskComposite>,
  "-webkit-mask-image"?: Property$WebkitMaskImage | Array<Property$WebkitMaskImage>,
  "-webkit-mask-origin"?: Property$WebkitMaskOrigin | Array<Property$WebkitMaskOrigin>,
  "-webkit-mask-position"?: Property$WebkitMaskPosition<TLength> | Array<Property$WebkitMaskPosition<TLength>>,
  "-webkit-mask-position-x"?: Property$WebkitMaskPositionX<TLength> | Array<Property$WebkitMaskPositionX<TLength>>,
  "-webkit-mask-position-y"?: Property$WebkitMaskPositionY<TLength> | Array<Property$WebkitMaskPositionY<TLength>>,
  "-webkit-mask-repeat"?: Property$WebkitMaskRepeat | Array<Property$WebkitMaskRepeat>,
  "-webkit-mask-repeat-x"?: Property$WebkitMaskRepeatX | Array<Property$WebkitMaskRepeatX>,
  "-webkit-mask-repeat-y"?: Property$WebkitMaskRepeatY | Array<Property$WebkitMaskRepeatY>,
  "-webkit-mask-size"?: Property$WebkitMaskSize<TLength> | Array<Property$WebkitMaskSize<TLength>>,
  "-webkit-max-inline-size"?: Property$MaxInlineSize<TLength> | Array<Property$MaxInlineSize<TLength>>,
  "-webkit-order"?: Property$Order | Array<Property$Order>,
  "-webkit-overflow-scrolling"?: Property$WebkitOverflowScrolling | Array<Property$WebkitOverflowScrolling>,
  "-webkit-padding-end"?: Property$PaddingInlineEnd<TLength> | Array<Property$PaddingInlineEnd<TLength>>,
  "-webkit-padding-start"?: Property$PaddingInlineStart<TLength> | Array<Property$PaddingInlineStart<TLength>>,
  "-webkit-perspective"?: Property$Perspective<TLength> | Array<Property$Perspective<TLength>>,
  "-webkit-perspective-origin"?: Property$PerspectiveOrigin<TLength> | Array<Property$PerspectiveOrigin<TLength>>,
  "-webkit-print-color-adjust"?: Property$PrintColorAdjust | Array<Property$PrintColorAdjust>,
  "-webkit-ruby-position"?: Property$RubyPosition | Array<Property$RubyPosition>,
  "-webkit-scroll-snap-type"?: Property$ScrollSnapType | Array<Property$ScrollSnapType>,
  "-webkit-shape-margin"?: Property$ShapeMargin<TLength> | Array<Property$ShapeMargin<TLength>>,
  "-webkit-tap-highlight-color"?: Property$WebkitTapHighlightColor | Array<Property$WebkitTapHighlightColor>,
  "-webkit-text-combine"?: Property$TextCombineUpright | Array<Property$TextCombineUpright>,
  "-webkit-text-decoration-color"?: Property$TextDecorationColor | Array<Property$TextDecorationColor>,
  "-webkit-text-decoration-line"?: Property$TextDecorationLine | Array<Property$TextDecorationLine>,
  "-webkit-text-decoration-skip"?: Property$TextDecorationSkip | Array<Property$TextDecorationSkip>,
  "-webkit-text-decoration-style"?: Property$TextDecorationStyle | Array<Property$TextDecorationStyle>,
  "-webkit-text-emphasis-color"?: Property$TextEmphasisColor | Array<Property$TextEmphasisColor>,
  "-webkit-text-emphasis-position"?: Property$TextEmphasisPosition | Array<Property$TextEmphasisPosition>,
  "-webkit-text-emphasis-style"?: Property$TextEmphasisStyle | Array<Property$TextEmphasisStyle>,
  "-webkit-text-fill-color"?: Property$WebkitTextFillColor | Array<Property$WebkitTextFillColor>,
  "-webkit-text-orientation"?: Property$TextOrientation | Array<Property$TextOrientation>,
  "-webkit-text-size-adjust"?: Property$TextSizeAdjust | Array<Property$TextSizeAdjust>,
  "-webkit-text-stroke-color"?: Property$WebkitTextStrokeColor | Array<Property$WebkitTextStrokeColor>,
  "-webkit-text-stroke-width"?: Property$WebkitTextStrokeWidth<TLength> | Array<Property$WebkitTextStrokeWidth<TLength>>,
  "-webkit-text-underline-position"?: Property$TextUnderlinePosition | Array<Property$TextUnderlinePosition>,
  "-webkit-touch-callout"?: Property$WebkitTouchCallout | Array<Property$WebkitTouchCallout>,
  "-webkit-transform"?: Property$Transform | Array<Property$Transform>,
  "-webkit-transform-origin"?: Property$TransformOrigin<TLength> | Array<Property$TransformOrigin<TLength>>,
  "-webkit-transform-style"?: Property$TransformStyle | Array<Property$TransformStyle>,
  "-webkit-transition-delay"?: Property$TransitionDelay<TTime> | Array<Property$TransitionDelay<TTime>>,
  "-webkit-transition-duration"?: Property$TransitionDuration<TTime> | Array<Property$TransitionDuration<TTime>>,
  "-webkit-transition-property"?: Property$TransitionProperty | Array<Property$TransitionProperty>,
  "-webkit-transition-timing-function"?: Property$TransitionTimingFunction | Array<Property$TransitionTimingFunction>,
  "-webkit-user-modify"?: Property$WebkitUserModify | Array<Property$WebkitUserModify>,
  "-webkit-user-select"?: Property$UserSelect | Array<Property$UserSelect>,
  "-webkit-writing-mode"?: Property$WritingMode | Array<Property$WritingMode>,
|};

export type VendorShorthandPropertiesHyphenFallback<TLength = string | 0, TTime = string> = {|
  "-moz-animation"?: Property$Animation<TTime> | Array<Property$Animation<TTime>>,
  "-moz-border-image"?: Property$BorderImage | Array<Property$BorderImage>,
  "-moz-column-rule"?: Property$ColumnRule<TLength> | Array<Property$ColumnRule<TLength>>,
  "-moz-columns"?: Property$Columns<TLength> | Array<Property$Columns<TLength>>,
  "-moz-outline-radius"?: Property$MozOutlineRadius<TLength> | Array<Property$MozOutlineRadius<TLength>>,
  "-ms-content-zoom-limit"?: Property$MsContentZoomLimit | Array<Property$MsContentZoomLimit>,
  "-ms-content-zoom-snap"?: Property$MsContentZoomSnap | Array<Property$MsContentZoomSnap>,
  "-ms-flex"?: Property$Flex<TLength> | Array<Property$Flex<TLength>>,
  "-ms-scroll-limit"?: Property$MsScrollLimit | Array<Property$MsScrollLimit>,
  "-ms-scroll-snap-x"?: Property$MsScrollSnapX | Array<Property$MsScrollSnapX>,
  "-ms-scroll-snap-y"?: Property$MsScrollSnapY | Array<Property$MsScrollSnapY>,
  "-ms-transition"?: Property$Transition<TTime> | Array<Property$Transition<TTime>>,
  "-webkit-animation"?: Property$Animation<TTime> | Array<Property$Animation<TTime>>,
  "-webkit-border-before"?: Property$WebkitBorderBefore<TLength> | Array<Property$WebkitBorderBefore<TLength>>,
  "-webkit-border-image"?: Property$BorderImage | Array<Property$BorderImage>,
  "-webkit-border-radius"?: Property$BorderRadius<TLength> | Array<Property$BorderRadius<TLength>>,
  "-webkit-column-rule"?: Property$ColumnRule<TLength> | Array<Property$ColumnRule<TLength>>,
  "-webkit-columns"?: Property$Columns<TLength> | Array<Property$Columns<TLength>>,
  "-webkit-flex"?: Property$Flex<TLength> | Array<Property$Flex<TLength>>,
  "-webkit-flex-flow"?: Property$FlexFlow | Array<Property$FlexFlow>,
  "-webkit-mask"?: Property$WebkitMask<TLength> | Array<Property$WebkitMask<TLength>>,
  "-webkit-mask-box-image"?: Property$MaskBorder | Array<Property$MaskBorder>,
  "-webkit-text-emphasis"?: Property$TextEmphasis | Array<Property$TextEmphasis>,
  "-webkit-text-stroke"?: Property$WebkitTextStroke<TLength> | Array<Property$WebkitTextStroke<TLength>>,
  "-webkit-transition"?: Property$Transition<TTime> | Array<Property$Transition<TTime>>,
|};

export type VendorPropertiesHyphenFallback<TLength = string | 0, TTime = string> = {|
  ...VendorLonghandPropertiesHyphenFallback<TLength, TTime>,
  ...VendorShorthandPropertiesHyphenFallback<TLength, TTime>,
|};

export type ObsoletePropertiesHyphenFallback<TLength = string | 0, TTime = string> = {|
  azimuth?: Property$Azimuth | Array<Property$Azimuth>,
  "box-align"?: Property$BoxAlign | Array<Property$BoxAlign>,
  "box-direction"?: Property$BoxDirection | Array<Property$BoxDirection>,
  "box-flex"?: Property$BoxFlex | Array<Property$BoxFlex>,
  "box-flex-group"?: Property$BoxFlexGroup | Array<Property$BoxFlexGroup>,
  "box-lines"?: Property$BoxLines | Array<Property$BoxLines>,
  "box-ordinal-group"?: Property$BoxOrdinalGroup | Array<Property$BoxOrdinalGroup>,
  "box-orient"?: Property$BoxOrient | Array<Property$BoxOrient>,
  "box-pack"?: Property$BoxPack | Array<Property$BoxPack>,
  clip?: Property$Clip | Array<Property$Clip>,
  "grid-column-gap"?: Property$GridColumnGap<TLength> | Array<Property$GridColumnGap<TLength>>,
  "grid-gap"?: Property$GridGap<TLength> | Array<Property$GridGap<TLength>>,
  "grid-row-gap"?: Property$GridRowGap<TLength> | Array<Property$GridRowGap<TLength>>,
  "ime-mode"?: Property$ImeMode | Array<Property$ImeMode>,
  "offset-block"?: Property$InsetBlock<TLength> | Array<Property$InsetBlock<TLength>>,
  "offset-block-end"?: Property$InsetBlockEnd<TLength> | Array<Property$InsetBlockEnd<TLength>>,
  "offset-block-start"?: Property$InsetBlockStart<TLength> | Array<Property$InsetBlockStart<TLength>>,
  "offset-inline"?: Property$InsetInline<TLength> | Array<Property$InsetInline<TLength>>,
  "offset-inline-end"?: Property$InsetInlineEnd<TLength> | Array<Property$InsetInlineEnd<TLength>>,
  "offset-inline-start"?: Property$InsetInlineStart<TLength> | Array<Property$InsetInlineStart<TLength>>,
  "scroll-snap-coordinate"?: Property$ScrollSnapCoordinate<TLength> | Array<Property$ScrollSnapCoordinate<TLength>>,
  "scroll-snap-destination"?: Property$ScrollSnapDestination<TLength> | Array<Property$ScrollSnapDestination<TLength>>,
  "scroll-snap-points-x"?: Property$ScrollSnapPointsX | Array<Property$ScrollSnapPointsX>,
  "scroll-snap-points-y"?: Property$ScrollSnapPointsY | Array<Property$ScrollSnapPointsY>,
  "scroll-snap-type-x"?: Property$ScrollSnapTypeX | Array<Property$ScrollSnapTypeX>,
  "scroll-snap-type-y"?: Property$ScrollSnapTypeY | Array<Property$ScrollSnapTypeY>,
  "-khtml-box-align"?: Property$BoxAlign | Array<Property$BoxAlign>,
  "-khtml-box-direction"?: Property$BoxDirection | Array<Property$BoxDirection>,
  "-khtml-box-flex"?: Property$BoxFlex | Array<Property$BoxFlex>,
  "-khtml-box-flex-group"?: Property$BoxFlexGroup | Array<Property$BoxFlexGroup>,
  "-khtml-box-lines"?: Property$BoxLines | Array<Property$BoxLines>,
  "-khtml-box-ordinal-group"?: Property$BoxOrdinalGroup | Array<Property$BoxOrdinalGroup>,
  "-khtml-box-orient"?: Property$BoxOrient | Array<Property$BoxOrient>,
  "-khtml-box-pack"?: Property$BoxPack | Array<Property$BoxPack>,
  "-khtml-line-break"?: Property$LineBreak | Array<Property$LineBreak>,
  "-khtml-opacity"?: Property$Opacity | Array<Property$Opacity>,
  "-khtml-user-select"?: Property$UserSelect | Array<Property$UserSelect>,
  "-moz-backface-visibility"?: Property$BackfaceVisibility | Array<Property$BackfaceVisibility>,
  "-moz-background-clip"?: Property$BackgroundClip | Array<Property$BackgroundClip>,
  "-moz-background-inline-policy"?: Property$BoxDecorationBreak | Array<Property$BoxDecorationBreak>,
  "-moz-background-origin"?: Property$BackgroundOrigin | Array<Property$BackgroundOrigin>,
  "-moz-background-size"?: Property$BackgroundSize<TLength> | Array<Property$BackgroundSize<TLength>>,
  "-moz-border-radius"?: Property$BorderRadius<TLength> | Array<Property$BorderRadius<TLength>>,
  "-moz-border-radius-bottomleft"?: Property$BorderBottomLeftRadius<TLength> | Array<Property$BorderBottomLeftRadius<TLength>>,
  "-moz-border-radius-bottomright"?: Property$BorderBottomRightRadius<TLength> | Array<Property$BorderBottomRightRadius<TLength>>,
  "-moz-border-radius-topleft"?: Property$BorderTopLeftRadius<TLength> | Array<Property$BorderTopLeftRadius<TLength>>,
  "-moz-border-radius-topright"?: Property$BorderTopRightRadius<TLength> | Array<Property$BorderTopRightRadius<TLength>>,
  "-moz-box-align"?: Property$BoxAlign | Array<Property$BoxAlign>,
  "-moz-box-direction"?: Property$BoxDirection | Array<Property$BoxDirection>,
  "-moz-box-flex"?: Property$BoxFlex | Array<Property$BoxFlex>,
  "-moz-box-ordinal-group"?: Property$BoxOrdinalGroup | Array<Property$BoxOrdinalGroup>,
  "-moz-box-orient"?: Property$BoxOrient | Array<Property$BoxOrient>,
  "-moz-box-pack"?: Property$BoxPack | Array<Property$BoxPack>,
  "-moz-box-shadow"?: Property$BoxShadow | Array<Property$BoxShadow>,
  "-moz-float-edge"?: Property$MozFloatEdge | Array<Property$MozFloatEdge>,
  "-moz-force-broken-image-icon"?: Property$MozForceBrokenImageIcon | Array<Property$MozForceBrokenImageIcon>,
  "-moz-opacity"?: Property$Opacity | Array<Property$Opacity>,
  "-moz-outline"?: Property$Outline<TLength> | Array<Property$Outline<TLength>>,
  "-moz-outline-color"?: Property$OutlineColor | Array<Property$OutlineColor>,
  "-moz-outline-style"?: Property$OutlineStyle | Array<Property$OutlineStyle>,
  "-moz-outline-width"?: Property$OutlineWidth<TLength> | Array<Property$OutlineWidth<TLength>>,
  "-moz-perspective"?: Property$Perspective<TLength> | Array<Property$Perspective<TLength>>,
  "-moz-perspective-origin"?: Property$PerspectiveOrigin<TLength> | Array<Property$PerspectiveOrigin<TLength>>,
  "-moz-text-align-last"?: Property$TextAlignLast | Array<Property$TextAlignLast>,
  "-moz-text-decoration-color"?: Property$TextDecorationColor | Array<Property$TextDecorationColor>,
  "-moz-text-decoration-line"?: Property$TextDecorationLine | Array<Property$TextDecorationLine>,
  "-moz-text-decoration-style"?: Property$TextDecorationStyle | Array<Property$TextDecorationStyle>,
  "-moz-transform"?: Property$Transform | Array<Property$Transform>,
  "-moz-transform-origin"?: Property$TransformOrigin<TLength> | Array<Property$TransformOrigin<TLength>>,
  "-moz-transform-style"?: Property$TransformStyle | Array<Property$TransformStyle>,
  "-moz-transition"?: Property$Transition<TTime> | Array<Property$Transition<TTime>>,
  "-moz-transition-delay"?: Property$TransitionDelay<TTime> | Array<Property$TransitionDelay<TTime>>,
  "-moz-transition-duration"?: Property$TransitionDuration<TTime> | Array<Property$TransitionDuration<TTime>>,
  "-moz-transition-property"?: Property$TransitionProperty | Array<Property$TransitionProperty>,
  "-moz-transition-timing-function"?: Property$TransitionTimingFunction | Array<Property$TransitionTimingFunction>,
  "-moz-user-input"?: Property$MozUserInput | Array<Property$MozUserInput>,
  "-ms-ime-mode"?: Property$ImeMode | Array<Property$ImeMode>,
  "-o-animation"?: Property$Animation<TTime> | Array<Property$Animation<TTime>>,
  "-o-animation-delay"?: Property$AnimationDelay<TTime> | Array<Property$AnimationDelay<TTime>>,
  "-o-animation-direction"?: Property$AnimationDirection | Array<Property$AnimationDirection>,
  "-o-animation-duration"?: Property$AnimationDuration<TTime> | Array<Property$AnimationDuration<TTime>>,
  "-o-animation-fill-mode"?: Property$AnimationFillMode | Array<Property$AnimationFillMode>,
  "-o-animation-iteration-count"?: Property$AnimationIterationCount | Array<Property$AnimationIterationCount>,
  "-o-animation-name"?: Property$AnimationName | Array<Property$AnimationName>,
  "-o-animation-play-state"?: Property$AnimationPlayState | Array<Property$AnimationPlayState>,
  "-o-animation-timing-function"?: Property$AnimationTimingFunction | Array<Property$AnimationTimingFunction>,
  "-o-background-size"?: Property$BackgroundSize<TLength> | Array<Property$BackgroundSize<TLength>>,
  "-o-border-image"?: Property$BorderImage | Array<Property$BorderImage>,
  "-o-object-fit"?: Property$ObjectFit | Array<Property$ObjectFit>,
  "-o-object-position"?: Property$ObjectPosition<TLength> | Array<Property$ObjectPosition<TLength>>,
  "-o-tab-size"?: Property$TabSize<TLength> | Array<Property$TabSize<TLength>>,
  "-o-text-overflow"?: Property$TextOverflow | Array<Property$TextOverflow>,
  "-o-transform"?: Property$Transform | Array<Property$Transform>,
  "-o-transform-origin"?: Property$TransformOrigin<TLength> | Array<Property$TransformOrigin<TLength>>,
  "-o-transition"?: Property$Transition<TTime> | Array<Property$Transition<TTime>>,
  "-o-transition-delay"?: Property$TransitionDelay<TTime> | Array<Property$TransitionDelay<TTime>>,
  "-o-transition-duration"?: Property$TransitionDuration<TTime> | Array<Property$TransitionDuration<TTime>>,
  "-o-transition-property"?: Property$TransitionProperty | Array<Property$TransitionProperty>,
  "-o-transition-timing-function"?: Property$TransitionTimingFunction | Array<Property$TransitionTimingFunction>,
  "-webkit-box-align"?: Property$BoxAlign | Array<Property$BoxAlign>,
  "-webkit-box-direction"?: Property$BoxDirection | Array<Property$BoxDirection>,
  "-webkit-box-flex"?: Property$BoxFlex | Array<Property$BoxFlex>,
  "-webkit-box-flex-group"?: Property$BoxFlexGroup | Array<Property$BoxFlexGroup>,
  "-webkit-box-lines"?: Property$BoxLines | Array<Property$BoxLines>,
  "-webkit-box-ordinal-group"?: Property$BoxOrdinalGroup | Array<Property$BoxOrdinalGroup>,
  "-webkit-box-orient"?: Property$BoxOrient | Array<Property$BoxOrient>,
  "-webkit-box-pack"?: Property$BoxPack | Array<Property$BoxPack>,
|};

export type SvgPropertiesHyphenFallback<TLength = string | 0, TTime = string> = {|
  "alignment-baseline"?: Property$AlignmentBaseline | Array<Property$AlignmentBaseline>,
  "baseline-shift"?: Property$BaselineShift<TLength> | Array<Property$BaselineShift<TLength>>,
  clip?: Property$Clip | Array<Property$Clip>,
  "clip-path"?: Property$ClipPath | Array<Property$ClipPath>,
  "clip-rule"?: Property$ClipRule | Array<Property$ClipRule>,
  color?: Property$Color | Array<Property$Color>,
  "color-interpolation"?: Property$ColorInterpolation | Array<Property$ColorInterpolation>,
  "color-rendering"?: Property$ColorRendering | Array<Property$ColorRendering>,
  cursor?: Property$Cursor | Array<Property$Cursor>,
  direction?: Property$Direction | Array<Property$Direction>,
  display?: Property$Display | Array<Property$Display>,
  "dominant-baseline"?: Property$DominantBaseline | Array<Property$DominantBaseline>,
  fill?: Property$Fill | Array<Property$Fill>,
  "fill-opacity"?: Property$FillOpacity | Array<Property$FillOpacity>,
  "fill-rule"?: Property$FillRule | Array<Property$FillRule>,
  filter?: Property$Filter | Array<Property$Filter>,
  "flood-color"?: Property$FloodColor | Array<Property$FloodColor>,
  "flood-opacity"?: Property$FloodOpacity | Array<Property$FloodOpacity>,
  font?: Property$Font | Array<Property$Font>,
  "font-family"?: Property$FontFamily | Array<Property$FontFamily>,
  "font-size"?: Property$FontSize<TLength> | Array<Property$FontSize<TLength>>,
  "font-size-adjust"?: Property$FontSizeAdjust | Array<Property$FontSizeAdjust>,
  "font-stretch"?: Property$FontStretch | Array<Property$FontStretch>,
  "font-style"?: Property$FontStyle | Array<Property$FontStyle>,
  "font-variant"?: Property$FontVariant | Array<Property$FontVariant>,
  "font-weight"?: Property$FontWeight | Array<Property$FontWeight>,
  "glyph-orientation-vertical"?: Property$GlyphOrientationVertical | Array<Property$GlyphOrientationVertical>,
  "image-rendering"?: Property$ImageRendering | Array<Property$ImageRendering>,
  "letter-spacing"?: Property$LetterSpacing<TLength> | Array<Property$LetterSpacing<TLength>>,
  "lighting-color"?: Property$LightingColor | Array<Property$LightingColor>,
  "line-height"?: Property$LineHeight<TLength> | Array<Property$LineHeight<TLength>>,
  marker?: Property$Marker | Array<Property$Marker>,
  "marker-end"?: Property$MarkerEnd | Array<Property$MarkerEnd>,
  "marker-mid"?: Property$MarkerMid | Array<Property$MarkerMid>,
  "marker-start"?: Property$MarkerStart | Array<Property$MarkerStart>,
  mask?: Property$Mask<TLength> | Array<Property$Mask<TLength>>,
  opacity?: Property$Opacity | Array<Property$Opacity>,
  overflow?: Property$Overflow | Array<Property$Overflow>,
  "paint-order"?: Property$PaintOrder | Array<Property$PaintOrder>,
  "pointer-events"?: Property$PointerEvents | Array<Property$PointerEvents>,
  "shape-rendering"?: Property$ShapeRendering | Array<Property$ShapeRendering>,
  "stop-color"?: Property$StopColor | Array<Property$StopColor>,
  "stop-opacity"?: Property$StopOpacity | Array<Property$StopOpacity>,
  stroke?: Property$Stroke | Array<Property$Stroke>,
  "stroke-dasharray"?: Property$StrokeDasharray<TLength> | Array<Property$StrokeDasharray<TLength>>,
  "stroke-dashoffset"?: Property$StrokeDashoffset<TLength> | Array<Property$StrokeDashoffset<TLength>>,
  "stroke-linecap"?: Property$StrokeLinecap | Array<Property$StrokeLinecap>,
  "stroke-linejoin"?: Property$StrokeLinejoin | Array<Property$StrokeLinejoin>,
  "stroke-miterlimit"?: Property$StrokeMiterlimit | Array<Property$StrokeMiterlimit>,
  "stroke-opacity"?: Property$StrokeOpacity | Array<Property$StrokeOpacity>,
  "stroke-width"?: Property$StrokeWidth<TLength> | Array<Property$StrokeWidth<TLength>>,
  "text-anchor"?: Property$TextAnchor | Array<Property$TextAnchor>,
  "text-decoration"?: Property$TextDecoration<TLength> | Array<Property$TextDecoration<TLength>>,
  "text-rendering"?: Property$TextRendering | Array<Property$TextRendering>,
  "unicode-bidi"?: Property$UnicodeBidi | Array<Property$UnicodeBidi>,
  "vector-effect"?: Property$VectorEffect | Array<Property$VectorEffect>,
  visibility?: Property$Visibility | Array<Property$Visibility>,
  "white-space"?: Property$WhiteSpace | Array<Property$WhiteSpace>,
  "word-spacing"?: Property$WordSpacing<TLength> | Array<Property$WordSpacing<TLength>>,
  "writing-mode"?: Property$WritingMode | Array<Property$WritingMode>,
|};

export type PropertiesHyphenFallback<TLength = string | 0, TTime = string> = {|
  ...StandardPropertiesHyphenFallback<TLength, TTime>,
  ...VendorPropertiesHyphenFallback<TLength, TTime>,
  ...ObsoletePropertiesHyphenFallback<TLength, TTime>,
  ...SvgPropertiesHyphenFallback<TLength, TTime>,
|};

export type AtRules =
  | "@charset"
  | "@counter-style"
  | "@document"
  | "@font-face"
  | "@font-feature-values"
  | "@font-palette-values"
  | "@import"
  | "@keyframes"
  | "@layer"
  | "@media"
  | "@namespace"
  | "@page"
  | "@property"
  | "@scope"
  | "@scroll-timeline"
  | "@starting-style"
  | "@supports"
  | "@viewport";

export type AdvancedPseudos =
  | ":-moz-any()"
  | ":-moz-dir"
  | ":-webkit-any()"
  | "::cue"
  | "::cue-region"
  | "::part"
  | "::slotted"
  | "::view-transition-group"
  | "::view-transition-image-pair"
  | "::view-transition-new"
  | "::view-transition-old"
  | ":dir"
  | ":has"
  | ":host"
  | ":host-context"
  | ":is"
  | ":lang"
  | ":matches()"
  | ":not"
  | ":nth-child"
  | ":nth-last-child"
  | ":nth-last-of-type"
  | ":nth-of-type"
  | ":where";

export type SimplePseudos =
  | ":-khtml-any-link"
  | ":-moz-any-link"
  | ":-moz-focusring"
  | ":-moz-full-screen"
  | ":-moz-placeholder"
  | ":-moz-read-only"
  | ":-moz-read-write"
  | ":-moz-ui-invalid"
  | ":-moz-ui-valid"
  | ":-ms-fullscreen"
  | ":-ms-input-placeholder"
  | ":-webkit-any-link"
  | ":-webkit-full-screen"
  | "::-moz-placeholder"
  | "::-moz-progress-bar"
  | "::-moz-range-progress"
  | "::-moz-range-thumb"
  | "::-moz-range-track"
  | "::-moz-selection"
  | "::-ms-backdrop"
  | "::-ms-browse"
  | "::-ms-check"
  | "::-ms-clear"
  | "::-ms-expand"
  | "::-ms-fill"
  | "::-ms-fill-lower"
  | "::-ms-fill-upper"
  | "::-ms-input-placeholder"
  | "::-ms-reveal"
  | "::-ms-thumb"
  | "::-ms-ticks-after"
  | "::-ms-ticks-before"
  | "::-ms-tooltip"
  | "::-ms-track"
  | "::-ms-value"
  | "::-webkit-backdrop"
  | "::-webkit-input-placeholder"
  | "::-webkit-progress-bar"
  | "::-webkit-progress-inner-value"
  | "::-webkit-progress-value"
  | "::-webkit-slider-runnable-track"
  | "::-webkit-slider-thumb"
  | "::after"
  | "::backdrop"
  | "::before"
  | "::cue"
  | "::cue-region"
  | "::first-letter"
  | "::first-line"
  | "::grammar-error"
  | "::marker"
  | "::placeholder"
  | "::selection"
  | "::spelling-error"
  | "::target-text"
  | "::view-transition"
  | ":active"
  | ":after"
  | ":any-link"
  | ":before"
  | ":blank"
  | ":checked"
  | ":current"
  | ":default"
  | ":defined"
  | ":disabled"
  | ":empty"
  | ":enabled"
  | ":first"
  | ":first-child"
  | ":first-letter"
  | ":first-line"
  | ":first-of-type"
  | ":focus"
  | ":focus-visible"
  | ":focus-within"
  | ":fullscreen"
  | ":future"
  | ":hover"
  | ":in-range"
  | ":indeterminate"
  | ":invalid"
  | ":last-child"
  | ":last-of-type"
  | ":left"
  | ":link"
  | ":local-link"
  | ":nth-col"
  | ":nth-last-col"
  | ":only-child"
  | ":only-of-type"
  | ":optional"
  | ":out-of-range"
  | ":past"
  | ":paused"
  | ":picture-in-picture"
  | ":placeholder-shown"
  | ":playing"
  | ":read-only"
  | ":read-write"
  | ":required"
  | ":right"
  | ":root"
  | ":scope"
  | ":target"
  | ":target-within"
  | ":user-invalid"
  | ":user-valid"
  | ":valid"
  | ":visited";

export type Pseudos = AdvancedPseudos | SimplePseudos;

export type HtmlAttributes =
  | "[abbr]"
  | "[accept-charset]"
  | "[accept]"
  | "[accesskey]"
  | "[action]"
  | "[align]"
  | "[alink]"
  | "[allow]"
  | "[allowfullscreen]"
  | "[allowpaymentrequest]"
  | "[alt]"
  | "[archive]"
  | "[async]"
  | "[attributionsrc]"
  | "[autobuffer]"
  | "[autocapitalize]"
  | "[autocomplete]"
  | "[autofocus]"
  | "[autoplay]"
  | "[axis]"
  | "[background]"
  | "[behavior]"
  | "[bgcolor]"
  | "[blocking]"
  | "[border]"
  | "[bottommargin]"
  | "[browsingtopics]"
  | "[capture]"
  | "[cellpadding]"
  | "[cellspacing]"
  | "[char]"
  | "[charoff]"
  | "[charset]"
  | "[checked]"
  | "[cite]"
  | "[class]"
  | "[classid]"
  | "[clear]"
  | "[codebase]"
  | "[codetype]"
  | "[color]"
  | "[cols]"
  | "[colspan]"
  | "[compact]"
  | "[content]"
  | "[contenteditable]"
  | "[contextmenu]"
  | "[controls]"
  | "[coords]"
  | "[credentialless]"
  | "[crossorigin]"
  | "[data]"
  | "[datetime]"
  | "[declare]"
  | "[decoding]"
  | "[default]"
  | "[defer]"
  | "[dir]"
  | "[direction]"
  | "[dirname]"
  | "[disabled]"
  | "[download]"
  | "[draggable]"
  | "[enctype]"
  | "[enterkeyhint]"
  | "[exportparts]"
  | "[face]"
  | "[fetchpriority]"
  | "[for]"
  | "[form]"
  | "[formaction]"
  | "[formenctype]"
  | "[formmethod]"
  | "[formnovalidate]"
  | "[formtarget]"
  | "[frame]"
  | "[frameborder]"
  | "[headers]"
  | "[height]"
  | "[hidden]"
  | "[high]"
  | "[href]"
  | "[hreflang]"
  | "[hspace]"
  | "[http-equiv]"
  | "[id]"
  | "[imagesizes]"
  | "[imagesrcset]"
  | "[inert]"
  | "[inputmode]"
  | "[integrity]"
  | "[is]"
  | "[ismap]"
  | "[itemid]"
  | "[itemprop]"
  | "[itemref]"
  | "[itemscope]"
  | "[itemtype]"
  | "[kind]"
  | "[label]"
  | "[lang]"
  | "[language]"
  | "[leftmargin]"
  | "[link]"
  | "[list]"
  | "[loading]"
  | "[longdesc]"
  | "[loop]"
  | "[low]"
  | "[manifest]"
  | "[marginheight]"
  | "[marginwidth]"
  | "[max]"
  | "[maxlength]"
  | "[media]"
  | "[method]"
  | "[methods]"
  | "[min]"
  | "[minlength]"
  | "[moz-opaque]"
  | "[mozactionhint]"
  | "[mozallowfullscreen]"
  | "[msallowfullscreen]"
  | "[multiple]"
  | "[muted]"
  | "[name]"
  | "[nohref]"
  | "[nomodule]"
  | "[nonce]"
  | "[noresize]"
  | "[noshade]"
  | "[novalidate]"
  | "[nowrap]"
  | "[onerror]"
  | "[open]"
  | "[optimum]"
  | "[part]"
  | "[pattern]"
  | "[ping]"
  | "[placeholder]"
  | "[popover]"
  | "[popovertarget]"
  | "[popovertargetaction]"
  | "[poster]"
  | "[preload]"
  | "[profile]"
  | "[readonly]"
  | "[referrerpolicy]"
  | "[rel]"
  | "[required]"
  | "[rev]"
  | "[reversed]"
  | "[rightmargin]"
  | "[rows]"
  | "[rowspan]"
  | "[rules]"
  | "[sandbox]"
  | "[scope]"
  | "[scrollamount]"
  | "[scrolldelay]"
  | "[scrolling]"
  | "[selected]"
  | "[shadowroot]"
  | "[shadowrootmode]"
  | "[shape]"
  | "[size]"
  | "[sizes]"
  | "[slot]"
  | "[span]"
  | "[spellcheck]"
  | "[src]"
  | "[srcdoc]"
  | "[srclang]"
  | "[srcset]"
  | "[standby]"
  | "[start]"
  | "[step]"
  | "[style]"
  | "[summary]"
  | "[tabindex]"
  | "[target]"
  | "[text]"
  | "[title]"
  | "[topmargin]"
  | "[translate]"
  | "[truespeed]"
  | "[type]"
  | "[usemap]"
  | "[valign]"
  | "[value]"
  | "[valuetype]"
  | "[version]"
  | "[virtualkeyboardpolicy]"
  | "[vlink]"
  | "[vspace]"
  | "[webkitallowfullscreen]"
  | "[width]"
  | "[wrap]"
  | "[x-moz-errormessage]"
  | "[xmlns]";

export type SvgAttributes =
  | "[accent-height]"
  | "[alignment-baseline]"
  | "[allowReorder]"
  | "[alphabetic]"
  | "[animation]"
  | "[arabic-form]"
  | "[ascent]"
  | "[async]"
  | "[attributeName]"
  | "[attributeType]"
  | "[azimuth]"
  | "[baseFrequency]"
  | "[baseProfile]"
  | "[baseline-shift]"
  | "[bbox]"
  | "[bias]"
  | "[by]"
  | "[calcMode]"
  | "[cap-height]"
  | "[class]"
  | "[clip-path]"
  | "[clip-rule]"
  | "[clipPathUnits]"
  | "[clip]"
  | "[color-interpolation-filters]"
  | "[color-interpolation]"
  | "[color-profile]"
  | "[color]"
  | "[contentScriptType]"
  | "[contentStyleType]"
  | "[crossorigin]"
  | "[cursor]"
  | "[cx]"
  | "[cy]"
  | "[d]"
  | "[decoding]"
  | "[defer]"
  | "[descent]"
  | "[diffuseConstant]"
  | "[direction]"
  | "[display]"
  | "[divisor]"
  | "[document]"
  | "[dominant-baseline]"
  | "[download]"
  | "[dur]"
  | "[dx]"
  | "[dy]"
  | "[edgeMode]"
  | "[elevation]"
  | "[enable-background]"
  | "[fill-opacity]"
  | "[fill-rule]"
  | "[fill]"
  | "[filterRes]"
  | "[filterUnits]"
  | "[filter]"
  | "[flood-color]"
  | "[flood-opacity]"
  | "[font-family]"
  | "[font-size-adjust]"
  | "[font-size]"
  | "[font-stretch]"
  | "[font-style]"
  | "[font-variant]"
  | "[font-weight]"
  | "[format]"
  | "[fr]"
  | "[from]"
  | "[fx]"
  | "[fy]"
  | "[g1]"
  | "[g2]"
  | "[global]"
  | "[glyph-name]"
  | "[glyph-orientation-horizontal]"
  | "[glyph-orientation-vertical]"
  | "[glyphRef]"
  | "[gradientTransform]"
  | "[gradientUnits]"
  | "[graphical]"
  | "[hanging]"
  | "[height]"
  | "[horiz-adv-x]"
  | "[horiz-origin-x]"
  | "[horiz-origin-y]"
  | "[href]"
  | "[hreflang]"
  | "[id]"
  | "[ideographic]"
  | "[image-rendering]"
  | "[in2]"
  | "[in]"
  | "[k1]"
  | "[k2]"
  | "[k3]"
  | "[k4]"
  | "[k]"
  | "[kernelMatrix]"
  | "[kernelUnitLength]"
  | "[kerning]"
  | "[keyPoints]"
  | "[lang]"
  | "[lengthAdjust]"
  | "[letter-spacing]"
  | "[lighting-color]"
  | "[limitingConeAngle]"
  | "[marker-end]"
  | "[marker-mid]"
  | "[marker-start]"
  | "[markerHeight]"
  | "[markerUnits]"
  | "[markerWidth]"
  | "[maskContentUnits]"
  | "[maskUnits]"
  | "[mask]"
  | "[mathematical]"
  | "[media]"
  | "[mode]"
  | "[name]"
  | "[numOctaves]"
  | "[offset]"
  | "[opacity]"
  | "[operator]"
  | "[order]"
  | "[orient]"
  | "[orientation]"
  | "[origin]"
  | "[overflow]"
  | "[overline-position]"
  | "[overline-thickness]"
  | "[paint-order]"
  | "[panose-1]"
  | "[path]"
  | "[patternContentUnits]"
  | "[patternTransform]"
  | "[patternUnits]"
  | "[ping]"
  | "[pointer-events]"
  | "[pointsAtX]"
  | "[pointsAtY]"
  | "[pointsAtZ]"
  | "[points]"
  | "[preserveAlpha]"
  | "[preserveAspectRatio]"
  | "[primitiveUnits]"
  | "[r]"
  | "[radius]"
  | "[refX]"
  | "[refY]"
  | "[referrerpolicy]"
  | "[rel]"
  | "[repeatCount]"
  | "[requiredExtensions]"
  | "[requiredFeatures]"
  | "[rotate]"
  | "[rx]"
  | "[ry]"
  | "[scale]"
  | "[seed]"
  | "[shape-rendering]"
  | "[side]"
  | "[slope]"
  | "[solid-color]"
  | "[solid-opacity]"
  | "[spacing]"
  | "[specularConstant]"
  | "[specularExponent]"
  | "[spreadMethod]"
  | "[startOffset]"
  | "[stdDeviation]"
  | "[stemh]"
  | "[stemv]"
  | "[stitchTiles]"
  | "[stop-color]"
  | "[stop-opacity]"
  | "[strikethrough-position]"
  | "[strikethrough-thickness]"
  | "[string]"
  | "[stroke-dasharray]"
  | "[stroke-dashoffset]"
  | "[stroke-linecap]"
  | "[stroke-linejoin]"
  | "[stroke-miterlimit]"
  | "[stroke-opacity]"
  | "[stroke-width]"
  | "[stroke]"
  | "[style]"
  | "[surfaceScale]"
  | "[systemLanguage]"
  | "[tabindex]"
  | "[targetX]"
  | "[targetY]"
  | "[target]"
  | "[text-anchor]"
  | "[text-decoration]"
  | "[text-overflow]"
  | "[text-rendering]"
  | "[textLength]"
  | "[title]"
  | "[to]"
  | "[transform-origin]"
  | "[transform]"
  | "[type]"
  | "[u1]"
  | "[u2]"
  | "[underline-position]"
  | "[underline-thickness]"
  | "[unicode-bidi]"
  | "[unicode-range]"
  | "[unicode]"
  | "[units-per-em]"
  | "[v-alphabetic]"
  | "[v-hanging]"
  | "[v-ideographic]"
  | "[v-mathematical]"
  | "[values]"
  | "[vector-effect]"
  | "[version]"
  | "[vert-adv-y]"
  | "[vert-origin-x]"
  | "[vert-origin-y]"
  | "[viewBox]"
  | "[viewTarget]"
  | "[visibility]"
  | "[white-space]"
  | "[width]"
  | "[widths]"
  | "[word-spacing]"
  | "[writing-mode]"
  | "[x-height]"
  | "[x1]"
  | "[x2]"
  | "[xChannelSelector]"
  | "[x]"
  | "[y1]"
  | "[y2]"
  | "[yChannelSelector]"
  | "[y]"
  | "[z]"
  | "[zoomAndPan]";

export type Globals = "-moz-initial" | "inherit" | "initial" | "revert" | "revert-layer" | "unset";

export type Property$AccentColor = Globals | DataType$Color | "auto";

export type Property$AlignContent = Globals | DataType$ContentDistribution | DataType$ContentPosition | "baseline" | "normal" | string;

export type Property$AlignItems = Globals | DataType$SelfPosition | "baseline" | "normal" | "stretch" | string;

export type Property$AlignSelf = Globals | DataType$SelfPosition | "auto" | "baseline" | "normal" | "stretch" | string;

export type Property$AlignTracks = Globals | DataType$ContentDistribution | DataType$ContentPosition | "baseline" | "normal" | string;

export type Property$All = Globals;

export type Property$Animation<TTime = string> = Globals | DataType$SingleAnimation<TTime> | string;

export type Property$AnimationComposition = Globals | DataType$SingleAnimationComposition | string;

export type Property$AnimationDelay<TTime = string> = Globals | TTime | string;

export type Property$AnimationDirection = Globals | DataType$SingleAnimationDirection | string;

export type Property$AnimationDuration<TTime = string> = Globals | TTime | string;

export type Property$AnimationFillMode = Globals | DataType$SingleAnimationFillMode | string;

export type Property$AnimationIterationCount = Globals | "infinite" | string | number;

export type Property$AnimationName = Globals | "none" | string;

export type Property$AnimationPlayState = Globals | "paused" | "running" | string;

export type Property$AnimationRange<TLength = string | 0> = Globals | DataType$TimelineRangeName | TLength | "normal" | string;

export type Property$AnimationRangeEnd<TLength = string | 0> = Globals | DataType$TimelineRangeName | TLength | "normal" | string;

export type Property$AnimationRangeStart<TLength = string | 0> = Globals | DataType$TimelineRangeName | TLength | "normal" | string;

export type Property$AnimationTimeline = Globals | DataType$SingleAnimationTimeline | string;

export type Property$AnimationTimingFunction = Globals | DataType$EasingFunction | string;

export type Property$Appearance = Globals | DataType$CompatAuto | "auto" | "menulist-button" | "none" | "textfield";

export type Property$AspectRatio = Globals | "auto" | string | number;

export type Property$Azimuth =
  | Globals
  | "behind"
  | "center"
  | "center-left"
  | "center-right"
  | "far-left"
  | "far-right"
  | "left"
  | "left-side"
  | "leftwards"
  | "right"
  | "right-side"
  | "rightwards"
  | string;

export type Property$BackdropFilter = Globals | "none" | string;

export type Property$BackfaceVisibility = Globals | "hidden" | "visible";

export type Property$Background<TLength = string | 0> = Globals | DataType$FinalBgLayer<TLength> | string;

export type Property$BackgroundAttachment = Globals | DataType$Attachment | string;

export type Property$BackgroundBlendMode = Globals | DataType$BlendMode | string;

export type Property$BackgroundClip = Globals | DataType$Box | string;

export type Property$BackgroundColor = Globals | DataType$Color;

export type Property$BackgroundImage = Globals | "none" | string;

export type Property$BackgroundOrigin = Globals | DataType$Box | string;

export type Property$BackgroundPosition<TLength = string | 0> = Globals | DataType$BgPosition<TLength> | string;

export type Property$BackgroundPositionX<TLength = string | 0> = Globals | TLength | "center" | "left" | "right" | "x-end" | "x-start" | string;

export type Property$BackgroundPositionY<TLength = string | 0> = Globals | TLength | "bottom" | "center" | "top" | "y-end" | "y-start" | string;

export type Property$BackgroundRepeat = Globals | DataType$RepeatStyle | string;

export type Property$BackgroundSize<TLength = string | 0> = Globals | DataType$BgSize<TLength> | string;

export type Property$BlockOverflow = Globals | "clip" | "ellipsis" | string;

export type Property$BlockSize<TLength = string | 0> =
  | Globals
  | TLength
  | "-moz-fit-content"
  | "-moz-max-content"
  | "-moz-min-content"
  | "auto"
  | "fit-content"
  | "max-content"
  | "min-content"
  | string;

export type Property$Border<TLength = string | 0> = Globals | DataType$LineWidth<TLength> | DataType$LineStyle | DataType$Color | string;

export type Property$BorderBlock<TLength = string | 0> = Globals | DataType$LineWidth<TLength> | DataType$LineStyle | DataType$Color | string;

export type Property$BorderBlockColor = Globals | DataType$Color | string;

export type Property$BorderBlockEnd<TLength = string | 0> = Globals | DataType$LineWidth<TLength> | DataType$LineStyle | DataType$Color | string;

export type Property$BorderBlockEndColor = Globals | DataType$Color;

export type Property$BorderBlockEndStyle = Globals | DataType$LineStyle;

export type Property$BorderBlockEndWidth<TLength = string | 0> = Globals | DataType$LineWidth<TLength>;

export type Property$BorderBlockStart<TLength = string | 0> = Globals | DataType$LineWidth<TLength> | DataType$LineStyle | DataType$Color | string;

export type Property$BorderBlockStartColor = Globals | DataType$Color;

export type Property$BorderBlockStartStyle = Globals | DataType$LineStyle;

export type Property$BorderBlockStartWidth<TLength = string | 0> = Globals | DataType$LineWidth<TLength>;

export type Property$BorderBlockStyle = Globals | DataType$LineStyle;

export type Property$BorderBlockWidth<TLength = string | 0> = Globals | DataType$LineWidth<TLength>;

export type Property$BorderBottom<TLength = string | 0> = Globals | DataType$LineWidth<TLength> | DataType$LineStyle | DataType$Color | string;

export type Property$BorderBottomColor = Globals | DataType$Color;

export type Property$BorderBottomLeftRadius<TLength = string | 0> = Globals | TLength | string;

export type Property$BorderBottomRightRadius<TLength = string | 0> = Globals | TLength | string;

export type Property$BorderBottomStyle = Globals | DataType$LineStyle;

export type Property$BorderBottomWidth<TLength = string | 0> = Globals | DataType$LineWidth<TLength>;

export type Property$BorderCollapse = Globals | "collapse" | "separate";

export type Property$BorderColor = Globals | DataType$Color | string;

export type Property$BorderEndEndRadius<TLength = string | 0> = Globals | TLength | string;

export type Property$BorderEndStartRadius<TLength = string | 0> = Globals | TLength | string;

export type Property$BorderImage = Globals | "none" | "repeat" | "round" | "space" | "stretch" | string | number;

export type Property$BorderImageOutset<TLength = string | 0> = Globals | TLength | string | number;

export type Property$BorderImageRepeat = Globals | "repeat" | "round" | "space" | "stretch" | string;

export type Property$BorderImageSlice = Globals | string | number;

export type Property$BorderImageSource = Globals | "none" | string;

export type Property$BorderImageWidth<TLength = string | 0> = Globals | TLength | "auto" | string | number;

export type Property$BorderInline<TLength = string | 0> = Globals | DataType$LineWidth<TLength> | DataType$LineStyle | DataType$Color | string;

export type Property$BorderInlineColor = Globals | DataType$Color | string;

export type Property$BorderInlineEnd<TLength = string | 0> = Globals | DataType$LineWidth<TLength> | DataType$LineStyle | DataType$Color | string;

export type Property$BorderInlineEndColor = Globals | DataType$Color;

export type Property$BorderInlineEndStyle = Globals | DataType$LineStyle;

export type Property$BorderInlineEndWidth<TLength = string | 0> = Globals | DataType$LineWidth<TLength>;

export type Property$BorderInlineStart<TLength = string | 0> = Globals | DataType$LineWidth<TLength> | DataType$LineStyle | DataType$Color | string;

export type Property$BorderInlineStartColor = Globals | DataType$Color;

export type Property$BorderInlineStartStyle = Globals | DataType$LineStyle;

export type Property$BorderInlineStartWidth<TLength = string | 0> = Globals | DataType$LineWidth<TLength>;

export type Property$BorderInlineStyle = Globals | DataType$LineStyle;

export type Property$BorderInlineWidth<TLength = string | 0> = Globals | DataType$LineWidth<TLength>;

export type Property$BorderLeft<TLength = string | 0> = Globals | DataType$LineWidth<TLength> | DataType$LineStyle | DataType$Color | string;

export type Property$BorderLeftColor = Globals | DataType$Color;

export type Property$BorderLeftStyle = Globals | DataType$LineStyle;

export type Property$BorderLeftWidth<TLength = string | 0> = Globals | DataType$LineWidth<TLength>;

export type Property$BorderRadius<TLength = string | 0> = Globals | TLength | string;

export type Property$BorderRight<TLength = string | 0> = Globals | DataType$LineWidth<TLength> | DataType$LineStyle | DataType$Color | string;

export type Property$BorderRightColor = Globals | DataType$Color;

export type Property$BorderRightStyle = Globals | DataType$LineStyle;

export type Property$BorderRightWidth<TLength = string | 0> = Globals | DataType$LineWidth<TLength>;

export type Property$BorderSpacing<TLength = string | 0> = Globals | TLength | string;

export type Property$BorderStartEndRadius<TLength = string | 0> = Globals | TLength | string;

export type Property$BorderStartStartRadius<TLength = string | 0> = Globals | TLength | string;

export type Property$BorderStyle = Globals | DataType$LineStyle | string;

export type Property$BorderTop<TLength = string | 0> = Globals | DataType$LineWidth<TLength> | DataType$LineStyle | DataType$Color | string;

export type Property$BorderTopColor = Globals | DataType$Color;

export type Property$BorderTopLeftRadius<TLength = string | 0> = Globals | TLength | string;

export type Property$BorderTopRightRadius<TLength = string | 0> = Globals | TLength | string;

export type Property$BorderTopStyle = Globals | DataType$LineStyle;

export type Property$BorderTopWidth<TLength = string | 0> = Globals | DataType$LineWidth<TLength>;

export type Property$BorderWidth<TLength = string | 0> = Globals | DataType$LineWidth<TLength> | string;

export type Property$Bottom<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$BoxAlign = Globals | "baseline" | "center" | "end" | "start" | "stretch";

export type Property$BoxDecorationBreak = Globals | "clone" | "slice";

export type Property$BoxDirection = Globals | "inherit" | "normal" | "reverse";

export type Property$BoxFlex = Globals | number;

export type Property$BoxFlexGroup = Globals | number;

export type Property$BoxLines = Globals | "multiple" | "single";

export type Property$BoxOrdinalGroup = Globals | number;

export type Property$BoxOrient = Globals | "block-axis" | "horizontal" | "inherit" | "inline-axis" | "vertical";

export type Property$BoxPack = Globals | "center" | "end" | "justify" | "start";

export type Property$BoxShadow = Globals | "none" | string;

export type Property$BoxSizing = Globals | "border-box" | "content-box";

export type Property$BreakAfter =
  | Globals
  | "all"
  | "always"
  | "auto"
  | "avoid"
  | "avoid-column"
  | "avoid-page"
  | "avoid-region"
  | "column"
  | "left"
  | "page"
  | "recto"
  | "region"
  | "right"
  | "verso";

export type Property$BreakBefore =
  | Globals
  | "all"
  | "always"
  | "auto"
  | "avoid"
  | "avoid-column"
  | "avoid-page"
  | "avoid-region"
  | "column"
  | "left"
  | "page"
  | "recto"
  | "region"
  | "right"
  | "verso";

export type Property$BreakInside = Globals | "auto" | "avoid" | "avoid-column" | "avoid-page" | "avoid-region";

export type Property$CaptionSide = Globals | "block-end" | "block-start" | "bottom" | "inline-end" | "inline-start" | "top";

export type Property$Caret = Globals | DataType$Color | "auto" | "bar" | "block" | "underscore" | string;

export type Property$CaretColor = Globals | DataType$Color | "auto";

export type Property$CaretShape = Globals | "auto" | "bar" | "block" | "underscore";

export type Property$Clear = Globals | "both" | "inline-end" | "inline-start" | "left" | "none" | "right";

export type Property$Clip = Globals | "auto" | string;

export type Property$ClipPath = Globals | DataType$GeometryBox | "none" | string;

export type Property$Color = Globals | DataType$Color;

export type Property$PrintColorAdjust = Globals | "economy" | "exact";

export type Property$ColorScheme = Globals | "dark" | "light" | "normal" | string;

export type Property$ColumnCount = Globals | "auto" | number;

export type Property$ColumnFill = Globals | "auto" | "balance" | "balance-all";

export type Property$ColumnGap<TLength = string | 0> = Globals | TLength | "normal" | string;

export type Property$ColumnRule<TLength = string | 0> = Globals | DataType$LineWidth<TLength> | DataType$LineStyle | DataType$Color | string;

export type Property$ColumnRuleColor = Globals | DataType$Color;

export type Property$ColumnRuleStyle = Globals | DataType$LineStyle | string;

export type Property$ColumnRuleWidth<TLength = string | 0> = Globals | DataType$LineWidth<TLength> | string;

export type Property$ColumnSpan = Globals | "all" | "none";

export type Property$ColumnWidth<TLength = string | 0> = Globals | TLength | "auto";

export type Property$Columns<TLength = string | 0> = Globals | TLength | "auto" | string | number;

export type Property$Contain = Globals | "content" | "inline-size" | "layout" | "none" | "paint" | "size" | "strict" | "style" | string;

export type Property$ContainIntrinsicBlockSize<TLength = string | 0> = Globals | TLength | "none" | string;

export type Property$ContainIntrinsicHeight<TLength = string | 0> = Globals | TLength | "none" | string;

export type Property$ContainIntrinsicInlineSize<TLength = string | 0> = Globals | TLength | "none" | string;

export type Property$ContainIntrinsicSize<TLength = string | 0> = Globals | TLength | "none" | string;

export type Property$ContainIntrinsicWidth<TLength = string | 0> = Globals | TLength | "none" | string;

export type Property$Container = Globals | "none" | string;

export type Property$ContainerName = Globals | "none" | string;

export type Property$ContainerType = Globals | "inline-size" | "normal" | "size";

export type Property$Content = Globals | DataType$ContentList | "none" | "normal" | string;

export type Property$ContentVisibility = Globals | "auto" | "hidden" | "visible";

export type Property$CounterIncrement = Globals | "none" | string;

export type Property$CounterReset = Globals | "none" | string;

export type Property$CounterSet = Globals | "none" | string;

export type Property$Cursor =
  | Globals
  | "-moz-grab"
  | "-webkit-grab"
  | "alias"
  | "all-scroll"
  | "auto"
  | "cell"
  | "col-resize"
  | "context-menu"
  | "copy"
  | "crosshair"
  | "default"
  | "e-resize"
  | "ew-resize"
  | "grab"
  | "grabbing"
  | "help"
  | "move"
  | "n-resize"
  | "ne-resize"
  | "nesw-resize"
  | "no-drop"
  | "none"
  | "not-allowed"
  | "ns-resize"
  | "nw-resize"
  | "nwse-resize"
  | "pointer"
  | "progress"
  | "row-resize"
  | "s-resize"
  | "se-resize"
  | "sw-resize"
  | "text"
  | "vertical-text"
  | "w-resize"
  | "wait"
  | "zoom-in"
  | "zoom-out"
  | string;

export type Property$Direction = Globals | "ltr" | "rtl";

export type Property$Display =
  | Globals
  | DataType$DisplayOutside
  | DataType$DisplayInside
  | DataType$DisplayInternal
  | DataType$DisplayLegacy
  | "contents"
  | "list-item"
  | "none"
  | string;

export type Property$EmptyCells = Globals | "hide" | "show";

export type Property$Filter = Globals | "none" | string;

export type Property$Flex<TLength = string | 0> = Globals | TLength | "auto" | "content" | "fit-content" | "max-content" | "min-content" | "none" | string | number;

export type Property$FlexBasis<TLength = string | 0> =
  | Globals
  | TLength
  | "-moz-fit-content"
  | "-moz-max-content"
  | "-moz-min-content"
  | "-webkit-auto"
  | "auto"
  | "content"
  | "fit-content"
  | "max-content"
  | "min-content"
  | string;

export type Property$FlexDirection = Globals | "column" | "column-reverse" | "row" | "row-reverse";

export type Property$FlexFlow = Globals | "column" | "column-reverse" | "nowrap" | "row" | "row-reverse" | "wrap" | "wrap-reverse" | string;

export type Property$FlexGrow = Globals | number;

export type Property$FlexShrink = Globals | number;

export type Property$FlexWrap = Globals | "nowrap" | "wrap" | "wrap-reverse";

export type Property$Float = Globals | "inline-end" | "inline-start" | "left" | "none" | "right";

export type Property$Font = Globals | "caption" | "icon" | "menu" | "message-box" | "small-caption" | "status-bar" | string;

export type Property$FontFamily = Globals | DataType$GenericFamily | string;

export type Property$FontFeatureSettings = Globals | "normal" | string;

export type Property$FontKerning = Globals | "auto" | "none" | "normal";

export type Property$FontLanguageOverride = Globals | "normal" | string;

export type Property$FontOpticalSizing = Globals | "auto" | "none";

export type Property$FontPalette = Globals | "dark" | "light" | "normal" | string;

export type Property$FontSize<TLength = string | 0> = Globals | DataType$AbsoluteSize | TLength | "larger" | "smaller" | string;

export type Property$FontSizeAdjust = Globals | "from-font" | "none" | string | number;

export type Property$FontSmooth<TLength = string | 0> = Globals | DataType$AbsoluteSize | TLength | "always" | "auto" | "never";

export type Property$FontStretch = Globals | DataType$FontStretchAbsolute;

export type Property$FontStyle = Globals | "italic" | "normal" | "oblique" | string;

export type Property$FontSynthesis = Globals | "none" | "position" | "small-caps" | "style" | "weight" | string;

export type Property$FontSynthesisPosition = Globals | "auto" | "none";

export type Property$FontSynthesisSmallCaps = Globals | "auto" | "none";

export type Property$FontSynthesisStyle = Globals | "auto" | "none";

export type Property$FontSynthesisWeight = Globals | "auto" | "none";

export type Property$FontVariant =
  | Globals
  | DataType$EastAsianVariantValues
  | "all-petite-caps"
  | "all-small-caps"
  | "common-ligatures"
  | "contextual"
  | "diagonal-fractions"
  | "discretionary-ligatures"
  | "full-width"
  | "historical-forms"
  | "historical-ligatures"
  | "lining-nums"
  | "no-common-ligatures"
  | "no-contextual"
  | "no-discretionary-ligatures"
  | "no-historical-ligatures"
  | "none"
  | "normal"
  | "oldstyle-nums"
  | "ordinal"
  | "petite-caps"
  | "proportional-nums"
  | "proportional-width"
  | "ruby"
  | "slashed-zero"
  | "small-caps"
  | "stacked-fractions"
  | "tabular-nums"
  | "titling-caps"
  | "unicase"
  | string;

export type Property$FontVariantAlternates = Globals | "historical-forms" | "normal" | string;

export type Property$FontVariantCaps = Globals | "all-petite-caps" | "all-small-caps" | "normal" | "petite-caps" | "small-caps" | "titling-caps" | "unicase";

export type Property$FontVariantEastAsian = Globals | DataType$EastAsianVariantValues | "full-width" | "normal" | "proportional-width" | "ruby" | string;

export type Property$FontVariantEmoji = Globals | "emoji" | "normal" | "text" | "unicode";

export type Property$FontVariantLigatures =
  | Globals
  | "common-ligatures"
  | "contextual"
  | "discretionary-ligatures"
  | "historical-ligatures"
  | "no-common-ligatures"
  | "no-contextual"
  | "no-discretionary-ligatures"
  | "no-historical-ligatures"
  | "none"
  | "normal"
  | string;

export type Property$FontVariantNumeric =
  | Globals
  | "diagonal-fractions"
  | "lining-nums"
  | "normal"
  | "oldstyle-nums"
  | "ordinal"
  | "proportional-nums"
  | "slashed-zero"
  | "stacked-fractions"
  | "tabular-nums"
  | string;

export type Property$FontVariantPosition = Globals | "normal" | "sub" | "super";

export type Property$FontVariationSettings = Globals | "normal" | string;

export type Property$FontWeight = Globals | DataType$FontWeightAbsolute | "bolder" | "lighter";

export type Property$ForcedColorAdjust = Globals | "auto" | "none";

export type Property$Gap<TLength = string | 0> = Globals | TLength | "normal" | string;

export type Property$Grid = Globals | "none" | string;

export type Property$GridArea = Globals | DataType$GridLine | string;

export type Property$GridAutoColumns<TLength = string | 0> = Globals | DataType$TrackBreadth<TLength> | string;

export type Property$GridAutoFlow = Globals | "column" | "dense" | "row" | string;

export type Property$GridAutoRows<TLength = string | 0> = Globals | DataType$TrackBreadth<TLength> | string;

export type Property$GridColumn = Globals | DataType$GridLine | string;

export type Property$GridColumnEnd = Globals | DataType$GridLine;

export type Property$GridColumnGap<TLength = string | 0> = Globals | TLength | string;

export type Property$GridColumnStart = Globals | DataType$GridLine;

export type Property$GridGap<TLength = string | 0> = Globals | TLength | string;

export type Property$GridRow = Globals | DataType$GridLine | string;

export type Property$GridRowEnd = Globals | DataType$GridLine;

export type Property$GridRowGap<TLength = string | 0> = Globals | TLength | string;

export type Property$GridRowStart = Globals | DataType$GridLine;

export type Property$GridTemplate = Globals | "none" | string;

export type Property$GridTemplateAreas = Globals | "none" | string;

export type Property$GridTemplateColumns<TLength = string | 0> = Globals | DataType$TrackBreadth<TLength> | "none" | "subgrid" | string;

export type Property$GridTemplateRows<TLength = string | 0> = Globals | DataType$TrackBreadth<TLength> | "none" | "subgrid" | string;

export type Property$HangingPunctuation = Globals | "allow-end" | "first" | "force-end" | "last" | "none" | string;

export type Property$Height<TLength = string | 0> =
  | Globals
  | TLength
  | "-moz-max-content"
  | "-moz-min-content"
  | "-webkit-fit-content"
  | "auto"
  | "fit-content"
  | "max-content"
  | "min-content"
  | string;

export type Property$HyphenateCharacter = Globals | "auto" | string;

export type Property$HyphenateLimitChars = Globals | "auto" | string | number;

export type Property$Hyphens = Globals | "auto" | "manual" | "none";

export type Property$ImageOrientation = Globals | "flip" | "from-image" | string;

export type Property$ImageRendering = Globals | "-moz-crisp-edges" | "-webkit-optimize-contrast" | "auto" | "crisp-edges" | "pixelated";

export type Property$ImageResolution = Globals | "from-image" | string;

export type Property$ImeMode = Globals | "active" | "auto" | "disabled" | "inactive" | "normal";

export type Property$InitialLetter = Globals | "normal" | string | number;

export type Property$InlineSize<TLength = string | 0> =
  | Globals
  | TLength
  | "-moz-fit-content"
  | "-moz-max-content"
  | "-moz-min-content"
  | "-webkit-fill-available"
  | "auto"
  | "fit-content"
  | "max-content"
  | "min-content"
  | string;

export type Property$InputSecurity = Globals | "auto" | "none";

export type Property$Inset<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$InsetBlock<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$InsetBlockEnd<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$InsetBlockStart<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$InsetInline<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$InsetInlineEnd<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$InsetInlineStart<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$Isolation = Globals | "auto" | "isolate";

export type Property$JustifyContent = Globals | DataType$ContentDistribution | DataType$ContentPosition | "left" | "normal" | "right" | string;

export type Property$JustifyItems = Globals | DataType$SelfPosition | "baseline" | "left" | "legacy" | "normal" | "right" | "stretch" | string;

export type Property$JustifySelf = Globals | DataType$SelfPosition | "auto" | "baseline" | "left" | "normal" | "right" | "stretch" | string;

export type Property$JustifyTracks = Globals | DataType$ContentDistribution | DataType$ContentPosition | "left" | "normal" | "right" | string;

export type Property$Left<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$LetterSpacing<TLength = string | 0> = Globals | TLength | "normal";

export type Property$LineBreak = Globals | "anywhere" | "auto" | "loose" | "normal" | "strict";

export type Property$LineClamp = Globals | "none" | number;

export type Property$LineHeight<TLength = string | 0> = Globals | TLength | "normal" | string | number;

export type Property$LineHeightStep<TLength = string | 0> = Globals | TLength;

export type Property$ListStyle = Globals | "inside" | "none" | "outside" | string;

export type Property$ListStyleImage = Globals | "none" | string;

export type Property$ListStylePosition = Globals | "inside" | "outside";

export type Property$ListStyleType = Globals | "none" | string;

export type Property$Margin<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$MarginBlock<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$MarginBlockEnd<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$MarginBlockStart<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$MarginBottom<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$MarginInline<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$MarginInlineEnd<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$MarginInlineStart<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$MarginLeft<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$MarginRight<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$MarginTop<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$MarginTrim = Globals | "all" | "in-flow" | "none";

export type Property$Mask<TLength = string | 0> = Globals | DataType$MaskLayer<TLength> | string;

export type Property$MaskBorder = Globals | "alpha" | "luminance" | "none" | "repeat" | "round" | "space" | "stretch" | string | number;

export type Property$MaskBorderMode = Globals | "alpha" | "luminance";

export type Property$MaskBorderOutset<TLength = string | 0> = Globals | TLength | string | number;

export type Property$MaskBorderRepeat = Globals | "repeat" | "round" | "space" | "stretch" | string;

export type Property$MaskBorderSlice = Globals | string | number;

export type Property$MaskBorderSource = Globals | "none" | string;

export type Property$MaskBorderWidth<TLength = string | 0> = Globals | TLength | "auto" | string | number;

export type Property$MaskClip = Globals | DataType$GeometryBox | "no-clip" | string;

export type Property$MaskComposite = Globals | DataType$CompositingOperator | string;

export type Property$MaskImage = Globals | "none" | string;

export type Property$MaskMode = Globals | DataType$MaskingMode | string;

export type Property$MaskOrigin = Globals | DataType$GeometryBox | string;

export type Property$MaskPosition<TLength = string | 0> = Globals | DataType$Position<TLength> | string;

export type Property$MaskRepeat = Globals | DataType$RepeatStyle | string;

export type Property$MaskSize<TLength = string | 0> = Globals | DataType$BgSize<TLength> | string;

export type Property$MaskType = Globals | "alpha" | "luminance";

export type Property$MasonryAutoFlow = Globals | "definite-first" | "next" | "ordered" | "pack" | string;

export type Property$MathDepth = Globals | "auto-add" | string | number;

export type Property$MathShift = Globals | "compact" | "normal";

export type Property$MathStyle = Globals | "compact" | "normal";

export type Property$MaxBlockSize<TLength = string | 0> =
  | Globals
  | TLength
  | "-moz-max-content"
  | "-moz-min-content"
  | "-webkit-fill-available"
  | "fit-content"
  | "max-content"
  | "min-content"
  | "none"
  | string;

export type Property$MaxHeight<TLength = string | 0> =
  | Globals
  | TLength
  | "-moz-fit-content"
  | "-moz-max-content"
  | "-moz-min-content"
  | "-webkit-fit-content"
  | "-webkit-max-content"
  | "-webkit-min-content"
  | "fit-content"
  | "intrinsic"
  | "max-content"
  | "min-content"
  | "none"
  | string;

export type Property$MaxInlineSize<TLength = string | 0> =
  | Globals
  | TLength
  | "-moz-fit-content"
  | "-moz-max-content"
  | "-moz-min-content"
  | "-webkit-fill-available"
  | "fit-content"
  | "max-content"
  | "min-content"
  | "none"
  | string;

export type Property$MaxLines = Globals | "none" | number;

export type Property$MaxWidth<TLength = string | 0> =
  | Globals
  | TLength
  | "-moz-fit-content"
  | "-moz-max-content"
  | "-moz-min-content"
  | "-webkit-fit-content"
  | "-webkit-max-content"
  | "-webkit-min-content"
  | "fit-content"
  | "intrinsic"
  | "max-content"
  | "min-content"
  | "none"
  | string;

export type Property$MinBlockSize<TLength = string | 0> =
  | Globals
  | TLength
  | "-moz-max-content"
  | "-moz-min-content"
  | "-webkit-fill-available"
  | "auto"
  | "fit-content"
  | "max-content"
  | "min-content"
  | string;

export type Property$MinHeight<TLength = string | 0> =
  | Globals
  | TLength
  | "-moz-fit-content"
  | "-moz-max-content"
  | "-moz-min-content"
  | "-webkit-fit-content"
  | "-webkit-max-content"
  | "-webkit-min-content"
  | "auto"
  | "fit-content"
  | "intrinsic"
  | "max-content"
  | "min-content"
  | string;

export type Property$MinInlineSize<TLength = string | 0> =
  | Globals
  | TLength
  | "-moz-fit-content"
  | "-moz-max-content"
  | "-moz-min-content"
  | "-webkit-fill-available"
  | "auto"
  | "fit-content"
  | "max-content"
  | "min-content"
  | string;

export type Property$MinWidth<TLength = string | 0> =
  | Globals
  | TLength
  | "-moz-fit-content"
  | "-moz-max-content"
  | "-moz-min-content"
  | "-webkit-fill-available"
  | "-webkit-fit-content"
  | "-webkit-max-content"
  | "-webkit-min-content"
  | "auto"
  | "fit-content"
  | "intrinsic"
  | "max-content"
  | "min-content"
  | "min-intrinsic"
  | string;

export type Property$MixBlendMode = Globals | DataType$BlendMode | "plus-lighter";

export type Property$Offset<TLength = string | 0> = Globals | DataType$Position<TLength> | "auto" | "none" | "normal" | string;

export type Property$OffsetDistance<TLength = string | 0> = Globals | TLength | string;

export type Property$OffsetPath = Globals | "none" | string;

export type Property$OffsetRotate = Globals | "auto" | "reverse" | string;

export type Property$ObjectFit = Globals | "contain" | "cover" | "fill" | "none" | "scale-down";

export type Property$ObjectPosition<TLength = string | 0> = Globals | DataType$Position<TLength>;

export type Property$OffsetAnchor<TLength = string | 0> = Globals | DataType$Position<TLength> | "auto";

export type Property$OffsetPosition<TLength = string | 0> = Globals | DataType$Position<TLength> | "auto" | "normal";

export type Property$Opacity = Globals | string | number;

export type Property$Order = Globals | number;

export type Property$Orphans = Globals | number;

export type Property$Outline<TLength = string | 0> = Globals | DataType$Color | DataType$LineStyle | DataType$LineWidth<TLength> | "auto" | "invert" | string;

export type Property$OutlineColor = Globals | DataType$Color | "invert";

export type Property$OutlineOffset<TLength = string | 0> = Globals | TLength;

export type Property$OutlineStyle = Globals | DataType$LineStyle | "auto" | string;

export type Property$OutlineWidth<TLength = string | 0> = Globals | DataType$LineWidth<TLength>;

export type Property$Overflow = Globals | "-moz-hidden-unscrollable" | "auto" | "clip" | "hidden" | "scroll" | "visible" | string;

export type Property$OverflowAnchor = Globals | "auto" | "none";

export type Property$OverflowBlock = Globals | "auto" | "clip" | "hidden" | "scroll" | "visible";

export type Property$OverflowClipBox = Globals | "content-box" | "padding-box";

export type Property$OverflowClipMargin<TLength = string | 0> = Globals | DataType$VisualBox | TLength | string;

export type Property$OverflowInline = Globals | "auto" | "clip" | "hidden" | "scroll" | "visible";

export type Property$OverflowWrap = Globals | "anywhere" | "break-word" | "normal";

export type Property$OverflowX = Globals | "-moz-hidden-unscrollable" | "auto" | "clip" | "hidden" | "scroll" | "visible";

export type Property$OverflowY = Globals | "-moz-hidden-unscrollable" | "auto" | "clip" | "hidden" | "scroll" | "visible";

export type Property$Overlay = Globals | "auto" | "none";

export type Property$OverscrollBehavior = Globals | "auto" | "contain" | "none" | string;

export type Property$OverscrollBehaviorBlock = Globals | "auto" | "contain" | "none";

export type Property$OverscrollBehaviorInline = Globals | "auto" | "contain" | "none";

export type Property$OverscrollBehaviorX = Globals | "auto" | "contain" | "none";

export type Property$OverscrollBehaviorY = Globals | "auto" | "contain" | "none";

export type Property$Padding<TLength = string | 0> = Globals | TLength | string;

export type Property$PaddingBlock<TLength = string | 0> = Globals | TLength | string;

export type Property$PaddingBlockEnd<TLength = string | 0> = Globals | TLength | string;

export type Property$PaddingBlockStart<TLength = string | 0> = Globals | TLength | string;

export type Property$PaddingBottom<TLength = string | 0> = Globals | TLength | string;

export type Property$PaddingInline<TLength = string | 0> = Globals | TLength | string;

export type Property$PaddingInlineEnd<TLength = string | 0> = Globals | TLength | string;

export type Property$PaddingInlineStart<TLength = string | 0> = Globals | TLength | string;

export type Property$PaddingLeft<TLength = string | 0> = Globals | TLength | string;

export type Property$PaddingRight<TLength = string | 0> = Globals | TLength | string;

export type Property$PaddingTop<TLength = string | 0> = Globals | TLength | string;

export type Property$Page = Globals | "auto" | string;

export type Property$PageBreakAfter = Globals | "always" | "auto" | "avoid" | "left" | "recto" | "right" | "verso";

export type Property$PageBreakBefore = Globals | "always" | "auto" | "avoid" | "left" | "recto" | "right" | "verso";

export type Property$PageBreakInside = Globals | "auto" | "avoid";

export type Property$PaintOrder = Globals | "fill" | "markers" | "normal" | "stroke" | string;

export type Property$Perspective<TLength = string | 0> = Globals | TLength | "none";

export type Property$PerspectiveOrigin<TLength = string | 0> = Globals | DataType$Position<TLength>;

export type Property$PlaceContent = Globals | DataType$ContentDistribution | DataType$ContentPosition | "baseline" | "normal" | string;

export type Property$PlaceItems = Globals | DataType$SelfPosition | "baseline" | "normal" | "stretch" | string;

export type Property$PlaceSelf = Globals | DataType$SelfPosition | "auto" | "baseline" | "normal" | "stretch" | string;

export type Property$PointerEvents = Globals | "all" | "auto" | "fill" | "inherit" | "none" | "painted" | "stroke" | "visible" | "visibleFill" | "visiblePainted" | "visibleStroke";

export type Property$Position = Globals | "-webkit-sticky" | "absolute" | "fixed" | "relative" | "static" | "sticky";

export type Property$Quotes = Globals | "auto" | "none" | string;

export type Property$Resize = Globals | "block" | "both" | "horizontal" | "inline" | "none" | "vertical";

export type Property$Right<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$Rotate = Globals | "none" | string;

export type Property$RowGap<TLength = string | 0> = Globals | TLength | "normal" | string;

export type Property$RubyAlign = Globals | "center" | "space-around" | "space-between" | "start";

export type Property$RubyMerge = Globals | "auto" | "collapse" | "separate";

export type Property$RubyPosition = Globals | "alternate" | "inter-character" | "over" | "under" | string;

export type Property$Scale = Globals | "none" | string | number;

export type Property$ScrollBehavior = Globals | "auto" | "smooth";

export type Property$ScrollMargin<TLength = string | 0> = Globals | TLength | string;

export type Property$ScrollMarginBlock<TLength = string | 0> = Globals | TLength | string;

export type Property$ScrollMarginBlockEnd<TLength = string | 0> = Globals | TLength;

export type Property$ScrollMarginBlockStart<TLength = string | 0> = Globals | TLength;

export type Property$ScrollMarginBottom<TLength = string | 0> = Globals | TLength;

export type Property$ScrollMarginInline<TLength = string | 0> = Globals | TLength | string;

export type Property$ScrollMarginInlineEnd<TLength = string | 0> = Globals | TLength;

export type Property$ScrollMarginInlineStart<TLength = string | 0> = Globals | TLength;

export type Property$ScrollMarginLeft<TLength = string | 0> = Globals | TLength;

export type Property$ScrollMarginRight<TLength = string | 0> = Globals | TLength;

export type Property$ScrollMarginTop<TLength = string | 0> = Globals | TLength;

export type Property$ScrollPadding<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$ScrollPaddingBlock<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$ScrollPaddingBlockEnd<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$ScrollPaddingBlockStart<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$ScrollPaddingBottom<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$ScrollPaddingInline<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$ScrollPaddingInlineEnd<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$ScrollPaddingInlineStart<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$ScrollPaddingLeft<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$ScrollPaddingRight<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$ScrollPaddingTop<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$ScrollSnapAlign = Globals | "center" | "end" | "none" | "start" | string;

export type Property$ScrollSnapCoordinate<TLength = string | 0> = Globals | DataType$Position<TLength> | "none" | string;

export type Property$ScrollSnapDestination<TLength = string | 0> = Globals | DataType$Position<TLength>;

export type Property$ScrollSnapPointsX = Globals | "none" | string;

export type Property$ScrollSnapPointsY = Globals | "none" | string;

export type Property$ScrollSnapStop = Globals | "always" | "normal";

export type Property$ScrollSnapType = Globals | "block" | "both" | "inline" | "none" | "x" | "y" | string;

export type Property$ScrollSnapTypeX = Globals | "mandatory" | "none" | "proximity";

export type Property$ScrollSnapTypeY = Globals | "mandatory" | "none" | "proximity";

export type Property$ScrollTimeline = Globals | "none" | string;

export type Property$ScrollTimelineAxis = Globals | "block" | "inline" | "x" | "y" | string;

export type Property$ScrollTimelineName = Globals | "none" | string;

export type Property$ScrollbarColor = Globals | "auto" | string;

export type Property$ScrollbarGutter = Globals | "auto" | "stable" | string;

export type Property$ScrollbarWidth = Globals | "auto" | "none" | "thin";

export type Property$ShapeImageThreshold = Globals | string | number;

export type Property$ShapeMargin<TLength = string | 0> = Globals | TLength | string;

export type Property$ShapeOutside = Globals | DataType$Box | "margin-box" | "none" | string;

export type Property$TabSize<TLength = string | 0> = Globals | TLength | number;

export type Property$TableLayout = Globals | "auto" | "fixed";

export type Property$TextAlign = Globals | "-webkit-match-parent" | "center" | "end" | "justify" | "left" | "match-parent" | "right" | "start";

export type Property$TextAlignLast = Globals | "auto" | "center" | "end" | "justify" | "left" | "right" | "start";

export type Property$TextCombineUpright = Globals | "all" | "none" | string;

export type Property$TextDecoration<TLength = string | 0> =
  | Globals
  | DataType$Color
  | TLength
  | "auto"
  | "blink"
  | "dashed"
  | "dotted"
  | "double"
  | "from-font"
  | "grammar-error"
  | "line-through"
  | "none"
  | "overline"
  | "solid"
  | "spelling-error"
  | "underline"
  | "wavy"
  | string;

export type Property$TextDecorationColor = Globals | DataType$Color;

export type Property$TextDecorationLine = Globals | "blink" | "grammar-error" | "line-through" | "none" | "overline" | "spelling-error" | "underline" | string;

export type Property$TextDecorationSkip = Globals | "box-decoration" | "edges" | "leading-spaces" | "none" | "objects" | "spaces" | "trailing-spaces" | string;

export type Property$TextDecorationSkipInk = Globals | "all" | "auto" | "none";

export type Property$TextDecorationStyle = Globals | "dashed" | "dotted" | "double" | "solid" | "wavy";

export type Property$TextDecorationThickness<TLength = string | 0> = Globals | TLength | "auto" | "from-font" | string;

export type Property$TextEmphasis = Globals | DataType$Color | "circle" | "dot" | "double-circle" | "filled" | "none" | "open" | "sesame" | "triangle" | string;

export type Property$TextEmphasisColor = Globals | DataType$Color;

export type Property$TextEmphasisPosition = Globals | string;

export type Property$TextEmphasisStyle = Globals | "circle" | "dot" | "double-circle" | "filled" | "none" | "open" | "sesame" | "triangle" | string;

export type Property$TextIndent<TLength = string | 0> = Globals | TLength | string;

export type Property$TextJustify = Globals | "auto" | "inter-character" | "inter-word" | "none";

export type Property$TextOrientation = Globals | "mixed" | "sideways" | "upright";

export type Property$TextOverflow = Globals | "clip" | "ellipsis" | string;

export type Property$TextRendering = Globals | "auto" | "geometricPrecision" | "optimizeLegibility" | "optimizeSpeed";

export type Property$TextShadow = Globals | "none" | string;

export type Property$TextSizeAdjust = Globals | "auto" | "none" | string;

export type Property$TextTransform = Globals | "capitalize" | "full-size-kana" | "full-width" | "lowercase" | "none" | "uppercase";

export type Property$TextUnderlineOffset<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$TextUnderlinePosition = Globals | "auto" | "from-font" | "left" | "right" | "under" | string;

export type Property$TextWrap = Globals | "balance" | "nowrap" | "pretty" | "stable" | "wrap";

export type Property$TimelineScope = Globals | "none" | string;

export type Property$Top<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$TouchAction =
  | Globals
  | "-ms-manipulation"
  | "-ms-none"
  | "-ms-pinch-zoom"
  | "auto"
  | "manipulation"
  | "none"
  | "pan-down"
  | "pan-left"
  | "pan-right"
  | "pan-up"
  | "pan-x"
  | "pan-y"
  | "pinch-zoom"
  | string;

export type Property$Transform = Globals | "none" | string;

export type Property$TransformBox = Globals | "border-box" | "content-box" | "fill-box" | "stroke-box" | "view-box";

export type Property$TransformOrigin<TLength = string | 0> = Globals | TLength | "bottom" | "center" | "left" | "right" | "top" | string;

export type Property$TransformStyle = Globals | "flat" | "preserve-3d";

export type Property$Transition<TTime = string> = Globals | DataType$SingleTransition<TTime> | string;

export type Property$TransitionBehavior = Globals | "allow-discrete" | "normal" | string;

export type Property$TransitionDelay<TTime = string> = Globals | TTime | string;

export type Property$TransitionDuration<TTime = string> = Globals | TTime | string;

export type Property$TransitionProperty = Globals | "all" | "none" | string;

export type Property$TransitionTimingFunction = Globals | DataType$EasingFunction | string;

export type Property$Translate<TLength = string | 0> = Globals | TLength | "none" | string;

export type Property$UnicodeBidi =
  | Globals
  | "-moz-isolate"
  | "-moz-isolate-override"
  | "-moz-plaintext"
  | "-webkit-isolate"
  | "-webkit-isolate-override"
  | "-webkit-plaintext"
  | "bidi-override"
  | "embed"
  | "isolate"
  | "isolate-override"
  | "normal"
  | "plaintext";

export type Property$UserSelect = Globals | "-moz-none" | "all" | "auto" | "contain" | "element" | "none" | "text";

export type Property$VerticalAlign<TLength = string | 0> = Globals | TLength | "baseline" | "bottom" | "middle" | "sub" | "super" | "text-bottom" | "text-top" | "top" | string;

export type Property$ViewTimeline = Globals | "none" | string;

export type Property$ViewTimelineAxis = Globals | "block" | "inline" | "x" | "y" | string;

export type Property$ViewTimelineInset<TLength = string | 0> = Globals | TLength | "auto" | string;

export type Property$ViewTimelineName = Globals | "none" | string;

export type Property$ViewTransitionName = Globals | "none" | string;

export type Property$Visibility = Globals | "collapse" | "hidden" | "visible";

export type Property$WhiteSpace =
  | Globals
  | "-moz-pre-wrap"
  | "balance"
  | "break-spaces"
  | "collapse"
  | "discard"
  | "discard-after"
  | "discard-before"
  | "discard-inner"
  | "none"
  | "normal"
  | "nowrap"
  | "pre"
  | "pre-line"
  | "pre-wrap"
  | "preserve"
  | "preserve-breaks"
  | "preserve-spaces"
  | "pretty"
  | "stable"
  | "wrap"
  | string;

export type Property$WhiteSpaceCollapse = Globals | "break-spaces" | "collapse" | "discard" | "preserve" | "preserve-breaks" | "preserve-spaces";

export type Property$WhiteSpaceTrim = Globals | "discard-after" | "discard-before" | "discard-inner" | "none" | string;

export type Property$Widows = Globals | number;

export type Property$Width<TLength = string | 0> =
  | Globals
  | TLength
  | "-moz-fit-content"
  | "-moz-max-content"
  | "-moz-min-content"
  | "-webkit-fit-content"
  | "-webkit-max-content"
  | "auto"
  | "fit-content"
  | "intrinsic"
  | "max-content"
  | "min-content"
  | "min-intrinsic"
  | string;

export type Property$WillChange = Globals | DataType$AnimateableFeature | "auto" | string;

export type Property$WordBreak = Globals | "break-all" | "break-word" | "keep-all" | "normal";

export type Property$WordSpacing<TLength = string | 0> = Globals | TLength | "normal";

export type Property$WordWrap = Globals | "break-word" | "normal";

export type Property$WritingMode = Globals | "horizontal-tb" | "sideways-lr" | "sideways-rl" | "vertical-lr" | "vertical-rl";

export type Property$ZIndex = Globals | "auto" | number;

export type Property$Zoom = Globals | "normal" | "reset" | string | number;

export type Property$MozAppearance =
  | Globals
  | "-moz-mac-unified-toolbar"
  | "-moz-win-borderless-glass"
  | "-moz-win-browsertabbar-toolbox"
  | "-moz-win-communications-toolbox"
  | "-moz-win-communicationstext"
  | "-moz-win-exclude-glass"
  | "-moz-win-glass"
  | "-moz-win-media-toolbox"
  | "-moz-win-mediatext"
  | "-moz-window-button-box"
  | "-moz-window-button-box-maximized"
  | "-moz-window-button-close"
  | "-moz-window-button-maximize"
  | "-moz-window-button-minimize"
  | "-moz-window-button-restore"
  | "-moz-window-frame-bottom"
  | "-moz-window-frame-left"
  | "-moz-window-frame-right"
  | "-moz-window-titlebar"
  | "-moz-window-titlebar-maximized"
  | "button"
  | "button-arrow-down"
  | "button-arrow-next"
  | "button-arrow-previous"
  | "button-arrow-up"
  | "button-bevel"
  | "button-focus"
  | "caret"
  | "checkbox"
  | "checkbox-container"
  | "checkbox-label"
  | "checkmenuitem"
  | "dualbutton"
  | "groupbox"
  | "listbox"
  | "listitem"
  | "menuarrow"
  | "menubar"
  | "menucheckbox"
  | "menuimage"
  | "menuitem"
  | "menuitemtext"
  | "menulist"
  | "menulist-button"
  | "menulist-text"
  | "menulist-textfield"
  | "menupopup"
  | "menuradio"
  | "menuseparator"
  | "meterbar"
  | "meterchunk"
  | "none"
  | "progressbar"
  | "progressbar-vertical"
  | "progresschunk"
  | "progresschunk-vertical"
  | "radio"
  | "radio-container"
  | "radio-label"
  | "radiomenuitem"
  | "range"
  | "range-thumb"
  | "resizer"
  | "resizerpanel"
  | "scale-horizontal"
  | "scale-vertical"
  | "scalethumb-horizontal"
  | "scalethumb-vertical"
  | "scalethumbend"
  | "scalethumbstart"
  | "scalethumbtick"
  | "scrollbarbutton-down"
  | "scrollbarbutton-left"
  | "scrollbarbutton-right"
  | "scrollbarbutton-up"
  | "scrollbarthumb-horizontal"
  | "scrollbarthumb-vertical"
  | "scrollbartrack-horizontal"
  | "scrollbartrack-vertical"
  | "searchfield"
  | "separator"
  | "sheet"
  | "spinner"
  | "spinner-downbutton"
  | "spinner-textfield"
  | "spinner-upbutton"
  | "splitter"
  | "statusbar"
  | "statusbarpanel"
  | "tab"
  | "tab-scroll-arrow-back"
  | "tab-scroll-arrow-forward"
  | "tabpanel"
  | "tabpanels"
  | "textfield"
  | "textfield-multiline"
  | "toolbar"
  | "toolbarbutton"
  | "toolbarbutton-dropdown"
  | "toolbargripper"
  | "toolbox"
  | "tooltip"
  | "treeheader"
  | "treeheadercell"
  | "treeheadersortarrow"
  | "treeitem"
  | "treeline"
  | "treetwisty"
  | "treetwistyopen"
  | "treeview";

export type Property$MozBinding = Globals | "none" | string;

export type Property$MozBorderBottomColors = Globals | DataType$Color | "none" | string;

export type Property$MozBorderLeftColors = Globals | DataType$Color | "none" | string;

export type Property$MozBorderRightColors = Globals | DataType$Color | "none" | string;

export type Property$MozBorderTopColors = Globals | DataType$Color | "none" | string;

export type Property$MozContextProperties = Globals | "fill" | "fill-opacity" | "none" | "stroke" | "stroke-opacity" | string;

export type Property$MozFloatEdge = Globals | "border-box" | "content-box" | "margin-box" | "padding-box";

export type Property$MozForceBrokenImageIcon = Globals | 0 | 1;

export type Property$MozImageRegion = Globals | "auto" | string;

export type Property$MozOrient = Globals | "block" | "horizontal" | "inline" | "vertical";

export type Property$MozOutlineRadius<TLength = string | 0> = Globals | TLength | string;

export type Property$MozOutlineRadiusBottomleft<TLength = string | 0> = Globals | TLength | string;

export type Property$MozOutlineRadiusBottomright<TLength = string | 0> = Globals | TLength | string;

export type Property$MozOutlineRadiusTopleft<TLength = string | 0> = Globals | TLength | string;

export type Property$MozOutlineRadiusTopright<TLength = string | 0> = Globals | TLength | string;

export type Property$MozStackSizing = Globals | "ignore" | "stretch-to-fit";

export type Property$MozTextBlink = Globals | "blink" | "none";

export type Property$MozUserFocus = Globals | "ignore" | "none" | "normal" | "select-after" | "select-all" | "select-before" | "select-menu" | "select-same";

export type Property$MozUserInput = Globals | "auto" | "disabled" | "enabled" | "none";

export type Property$MozUserModify = Globals | "read-only" | "read-write" | "write-only";

export type Property$MozWindowDragging = Globals | "drag" | "no-drag";

export type Property$MozWindowShadow = Globals | "default" | "menu" | "none" | "sheet" | "tooltip";

export type Property$MsAccelerator = Globals | "false" | "true";

export type Property$MsBlockProgression = Globals | "bt" | "lr" | "rl" | "tb";

export type Property$MsContentZoomChaining = Globals | "chained" | "none";

export type Property$MsContentZoomLimit = Globals | string;

export type Property$MsContentZoomLimitMax = Globals | string;

export type Property$MsContentZoomLimitMin = Globals | string;

export type Property$MsContentZoomSnap = Globals | "mandatory" | "none" | "proximity" | string;

export type Property$MsContentZoomSnapPoints = Globals | string;

export type Property$MsContentZoomSnapType = Globals | "mandatory" | "none" | "proximity";

export type Property$MsContentZooming = Globals | "none" | "zoom";

export type Property$MsFilter = Globals | string;

export type Property$MsFlowFrom = Globals | "none" | string;

export type Property$MsFlowInto = Globals | "none" | string;

export type Property$MsGridColumns<TLength = string | 0> = Globals | DataType$TrackBreadth<TLength> | "none" | string;

export type Property$MsGridRows<TLength = string | 0> = Globals | DataType$TrackBreadth<TLength> | "none" | string;

export type Property$MsHighContrastAdjust = Globals | "auto" | "none";

export type Property$MsHyphenateLimitChars = Globals | "auto" | string | number;

export type Property$MsHyphenateLimitLines = Globals | "no-limit" | number;

export type Property$MsHyphenateLimitZone<TLength = string | 0> = Globals | TLength | string;

export type Property$MsImeAlign = Globals | "after" | "auto";

export type Property$MsOverflowStyle = Globals | "-ms-autohiding-scrollbar" | "auto" | "none" | "scrollbar";

export type Property$MsScrollChaining = Globals | "chained" | "none";

export type Property$MsScrollLimit = Globals | string;

export type Property$MsScrollLimitXMax<TLength = string | 0> = Globals | TLength | "auto";

export type Property$MsScrollLimitXMin<TLength = string | 0> = Globals | TLength;

export type Property$MsScrollLimitYMax<TLength = string | 0> = Globals | TLength | "auto";

export type Property$MsScrollLimitYMin<TLength = string | 0> = Globals | TLength;

export type Property$MsScrollRails = Globals | "none" | "railed";

export type Property$MsScrollSnapPointsX = Globals | string;

export type Property$MsScrollSnapPointsY = Globals | string;

export type Property$MsScrollSnapType = Globals | "mandatory" | "none" | "proximity";

export type Property$MsScrollSnapX = Globals | string;

export type Property$MsScrollSnapY = Globals | string;

export type Property$MsScrollTranslation = Globals | "none" | "vertical-to-horizontal";

export type Property$MsScrollbar3dlightColor = Globals | DataType$Color;

export type Property$MsScrollbarArrowColor = Globals | DataType$Color;

export type Property$MsScrollbarBaseColor = Globals | DataType$Color;

export type Property$MsScrollbarDarkshadowColor = Globals | DataType$Color;

export type Property$MsScrollbarFaceColor = Globals | DataType$Color;

export type Property$MsScrollbarHighlightColor = Globals | DataType$Color;

export type Property$MsScrollbarShadowColor = Globals | DataType$Color;

export type Property$MsScrollbarTrackColor = Globals | DataType$Color;

export type Property$MsTextAutospace = Globals | "ideograph-alpha" | "ideograph-numeric" | "ideograph-parenthesis" | "ideograph-space" | "none";

export type Property$MsTouchSelect = Globals | "grippers" | "none";

export type Property$MsUserSelect = Globals | "element" | "none" | "text";

export type Property$MsWrapFlow = Globals | "auto" | "both" | "clear" | "end" | "maximum" | "start";

export type Property$MsWrapMargin<TLength = string | 0> = Globals | TLength;

export type Property$MsWrapThrough = Globals | "none" | "wrap";

export type Property$WebkitAppearance =
  | Globals
  | "-apple-pay-button"
  | "button"
  | "button-bevel"
  | "caret"
  | "checkbox"
  | "default-button"
  | "inner-spin-button"
  | "listbox"
  | "listitem"
  | "media-controls-background"
  | "media-controls-fullscreen-background"
  | "media-current-time-display"
  | "media-enter-fullscreen-button"
  | "media-exit-fullscreen-button"
  | "media-fullscreen-button"
  | "media-mute-button"
  | "media-overlay-play-button"
  | "media-play-button"
  | "media-seek-back-button"
  | "media-seek-forward-button"
  | "media-slider"
  | "media-sliderthumb"
  | "media-time-remaining-display"
  | "media-toggle-closed-captions-button"
  | "media-volume-slider"
  | "media-volume-slider-container"
  | "media-volume-sliderthumb"
  | "menulist"
  | "menulist-button"
  | "menulist-text"
  | "menulist-textfield"
  | "meter"
  | "none"
  | "progress-bar"
  | "progress-bar-value"
  | "push-button"
  | "radio"
  | "searchfield"
  | "searchfield-cancel-button"
  | "searchfield-decoration"
  | "searchfield-results-button"
  | "searchfield-results-decoration"
  | "slider-horizontal"
  | "slider-vertical"
  | "sliderthumb-horizontal"
  | "sliderthumb-vertical"
  | "square-button"
  | "textarea"
  | "textfield";

export type Property$WebkitBorderBefore<TLength = string | 0> = Globals | DataType$LineWidth<TLength> | DataType$LineStyle | DataType$Color | string;

export type Property$WebkitBorderBeforeColor = Globals | DataType$Color;

export type Property$WebkitBorderBeforeStyle = Globals | DataType$LineStyle | string;

export type Property$WebkitBorderBeforeWidth<TLength = string | 0> = Globals | DataType$LineWidth<TLength> | string;

export type Property$WebkitBoxReflect<TLength = string | 0> = Globals | TLength | "above" | "below" | "left" | "right" | string;

export type Property$WebkitLineClamp = Globals | "none" | number;

export type Property$WebkitMask<TLength = string | 0> =
  | Globals
  | DataType$Position<TLength>
  | DataType$RepeatStyle
  | DataType$Box
  | "border"
  | "content"
  | "none"
  | "padding"
  | "text"
  | string;

export type Property$WebkitMaskAttachment = Globals | DataType$Attachment | string;

export type Property$WebkitMaskClip = Globals | DataType$Box | "border" | "content" | "padding" | "text" | string;

export type Property$WebkitMaskComposite = Globals | DataType$CompositeStyle | string;

export type Property$WebkitMaskImage = Globals | "none" | string;

export type Property$WebkitMaskOrigin = Globals | DataType$Box | "border" | "content" | "padding" | string;

export type Property$WebkitMaskPosition<TLength = string | 0> = Globals | DataType$Position<TLength> | string;

export type Property$WebkitMaskPositionX<TLength = string | 0> = Globals | TLength | "center" | "left" | "right" | string;

export type Property$WebkitMaskPositionY<TLength = string | 0> = Globals | TLength | "bottom" | "center" | "top" | string;

export type Property$WebkitMaskRepeat = Globals | DataType$RepeatStyle | string;

export type Property$WebkitMaskRepeatX = Globals | "no-repeat" | "repeat" | "round" | "space";

export type Property$WebkitMaskRepeatY = Globals | "no-repeat" | "repeat" | "round" | "space";

export type Property$WebkitMaskSize<TLength = string | 0> = Globals | DataType$BgSize<TLength> | string;

export type Property$WebkitOverflowScrolling = Globals | "auto" | "touch";

export type Property$WebkitTapHighlightColor = Globals | DataType$Color;

export type Property$WebkitTextFillColor = Globals | DataType$Color;

export type Property$WebkitTextStroke<TLength = string | 0> = Globals | DataType$Color | TLength | string;

export type Property$WebkitTextStrokeColor = Globals | DataType$Color;

export type Property$WebkitTextStrokeWidth<TLength = string | 0> = Globals | TLength;

export type Property$WebkitTouchCallout = Globals | "default" | "none";

export type Property$WebkitUserModify = Globals | "read-only" | "read-write" | "read-write-plaintext-only";

export type Property$AlignmentBaseline =
  | Globals
  | "after-edge"
  | "alphabetic"
  | "auto"
  | "baseline"
  | "before-edge"
  | "central"
  | "hanging"
  | "ideographic"
  | "mathematical"
  | "middle"
  | "text-after-edge"
  | "text-before-edge";

export type Property$BaselineShift<TLength = string | 0> = Globals | TLength | "baseline" | "sub" | "super" | string;

export type Property$ClipRule = Globals | "evenodd" | "nonzero";

export type Property$ColorInterpolation = Globals | "auto" | "linearRGB" | "sRGB";

export type Property$ColorRendering = Globals | "auto" | "optimizeQuality" | "optimizeSpeed";

export type Property$DominantBaseline =
  | Globals
  | "alphabetic"
  | "auto"
  | "central"
  | "hanging"
  | "ideographic"
  | "mathematical"
  | "middle"
  | "no-change"
  | "reset-size"
  | "text-after-edge"
  | "text-before-edge"
  | "use-script";

export type Property$Fill = Globals | DataType$Paint;

export type Property$FillOpacity = Globals | number;

export type Property$FillRule = Globals | "evenodd" | "nonzero";

export type Property$FloodColor = Globals | DataType$Color | "currentColor";

export type Property$FloodOpacity = Globals | number;

export type Property$GlyphOrientationVertical = Globals | "auto" | string | number;

export type Property$LightingColor = Globals | DataType$Color | "currentColor";

export type Property$Marker = Globals | "none" | string;

export type Property$MarkerEnd = Globals | "none" | string;

export type Property$MarkerMid = Globals | "none" | string;

export type Property$MarkerStart = Globals | "none" | string;

export type Property$ShapeRendering = Globals | "auto" | "crispEdges" | "geometricPrecision" | "optimizeSpeed";

export type Property$StopColor = Globals | DataType$Color | "currentColor";

export type Property$StopOpacity = Globals | number;

export type Property$Stroke = Globals | DataType$Paint;

export type Property$StrokeDasharray<TLength = string | 0> = Globals | DataType$Dasharray<TLength> | "none";

export type Property$StrokeDashoffset<TLength = string | 0> = Globals | TLength | string;

export type Property$StrokeLinecap = Globals | "butt" | "round" | "square";

export type Property$StrokeLinejoin = Globals | "bevel" | "miter" | "round";

export type Property$StrokeMiterlimit = Globals | number;

export type Property$StrokeOpacity = Globals | number;

export type Property$StrokeWidth<TLength = string | 0> = Globals | TLength | string;

export type Property$TextAnchor = Globals | "end" | "middle" | "start";

export type Property$VectorEffect = Globals | "non-scaling-stroke" | "none";

export type AtRule$CounterStyle<TLength = string | 0, TTime = string> = {|
  additiveSymbols?: string,
  fallback?: string,
  negative?: string,
  pad?: string,
  prefix?: string,
  range?: AtRule$Range,
  speakAs?: AtRule$SpeakAs,
  suffix?: string,
  symbols?: string,
  system?: AtRule$System,
|};

export type AtRule$CounterStyleHyphen<TLength = string | 0, TTime = string> = {|
  "additive-symbols"?: string,
  fallback?: string,
  negative?: string,
  pad?: string,
  prefix?: string,
  range?: AtRule$Range,
  "speak-as"?: AtRule$SpeakAs,
  suffix?: string,
  symbols?: string,
  system?: AtRule$System,
|};

export type AtRule$CounterStyleFallback<TLength = string | 0, TTime = string> = {|
  additiveSymbols?: string | Array<string>,
  fallback?: string | Array<string>,
  negative?: string | Array<string>,
  pad?: string | Array<string>,
  prefix?: string | Array<string>,
  range?: AtRule$Range | Array<AtRule$Range>,
  speakAs?: AtRule$SpeakAs | Array<AtRule$SpeakAs>,
  suffix?: string | Array<string>,
  symbols?: string | Array<string>,
  system?: AtRule$System | Array<AtRule$System>,
|};

export type AtRule$CounterStyleHyphenFallback<TLength = string | 0, TTime = string> = {|
  "additive-symbols"?: string | Array<string>,
  fallback?: string | Array<string>,
  negative?: string | Array<string>,
  pad?: string | Array<string>,
  prefix?: string | Array<string>,
  range?: AtRule$Range | Array<AtRule$Range>,
  "speak-as"?: AtRule$SpeakAs | Array<AtRule$SpeakAs>,
  suffix?: string | Array<string>,
  symbols?: string | Array<string>,
  system?: AtRule$System | Array<AtRule$System>,
|};

export type AtRule$FontFace<TLength = string | 0, TTime = string> = {|
  MozFontFeatureSettings?: AtRule$FontFeatureSettings,
  ascentOverride?: AtRule$AscentOverride,
  descentOverride?: AtRule$DescentOverride,
  fontDisplay?: AtRule$FontDisplay,
  fontFamily?: string,
  fontFeatureSettings?: AtRule$FontFeatureSettings,
  fontStretch?: AtRule$FontStretch,
  fontStyle?: AtRule$FontStyle,
  fontVariant?: AtRule$FontVariant,
  fontVariationSettings?: AtRule$FontVariationSettings,
  fontWeight?: AtRule$FontWeight,
  lineGapOverride?: AtRule$LineGapOverride,
  sizeAdjust?: string,
  src?: string,
  unicodeRange?: string,
|};

export type AtRule$FontFaceHyphen<TLength = string | 0, TTime = string> = {|
  "-moz-font-feature-settings"?: AtRule$FontFeatureSettings,
  "ascent-override"?: AtRule$AscentOverride,
  "descent-override"?: AtRule$DescentOverride,
  "font-display"?: AtRule$FontDisplay,
  "font-family"?: string,
  "font-feature-settings"?: AtRule$FontFeatureSettings,
  "font-stretch"?: AtRule$FontStretch,
  "font-style"?: AtRule$FontStyle,
  "font-variant"?: AtRule$FontVariant,
  "font-variation-settings"?: AtRule$FontVariationSettings,
  "font-weight"?: AtRule$FontWeight,
  "line-gap-override"?: AtRule$LineGapOverride,
  "size-adjust"?: string,
  src?: string,
  "unicode-range"?: string,
|};

export type AtRule$FontFaceFallback<TLength = string | 0, TTime = string> = {|
  MozFontFeatureSettings?: AtRule$FontFeatureSettings | Array<AtRule$FontFeatureSettings>,
  ascentOverride?: AtRule$AscentOverride | Array<AtRule$AscentOverride>,
  descentOverride?: AtRule$DescentOverride | Array<AtRule$DescentOverride>,
  fontDisplay?: AtRule$FontDisplay | Array<AtRule$FontDisplay>,
  fontFamily?: string | Array<string>,
  fontFeatureSettings?: AtRule$FontFeatureSettings | Array<AtRule$FontFeatureSettings>,
  fontStretch?: AtRule$FontStretch | Array<AtRule$FontStretch>,
  fontStyle?: AtRule$FontStyle | Array<AtRule$FontStyle>,
  fontVariant?: AtRule$FontVariant | Array<AtRule$FontVariant>,
  fontVariationSettings?: AtRule$FontVariationSettings | Array<AtRule$FontVariationSettings>,
  fontWeight?: AtRule$FontWeight | Array<AtRule$FontWeight>,
  lineGapOverride?: AtRule$LineGapOverride | Array<AtRule$LineGapOverride>,
  sizeAdjust?: string | Array<string>,
  src?: string | Array<string>,
  unicodeRange?: string | Array<string>,
|};

export type AtRule$FontFaceHyphenFallback<TLength = string | 0, TTime = string> = {|
  "-moz-font-feature-settings"?: AtRule$FontFeatureSettings | Array<AtRule$FontFeatureSettings>,
  "ascent-override"?: AtRule$AscentOverride | Array<AtRule$AscentOverride>,
  "descent-override"?: AtRule$DescentOverride | Array<AtRule$DescentOverride>,
  "font-display"?: AtRule$FontDisplay | Array<AtRule$FontDisplay>,
  "font-family"?: string | Array<string>,
  "font-feature-settings"?: AtRule$FontFeatureSettings | Array<AtRule$FontFeatureSettings>,
  "font-stretch"?: AtRule$FontStretch | Array<AtRule$FontStretch>,
  "font-style"?: AtRule$FontStyle | Array<AtRule$FontStyle>,
  "font-variant"?: AtRule$FontVariant | Array<AtRule$FontVariant>,
  "font-variation-settings"?: AtRule$FontVariationSettings | Array<AtRule$FontVariationSettings>,
  "font-weight"?: AtRule$FontWeight | Array<AtRule$FontWeight>,
  "line-gap-override"?: AtRule$LineGapOverride | Array<AtRule$LineGapOverride>,
  "size-adjust"?: string | Array<string>,
  src?: string | Array<string>,
  "unicode-range"?: string | Array<string>,
|};

export type AtRule$FontPaletteValues<TLength = string | 0, TTime = string> = {|
  basePalette?: AtRule$BasePalette,
  fontFamily?: string,
  overrideColors?: string,
|};

export type AtRule$FontPaletteValuesHyphen<TLength = string | 0, TTime = string> = {|
  "base-palette"?: AtRule$BasePalette,
  "font-family"?: string,
  "override-colors"?: string,
|};

export type AtRule$FontPaletteValuesFallback<TLength = string | 0, TTime = string> = {|
  basePalette?: AtRule$BasePalette | Array<AtRule$BasePalette>,
  fontFamily?: string | Array<string>,
  overrideColors?: string | Array<string>,
|};

export type AtRule$FontPaletteValuesHyphenFallback<TLength = string | 0, TTime = string> = {|
  "base-palette"?: AtRule$BasePalette | Array<AtRule$BasePalette>,
  "font-family"?: string | Array<string>,
  "override-colors"?: string | Array<string>,
|};

export type AtRule$Page<TLength = string | 0, TTime = string> = {|
  bleed?: AtRule$Bleed<TLength>,
  marks?: AtRule$Marks,
  pageOrientation?: AtRule$PageOrientation,
  size?: AtRule$Size<TLength>,
|};

export type AtRule$PageHyphen<TLength = string | 0, TTime = string> = {|
  bleed?: AtRule$Bleed<TLength>,
  marks?: AtRule$Marks,
  "page-orientation"?: AtRule$PageOrientation,
  size?: AtRule$Size<TLength>,
|};

export type AtRule$PageFallback<TLength = string | 0, TTime = string> = {|
  bleed?: AtRule$Bleed<TLength> | Array<AtRule$Bleed<TLength>>,
  marks?: AtRule$Marks | Array<AtRule$Marks>,
  pageOrientation?: AtRule$PageOrientation | Array<AtRule$PageOrientation>,
  size?: AtRule$Size<TLength> | Array<AtRule$Size<TLength>>,
|};

export type AtRule$PageHyphenFallback<TLength = string | 0, TTime = string> = {|
  bleed?: AtRule$Bleed<TLength> | Array<AtRule$Bleed<TLength>>,
  marks?: AtRule$Marks | Array<AtRule$Marks>,
  "page-orientation"?: AtRule$PageOrientation | Array<AtRule$PageOrientation>,
  size?: AtRule$Size<TLength> | Array<AtRule$Size<TLength>>,
|};

export type AtRule$Property<TLength = string | 0, TTime = string> = {|
  inherits?: AtRule$Inherits,
  initialValue?: string,
  syntax?: string,
|};

export type AtRule$PropertyHyphen<TLength = string | 0, TTime = string> = {|
  inherits?: AtRule$Inherits,
  "initial-value"?: string,
  syntax?: string,
|};

export type AtRule$PropertyFallback<TLength = string | 0, TTime = string> = {|
  inherits?: AtRule$Inherits | Array<AtRule$Inherits>,
  initialValue?: string | Array<string>,
  syntax?: string | Array<string>,
|};

export type AtRule$PropertyHyphenFallback<TLength = string | 0, TTime = string> = {|
  inherits?: AtRule$Inherits | Array<AtRule$Inherits>,
  "initial-value"?: string | Array<string>,
  syntax?: string | Array<string>,
|};

export type AtRule$Viewport<TLength = string | 0, TTime = string> = {|
  height?: AtRule$Height<TLength>,
  maxHeight?: AtRule$MaxHeight<TLength>,
  maxWidth?: AtRule$MaxWidth<TLength>,
  maxZoom?: AtRule$MaxZoom,
  minHeight?: AtRule$MinHeight<TLength>,
  minWidth?: AtRule$MinWidth<TLength>,
  minZoom?: AtRule$MinZoom,
  orientation?: AtRule$Orientation,
  userZoom?: AtRule$UserZoom,
  viewportFit?: AtRule$ViewportFit,
  width?: AtRule$Width<TLength>,
  zoom?: AtRule$Zoom,
|};

export type AtRule$ViewportHyphen<TLength = string | 0, TTime = string> = {|
  height?: AtRule$Height<TLength>,
  "max-height"?: AtRule$MaxHeight<TLength>,
  "max-width"?: AtRule$MaxWidth<TLength>,
  "max-zoom"?: AtRule$MaxZoom,
  "min-height"?: AtRule$MinHeight<TLength>,
  "min-width"?: AtRule$MinWidth<TLength>,
  "min-zoom"?: AtRule$MinZoom,
  orientation?: AtRule$Orientation,
  "user-zoom"?: AtRule$UserZoom,
  "viewport-fit"?: AtRule$ViewportFit,
  width?: AtRule$Width<TLength>,
  zoom?: AtRule$Zoom,
|};

export type AtRule$ViewportFallback<TLength = string | 0, TTime = string> = {|
  height?: AtRule$Height<TLength> | Array<AtRule$Height<TLength>>,
  maxHeight?: AtRule$MaxHeight<TLength> | Array<AtRule$MaxHeight<TLength>>,
  maxWidth?: AtRule$MaxWidth<TLength> | Array<AtRule$MaxWidth<TLength>>,
  maxZoom?: AtRule$MaxZoom | Array<AtRule$MaxZoom>,
  minHeight?: AtRule$MinHeight<TLength> | Array<AtRule$MinHeight<TLength>>,
  minWidth?: AtRule$MinWidth<TLength> | Array<AtRule$MinWidth<TLength>>,
  minZoom?: AtRule$MinZoom | Array<AtRule$MinZoom>,
  orientation?: AtRule$Orientation | Array<AtRule$Orientation>,
  userZoom?: AtRule$UserZoom | Array<AtRule$UserZoom>,
  viewportFit?: AtRule$ViewportFit | Array<AtRule$ViewportFit>,
  width?: AtRule$Width<TLength> | Array<AtRule$Width<TLength>>,
  zoom?: AtRule$Zoom | Array<AtRule$Zoom>,
|};

export type AtRule$ViewportHyphenFallback<TLength = string | 0, TTime = string> = {|
  height?: AtRule$Height<TLength> | Array<AtRule$Height<TLength>>,
  "max-height"?: AtRule$MaxHeight<TLength> | Array<AtRule$MaxHeight<TLength>>,
  "max-width"?: AtRule$MaxWidth<TLength> | Array<AtRule$MaxWidth<TLength>>,
  "max-zoom"?: AtRule$MaxZoom | Array<AtRule$MaxZoom>,
  "min-height"?: AtRule$MinHeight<TLength> | Array<AtRule$MinHeight<TLength>>,
  "min-width"?: AtRule$MinWidth<TLength> | Array<AtRule$MinWidth<TLength>>,
  "min-zoom"?: AtRule$MinZoom | Array<AtRule$MinZoom>,
  orientation?: AtRule$Orientation | Array<AtRule$Orientation>,
  "user-zoom"?: AtRule$UserZoom | Array<AtRule$UserZoom>,
  "viewport-fit"?: AtRule$ViewportFit | Array<AtRule$ViewportFit>,
  width?: AtRule$Width<TLength> | Array<AtRule$Width<TLength>>,
  zoom?: AtRule$Zoom | Array<AtRule$Zoom>,
|};

type AtRule$Range = "auto" | string;

type AtRule$SpeakAs = "auto" | "bullets" | "numbers" | "spell-out" | "words" | string;

type AtRule$System = "additive" | "alphabetic" | "cyclic" | "fixed" | "numeric" | "symbolic" | string;

type AtRule$FontFeatureSettings = "normal" | string;

type AtRule$AscentOverride = "normal" | string;

type AtRule$DescentOverride = "normal" | string;

type AtRule$FontDisplay = "auto" | "block" | "fallback" | "optional" | "swap";

type AtRule$FontStretch = DataType$FontStretchAbsolute | string;

type AtRule$FontStyle = "italic" | "normal" | "oblique" | string;

type AtRule$FontVariant =
  | DataType$EastAsianVariantValues
  | "all-petite-caps"
  | "all-small-caps"
  | "common-ligatures"
  | "contextual"
  | "diagonal-fractions"
  | "discretionary-ligatures"
  | "full-width"
  | "historical-forms"
  | "historical-ligatures"
  | "lining-nums"
  | "no-common-ligatures"
  | "no-contextual"
  | "no-discretionary-ligatures"
  | "no-historical-ligatures"
  | "none"
  | "normal"
  | "oldstyle-nums"
  | "ordinal"
  | "petite-caps"
  | "proportional-nums"
  | "proportional-width"
  | "ruby"
  | "slashed-zero"
  | "small-caps"
  | "stacked-fractions"
  | "tabular-nums"
  | "titling-caps"
  | "unicase"
  | string;

type AtRule$FontVariationSettings = "normal" | string;

type AtRule$FontWeight = DataType$FontWeightAbsolute | string;

type AtRule$LineGapOverride = "normal" | string;

type AtRule$BasePalette = "dark" | "light" | number;

type AtRule$Bleed<TLength> = TLength | "auto";

type AtRule$Marks = "crop" | "cross" | "none" | string;

type AtRule$PageOrientation = "rotate-left" | "rotate-right" | "upright";

type AtRule$Size<TLength> = DataType$PageSize | TLength | "auto" | "landscape" | "portrait" | string;

type AtRule$Inherits = "false" | "true";

type AtRule$Height<TLength> = DataType$ViewportLength<TLength> | string;

type AtRule$MaxHeight<TLength> = DataType$ViewportLength<TLength>;

type AtRule$MaxWidth<TLength> = DataType$ViewportLength<TLength>;

type AtRule$MaxZoom = "auto" | string | number;

type AtRule$MinHeight<TLength> = DataType$ViewportLength<TLength>;

type AtRule$MinWidth<TLength> = DataType$ViewportLength<TLength>;

type AtRule$MinZoom = "auto" | string | number;

type AtRule$Orientation = "auto" | "landscape" | "portrait";

type AtRule$UserZoom = "fixed" | "zoom";

type AtRule$ViewportFit = "auto" | "contain" | "cover";

type AtRule$Width<TLength> = DataType$ViewportLength<TLength> | string;

type AtRule$Zoom = "auto" | string | number;

type DataType$AbsoluteSize = "large" | "medium" | "small" | "x-large" | "x-small" | "xx-large" | "xx-small" | "xxx-large";

type DataType$AnimateableFeature = "contents" | "scroll-position" | string;

type DataType$Attachment = "fixed" | "local" | "scroll";

type DataType$BgPosition<TLength> = TLength | "bottom" | "center" | "left" | "right" | "top" | string;

type DataType$BgSize<TLength> = TLength | "auto" | "contain" | "cover" | string;

type DataType$BlendMode =
  | "color"
  | "color-burn"
  | "color-dodge"
  | "darken"
  | "difference"
  | "exclusion"
  | "hard-light"
  | "hue"
  | "lighten"
  | "luminosity"
  | "multiply"
  | "normal"
  | "overlay"
  | "saturation"
  | "screen"
  | "soft-light";

type DataType$Box = "border-box" | "content-box" | "padding-box";

type DataType$Color = DataType$NamedColor | DataType$DeprecatedSystemColor | "currentcolor" | string;

type DataType$CompatAuto =
  | "button"
  | "checkbox"
  | "listbox"
  | "menulist"
  | "meter"
  | "progress-bar"
  | "push-button"
  | "radio"
  | "searchfield"
  | "slider-horizontal"
  | "square-button"
  | "textarea";

type DataType$CompositeStyle =
  | "clear"
  | "copy"
  | "destination-atop"
  | "destination-in"
  | "destination-out"
  | "destination-over"
  | "source-atop"
  | "source-in"
  | "source-out"
  | "source-over"
  | "xor";

type DataType$CompositingOperator = "add" | "exclude" | "intersect" | "subtract";

type DataType$ContentDistribution = "space-around" | "space-between" | "space-evenly" | "stretch";

type DataType$ContentList = DataType$Quote | "contents" | string;

type DataType$ContentPosition = "center" | "end" | "flex-end" | "flex-start" | "start";

type DataType$CubicBezierTimingFunction = "ease" | "ease-in" | "ease-in-out" | "ease-out" | string;

type DataType$Dasharray<TLength> = TLength | string | number;

type DataType$DeprecatedSystemColor =
  | "ActiveBorder"
  | "ActiveCaption"
  | "AppWorkspace"
  | "Background"
  | "ButtonFace"
  | "ButtonHighlight"
  | "ButtonShadow"
  | "ButtonText"
  | "CaptionText"
  | "GrayText"
  | "Highlight"
  | "HighlightText"
  | "InactiveBorder"
  | "InactiveCaption"
  | "InactiveCaptionText"
  | "InfoBackground"
  | "InfoText"
  | "Menu"
  | "MenuText"
  | "Scrollbar"
  | "ThreeDDarkShadow"
  | "ThreeDFace"
  | "ThreeDHighlight"
  | "ThreeDLightShadow"
  | "ThreeDShadow"
  | "Window"
  | "WindowFrame"
  | "WindowText";

type DataType$DisplayInside = "-ms-flexbox" | "-ms-grid" | "-webkit-flex" | "flex" | "flow" | "flow-root" | "grid" | "ruby" | "table";

type DataType$DisplayInternal =
  | "ruby-base"
  | "ruby-base-container"
  | "ruby-text"
  | "ruby-text-container"
  | "table-caption"
  | "table-cell"
  | "table-column"
  | "table-column-group"
  | "table-footer-group"
  | "table-header-group"
  | "table-row"
  | "table-row-group";

type DataType$DisplayLegacy =
  | "-ms-inline-flexbox"
  | "-ms-inline-grid"
  | "-webkit-inline-flex"
  | "inline-block"
  | "inline-flex"
  | "inline-grid"
  | "inline-list-item"
  | "inline-table";

type DataType$DisplayOutside = "block" | "inline" | "run-in";

type DataType$EasingFunction = DataType$CubicBezierTimingFunction | DataType$StepTimingFunction | "linear";

type DataType$EastAsianVariantValues = "jis04" | "jis78" | "jis83" | "jis90" | "simplified" | "traditional";

type DataType$FinalBgLayer<TLength> = DataType$Color | DataType$BgPosition<TLength> | DataType$RepeatStyle | DataType$Attachment | DataType$Box | "none" | string;

type DataType$FontStretchAbsolute =
  | "condensed"
  | "expanded"
  | "extra-condensed"
  | "extra-expanded"
  | "normal"
  | "semi-condensed"
  | "semi-expanded"
  | "ultra-condensed"
  | "ultra-expanded"
  | string;

type DataType$FontWeightAbsolute = "bold" | "normal" | number;

type DataType$GenericFamily = "cursive" | "fantasy" | "monospace" | "sans-serif" | "serif";

type DataType$GeometryBox = DataType$Box | "fill-box" | "margin-box" | "stroke-box" | "view-box";

type DataType$GridLine = "auto" | string | number;

type DataType$LineStyle = "dashed" | "dotted" | "double" | "groove" | "hidden" | "inset" | "none" | "outset" | "ridge" | "solid";

type DataType$LineWidth<TLength> = TLength | "medium" | "thick" | "thin";

type DataType$MaskLayer<TLength> =
  | DataType$Position<TLength>
  | DataType$RepeatStyle
  | DataType$GeometryBox
  | DataType$CompositingOperator
  | DataType$MaskingMode
  | "no-clip"
  | "none"
  | string;

type DataType$MaskingMode = "alpha" | "luminance" | "match-source";

type DataType$NamedColor =
  | "aliceblue"
  | "antiquewhite"
  | "aqua"
  | "aquamarine"
  | "azure"
  | "beige"
  | "bisque"
  | "black"
  | "blanchedalmond"
  | "blue"
  | "blueviolet"
  | "brown"
  | "burlywood"
  | "cadetblue"
  | "chartreuse"
  | "chocolate"
  | "coral"
  | "cornflowerblue"
  | "cornsilk"
  | "crimson"
  | "cyan"
  | "darkblue"
  | "darkcyan"
  | "darkgoldenrod"
  | "darkgray"
  | "darkgreen"
  | "darkgrey"
  | "darkkhaki"
  | "darkmagenta"
  | "darkolivegreen"
  | "darkorange"
  | "darkorchid"
  | "darkred"
  | "darksalmon"
  | "darkseagreen"
  | "darkslateblue"
  | "darkslategray"
  | "darkslategrey"
  | "darkturquoise"
  | "darkviolet"
  | "deeppink"
  | "deepskyblue"
  | "dimgray"
  | "dimgrey"
  | "dodgerblue"
  | "firebrick"
  | "floralwhite"
  | "forestgreen"
  | "fuchsia"
  | "gainsboro"
  | "ghostwhite"
  | "gold"
  | "goldenrod"
  | "gray"
  | "green"
  | "greenyellow"
  | "grey"
  | "honeydew"
  | "hotpink"
  | "indianred"
  | "indigo"
  | "ivory"
  | "khaki"
  | "lavender"
  | "lavenderblush"
  | "lawngreen"
  | "lemonchiffon"
  | "lightblue"
  | "lightcoral"
  | "lightcyan"
  | "lightgoldenrodyellow"
  | "lightgray"
  | "lightgreen"
  | "lightgrey"
  | "lightpink"
  | "lightsalmon"
  | "lightseagreen"
  | "lightskyblue"
  | "lightslategray"
  | "lightslategrey"
  | "lightsteelblue"
  | "lightyellow"
  | "lime"
  | "limegreen"
  | "linen"
  | "magenta"
  | "maroon"
  | "mediumaquamarine"
  | "mediumblue"
  | "mediumorchid"
  | "mediumpurple"
  | "mediumseagreen"
  | "mediumslateblue"
  | "mediumspringgreen"
  | "mediumturquoise"
  | "mediumvioletred"
  | "midnightblue"
  | "mintcream"
  | "mistyrose"
  | "moccasin"
  | "navajowhite"
  | "navy"
  | "oldlace"
  | "olive"
  | "olivedrab"
  | "orange"
  | "orangered"
  | "orchid"
  | "palegoldenrod"
  | "palegreen"
  | "paleturquoise"
  | "palevioletred"
  | "papayawhip"
  | "peachpuff"
  | "peru"
  | "pink"
  | "plum"
  | "powderblue"
  | "purple"
  | "rebeccapurple"
  | "red"
  | "rosybrown"
  | "royalblue"
  | "saddlebrown"
  | "salmon"
  | "sandybrown"
  | "seagreen"
  | "seashell"
  | "sienna"
  | "silver"
  | "skyblue"
  | "slateblue"
  | "slategray"
  | "slategrey"
  | "snow"
  | "springgreen"
  | "steelblue"
  | "tan"
  | "teal"
  | "thistle"
  | "tomato"
  | "transparent"
  | "turquoise"
  | "violet"
  | "wheat"
  | "white"
  | "whitesmoke"
  | "yellow"
  | "yellowgreen";

type DataType$PageSize = "A3" | "A4" | "A5" | "B4" | "B5" | "JIS-B4" | "JIS-B5" | "ledger" | "legal" | "letter";

type DataType$Paint = DataType$Color | "child" | "context-fill" | "context-stroke" | "none" | string;

type DataType$Position<TLength> = TLength | "bottom" | "center" | "left" | "right" | "top" | string;

type DataType$Quote = "close-quote" | "no-close-quote" | "no-open-quote" | "open-quote";

type DataType$RepeatStyle = "no-repeat" | "repeat" | "repeat-x" | "repeat-y" | "round" | "space" | string;

type DataType$SelfPosition = "center" | "end" | "flex-end" | "flex-start" | "self-end" | "self-start" | "start";

type DataType$SingleAnimation<TTime> =
  | DataType$EasingFunction
  | DataType$SingleAnimationDirection
  | DataType$SingleAnimationFillMode
  | DataType$SingleAnimationTimeline
  | TTime
  | "infinite"
  | "none"
  | "paused"
  | "running"
  | string
  | number;

type DataType$SingleAnimationComposition = "accumulate" | "add" | "replace";

type DataType$SingleAnimationDirection = "alternate" | "alternate-reverse" | "normal" | "reverse";

type DataType$SingleAnimationFillMode = "backwards" | "both" | "forwards" | "none";

type DataType$SingleAnimationTimeline = "auto" | "none" | string;

type DataType$SingleTransition<TTime> = DataType$EasingFunction | TTime | "all" | "allow-discrete" | "none" | "normal" | string;

type DataType$StepTimingFunction = "step-end" | "step-start" | string;

type DataType$TimelineRangeName = "contain" | "cover" | "entry" | "entry-crossing" | "exit" | "exit-crossing";

type DataType$TrackBreadth<TLength> = TLength | "auto" | "max-content" | "min-content" | string;

type DataType$ViewportLength<TLength> = TLength | "auto" | string;

type DataType$VisualBox = "border-box" | "content-box" | "padding-box";
