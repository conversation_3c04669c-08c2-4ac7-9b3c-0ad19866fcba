/**
 * This file should contain behavior for functional keys as described here:
 * https://w3c.github.io/uievents-code/#key-alphanumeric-functional
 */
import { behaviorPlugin } from '../types';
export declare const preKeydownBehavior: behaviorPlugin[];
export declare const keydownBehavior: behaviorPlugin[];
export declare const keypressBehavior: behaviorPlugin[];
export declare const preKeyupBehavior: behaviorPlugin[];
export declare const keyupBehavior: behaviorPlugin[];
export declare const postKeyupBehavior: behaviorPlugin[];
