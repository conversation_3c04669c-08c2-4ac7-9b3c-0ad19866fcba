{"version": 3, "file": "accessible-description.js", "names": ["_accessibleNameAndDescription", "require", "_util", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "call", "TypeError", "Number", "computeAccessibleDescription", "root", "options", "description", "queryIdRefs", "map", "element", "computeTextAlternative", "compute", "join", "ariaDescription", "getAttribute", "title"], "sources": ["../sources/accessible-description.ts"], "sourcesContent": ["import {\n\tcomputeTextAlternative,\n\tComputeTextAlternativeOptions,\n} from \"./accessible-name-and-description\";\nimport { queryIdRefs } from \"./util\";\n\n/**\n * @param root\n * @param options\n * @returns\n */\nexport function computeAccessibleDescription(\n\troot: Element,\n\toptions: ComputeTextAlternativeOptions = {},\n): string {\n\tlet description = queryIdRefs(root, \"aria-describedby\")\n\t\t.map((element) => {\n\t\t\treturn computeTextAlternative(element, {\n\t\t\t\t...options,\n\t\t\t\tcompute: \"description\",\n\t\t\t});\n\t\t})\n\t\t.join(\" \");\n\n\t// TODO: Technically we need to make sure that node wasn't used for the accessible name\n\t//       This causes `description_1.0_combobox-focusable-manual` to fail\n\n\t// https://w3c.github.io/aria/#aria-description\n\t// mentions that aria-description should only be calculated if aria-describedby didn't provide\n\t// a description\n\tif (description === \"\") {\n\t\tconst ariaDescription = root.getAttribute(\"aria-description\");\n\t\tdescription = ariaDescription === null ? \"\" : ariaDescription;\n\t}\n\n\t// https://www.w3.org/TR/html-aam-1.0/#accessible-name-and-description-computation\n\t// says for so many elements to use the `title` that we assume all elements are considered\n\tif (description === \"\") {\n\t\tconst title = root.getAttribute(\"title\");\n\t\tdescription = title === null ? \"\" : title;\n\t}\n\n\treturn description;\n}\n"], "mappings": ";;;;AAAA,IAAAA,6BAAA,GAAAC,OAAA;AAIA,IAAAC,KAAA,GAAAD,OAAA;AAAqC,SAAAE,QAAAC,CAAA,sCAAAD,OAAA,wBAAAE,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAF,CAAA,kBAAAA,CAAA,gBAAAA,CAAA,WAAAA,CAAA,yBAAAC,MAAA,IAAAD,CAAA,CAAAG,WAAA,KAAAF,MAAA,IAAAD,CAAA,KAAAC,MAAA,CAAAG,SAAA,qBAAAJ,CAAA,KAAAD,OAAA,CAAAC,CAAA;AAAA,SAAAK,QAAAC,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAJ,CAAA,OAAAG,MAAA,CAAAE,qBAAA,QAAAX,CAAA,GAAAS,MAAA,CAAAE,qBAAA,CAAAL,CAAA,GAAAC,CAAA,KAAAP,CAAA,GAAAA,CAAA,CAAAY,MAAA,WAAAL,CAAA,WAAAE,MAAA,CAAAI,wBAAA,CAAAP,CAAA,EAAAC,CAAA,EAAAO,UAAA,OAAAN,CAAA,CAAAO,IAAA,CAAAC,KAAA,CAAAR,CAAA,EAAAR,CAAA,YAAAQ,CAAA;AAAA,SAAAS,cAAAX,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAW,SAAA,CAAAC,MAAA,EAAAZ,CAAA,UAAAC,CAAA,WAAAU,SAAA,CAAAX,CAAA,IAAAW,SAAA,CAAAX,CAAA,QAAAA,CAAA,OAAAF,OAAA,CAAAI,MAAA,CAAAD,CAAA,OAAAY,OAAA,WAAAb,CAAA,IAAAc,eAAA,CAAAf,CAAA,EAAAC,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAa,yBAAA,GAAAb,MAAA,CAAAc,gBAAA,CAAAjB,CAAA,EAAAG,MAAA,CAAAa,yBAAA,CAAAd,CAAA,KAAAH,OAAA,CAAAI,MAAA,CAAAD,CAAA,GAAAY,OAAA,WAAAb,CAAA,IAAAE,MAAA,CAAAe,cAAA,CAAAlB,CAAA,EAAAC,CAAA,EAAAE,MAAA,CAAAI,wBAAA,CAAAL,CAAA,EAAAD,CAAA,iBAAAD,CAAA;AAAA,SAAAe,gBAAAI,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAhB,MAAA,CAAAe,cAAA,CAAAC,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAb,UAAA,QAAAe,YAAA,QAAAC,QAAA,oBAAAL,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAG,GAAA,QAAAL,GAAA,GAAAM,YAAA,CAAAD,GAAA,oBAAAhC,OAAA,CAAA2B,GAAA,iBAAAA,GAAA,GAAAO,MAAA,CAAAP,GAAA;AAAA,SAAAM,aAAAE,KAAA,EAAAC,IAAA,QAAApC,OAAA,CAAAmC,KAAA,kBAAAA,KAAA,kBAAAA,KAAA,MAAAE,IAAA,GAAAF,KAAA,CAAAjC,MAAA,CAAAoC,WAAA,OAAAD,IAAA,KAAAE,SAAA,QAAAC,GAAA,GAAAH,IAAA,CAAAI,IAAA,CAAAN,KAAA,EAAAC,IAAA,oBAAApC,OAAA,CAAAwC,GAAA,uBAAAA,GAAA,YAAAE,SAAA,4DAAAN,IAAA,gBAAAF,MAAA,GAAAS,MAAA,EAAAR,KAAA;AAErC;AACA;AACA;AACA;AACA;AACO,SAASS,4BAA4BA,CAC3CC,IAAa,EAEJ;EAAA,IADTC,OAAsC,GAAA3B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAoB,SAAA,GAAApB,SAAA,MAAG,CAAC,CAAC;EAE3C,IAAI4B,WAAW,GAAG,IAAAC,iBAAW,EAACH,IAAI,EAAE,kBAAkB,CAAC,CACrDI,GAAG,CAAC,UAACC,OAAO,EAAK;IACjB,OAAO,IAAAC,oDAAsB,EAACD,OAAO,EAAAhC,aAAA,CAAAA,aAAA,KACjC4B,OAAO;MACVM,OAAO,EAAE;IAAa,EACtB,CAAC;EACH,CAAC,CAAC,CACDC,IAAI,CAAC,GAAG,CAAC;;EAEX;EACA;;EAEA;EACA;EACA;EACA,IAAIN,WAAW,KAAK,EAAE,EAAE;IACvB,IAAMO,eAAe,GAAGT,IAAI,CAACU,YAAY,CAAC,kBAAkB,CAAC;IAC7DR,WAAW,GAAGO,eAAe,KAAK,IAAI,GAAG,EAAE,GAAGA,eAAe;EAC9D;;EAEA;EACA;EACA,IAAIP,WAAW,KAAK,EAAE,EAAE;IACvB,IAAMS,KAAK,GAAGX,IAAI,CAACU,YAAY,CAAC,OAAO,CAAC;IACxCR,WAAW,GAAGS,KAAK,KAAK,IAAI,GAAG,EAAE,GAAGA,KAAK;EAC1C;EAEA,OAAOT,WAAW;AACnB"}