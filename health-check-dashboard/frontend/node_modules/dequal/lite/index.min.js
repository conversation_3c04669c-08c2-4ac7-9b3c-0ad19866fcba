!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(e.dequal={})}(this,(function(e){var t=Object.prototype.hasOwnProperty;e.dequal=function e(r,n){var o,i;if(r===n)return!0;if(r&&n&&(o=r.constructor)===n.constructor){if(o===Date)return r.getTime()===n.getTime();if(o===RegExp)return r.toString()===n.toString();if(o===Array){if((i=r.length)===n.length)for(;i--&&e(r[i],n[i]););return-1===i}if(!o||"object"==typeof r){for(o in i=0,r){if(t.call(r,o)&&++i&&!t.call(n,o))return!1;if(!(o in n)||!e(r[o],n[o]))return!1}return Object.keys(n).length===i}}return r!=r&&n!=n}}));