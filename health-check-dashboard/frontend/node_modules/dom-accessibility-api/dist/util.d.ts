export { getLocalN<PERSON> } from "./getRole";
export declare function isElement(node: Node | null): node is Element;
export declare function isHTMLTableCaptionElement(node: Node | null): node is HTMLTableCaptionElement;
export declare function isHTMLInputElement(node: Node | null): node is HTMLInputElement;
export declare function isHTMLOptGroupElement(node: Node | null): node is HTMLOptGroupElement;
export declare function isHTMLSelectElement(node: Node | null): node is HTMLSelectElement;
export declare function isHTMLTableElement(node: Node | null): node is HTMLTableElement;
export declare function isHTMLTextAreaElement(node: Node | null): node is HTMLTextAreaElement;
export declare function safeWindow(node: Node): Window;
export declare function isHTMLFieldSetElement(node: Node | null): node is HTMLFieldSetElement;
export declare function isHTMLLegendElement(node: Node | null): node is HTMLLegendElement;
export declare function isHTMLSlotElement(node: Node | null): node is HTMLSlotElement;
export declare function isSVGElement(node: Node | null): node is SVGElement;
export declare function isSVGSVGElement(node: Node | null): node is SVGSVGElement;
export declare function isSVGTitleElement(node: Node | null): node is SVGTitleElement;
/**
 *
 * @param {Node} node -
 * @param {string} attributeName -
 * @returns {Element[]} -
 */
export declare function queryIdRefs(node: Node, attributeName: string): Element[];
export declare function hasAnyConcreteRoles(node: Node, roles: Array<string | null>): node is Element;
//# sourceMappingURL=util.d.ts.map