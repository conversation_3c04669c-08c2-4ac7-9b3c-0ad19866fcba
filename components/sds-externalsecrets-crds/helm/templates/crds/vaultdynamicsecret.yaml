apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    {{- with .Values.crds.annotations }}
    {{- toYaml . | nindent 4}}
    {{- end }}
    controller-gen.kubebuilder.io/version: v0.17.2
  labels:
    external-secrets.io/component: controller
  name: vaultdynamicsecrets.generators.external-secrets.io
spec:
  group: generators.external-secrets.io
  names:
    categories:
      - external-secrets
      - external-secrets-generators
    kind: VaultDynamicSecret
    listKind: VaultDynamicSecretList
    plural: vaultdynamicsecrets
    singular: vaultdynamicsecret
  scope: Namespaced
  versions:
    - name: v1alpha1
      schema:
        openAPIV3Schema:
          properties:
            apiVersion:
              description: |-
                APIVersion defines the versioned schema of this representation of an object.
                Servers should convert recognized schemas to the latest internal value, and
                may reject unrecognized values.
                More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
              type: string
            kind:
              description: |-
                Kind is a string value representing the REST resource this object represents.
                Servers may infer this from the endpoint the client submits requests to.
                Cannot be updated.
                In CamelCase.
                More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
              type: string
            metadata:
              type: object
            spec:
              properties:
                allowEmptyResponse:
                  default: false
                  description: Do not fail if no secrets are found. Useful for requests where no data is expected.
                  type: boolean
                controller:
                  description: |-
                    Used to select the correct ESO controller (think: ingress.ingressClassName)
                    The ESO controller is instantiated with a specific controller name and filters VDS based on this property
                  type: string
                method:
                  description: Vault API method to use (GET/POST/other)
                  type: string
                parameters:
                  description: Parameters to pass to Vault write (for non-GET methods)
                  x-kubernetes-preserve-unknown-fields: true
                path:
                  description: Vault path to obtain the dynamic secret from
                  type: string
                provider:
                  description: Vault provider common spec
                  properties:
                    auth:
                      description: Auth configures how secret-manager authenticates with the Vault server.
                      properties:
                        appRole:
                          description: |-
                            AppRole authenticates with Vault using the App Role auth mechanism,
                            with the role and secret stored in a Kubernetes Secret resource.
                          properties:
                            path:
                              default: approle
                              description: |-
                                Path where the App Role authentication backend is mounted
                                in Vault, e.g: "approle"
                              type: string
                            roleId:
                              description: |-
                                RoleID configured in the App Role authentication backend when setting
                                up the authentication backend in Vault.
                              type: string
                            roleRef:
                              description: |-
                                Reference to a key in a Secret that contains the App Role ID used
                                to authenticate with Vault.
                                The `key` field must be specified and denotes which entry within the Secret
                                resource is used as the app role id.
                              properties:
                                key:
                                  description: |-
                                    A key in the referenced Secret.
                                    Some instances of this field may be defaulted, in others it may be required.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[-._a-zA-Z0-9]+$
                                  type: string
                                name:
                                  description: The name of the Secret resource being referred to.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                  type: string
                                namespace:
                                  description: |-
                                    The namespace of the Secret resource being referred to.
                                    Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                                  maxLength: 63
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                                  type: string
                              type: object
                            secretRef:
                              description: |-
                                Reference to a key in a Secret that contains the App Role secret used
                                to authenticate with Vault.
                                The `key` field must be specified and denotes which entry within the Secret
                                resource is used as the app role secret.
                              properties:
                                key:
                                  description: |-
                                    A key in the referenced Secret.
                                    Some instances of this field may be defaulted, in others it may be required.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[-._a-zA-Z0-9]+$
                                  type: string
                                name:
                                  description: The name of the Secret resource being referred to.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                  type: string
                                namespace:
                                  description: |-
                                    The namespace of the Secret resource being referred to.
                                    Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                                  maxLength: 63
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                                  type: string
                              type: object
                          required:
                            - path
                            - secretRef
                          type: object
                        cert:
                          description: |-
                            Cert authenticates with TLS Certificates by passing client certificate, private key and ca certificate
                            Cert authentication method
                          properties:
                            clientCert:
                              description: |-
                                ClientCert is a certificate to authenticate using the Cert Vault
                                authentication method
                              properties:
                                key:
                                  description: |-
                                    A key in the referenced Secret.
                                    Some instances of this field may be defaulted, in others it may be required.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[-._a-zA-Z0-9]+$
                                  type: string
                                name:
                                  description: The name of the Secret resource being referred to.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                  type: string
                                namespace:
                                  description: |-
                                    The namespace of the Secret resource being referred to.
                                    Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                                  maxLength: 63
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                                  type: string
                              type: object
                            secretRef:
                              description: |-
                                SecretRef to a key in a Secret resource containing client private key to
                                authenticate with Vault using the Cert authentication method
                              properties:
                                key:
                                  description: |-
                                    A key in the referenced Secret.
                                    Some instances of this field may be defaulted, in others it may be required.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[-._a-zA-Z0-9]+$
                                  type: string
                                name:
                                  description: The name of the Secret resource being referred to.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                  type: string
                                namespace:
                                  description: |-
                                    The namespace of the Secret resource being referred to.
                                    Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                                  maxLength: 63
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                                  type: string
                              type: object
                          type: object
                        iam:
                          description: |-
                            Iam authenticates with vault by passing a special AWS request signed with AWS IAM credentials
                            AWS IAM authentication method
                          properties:
                            externalID:
                              description: AWS External ID set on assumed IAM roles
                              type: string
                            jwt:
                              description: Specify a service account with IRSA enabled
                              properties:
                                serviceAccountRef:
                                  description: A reference to a ServiceAccount resource.
                                  properties:
                                    audiences:
                                      description: |-
                                        Audience specifies the `aud` claim for the service account token
                                        If the service account uses a well-known annotation for e.g. IRSA or GCP Workload Identity
                                        then this audiences will be appended to the list
                                      items:
                                        type: string
                                      type: array
                                    name:
                                      description: The name of the ServiceAccount resource being referred to.
                                      maxLength: 253
                                      minLength: 1
                                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                      type: string
                                    namespace:
                                      description: |-
                                        Namespace of the resource being referred to.
                                        Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                                      maxLength: 63
                                      minLength: 1
                                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                                      type: string
                                  required:
                                    - name
                                  type: object
                              type: object
                            path:
                              description: 'Path where the AWS auth method is enabled in Vault, e.g: "aws"'
                              type: string
                            region:
                              description: AWS region
                              type: string
                            role:
                              description: This is the AWS role to be assumed before talking to vault
                              type: string
                            secretRef:
                              description: Specify credentials in a Secret object
                              properties:
                                accessKeyIDSecretRef:
                                  description: The AccessKeyID is used for authentication
                                  properties:
                                    key:
                                      description: |-
                                        A key in the referenced Secret.
                                        Some instances of this field may be defaulted, in others it may be required.
                                      maxLength: 253
                                      minLength: 1
                                      pattern: ^[-._a-zA-Z0-9]+$
                                      type: string
                                    name:
                                      description: The name of the Secret resource being referred to.
                                      maxLength: 253
                                      minLength: 1
                                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                      type: string
                                    namespace:
                                      description: |-
                                        The namespace of the Secret resource being referred to.
                                        Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                                      maxLength: 63
                                      minLength: 1
                                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                                      type: string
                                  type: object
                                secretAccessKeySecretRef:
                                  description: The SecretAccessKey is used for authentication
                                  properties:
                                    key:
                                      description: |-
                                        A key in the referenced Secret.
                                        Some instances of this field may be defaulted, in others it may be required.
                                      maxLength: 253
                                      minLength: 1
                                      pattern: ^[-._a-zA-Z0-9]+$
                                      type: string
                                    name:
                                      description: The name of the Secret resource being referred to.
                                      maxLength: 253
                                      minLength: 1
                                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                      type: string
                                    namespace:
                                      description: |-
                                        The namespace of the Secret resource being referred to.
                                        Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                                      maxLength: 63
                                      minLength: 1
                                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                                      type: string
                                  type: object
                                sessionTokenSecretRef:
                                  description: |-
                                    The SessionToken used for authentication
                                    This must be defined if AccessKeyID and SecretAccessKey are temporary credentials
                                    see: https://docs.aws.amazon.com/IAM/latest/UserGuide/id_credentials_temp_use-resources.html
                                  properties:
                                    key:
                                      description: |-
                                        A key in the referenced Secret.
                                        Some instances of this field may be defaulted, in others it may be required.
                                      maxLength: 253
                                      minLength: 1
                                      pattern: ^[-._a-zA-Z0-9]+$
                                      type: string
                                    name:
                                      description: The name of the Secret resource being referred to.
                                      maxLength: 253
                                      minLength: 1
                                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                      type: string
                                    namespace:
                                      description: |-
                                        The namespace of the Secret resource being referred to.
                                        Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                                      maxLength: 63
                                      minLength: 1
                                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                                      type: string
                                  type: object
                              type: object
                            vaultAwsIamServerID:
                              description: 'X-Vault-AWS-IAM-Server-ID is an additional header used by Vault IAM auth method to mitigate against different types of replay attacks. More details here: https://developer.hashicorp.com/vault/docs/auth/aws'
                              type: string
                            vaultRole:
                              description: Vault Role. In vault, a role describes an identity with a set of permissions, groups, or policies you want to attach a user of the secrets engine
                              type: string
                          required:
                            - vaultRole
                          type: object
                        jwt:
                          description: |-
                            Jwt authenticates with Vault by passing role and JWT token using the
                            JWT/OIDC authentication method
                          properties:
                            kubernetesServiceAccountToken:
                              description: |-
                                Optional ServiceAccountToken specifies the Kubernetes service account for which to request
                                a token for with the `TokenRequest` API.
                              properties:
                                audiences:
                                  description: |-
                                    Optional audiences field that will be used to request a temporary Kubernetes service
                                    account token for the service account referenced by `serviceAccountRef`.
                                    Defaults to a single audience `vault` it not specified.
                                    Deprecated: use serviceAccountRef.Audiences instead
                                  items:
                                    type: string
                                  type: array
                                expirationSeconds:
                                  description: |-
                                    Optional expiration time in seconds that will be used to request a temporary
                                    Kubernetes service account token for the service account referenced by
                                    `serviceAccountRef`.
                                    Deprecated: this will be removed in the future.
                                    Defaults to 10 minutes.
                                  format: int64
                                  type: integer
                                serviceAccountRef:
                                  description: Service account field containing the name of a kubernetes ServiceAccount.
                                  properties:
                                    audiences:
                                      description: |-
                                        Audience specifies the `aud` claim for the service account token
                                        If the service account uses a well-known annotation for e.g. IRSA or GCP Workload Identity
                                        then this audiences will be appended to the list
                                      items:
                                        type: string
                                      type: array
                                    name:
                                      description: The name of the ServiceAccount resource being referred to.
                                      maxLength: 253
                                      minLength: 1
                                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                      type: string
                                    namespace:
                                      description: |-
                                        Namespace of the resource being referred to.
                                        Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                                      maxLength: 63
                                      minLength: 1
                                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                                      type: string
                                  required:
                                    - name
                                  type: object
                              required:
                                - serviceAccountRef
                              type: object
                            path:
                              default: jwt
                              description: |-
                                Path where the JWT authentication backend is mounted
                                in Vault, e.g: "jwt"
                              type: string
                            role:
                              description: |-
                                Role is a JWT role to authenticate using the JWT/OIDC Vault
                                authentication method
                              type: string
                            secretRef:
                              description: |-
                                Optional SecretRef that refers to a key in a Secret resource containing JWT token to
                                authenticate with Vault using the JWT/OIDC authentication method.
                              properties:
                                key:
                                  description: |-
                                    A key in the referenced Secret.
                                    Some instances of this field may be defaulted, in others it may be required.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[-._a-zA-Z0-9]+$
                                  type: string
                                name:
                                  description: The name of the Secret resource being referred to.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                  type: string
                                namespace:
                                  description: |-
                                    The namespace of the Secret resource being referred to.
                                    Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                                  maxLength: 63
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                                  type: string
                              type: object
                          required:
                            - path
                          type: object
                        kubernetes:
                          description: |-
                            Kubernetes authenticates with Vault by passing the ServiceAccount
                            token stored in the named Secret resource to the Vault server.
                          properties:
                            mountPath:
                              default: kubernetes
                              description: |-
                                Path where the Kubernetes authentication backend is mounted in Vault, e.g:
                                "kubernetes"
                              type: string
                            role:
                              description: |-
                                A required field containing the Vault Role to assume. A Role binds a
                                Kubernetes ServiceAccount with a set of Vault policies.
                              type: string
                            secretRef:
                              description: |-
                                Optional secret field containing a Kubernetes ServiceAccount JWT used
                                for authenticating with Vault. If a name is specified without a key,
                                `token` is the default. If one is not specified, the one bound to
                                the controller will be used.
                              properties:
                                key:
                                  description: |-
                                    A key in the referenced Secret.
                                    Some instances of this field may be defaulted, in others it may be required.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[-._a-zA-Z0-9]+$
                                  type: string
                                name:
                                  description: The name of the Secret resource being referred to.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                  type: string
                                namespace:
                                  description: |-
                                    The namespace of the Secret resource being referred to.
                                    Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                                  maxLength: 63
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                                  type: string
                              type: object
                            serviceAccountRef:
                              description: |-
                                Optional service account field containing the name of a kubernetes ServiceAccount.
                                If the service account is specified, the service account secret token JWT will be used
                                for authenticating with Vault. If the service account selector is not supplied,
                                the secretRef will be used instead.
                              properties:
                                audiences:
                                  description: |-
                                    Audience specifies the `aud` claim for the service account token
                                    If the service account uses a well-known annotation for e.g. IRSA or GCP Workload Identity
                                    then this audiences will be appended to the list
                                  items:
                                    type: string
                                  type: array
                                name:
                                  description: The name of the ServiceAccount resource being referred to.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                  type: string
                                namespace:
                                  description: |-
                                    Namespace of the resource being referred to.
                                    Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                                  maxLength: 63
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                                  type: string
                              required:
                                - name
                              type: object
                          required:
                            - mountPath
                            - role
                          type: object
                        ldap:
                          description: |-
                            Ldap authenticates with Vault by passing username/password pair using
                            the LDAP authentication method
                          properties:
                            path:
                              default: ldap
                              description: |-
                                Path where the LDAP authentication backend is mounted
                                in Vault, e.g: "ldap"
                              type: string
                            secretRef:
                              description: |-
                                SecretRef to a key in a Secret resource containing password for the LDAP
                                user used to authenticate with Vault using the LDAP authentication
                                method
                              properties:
                                key:
                                  description: |-
                                    A key in the referenced Secret.
                                    Some instances of this field may be defaulted, in others it may be required.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[-._a-zA-Z0-9]+$
                                  type: string
                                name:
                                  description: The name of the Secret resource being referred to.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                  type: string
                                namespace:
                                  description: |-
                                    The namespace of the Secret resource being referred to.
                                    Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                                  maxLength: 63
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                                  type: string
                              type: object
                            username:
                              description: |-
                                Username is an LDAP username used to authenticate using the LDAP Vault
                                authentication method
                              type: string
                          required:
                            - path
                            - username
                          type: object
                        namespace:
                          description: |-
                            Name of the vault namespace to authenticate to. This can be different than the namespace your secret is in.
                            Namespaces is a set of features within Vault Enterprise that allows
                            Vault environments to support Secure Multi-tenancy. e.g: "ns1".
                            More about namespaces can be found here https://www.vaultproject.io/docs/enterprise/namespaces
                            This will default to Vault.Namespace field if set, or empty otherwise
                          type: string
                        tokenSecretRef:
                          description: TokenSecretRef authenticates with Vault by presenting a token.
                          properties:
                            key:
                              description: |-
                                A key in the referenced Secret.
                                Some instances of this field may be defaulted, in others it may be required.
                              maxLength: 253
                              minLength: 1
                              pattern: ^[-._a-zA-Z0-9]+$
                              type: string
                            name:
                              description: The name of the Secret resource being referred to.
                              maxLength: 253
                              minLength: 1
                              pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                              type: string
                            namespace:
                              description: |-
                                The namespace of the Secret resource being referred to.
                                Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                              maxLength: 63
                              minLength: 1
                              pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                              type: string
                          type: object
                        userPass:
                          description: UserPass authenticates with Vault by passing username/password pair
                          properties:
                            path:
                              default: userpass
                              description: |-
                                Path where the UserPassword authentication backend is mounted
                                in Vault, e.g: "userpass"
                              type: string
                            secretRef:
                              description: |-
                                SecretRef to a key in a Secret resource containing password for the
                                user used to authenticate with Vault using the UserPass authentication
                                method
                              properties:
                                key:
                                  description: |-
                                    A key in the referenced Secret.
                                    Some instances of this field may be defaulted, in others it may be required.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[-._a-zA-Z0-9]+$
                                  type: string
                                name:
                                  description: The name of the Secret resource being referred to.
                                  maxLength: 253
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                  type: string
                                namespace:
                                  description: |-
                                    The namespace of the Secret resource being referred to.
                                    Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                                  maxLength: 63
                                  minLength: 1
                                  pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                                  type: string
                              type: object
                            username:
                              description: |-
                                Username is a username used to authenticate using the UserPass Vault
                                authentication method
                              type: string
                          required:
                            - path
                            - username
                          type: object
                      type: object
                    caBundle:
                      description: |-
                        PEM encoded CA bundle used to validate Vault server certificate. Only used
                        if the Server URL is using HTTPS protocol. This parameter is ignored for
                        plain HTTP protocol connection. If not set the system root certificates
                        are used to validate the TLS connection.
                      format: byte
                      type: string
                    caProvider:
                      description: The provider for the CA bundle to use to validate Vault server certificate.
                      properties:
                        key:
                          description: The key where the CA certificate can be found in the Secret or ConfigMap.
                          maxLength: 253
                          minLength: 1
                          pattern: ^[-._a-zA-Z0-9]+$
                          type: string
                        name:
                          description: The name of the object located at the provider type.
                          maxLength: 253
                          minLength: 1
                          pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                          type: string
                        namespace:
                          description: |-
                            The namespace the Provider type is in.
                            Can only be defined when used in a ClusterSecretStore.
                          maxLength: 63
                          minLength: 1
                          pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                          type: string
                        type:
                          description: The type of provider to use such as "Secret", or "ConfigMap".
                          enum:
                            - Secret
                            - ConfigMap
                          type: string
                      required:
                        - name
                        - type
                      type: object
                    forwardInconsistent:
                      description: |-
                        ForwardInconsistent tells Vault to forward read-after-write requests to the Vault
                        leader instead of simply retrying within a loop. This can increase performance if
                        the option is enabled serverside.
                        https://www.vaultproject.io/docs/configuration/replication#allow_forwarding_via_header
                      type: boolean
                    headers:
                      additionalProperties:
                        type: string
                      description: Headers to be added in Vault request
                      type: object
                    namespace:
                      description: |-
                        Name of the vault namespace. Namespaces is a set of features within Vault Enterprise that allows
                        Vault environments to support Secure Multi-tenancy. e.g: "ns1".
                        More about namespaces can be found here https://www.vaultproject.io/docs/enterprise/namespaces
                      type: string
                    path:
                      description: |-
                        Path is the mount path of the Vault KV backend endpoint, e.g:
                        "secret". The v2 KV secret engine version specific "/data" path suffix
                        for fetching secrets from Vault is optional and will be appended
                        if not present in specified path.
                      type: string
                    readYourWrites:
                      description: |-
                        ReadYourWrites ensures isolated read-after-write semantics by
                        providing discovered cluster replication states in each request.
                        More information about eventual consistency in Vault can be found here
                        https://www.vaultproject.io/docs/enterprise/consistency
                      type: boolean
                    server:
                      description: 'Server is the connection address for the Vault server, e.g: "https://vault.example.com:8200".'
                      type: string
                    tls:
                      description: |-
                        The configuration used for client side related TLS communication, when the Vault server
                        requires mutual authentication. Only used if the Server URL is using HTTPS protocol.
                        This parameter is ignored for plain HTTP protocol connection.
                        It's worth noting this configuration is different from the "TLS certificates auth method",
                        which is available under the `auth.cert` section.
                      properties:
                        certSecretRef:
                          description: |-
                            CertSecretRef is a certificate added to the transport layer
                            when communicating with the Vault server.
                            If no key for the Secret is specified, external-secret will default to 'tls.crt'.
                          properties:
                            key:
                              description: |-
                                A key in the referenced Secret.
                                Some instances of this field may be defaulted, in others it may be required.
                              maxLength: 253
                              minLength: 1
                              pattern: ^[-._a-zA-Z0-9]+$
                              type: string
                            name:
                              description: The name of the Secret resource being referred to.
                              maxLength: 253
                              minLength: 1
                              pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                              type: string
                            namespace:
                              description: |-
                                The namespace of the Secret resource being referred to.
                                Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                              maxLength: 63
                              minLength: 1
                              pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                              type: string
                          type: object
                        keySecretRef:
                          description: |-
                            KeySecretRef to a key in a Secret resource containing client private key
                            added to the transport layer when communicating with the Vault server.
                            If no key for the Secret is specified, external-secret will default to 'tls.key'.
                          properties:
                            key:
                              description: |-
                                A key in the referenced Secret.
                                Some instances of this field may be defaulted, in others it may be required.
                              maxLength: 253
                              minLength: 1
                              pattern: ^[-._a-zA-Z0-9]+$
                              type: string
                            name:
                              description: The name of the Secret resource being referred to.
                              maxLength: 253
                              minLength: 1
                              pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                              type: string
                            namespace:
                              description: |-
                                The namespace of the Secret resource being referred to.
                                Ignored if referent is not cluster-scoped, otherwise defaults to the namespace of the referent.
                              maxLength: 63
                              minLength: 1
                              pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                              type: string
                          type: object
                      type: object
                    version:
                      default: v2
                      description: |-
                        Version is the Vault KV secret engine version. This can be either "v1" or
                        "v2". Version defaults to "v2".
                      enum:
                        - v1
                        - v2
                      type: string
                  required:
                    - server
                  type: object
                resultType:
                  default: Data
                  description: |-
                    Result type defines which data is returned from the generator.
                    By default it is the "data" section of the Vault API response.
                    When using e.g. /auth/token/create the "data" section is empty but
                    the "auth" section contains the generated token.
                    Please refer to the vault docs regarding the result data structure.
                    Additionally, accessing the raw response is possibly by using "Raw" result type.
                  enum:
                    - Data
                    - Auth
                    - Raw
                  type: string
                retrySettings:
                  description: Used to configure http retries if failed
                  properties:
                    maxRetries:
                      format: int32
                      type: integer
                    retryInterval:
                      type: string
                  type: object
              required:
                - path
                - provider
              type: object
          type: object
      served: true
      storage: true
      subresources:
        status: {}
{{- if .Values.crds.conversion.enabled }}
  conversion:
    strategy: Webhook
    webhook:
      conversionReviewVersions:
        - v1
      clientConfig:
        service:
          name: {{ include "external-secrets.fullname" . }}-webhook
          namespace: {{ .Release.Namespace | quote }}
          path: /convert
{{- end }}