{{- if .Values.createClusterRoles }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "argo-cd.server.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "argo-cd.server.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "argo-cd.server.serviceAccountName" . }}
  namespace: {{ include "argo-cd.namespace" . }}
{{- end }}
