{{- if and .Values.server.ingress.enabled (eq .Values.server.ingress.controller "aws") }}
{{- $insecure := index .Values.configs.params "server.insecure" | toString -}}
{{- $servicePort := eq $insecure "true" | ternary .Values.server.service.servicePortHttp .Values.server.service.servicePortHttps -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "argo-cd.server.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
    {{- with .Values.server.ingress.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  annotations:
    alb.ingress.kubernetes.io/conditions.{{ include "argo-cd.server.fullname" . }}-grpc: |
      [{"field":"http-header","httpHeaderConfig":{"httpHeaderName": "Content-Type", "values":["application/grpc"]}}]
    {{- range $key, $value := .Values.server.ingress.annotations }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
spec:
  {{- with .Values.server.ingress.ingressClassName }}
  ingressClassName: {{ . }}
  {{- end }}
  rules:
    - host: {{ .Values.server.ingress.hostname | default .Values.global.domain }}
      http:
        paths:
          {{- with .Values.server.ingress.extraPaths }}
            {{- tpl (toYaml .) $ | nindent 10 }}
          {{- end }}
          - path: {{ .Values.server.ingress.path }}
            pathType: {{ $.Values.server.ingressGrpc.pathType }}
            backend:
              service:
                name: {{ include "argo-cd.server.fullname" $ }}-grpc
                port:
                  number: {{ $servicePort }}
          - path: {{ .Values.server.ingress.path }}
            pathType: {{ $.Values.server.ingress.pathType }}
            backend:
              service:
                name: {{ include "argo-cd.server.fullname" . }}
                port:
                  number: {{ $servicePort }}
    {{- range .Values.server.ingress.extraHosts }}
    - host: {{ .name | quote }}
      http:
        paths:
          - path: {{ default $.Values.server.ingress.path .path }}
            pathType: {{ default $.Values.server.ingress.pathType .pathType }}
            backend:
              service:
                name: {{ include "argo-cd.server.fullname" $ }}
                port:
                  number: {{ $servicePort }}
    {{- end }}
    {{- with .Values.server.ingress.extraRules }}
      {{- tpl (toYaml .) $ | nindent 4 }}
    {{- end }}
  {{- if or .Values.server.ingress.tls .Values.server.ingress.extraTls }}
  tls:
    {{- if .Values.server.ingress.tls }}
    - hosts:
      - {{ .Values.server.ingress.hostname | default .Values.global.domain }}
      secretName: argocd-server-tls
    {{- end }}
    {{- with .Values.server.ingress.extraTls }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
{{- end }}
