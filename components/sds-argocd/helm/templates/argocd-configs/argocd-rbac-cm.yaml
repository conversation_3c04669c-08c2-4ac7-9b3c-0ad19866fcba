{{- if .Values.configs.rbac.create }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-rbac-cm
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" "rbac-cm") | nindent 4 }}
  {{- with .Values.configs.rbac.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
{{- with (omit .Values.configs.rbac "create" "annotations") }}
data:
  {{- toYaml . | nindent 2 }}
{{- end }}
{{- end }}
