{{- if .Values.configs.cmp.create }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-cmp-cm
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.repoServer.name "name" "cmp-cm") | nindent 4 }}
  {{- with .Values.configs.cmp.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
data:
  {{- range $cmp_plugin, $cmp_plugin_config := .Values.configs.cmp.plugins }}
  {{ $cmp_plugin }}.yaml: |
    apiVersion: argoproj.io/v1alpha1
    kind: ConfigManagementPlugin
    metadata:
      name: {{ $cmp_plugin }}
    spec:
      {{- toYaml $cmp_plugin_config | nindent 6 }}
  {{- end }}
{{- end }}
