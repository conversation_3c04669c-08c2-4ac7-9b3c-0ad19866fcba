{{- range $repo_key, $repo_value := .Values.configs.repositories }}
---
apiVersion: v1
kind: Secret
metadata:
  name: argocd-repo-{{ $repo_key }}
  namespace: {{ include  "argo-cd.namespace" $ | quote }}
  labels:
    argocd.argoproj.io/secret-type: repository
    {{- include "argo-cd.labels" (dict "context" $) | nindent 4 }}
  {{- with $.Values.configs.repositoriesAnnotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
data:
  {{- range $key, $value := $repo_value }}
  {{ $key }}: {{ $value | b64enc }}
  {{- end }}
{{- end }}
