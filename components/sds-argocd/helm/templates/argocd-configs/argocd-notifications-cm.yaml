apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-notifications-cm
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.notifications.name "name" .Values.notifications.name) | nindent 4 }}
data:
data:
  service.email: |
    host: smtp.office365.com
    port: 587
    from: <EMAIL>
    username: $email-username
    password: $email-password
    format: text
  service.teams: |
    recipientUrls:
      channelName: $channel-teams-url
  template.app-created: |
    email:
      subject: Application {{`{{.app.metadata.name}}`}} has been created.
    message: Application {{`{{.app.metadata.name}}`}} has been created.
    teams:
      title: Application {{`{{.app.metadata.name}}`}} has been created.
  template.app-deleted: |
    email:
      subject: Application {{`{{.app.metadata.name}}`}} has been deleted.
    message: Application {{`{{.app.metadata.name}}`}} has been deleted.
    teams:
      title: Application {{`{{.app.metadata.name}}`}} has been deleted.
  template.app-deployed: |
    email:
      subject: New version of an application {{`{{.app.metadata.name}}`}} is up and running.
    message: |
      {{`{{if eq .serviceType "slack"}}`}}:white_check_mark:{{`{{end}}`}} Application {{`{{.app.metadata.name}}`}} is now running new version of deployments manifests.
      deliveryPolicy: Post
      groupingKey: ""
      notifyBroadcast: false
    teams:
      facts: |
        [{
          "name": "Sync Status",
          "value": "{{`{{.app.status.sync.status}}`}}"
        },
        {
          "name": "Repository",
          "value": "{{`{{.app.spec.source.repoURL}}`}}"
        },
        {
          "name": "Revision",
          "value": "{{`{{.app.status.sync.revision}}`}}"
        }
        {{`{{range $index, $c := .app.status.conditions}}`}}
          ,
          {
            "name": "{{`{{$c.type}}`}}",
            "value": "{{`{{$c.message}}`}}"
          }
        {{`{{end}}`}}
        ]
      themeColor: '#000080'
      title: New version of an application {{`{{.app.metadata.name}}`}} is up and running.
  template.app-health-degraded: |
    email:
      subject: Application {{`{{.app.metadata.name}}`}} has degraded.
    message: |
      {{`{{if eq .serviceType "slack"}}`}}:exclamation:{{`{{end}}`}} Application {{`{{.app.metadata.name}}`}} has degraded.
      Application details: {{`{{.context.argocdUrl}}`}}/applications/{{`{{.app.metadata.name}}`}}.
      deliveryPolicy: Post
      groupingKey: ""
      notifyBroadcast: false
    teams:
      facts: |
        [{
          "name": "Health Status",
          "value": "{{`{{.app.status.health.status}}`}}"
        },
        {
          "name": "Repository",
          "value": "{{`{{.app.spec.source.repoURL}}`}}"
        }
        {{`{{range $index, $c := .app.status.conditions}}`}}
          ,
          {
            "name": "{{`{{$c.type}}`}}",
            "value": "{{`{{$c.message}}`}}"
          }
        {{`{{end}}`}}
        ]
      themeColor: '#FF0000'
      title: Application {{`{{.app.metadata.name}}`}} has degraded.
  template.app-sync-failed: |
    email:
      subject: Failed to sync application {{`{{.app.metadata.name}}`}}.
    message: |
      {{`{{if eq .serviceType "slack"}}`}}:exclamation:{{`{{end}}`}}  The sync operation of application {{`{{.app.metadata.name}}`}} has failed at {{`{{.app.status.operationState.finishedAt}}`}} with the following error: {{`{{.app.status.operationState.message}}`}}
      Sync operation details are available at: {{`{{.context.argocdUrl}}`}}/applications/{{`{{.app.metadata.name}}`}}?operation=true .
      deliveryPolicy: Post
      groupingKey: ""
      notifyBroadcast: false
    teams:
      facts: |
        [{
          "name": "Sync Status",
          "value": "{{`{{.app.status.sync.status}}`}}"
        },
        {
          "name": "Failed at",
          "value": "{{`{{.app.status.operationState.finishedAt}}`}}"
        },
        {
          "name": "Repository",
          "value": "{{`{{.app.spec.source.repoURL}}`}}"
        }
        {{`{{range $index, $c := .app.status.conditions}}`}}
          ,
          {
            "name": "{{`{{$c.type}}`}}",
            "value": "{{`{{$c.message}}`}}"
          }
        {{`{{end}}`}}
        ]
      themeColor: '#FF0000'
      title: Failed to sync application {{`{{.app.metadata.name}}`}}.
  template.app-sync-running: |
    email:
      subject: Start syncing application {{`{{.app.metadata.name}}`}}.
    message: |
      The sync operation of application {{`{{.app.metadata.name}}`}} has started at {{`{{.app.status.operationState.startedAt}}`}}.
      Sync operation details are available at: {{`{{.context.argocdUrl}}`}}/applications/{{`{{.app.metadata.name}}`}}?operation=true .
      deliveryPolicy: Post
      groupingKey: ""
      notifyBroadcast: false
      title: Start syncing application {{`{{.app.metadata.name}}`}}.
  template.app-sync-status-unknown: |
    email:
      subject: Application {{`{{.app.metadata.name}}`}} sync status is 'Unknown'
    message: |
      {{`{{if eq .serviceType "slack"}}`}}:exclamation:{{`{{end}}`}} Application {{`{{.app.metadata.name}}`}} sync is 'Unknown'.
      Application details: {{`{{.context.argocdUrl}}`}}/applications/{{`{{.app.metadata.name}}`}}.
      {{`{{if ne .serviceType "slack"}}`}}
      {{`{{range $c := .app.status.conditions}}`}}
          * {{`{{$c.message}}`}}
      {{`{{end}}`}}
      {{`{{end}}`}}
      deliveryPolicy: Post
      groupingKey: ""
      notifyBroadcast: false
    teams:
      facts: |
        [{
          "name": "Sync Status",
          "value": "{{`{{.app.status.sync.status}}`}}"
        },
        {
          "name": "Started at",
          "value": "{{`{{.app.status.operationState.startedAt}}`}}"
        },
        {
          "name": "Repository",
          "value": "{{`{{.app.spec.source.repoURL}}`}}"
        }
        {{`{{range $index, $c := .app.status.conditions}}`}}
          ,
          {
            "name": "{{`{{$c.type}}`}}",
            "value": "{{`{{$c.message}}`}}"
          }
        {{`{{end}}`}}
        ]
      title: Application {{`{{.app.metadata.name}}`}} sync status is 'Unknown'
  template.app-sync-succeeded: |
    email:
      subject: Application {{`{{.app.metadata.name}}`}} has been successfully synced.
    message: |
      {{`{{if eq .serviceType "slack"}}`}}:white_check_mark:{{`{{end}}`}} Application {{`{{.app.metadata.name}}`}} has been successfully synced at {{`{{.app.status.operationState.finishedAt}}`}}.
      Sync operation details are available at: {{`{{.context.argocdUrl}}`}}/applications/{{`{{.app.metadata.name}}`}}?operation=true .
      deliveryPolicy: Post
      groupingKey: ""
      notifyBroadcast: false
    teams:
      facts: |
        [{
          "name": "Sync Status",
          "value": "{{`{{.app.status.sync.status}}`}}"
        },
        {
          "name": "Synced at",
          "value": "{{`{{.app.status.operationState.finishedAt}}`}}"
        },
        {
          "name": "Repository",
          "value": "{{`{{.app.spec.source.repoURL}}`}}"
        }
        {{`{{range $index, $c := .app.status.conditions}}`}}
          ,
          {
            "name": "{{`{{$c.type}}`}}",
            "value": "{{`{{$c.message}}`}}"
          }
        {{`{{end}}`}}
        ]
      themeColor: '#000080'
      title: Application {{`{{.app.metadata.name}}`}} has been successfully synced
  trigger.on-created: |
    - description: Application is created.
      oncePer: app.metadata.name
      send:
      - app-created
      when: "true"
  trigger.on-deleted: |
    - description: Application is deleted.
      oncePer: app.metadata.name
      send:
      - app-deleted
      when: app.metadata.deletionTimestamp != nil
  trigger.on-deployed: |
    - description: Application is synced and healthy. Triggered once per commit.
      oncePer: app.status.operationState?.syncResult?.revision
      send:
      - app-deployed
      when: app.status.operationState != nil and app.status.operationState.phase in ['Succeeded']
        and app.status.health.status == 'Healthy'
  trigger.on-health-degraded: |
    - description: Application has degraded
      send:
      - app-health-degraded
      when: app.status.health.status == 'Degraded'
  trigger.on-sync-failed: |
    - description: Application syncing has failed
      send:
      - app-sync-failed
      when: app.status.operationState != nil and app.status.operationState.phase in ['Error',
        'Failed']
  trigger.on-sync-running: |
    - description: Application is being synced
      send:
      - app-sync-running
      when: app.status.operationState != nil and app.status.operationState.phase in ['Running']
  trigger.on-sync-status-unknown: |
    - description: Application status is 'Unknown'
      send:
      - app-sync-status-unknown
      when: app.status.sync.status == 'Unknown'
  trigger.on-sync-succeeded: |
    - description: Application syncing has succeeded
      send:
      - app-sync-succeeded
      when: app.status.operationState != nil and app.status.operationState.phase in ['Succeeded']
