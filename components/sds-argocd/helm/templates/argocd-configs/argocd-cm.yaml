{{- if .Values.configs.cm.create }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-cm
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" "cm") | nindent 4 }}
  {{- with .Values.configs.cm.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
data:
  {{- include "argo-cd.config.cm" . | trim | nindent 2 }}
{{- end }}
