{{- if and .Values.dex.enabled .Values.dex.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.dex.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ include "argo-cd.dex.serviceAccountName" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  {{- with .Values.dex.serviceAccount.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.dex.name "name" .Values.dex.name) | nindent 4 }}
{{- end }}
