{{- if and .Values.commitServer.enabled .Values.global.networkPolicy.create }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ template "argo-cd.commitServer.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
spec:
  podSelector:
    matchLabels:
      {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.commitServer.name) | nindent 6 }}
  policyTypes:
    - Ingress
  ingress:
    - from:
        - podSelector:
            matchLabels:
              {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.controller.name) | nindent 14 }}
      ports:
        - protocol: TCP
          port: 8086
    - from:
        - namespaceSelector: { }
      ports:
        - port: 8087
{{- end }}
