{{- if and .Values.createClusterRoles .Values.repoServer.clusterRoleRules.enabled }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "argo-cd.repoServer.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.repoServer.name "name" .Values.repoServer.name) | nindent 4 }}
rules:
  {{- with .Values.repoServer.clusterRoleRules.rules }}
    {{- toYaml . | nindent 2 }}
  {{- else }}
  - apiGroups:
    - '*'
    resources:
    - '*'
    verbs:
    - '*'
  - nonResourceURLs:
    - '*'
    verbs:
    - '*'
  {{- end }}
{{- end }}
