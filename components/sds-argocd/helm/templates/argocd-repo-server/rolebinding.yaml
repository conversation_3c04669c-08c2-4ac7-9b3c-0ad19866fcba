{{- if .Values.repoServer.serviceAccount.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "argo-cd.repoServer.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.repoServer.name "name" .Values.repoServer.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "argo-cd.repoServer.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "argo-cd.repoServer.serviceAccountName" . }}
  namespace: {{ include "argo-cd.namespace" . }}
{{- end }}
