{{- if .Values.repoServer.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "argo-cd.repoServer.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.repoServer.name "name" .Values.repoServer.name) | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "argo-cd.repoServer.fullname" . }}
  minReplicas: {{ .Values.repoServer.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.repoServer.autoscaling.maxReplicas }}
  metrics:
  {{- with .Values.repoServer.autoscaling.metrics }}
    {{- toYaml . | nindent 4 }}
  {{- else }}
    {{- with .Values.repoServer.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ . }}
    {{- end }}
    {{- with .Values.repoServer.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ . }}
    {{- end }}
  {{- end }}
  {{- with .Values.repoServer.autoscaling.behavior }}
  behavior:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
