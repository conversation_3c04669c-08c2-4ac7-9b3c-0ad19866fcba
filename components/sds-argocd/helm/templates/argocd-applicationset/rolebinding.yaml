apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "argo-cd.applicationSet.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.applicationSet.name "name" .Values.applicationSet.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "argo-cd.applicationSet.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "argo-cd.applicationSet.serviceAccountName" . }}
    namespace: {{ include "argo-cd.namespace" . }}
