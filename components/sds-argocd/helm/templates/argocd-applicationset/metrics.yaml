{{- if .Values.applicationSet.metrics.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "argo-cd.applicationSet.fullname" . }}-metrics
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.applicationSet.name "name" "metrics") | nindent 4 }}
    {{- with .Values.applicationSet.metrics.service.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- if or .Values.applicationSet.metrics.service.annotations .Values.global.addPrometheusAnnotations }}
  annotations:
    {{- if .Values.global.addPrometheusAnnotations }}
    prometheus.io/port: {{ .Values.applicationSet.metrics.service.servicePort | quote }}
    prometheus.io/scrape: "true"
    {{- end }}
    {{- range $key, $value := .Values.applicationSet.metrics.service.annotations }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
spec:
  type: {{ .Values.applicationSet.metrics.service.type }}
  {{- if and .Values.applicationSet.metrics.service.clusterIP (eq .Values.applicationSet.metrics.service.type "ClusterIP") }}
  clusterIP: {{ .Values.applicationSet.metrics.service.clusterIP }}
  {{- end }}
  {{- include "argo-cd.dualStack" . | indent 2 }}
  ports:
  - name:  {{ .Values.applicationSet.metrics.service.portName }}
    protocol: TCP
    port: {{ .Values.applicationSet.metrics.service.servicePort }}
    targetPort: metrics
  selector:
    {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.applicationSet.name) | nindent 4 }}
{{- end }}
