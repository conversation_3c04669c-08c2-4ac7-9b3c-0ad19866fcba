apiVersion: apps/v1
kind: Deployment
metadata:
  {{- with (mergeOverwrite (deepCopy .Values.global.deploymentAnnotations) .Values.applicationSet.deploymentAnnotations) }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
  name: {{ include "argo-cd.applicationSet.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.applicationSet.name "name" .Values.applicationSet.name) | nindent 4 }}
spec:
  {{- with include "argo-cd.strategy" (mergeOverwrite (deepCopy .Values.global.deploymentStrategy) .Values.applicationSet.deploymentStrategy) }}
  strategy:
    {{- trim . | nindent 4 }}
  {{- end }}
  replicas: {{ .Values.applicationSet.replicas }}
  revisionHistoryLimit: {{ .Values.global.revisionHistoryLimit }}
  selector:
    matchLabels:
      {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.applicationSet.name) | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/cmd-params: {{ include (print $.Template.BasePath "/argocd-configs/argocd-cmd-params-cm.yaml") . | sha256sum }}
        {{- with (mergeOverwrite (deepCopy .Values.global.podAnnotations) .Values.applicationSet.podAnnotations) }}
        {{- range $key, $value := . }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
        {{- end }}
      labels:
        {{- include "argo-cd.labels" (dict "context" . "component" .Values.applicationSet.name "name" .Values.applicationSet.name) | nindent 8 }}
        {{- with (mergeOverwrite (deepCopy .Values.global.podLabels) .Values.applicationSet.podLabels) }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.applicationSet.runtimeClassName | default .Values.global.runtimeClassName }}
      runtimeClassName: {{ . }}
      {{- end }}
      {{- with .Values.applicationSet.imagePullSecrets | default .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.global.hostAliases }}
      hostAliases:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.global.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.applicationSet.priorityClassName | default .Values.global.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
      {{- if .Values.applicationSet.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.applicationSet.terminationGracePeriodSeconds }}
      {{- end }}
      serviceAccountName: {{ include "argo-cd.applicationSet.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.applicationSet.automountServiceAccountToken }}
      containers:
        - name: {{ .Values.applicationSet.name }}
          image: {{ default .Values.global.image.repository .Values.applicationSet.image.repository }}:{{ default (include "argo-cd.defaultTag" .) .Values.applicationSet.image.tag }}
          imagePullPolicy: {{ default .Values.global.image.imagePullPolicy .Values.applicationSet.image.imagePullPolicy }}
          args:
            - /usr/local/bin/argocd-applicationset-controller
            - --metrics-addr=:{{ .Values.applicationSet.containerPorts.metrics }}
            - --probe-addr=:{{ .Values.applicationSet.containerPorts.probe }}
            - --webhook-addr=:{{ .Values.applicationSet.containerPorts.webhook }}
            {{- with .Values.applicationSet.extraArgs }}
              {{- toYaml . | nindent 12 }}
            {{- end }}
          env:
            {{- with (concat .Values.global.env .Values.applicationSet.extraEnv) }}
              {{- toYaml . | nindent 12 }}
            {{- end }}
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_GLOBAL_PRESERVED_ANNOTATIONS
              valueFrom:
                configMapKeyRef:
                  key: applicationsetcontroller.global.preserved.annotations
                  name: argocd-cmd-params-cm
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_GLOBAL_PRESERVED_LABELS
              valueFrom:
                configMapKeyRef:
                  key: applicationsetcontroller.global.preserved.labels
                  name: argocd-cmd-params-cm
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_ENABLE_LEADER_ELECTION
              valueFrom:
                configMapKeyRef:
                  key: applicationsetcontroller.enable.leader.election
                  name: argocd-cmd-params-cm
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_REPO_SERVER
              valueFrom:
                configMapKeyRef:
                  key: repo.server
                  name: argocd-cmd-params-cm
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_POLICY
              valueFrom:
                configMapKeyRef:
                  key: applicationsetcontroller.policy
                  name: argocd-cmd-params-cm
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_ENABLE_POLICY_OVERRIDE
              valueFrom:
                configMapKeyRef:
                  key: applicationsetcontroller.enable.policy.override
                  name: argocd-cmd-params-cm
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_DEBUG
              valueFrom:
                configMapKeyRef:
                  key: applicationsetcontroller.debug
                  name: argocd-cmd-params-cm
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_LOGFORMAT
              valueFrom:
                configMapKeyRef:
                  key: applicationsetcontroller.log.format
                  name: argocd-cmd-params-cm
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_LOGLEVEL
              valueFrom:
                configMapKeyRef:
                  key: applicationsetcontroller.log.level
                  name: argocd-cmd-params-cm
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_DRY_RUN
              valueFrom:
                configMapKeyRef:
                  key: applicationsetcontroller.dryrun
                  name: argocd-cmd-params-cm
                  optional: true
            - name: ARGOCD_GIT_MODULES_ENABLED
              valueFrom:
                configMapKeyRef:
                  key: applicationsetcontroller.enable.git.submodule
                  name: argocd-cmd-params-cm
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_ENABLE_PROGRESSIVE_SYNCS
              valueFrom:
                configMapKeyRef:
                  key: applicationsetcontroller.enable.progressive.syncs
                  name: argocd-cmd-params-cm
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_TOKENREF_STRICT_MODE
              valueFrom:
                configMapKeyRef:
                  key: applicationsetcontroller.enable.tokenref.strict.mode
                  name: argocd-cmd-params-cm
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_ENABLE_NEW_GIT_FILE_GLOBBING
              valueFrom:
                configMapKeyRef:
                  key: applicationsetcontroller.enable.new.git.file.globbing
                  name: argocd-cmd-params-cm
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_REPO_SERVER_PLAINTEXT
              valueFrom:
                configMapKeyRef:
                  name: argocd-cmd-params-cm
                  key: applicationsetcontroller.repo.server.plaintext
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_REPO_SERVER_STRICT_TLS
              valueFrom:
                configMapKeyRef:
                  name: argocd-cmd-params-cm
                  key: applicationsetcontroller.repo.server.strict.tls
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_REPO_SERVER_TIMEOUT_SECONDS
              valueFrom:
                configMapKeyRef:
                  name: argocd-cmd-params-cm
                  key: applicationsetcontroller.repo.server.timeout.seconds
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_CONCURRENT_RECONCILIATIONS
              valueFrom:
                configMapKeyRef:
                  name: argocd-cmd-params-cm
                  key: applicationsetcontroller.concurrent.reconciliations.max
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_NAMESPACES
              valueFrom:
                configMapKeyRef:
                  key: applicationsetcontroller.namespaces
                  name: argocd-cmd-params-cm
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_SCM_ROOT_CA_PATH
              valueFrom:
                configMapKeyRef:
                  key: applicationsetcontroller.scm.root.ca.path
                  name: argocd-cmd-params-cm
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_ALLOWED_SCM_PROVIDERS
              valueFrom:
                configMapKeyRef:
                  name: argocd-cmd-params-cm
                  key: applicationsetcontroller.allowed.scm.providers
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_ENABLE_SCM_PROVIDERS
              valueFrom:
                configMapKeyRef:
                  name: argocd-cmd-params-cm
                  key: applicationsetcontroller.enable.scm.providers
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_WEBHOOK_PARALLELISM_LIMIT
              valueFrom:
                configMapKeyRef:
                  name: argocd-cmd-params-cm
                  key: applicationsetcontroller.webhook.parallelism.limit
                  optional: true
            - name: ARGOCD_APPLICATIONSET_CONTROLLER_REQUEUE_AFTER
              valueFrom:
                configMapKeyRef:
                  key: applicationsetcontroller.requeue.after
                  name: argocd-cmd-params-cm
                  optional: true
          {{- with .Values.applicationSet.extraEnvFrom }}
          envFrom:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          ports:
            - name: metrics
              containerPort: {{ .Values.applicationSet.containerPorts.metrics }}
              protocol: TCP
            - name: probe
              containerPort: {{ .Values.applicationSet.containerPorts.probe }}
              protocol: TCP
            - name: webhook
              containerPort: {{ .Values.applicationSet.containerPorts.webhook }}
              protocol: TCP
          {{- if .Values.applicationSet.livenessProbe.enabled }}
          livenessProbe:
            tcpSocket:
              port: probe
            initialDelaySeconds: {{ .Values.applicationSet.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.applicationSet.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.applicationSet.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.applicationSet.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.applicationSet.livenessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.applicationSet.readinessProbe.enabled }}
          readinessProbe:
            tcpSocket:
              port: probe
            initialDelaySeconds: {{ .Values.applicationSet.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.applicationSet.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.applicationSet.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.applicationSet.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.applicationSet.readinessProbe.failureThreshold }}
          {{- end }}
          resources:
            {{- toYaml .Values.applicationSet.resources | nindent 12 }}
          {{- with .Values.applicationSet.containerSecurityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          volumeMounts:
            {{- with .Values.applicationSet.extraVolumeMounts }}
              {{- toYaml . | nindent 12 }}
            {{- end }}
            - mountPath: /app/config/ssh
              name: ssh-known-hosts
            - mountPath: /app/config/tls
              name: tls-certs
            - mountPath: /app/config/gpg/source
              name: gpg-keys
            - mountPath: /app/config/gpg/keys
              name: gpg-keyring
            - mountPath: /app/config/reposerver/tls
              name: argocd-repo-server-tls
            - mountPath: /tmp
              name: tmp
        {{- with .Values.applicationSet.extraContainers }}
          {{- tpl (toYaml .) $ | nindent 8 }}
        {{- end }}
      {{- with .Values.applicationSet.initContainers }}
      initContainers:
        {{- tpl (toYaml .) $ | nindent 6 }}
      {{- end }}
      {{- with include "argo-cd.affinity" (dict "context" . "component" .Values.applicationSet) }}
      affinity:
        {{- trim . | nindent 8 }}
      {{- end }}
      {{- with .Values.applicationSet.nodeSelector | default .Values.global.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.applicationSet.tolerations | default .Values.global.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.applicationSet.topologySpreadConstraints | default .Values.global.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- range $constraint := . }}
      - {{ toYaml $constraint | nindent 8 | trim }}
          {{- if not $constraint.labelSelector }}
        labelSelector:
          matchLabels:
            {{- include "argo-cd.selectorLabels" (dict "context" $ "name" $.Values.applicationSet.name) | nindent 12 }}
          {{- end }}
        {{- end }}
      {{- end }}
      volumes:
        {{- with .Values.applicationSet.extraVolumes }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        - name: ssh-known-hosts
          configMap:
            name: argocd-ssh-known-hosts-cm
        - name: tls-certs
          configMap:
            name: argocd-tls-certs-cm
        - name: gpg-keys
          configMap:
            name: argocd-gpg-keys-cm
        - name: gpg-keyring
          {{- if .Values.applicationSet.emptyDir.sizeLimit }}
          emptyDir:
            sizeLimit: {{ .Values.applicationSet.emptyDir.sizeLimit }}
          {{- else }}
          emptyDir: {}
          {{- end }}
        - name: tmp
          {{- if .Values.applicationSet.emptyDir.sizeLimit }}
          emptyDir:
            sizeLimit: {{ .Values.applicationSet.emptyDir.sizeLimit }}
          {{- else }}
          emptyDir: {}
          {{- end }}
        - name: argocd-repo-server-tls
          secret:
            secretName: argocd-repo-server-tls
            optional: true
            items:
              - key: tls.crt
                path: tls.crt
              - key: tls.key
                path: tls.key
              - key: ca.crt
                path: ca.crt
      {{- with .Values.applicationSet.dnsConfig }}
      dnsConfig:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      dnsPolicy: {{ .Values.applicationSet.dnsPolicy }}
