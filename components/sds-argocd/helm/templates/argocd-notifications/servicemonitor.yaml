{{- if and (.Capabilities.APIVersions.Has "monitoring.coreos.com/v1") .Values.notifications.enabled .Values.notifications.metrics.enabled .Values.notifications.metrics.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "argo-cd.notifications.fullname" . }}
  namespace: {{ default (include  "argo-cd.namespace" .) .Values.notifications.metrics.serviceMonitor.namespace | quote }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.notifications.name "name" .Values.notifications.name) | nindent 4 }}
    {{- with .Values.notifications.metrics.serviceMonitor.selector }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- if .Values.notifications.metrics.serviceMonitor.additionalLabels }}
      {{- toYaml .Values.notifications.metrics.serviceMonitor.additionalLabels | nindent 4 }}
    {{- end }}
  {{- with .Values.notifications.metrics.serviceMonitor.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  endpoints:
    - port: {{ .Values.notifications.metrics.service.portName }}
      path: /metrics
      {{- if .Values.notifications.metrics.serviceMonitor.interval }}
      interval: {{ .Values.notifications.metrics.serviceMonitor.interval }}
      {{- end }}
      {{- if .Values.notifications.metrics.serviceMonitor.scrapeTimeout }}
      scrapeTimeout: {{ .Values.notifications.metrics.serviceMonitor.scrapeTimeout }}
      {{- end }}
      {{- with .Values.notifications.metrics.serviceMonitor.scheme }}
      scheme: {{ . }}
      {{- end }}
      {{- with .Values.notifications.metrics.serviceMonitor.tlsConfig }}
      tlsConfig:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.notifications.metrics.serviceMonitor.relabelings }}
      relabelings:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.notifications.metrics.serviceMonitor.metricRelabelings }}
      metricRelabelings:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      honorLabels: {{ .Values.notifications.metrics.serviceMonitor.honorLabels }}
  namespaceSelector:
    matchNames:
      - {{ include "argo-cd.namespace" . }}
  selector:
    matchLabels:
      {{- include "argo-cd.selectorLabels" (dict "context" . "component" .Values.notifications.name "name" "metrics") | nindent 6 }}
{{- end }}
