{{- if and .Values.notifications.enabled .Values.notifications.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.notifications.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ include "argo-cd.notifications.serviceAccountName" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  {{- with .Values.notifications.serviceAccount.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.notifications.name "name" .Values.notifications.name) | nindent 4 }}
    {{- with .Values.notifications.serviceAccount.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end }}
