{{- if .Values.notifications.enabled }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ template "argo-cd.notifications.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.notifications.name "name" .Values.notifications.name) | nindent 4 }}
rules:
- apiGroups:
  - argoproj.io
  resources:
  - applications
  - appprojects
  verbs:
  - get
  - list
  - watch
  - update
  - patch
- apiGroups:
  - ""
  resources:
  - configmaps
  - secrets
  verbs:
  - list
  - watch
- apiGroups:
  - ""
  resourceNames:
  - argocd-notifications-cm
  resources:
  - configmaps
  verbs:
  - get
- apiGroups:
  - ""
  resourceNames:
  - {{ .Values.notifications.secret.name }}
  resources:
  - secrets
  verbs:
  - get
{{- end }}
