{{- $redisHa := (index .Values "redis-ha") -}}
{{- if and .Values.global.networkPolicy.create .Values.redis.enabled (not $redisHa.enabled) }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.redis.name "name" .Values.redis.name) | nindent 4 }}
  name: {{ template "argo-cd.redis.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
spec:
  ingress:
  - from:
    - podSelector:
        matchLabels:
          {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.server.name) | nindent 10 }}
    - podSelector:
        matchLabels:
          {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.repoServer.name) | nindent 10 }}
    - podSelector:
        matchLabels:
          {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.controller.name) | nindent 10 }}
    ports:
    - port: redis
      protocol: TCP
  {{- if .Values.redis.metrics.enabled }}
  - from:
    - namespaceSelector: {}
    ports:
    - port: metrics
      protocol: TCP
  {{- end }}
  podSelector:
    matchLabels:
      {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.redis.name) | nindent 6 }}
  policyTypes:
  - Ingress
{{- end }}
