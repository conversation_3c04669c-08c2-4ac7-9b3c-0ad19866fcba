{{- $redisHa := index .Values "redis-ha" -}}
{{- if and .Values.redis.enabled (not $redisHa.enabled) .Values.redis.pdb.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "argo-cd.redis.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.redis.name "name" .Values.redis.name) | nindent 4 }}
    {{- with .Values.redis.pdb.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.redis.pdb.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
spec:
  {{- with .Values.redis.pdb.maxUnavailable }}
  maxUnavailable: {{ . }}
  {{- else }}
  minAvailable: {{ .Values.redis.pdb.minAvailable | default 0 }}
  {{- end }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "argo-cd.name" . }}-{{ .Values.redis.name }}
{{- end }}
