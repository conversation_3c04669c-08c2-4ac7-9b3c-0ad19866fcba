{{- if .Values.createAggregateRoles }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "argo-cd.fullname" . }}-aggregate-to-view
  labels:
    rbac.authorization.k8s.io/aggregate-to-view: "true"
    {{- include "argo-cd.labels" (dict "context" .) | nindent 4 }}
rules:
- apiGroups:
  - argoproj.io
  resources:
  - applications
  - applicationsets
  - appprojects
  verbs:
  - get
  - list
  - watch

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "argo-cd.fullname" . }}-aggregate-to-edit
  labels:
    rbac.authorization.k8s.io/aggregate-to-edit: "true"
    {{- include "argo-cd.labels" (dict "context" .) | nindent 4 }}
rules:
- apiGroups:
  - argoproj.io
  resources:
  - applications
  - applicationsets
  - appprojects
  verbs:
  - create
  - delete
  - deletecollection
  - get
  - list
  - patch
  - update
  - watch

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "argo-cd.fullname" . }}-aggregate-to-admin
  labels:
    rbac.authorization.k8s.io/aggregate-to-admin: "true"
    {{- include "argo-cd.labels" (dict "context" .) | nindent 4 }}
rules:
- apiGroups:
  - argoproj.io
  resources:
  - applications
  - applicationsets
  - appprojects
  verbs:
  - create
  - delete
  - deletecollection
  - get
  - list
  - patch
  - update
  - watch
{{- end }}
