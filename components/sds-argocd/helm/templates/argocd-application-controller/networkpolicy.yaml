{{- if .Values.global.networkPolicy.create }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.controller.name "name" .Values.controller.name) | nindent 4 }}
  name: {{ template "argo-cd.controller.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
spec:
  ingress:
  - from:
    - namespaceSelector: {}
    ports:
    - port: metrics
  podSelector:
    matchLabels:
      {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.controller.name) | nindent 6 }}
  policyTypes:
  - Ingress
{{- end }}
