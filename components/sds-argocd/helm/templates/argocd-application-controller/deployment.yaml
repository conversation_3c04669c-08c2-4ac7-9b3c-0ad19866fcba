{{- if .Values.controller.dynamicClusterDistribution }}
apiVersion: apps/v1
kind: Deployment
metadata:
  {{- with (mergeOverwrite (deepCopy .Values.global.deploymentAnnotations) .Values.controller.deploymentAnnotations) }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
  name: {{ template "argo-cd.controller.fullname" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.controller.name "name" .Values.controller.name) | nindent 4 }}
spec:
  replicas: {{ .Values.controller.replicas }}
  revisionHistoryLimit: {{ .Values.controller.revisionHistoryLimit | default .Values.global.revisionHistoryLimit }}
  selector:
    matchLabels:
      {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.controller.name) | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/cmd-params: {{ include (print $.Template.BasePath "/argocd-configs/argocd-cmd-params-cm.yaml") . | sha256sum }}
        {{- if .Values.configs.cm.create }}
        checksum/cm: {{ include (print $.Template.BasePath "/argocd-configs/argocd-cm.yaml") . | sha256sum }}
        {{- end }}
        {{- with (mergeOverwrite (deepCopy .Values.global.podAnnotations) .Values.controller.podAnnotations) }}
        {{- range $key, $value := . }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
        {{- end }}
      labels:
        {{- include "argo-cd.labels" (dict "context" . "component" .Values.controller.name "name" .Values.controller.name) | nindent 8 }}
        {{- with (mergeOverwrite (deepCopy .Values.global.podLabels) .Values.controller.podLabels) }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.controller.runtimeClassName | default .Values.global.runtimeClassName }}
      runtimeClassName: {{ . }}
      {{- end }}
      {{- with .Values.controller.imagePullSecrets | default .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.global.hostAliases }}
      hostAliases:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.global.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.controller.priorityClassName | default .Values.global.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
      {{- if .Values.controller.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.controller.terminationGracePeriodSeconds }}
      {{- end }}
      serviceAccountName: {{ include "argo-cd.controller.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.controller.automountServiceAccountToken }}
      containers:
      - args:
        - /usr/local/bin/argocd-application-controller
        - --metrics-port={{ .Values.controller.containerPorts.metrics }}
        {{- if .Values.controller.metrics.applicationLabels.enabled }}
        {{- range .Values.controller.metrics.applicationLabels.labels }}
        - --metrics-application-labels
        - {{ . }}
        {{- end }}
        {{- end }}
        {{- with .Values.controller.extraArgs }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        image: {{ default .Values.global.image.repository .Values.controller.image.repository }}:{{ default (include "argo-cd.defaultTag" .) .Values.controller.image.tag }}
        imagePullPolicy: {{ default .Values.global.image.imagePullPolicy .Values.controller.image.imagePullPolicy }}
        name: {{ .Values.controller.name }}
        env:
          {{- with (concat .Values.global.env .Values.controller.env) }}
            {{- toYaml . | nindent 10 }}
          {{- end }}
          - name: ARGOCD_ENABLE_DYNAMIC_CLUSTER_DISTRIBUTION
            value: "true"
          - name: ARGOCD_CONTROLLER_HEARTBEAT_TIME
            value: {{ .Values.controller.heartbeatTime | quote }}
          - name: ARGOCD_APPLICATION_CONTROLLER_NAME
            value: {{ template "argo-cd.controller.fullname" . }}
          - name: ARGOCD_RECONCILIATION_TIMEOUT
            valueFrom:
              configMapKeyRef:
                name: argocd-cm
                key: timeout.reconciliation
                optional: true
          - name: ARGOCD_HARD_RECONCILIATION_TIMEOUT
            valueFrom:
              configMapKeyRef:
                name: argocd-cm
                key: timeout.hard.reconciliation
                optional: true
          - name: ARGOCD_RECONCILIATION_JITTER
            valueFrom:
              configMapKeyRef:
                key: timeout.reconciliation.jitter
                name: argocd-cm
                optional: true
          - name: ARGOCD_REPO_ERROR_GRACE_PERIOD_SECONDS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.repo.error.grace.period.seconds
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_REPO_SERVER
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: repo.server
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_REPO_SERVER_TIMEOUT_SECONDS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.repo.server.timeout.seconds
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_STATUS_PROCESSORS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.status.processors
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_OPERATION_PROCESSORS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.operation.processors
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_LOGFORMAT
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.log.format
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_LOGLEVEL
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.log.level
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_METRICS_CACHE_EXPIRATION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.metrics.cache.expiration
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_SELF_HEAL_TIMEOUT_SECONDS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.self.heal.timeout.seconds
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_SELF_HEAL_BACKOFF_TIMEOUT_SECONDS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.self.heal.backoff.timeout.seconds
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_SELF_HEAL_BACKOFF_FACTOR
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.self.heal.backoff.factor
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_SELF_HEAL_BACKOFF_CAP_SECONDS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.self.heal.backoff.cap.seconds
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_SYNC_TIMEOUT
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.sync.timeout.seconds
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_REPO_SERVER_PLAINTEXT
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.repo.server.plaintext
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_REPO_SERVER_STRICT_TLS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.repo.server.strict.tls
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_PERSIST_RESOURCE_HEALTH
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.resource.health.persist
                optional: true
          - name: ARGOCD_APP_STATE_CACHE_EXPIRATION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.app.state.cache.expiration
                optional: true
          - name: REDIS_SERVER
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: redis.server
                optional: true
          - name: REDIS_COMPRESSION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: redis.compression
                optional: true
          - name: REDISDB
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: redis.db
                optional: true
          - name: REDIS_USERNAME
            valueFrom:
              secretKeyRef:
                name: {{ default "argocd-redis" .Values.externalRedis.existingSecret }}
                key: redis-username
                optional: true
          - name: REDIS_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ default "argocd-redis" .Values.externalRedis.existingSecret }}
                {{- if .Values.externalRedis.host }}
                key: redis-password
                {{- else }}
                key: auth
                {{- end }}
                optional: true
          - name: REDIS_SENTINEL_USERNAME
            valueFrom:
              secretKeyRef:
                name: {{ default (include "argo-cd.redis.fullname" .) .Values.externalRedis.existingSecret }}
                key: redis-sentinel-username
                optional: true
          - name: REDIS_SENTINEL_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ default (include "argo-cd.redis.fullname" .) .Values.externalRedis.existingSecret }}
                key: redis-sentinel-password
                optional: true
          - name: ARGOCD_DEFAULT_CACHE_EXPIRATION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.default.cache.expiration
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_OTLP_ADDRESS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: otlp.address
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_OTLP_INSECURE
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: otlp.insecure
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_OTLP_HEADERS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: otlp.headers
                optional: true
          - name: ARGOCD_APPLICATION_NAMESPACES
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: application.namespaces
                optional: true
          - name: ARGOCD_CONTROLLER_SHARDING_ALGORITHM
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.sharding.algorithm
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_KUBECTL_PARALLELISM_LIMIT
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.kubectl.parallelism.limit
                optional: true
          - name: ARGOCD_K8SCLIENT_RETRY_MAX
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.k8sclient.retry.max
                optional: true
          - name: ARGOCD_K8SCLIENT_RETRY_BASE_BACKOFF
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.k8sclient.retry.base.backoff
                optional: true
          - name: ARGOCD_APPLICATION_CONTROLLER_SERVER_SIDE_DIFF
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.diff.server.side
                optional: true
          - name: ARGOCD_IGNORE_NORMALIZER_JQ_TIMEOUT
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.ignore.normalizer.jq.timeout
                optional: true
          - name: ARGOCD_HYDRATOR_ENABLED
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: hydrator.enabled
                optional: true
          - name: ARGOCD_CLUSTER_CACHE_BATCH_EVENTS_PROCESSING
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.cluster.cache.batch.events.processing
                optional: true
          - name: ARGOCD_CLUSTER_CACHE_EVENTS_PROCESSING_INTERVAL
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: controller.cluster.cache.events.processing.interval
                optional: true
        {{- with .Values.controller.envFrom }}
        envFrom:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        ports:
        - name: metrics
          containerPort: {{ .Values.controller.containerPorts.metrics }}
          protocol: TCP
        readinessProbe:
          httpGet:
            path: /healthz
            port: metrics
          initialDelaySeconds: {{ .Values.controller.readinessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.controller.readinessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.controller.readinessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.controller.readinessProbe.successThreshold }}
          failureThreshold: {{ .Values.controller.readinessProbe.failureThreshold }}
        resources:
          {{- toYaml .Values.controller.resources | nindent 10 }}
        {{- with .Values.controller.containerSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        workingDir: /home/<USER>
        volumeMounts:
        {{- with .Values.controller.volumeMounts }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        - mountPath: /app/config/controller/tls
          name: argocd-repo-server-tls
        - mountPath: /home/<USER>
          name: argocd-home
        - name: argocd-cmd-params-cm
          mountPath: /home/<USER>/params
      {{- with .Values.controller.extraContainers }}
        {{- tpl (toYaml .) $ | nindent 6 }}
      {{- end }}
      {{- with .Values.controller.initContainers }}
      initContainers:
        {{- tpl (toYaml .) $ | nindent 6 }}
      {{- end }}
      {{- with include "argo-cd.affinity" (dict "context" . "component" .Values.controller) }}
      affinity:
        {{- trim . | nindent 8 }}
      {{- end }}
      {{- with .Values.controller.nodeSelector | default .Values.global.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.controller.tolerations | default .Values.global.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.controller.topologySpreadConstraints | default .Values.global.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- range $constraint := . }}
      - {{ toYaml $constraint | nindent 8 | trim }}
          {{- if not $constraint.labelSelector }}
        labelSelector:
          matchLabels:
            {{- include "argo-cd.selectorLabels" (dict "context" $ "name" $.Values.controller.name) | nindent 12 }}
          {{- end }}
        {{- end }}
      {{- end }}
      volumes:
      {{- with .Values.controller.volumes }}
        {{- toYaml . | nindent 6 }}
      {{- end }}
      - name: argocd-home
        {{- if .Values.controller.emptyDir.sizeLimit }}
        emptyDir:
          sizeLimit: {{ .Values.controller.emptyDir.sizeLimit }}
        {{- else }}
        emptyDir: {}
        {{- end }}

      - name: argocd-repo-server-tls
        secret:
          secretName: argocd-repo-server-tls
          optional: true
          items:
          - key: tls.crt
            path: tls.crt
          - key: tls.key
            path: tls.key
          - key: ca.crt
            path: ca.crt
      - name: argocd-cmd-params-cm
        configMap:
          optional: true
          name: argocd-cmd-params-cm
          items:
          - key: controller.profile.enabled
            path: profiler.enabled
      {{- if .Values.controller.hostNetwork }}
      hostNetwork: {{ .Values.controller.hostNetwork }}
      {{- end }}
      {{- with .Values.controller.dnsConfig }}
      dnsConfig:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      dnsPolicy: {{ .Values.controller.dnsPolicy }}
{{- end }}
