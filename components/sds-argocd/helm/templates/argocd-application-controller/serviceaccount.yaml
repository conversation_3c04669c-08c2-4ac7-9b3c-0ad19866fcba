{{- if .Values.controller.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.controller.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ include "argo-cd.controller.serviceAccountName" . }}
  namespace: {{ include  "argo-cd.namespace" . }}
  {{- with .Values.controller.serviceAccount.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.controller.name "name" .Values.controller.name) | nindent 4 }}
    {{- with .Values.controller.serviceAccount.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end }}
