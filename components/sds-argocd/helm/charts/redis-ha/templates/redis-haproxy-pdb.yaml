{{- if .Values.haproxy.podDisruptionBudget -}}
apiVersion: {{ template "redis-ha.podDisruptionBudget.apiVersion" . }}
kind: PodDisruptionBudget
metadata:
  name: {{ template "redis-ha.fullname" . }}-haproxy-pdb
  namespace: {{ .Release.Namespace | quote }}
  labels:
{{ include "labels.standard" . | indent 4 }}
    {{- range $key, $value := .Values.extraLabels }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
spec:
  selector:
    matchLabels:
      release: {{ .Release.Name }}
      app: {{ template "redis-ha.name" . }}-haproxy
{{ toYaml .Values.haproxy.podDisruptionBudget | indent 2 }}
{{- end -}}
