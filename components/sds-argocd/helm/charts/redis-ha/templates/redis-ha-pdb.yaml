{{- if .Values.podDisruptionBudget -}}
apiVersion: {{ template "redis-ha.podDisruptionBudget.apiVersion" . }}
kind: PodDisruptionBudget
metadata:
  name: {{ template "redis-ha.fullname" . }}-pdb
  namespace: {{ .Release.Namespace | quote }}
  labels:
{{ include "labels.standard" . | indent 4 }}
    {{- range $key, $value := .Values.extraLabels }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
spec:
  selector:
    matchLabels:
      # The replica label is set on StatefulSet pods but not the Test pods
      # We want to avoid including the Test pods in the budget
      {{ template "redis-ha.fullname" . }}: replica
      release: {{ .Release.Name }}
      app: {{ template "redis-ha.name" . }}
{{ toYaml .Values.podDisruptionBudget | indent 2 }}
{{- end -}}
