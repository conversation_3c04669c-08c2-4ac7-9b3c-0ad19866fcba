annotations:
  artifacthub.io/changes: |
    - kind: changed
      description: Bump argo-cd to v2.14.4
  artifacthub.io/signKey: |
    fingerprint: 2B8F22F57260EFA67BE1C5824B11F800CD9D2252
    url: https://argoproj.github.io/argo-helm/pgp_keys.asc
apiVersion: v2
appVersion: v2.14.4
dependencies:
- condition: redis-ha.enabled
  name: redis-ha
  repository: https://dandydeveloper.github.io/charts/
  version: 4.29.4
description: A Helm chart for Argo CD, a declarative, GitOps continuous delivery tool
  for Kubernetes.
home: https://github.com/argoproj/argo-helm
icon: https://argo-cd.readthedocs.io/en/stable/assets/logo.png
keywords:
- argoproj
- argocd
- gitops
kubeVersion: '>=1.25.0-0'
maintainers:
- name: argoproj
  url: https://argoproj.github.io/
name: sds-argocd
sources:
- https://github.com/argoproj/argo-helm/tree/main/charts/argo-cd
- https://github.com/argoproj/argo-cd
version: 7.8.8
