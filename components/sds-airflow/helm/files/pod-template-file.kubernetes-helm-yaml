{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}
---
{{- $nodeSelector := or .Values.workers.nodeSelector .Values.nodeSelector }}
{{- $affinity := or .Values.workers.affinity .Values.affinity }}
{{- $tolerations := or .Values.workers.tolerations .Values.tolerations }}
{{- $topologySpreadConstraints := or .Values.workers.topologySpreadConstraints .Values.topologySpreadConstraints }}
{{- $securityContext := include "airflowPodSecurityContext" (list . .Values.workers) }}
{{- $containerSecurityContextKerberosSidecar := include "containerSecurityContext" (list . .Values.workers.kerberosSidecar) }}
{{- $containerLifecycleHooksKerberosSidecar := or .Values.workers.kerberosSidecar.containerLifecycleHooks .Values.containerLifecycleHooks }}
{{- $containerSecurityContext := include "containerSecurityContext" (list . .Values.workers) }}
{{- $containerLifecycleHooks := or .Values.workers.containerLifecycleHooks .Values.containerLifecycleHooks }}
{{- $safeToEvict := dict "cluster-autoscaler.kubernetes.io/safe-to-evict" (.Values.workers.safeToEvict | toString) }}
{{- $podAnnotations := mergeOverwrite (deepCopy .Values.airflowPodAnnotations) $safeToEvict .Values.workers.podAnnotations }}
apiVersion: v1
kind: Pod
metadata:
  name: dummy-name
  labels:
    app.kubernetes.io/name: airflow2-workers
    tier: airflow
    component: worker
    application_name: airflow
    sds_app_type: component
    application_component_name: airflow-worker
    release: {{ .Release.Name }}
{{- with .Values.labels }}
{{ toYaml . | indent 4 }}
    {{- end }}
{{- include "custom.azureLabel" . | nindent 4 }}
  {{- if .Values.airflowPodAnnotations }}
  annotations:
  {{- toYaml .Values.airflowPodAnnotations | nindent 4 }}
  {{- end }}
  {{- if .Values.workers.podAnnotations }}
  annotations:
  {{- toYaml .Values.workers.podAnnotations | nindent 4 }}
    {{- end }}
spec:
  {{- if .Values.initCopy.enabled }}
  initContainers:
    - name: copy-data-from-object-storage
      image: {{ .Values.initCopy.repository }}:{{ .Values.initCopy.tag }}
      securityContext:
        runAsUser: 1000
      imagePullPolicy: {{ .Values.initCopy.pullPolicy }}
      {{- if eq $.Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
      command:
      - 'sh'
      - '-c'
      - |
        /tmp/aws/bin/aws s3 cp s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE }}/{{ .Values.initCopy.dagsPath }} /opt/airflow/sds/dags/ --recursive && \
        /tmp/aws/bin/aws s3 cp s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE }}/{{ .Values.initCopy.pluginsPath }} /opt/airflow/sds/plugins/ --recursive && \
        /tmp/aws/bin/aws s3 cp s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE }}/{{ .Values.initCopy.commonsPath }} /opt/airflow/sds/commons/ --recursive && \
        /tmp/aws/bin/aws s3 cp s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE }}/{{ .Values.initCopy.clientPath }} /opt/airflow/sds/client_requirements/ --recursive && \
        find "/opt/airflow/sds/" -type f -name "*.tgz" | while read -r tgz_file; do \
          dir_name=$(dirname "$tgz_file"); \
          tar -xzvf "$tgz_file" -C "$dir_name"; \
          rm "$tgz_file"; \
        done
      {{- end }}
      {{- if eq $.Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
      command:
      - 'sh'
      - '-c'
      - |
        cat $AZURE_FEDERATED_TOKEN_FILE | xargs -I{} az login --federated-token {} --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID && \
        az storage blob download-batch  --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} -d /tmp/ -s {{ .Values.global.BLOB_APPS_CONTAINER_NAME }}   --pattern "{{ .Values.global.DEPLOY_NAMESPACE }}/{{ .Values.initCopy.dagsPath }}*.tgz" --auth-mode login && \
        az storage blob download-batch  --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} -d /tmp/ -s {{ .Values.global.BLOB_APPS_CONTAINER_NAME }}   --pattern "{{ .Values.global.DEPLOY_NAMESPACE }}/{{ .Values.initCopy.pluginsPath }}*.tgz" --auth-mode login && \
        az storage blob download-batch  --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} -d /tmp/ -s {{ .Values.global.BLOB_APPS_CONTAINER_NAME }}   --pattern "{{ .Values.global.DEPLOY_NAMESPACE }}/{{ .Values.initCopy.commonsPath }}*.tgz" --auth-mode login && \
        mv /tmp/{{ .Values.global.DEPLOY_NAMESPACE }}/sds/orchestration/lib/latest/* /opt/airflow/sds/
        find "/opt/airflow/sds/" -type f -name "*.tgz" | while read -r tgz_file; do \
          dir_name=$(dirname "$tgz_file"); \
          tar -xzvf "$tgz_file" -C "$dir_name"; \
          rm "$tgz_file"; \
        done
      {{- end }}
      volumeMounts:
      - name: sds-storage
        mountPath: "/opt/airflow/sds"
    {{- if and .Values.dags.gitSync.enabled (not .Values.dags.persistence.enabled) }}
    {{- include "git_sync_container" (dict "Values" .Values "is_init" "true") | nindent 4 }}
    {{- end }}
    {{- if .Values.workers.extraInitContainers }}
    {{- toYaml .Values.workers.extraInitContainers | nindent 4 }}
    {{- end }}
    {{- end }}
  volumes:
  - name: sds-storage
    emptyDir: {}
  containers:
    - envFrom:
      {{- include "custom_airflow_environment_from" . | default "\n  []" | indent 6 }}
      env:
      {{- if .Values.global.SELF_SIGNED_CERT }}
        - name: REQUESTS_CA_BUNDLE
          value: "/mnt/CABundle/pythoncacerts"
      {{- end }}
        - name: AIRFLOW__CORE__EXECUTOR
          value: LocalExecutor
        {{- if .Values.shared.persistence.enabled }}
        {{- end }}
        {{- include "standard_airflow_environment" . | indent 6}}
        {{- include "custom_airflow_environment" . | indent 6 }}
      image: {{ template "pod_template_image" . }}
      imagePullPolicy: {{ .Values.images.airflow.pullPolicy }}
      name: base
      resources:
{{ toYaml .Values.workers.resources | indent 8 }}
      volumeMounts:
        - name: sds-storage
          mountPath: "/opt/airflow/sds"
        - mountPath: {{ template "airflow_logs" . }}
          name: logs
        {{- if .Values.shared.persistence.enabled }}
        - mountPath: {{ template "airflow_shared" . }}
          name: shared
        {{- end }}
        - name: config
          mountPath: {{ template "airflow_config_path" . }}
          subPath: airflow.cfg
          readOnly: true
{{- if .Values.airflowLocalSettings }}
        - name: config
          mountPath: {{ template "airflow_local_setting_path" . }}
          subPath: airflow_local_settings.py
          readOnly: true
{{- end }}
{{- if .Values.global.SELF_SIGNED_CERT }}
        - name: pythoncacerts
          mountPath: "/mnt/CABundle"
          readOnly: true
{{- end }}
        {{- if or .Values.dags.gitSync.enabled .Values.dags.persistence.enabled }}
          {{- include "airflow_dags_mount" . | nindent 8 }}
        {{- end }}
        {{- if .Values.workers.extraVolumeMounts }}
{{ toYaml .Values.workers.extraVolumeMounts | indent 8 }}
    {{- end }}
    {{- if .Values.workers.extraContainers }}
{{- toYaml .Values.workers.extraContainers | nindent 4 }}
  {{- end }}
  {{- if or .Values.registry.secretName .Values.registry.connection }}
  imagePullSecrets:
    - name: {{ template "registry_secret" . }}
  {{- end }}
  restartPolicy: Never
  securityContext: {{ $securityContext | nindent 4 }}
  nodeSelector: {{ toYaml $nodeSelector | nindent 4 }}
  affinity: {{ toYaml $affinity | nindent 4 }}
  tolerations: {{ toYaml $tolerations | nindent 4 }}
  topologySpreadConstraints: {{ toYaml $topologySpreadConstraints | nindent 4 }}
  serviceAccountName: {{ include "worker.serviceAccountName" . }}
  volumes:
  - name: sds-storage
    emptyDir:
      sizeLimit: 1Gi
  {{- if .Values.dags.persistence.enabled }}
  - name: dags
    persistentVolumeClaim:
      claimName: {{ template "airflow_dags_volume_claim" . }}
  {{- else if .Values.dags.gitSync.enabled }}
  - name: dags
    emptyDir: {}
  {{- end }}
  {{- if .Values.logs.persistence.enabled }}
  - name: logs
    persistentVolumeClaim:
      claimName: {{ template "airflow_logs_volume_claim" . }}
  {{- else }}
  - emptyDir: {}
    name: logs
  {{- end }}
  {{- if .Values.shared.persistence.enabled }}
  - name: shared
    persistentVolumeClaim:
      claimName: airflow-shared
  {{- end }}
  {{- if .Values.global.SELF_SIGNED_CERT }}
  - name: pythoncacerts
    secret:
      secretName: pythoncacerts
      defaultMode: 511
  {{- end }}
  {{- if and  .Values.dags.gitSync.enabled  .Values.dags.gitSync.sshKeySecret }}
    {{- include "git_sync_ssh_key_volume" . | nindent 2 }}
  {{- end }}
  - configMap:
      name: {{ include "airflow_config" . }}
    name: config
  {{- if .Values.workers.extraVolumes }}
  {{ toYaml .Values.workers.extraVolumes | nindent 2 }}
  {{- end }}