# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
---
# {{/*
# WARNING: Be aware that most changes to these mappings will break backwards compatibility!
# E.g. moving to labels will break existing dashboards.
# instead utilize `statsd.extraMappings` or `statsd.overrideMappings` in your environment,
# until we have version 2 of the helm chart.
# */}}
mappings:
  # Map dot separated stats to labels
  - match: airflow.dagrun.dependency-check.*.*
    name: "airflow_dagrun_dependency_check"
    labels:
      dag_id: "$1"

  - match: airflow.operator_successes_(.*)
    match_type: regex
    name: "airflow_operator_successes"
    labels:
      operator: "$1"

  - match: airflow.operator_failures_(.*)
    match_type: regex
    name: "airflow_operator_failures"
    labels:
      operator: "$1"

  - match: airflow.scheduler_heartbeat
    match_type: regex
    name: "airflow_scheduler_heartbeat"
    labels:
      type: counter

  - match: airflow.dag_processor_heartbeat
    match_type: regex
    name: "airflow_dag_processor_heartbeat"
    labels:
      type: counter

  - match: airflow.dag.*.*.duration
    name: "airflow_task_duration"
    labels:
      dag_id: "$1"
      task_id: "$2"

  - match: airflow.dagrun.duration.success.*
    name: "airflow_dagrun_duration"
    labels:
      dag_id: "$1"

  - match: airflow.dagrun.duration.failed.*
    name: "airflow_dagrun_failed"
    labels:
      dag_id: "$1"

  - match: airflow.dagrun.schedule_delay.*
    name: "airflow_dagrun_schedule_delay"
    labels:
      dag_id: "$1"

  - match: airflow.dag_processing.last_runtime.*
    name: "airflow_dag_processing_last_runtime"
    labels:
      dag_file: "$1"

  - match: airflow.dag_processing.last_run.seconds_ago.*
    name: "airflow_dag_processing_last_run_seconds_ago"
    labels:
      dag_file: "$1"

  - match: airflow.pool.open_slots.*
    name: "airflow_pool_open_slots"
    labels:
      pool: "$1"

  - match: airflow.pool.used_slots.*
    name: "airflow_pool_used_slots"
    labels:
      pool: "$1"

  - match: airflow.pool.starving_tasks.*
    name: "airflow_pool_starving_tasks"
    labels:
      pool: "$1"

  - match: airflow.executor.open_slots.*
    name: "airflow_executor_open_slots"
    labels:
      executor: "$1"

  - match: airflow.executor.queued_tasks.*
    name: "airflow_executor_queued_tasks"
    labels:
      executor: "$1"

  - match: airflow.executor.running_tasks.*
    name: "airflow_executor_running_tasks"
    labels:
      executor: "$1"

  - match: airflow.ti.running.*.*.*
    name: "airflow_ti_running"
    labels:
      queue: "$1"
      dag_id: "$2"
      task_id: "$3"
