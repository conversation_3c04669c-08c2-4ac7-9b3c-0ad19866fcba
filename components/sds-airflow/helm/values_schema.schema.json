{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "description": "This schema is used to validate `values.schema.json` to ensure each parameter has `default` and `description` set, and that top level properties have a `x-docsSection` set.", "definitions": {"leafs": {"additionalProperties": {"if": {"not": {"properties": {"description": {"pattern": "^Labels for the configmap$|^Labels for the secret$|^Annotations for the configmap$|^Annotations for the secret$"}}}}, "then": {"additionalProperties": {"$ref": "#/definitions/leafs"}}}, "if": {"oneOf": [{"properties": {"type": {"const": "integer"}}}, {"properties": {"type": {"const": "number"}}}, {"properties": {"type": {"const": "string"}}}, {"properties": {"type": {"const": "boolean"}}}, {"properties": {"type": {"const": "object"}, "properties": false}}, {"properties": {"type": {"const": "array"}, "items": false}}]}, "then": {"required": ["description", "default"]}}}, "required": ["x-docsSectionOrder"], "properties": {"section_order": {"type": "array", "items": {"type": "string"}}, "properties": {"additionalProperties": {"allOf": [{"$ref": "#/definitions/leafs"}, {"required": ["x-docsSection"]}]}}}}