################################
## PAI Metatable Creation
#################################
{{- $nodeSelector := or .Values.createMetatableJob.nodeSelector .Values.nodeSelector }}
{{- $affinity := or .Values.createMetatableJob.affinity .Values.affinity }}
{{- $tolerations := or .Values.createMetatableJob.tolerations .Values.tolerations }}
{{- $securityContext := include "airflowSecurityContext" (list . .Values.createMetatableJob) }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Release.Name }}-run-create-metatable
  labels:
    tier: airflow
    component: run-create-metatable
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
{{- with .Values.labels }}
{{ toYaml . | indent 4 }}
{{- end }}
  {{- $annotations := dict }}
  {{- if .Values.createMetatableJob.useHelmHooks }}
    {{- $_ := set $annotations "helm.sh/hook" "post-install,post-upgrade" }}
    {{- $_ := set $annotations "helm.sh/hook-weight" "1" }}
    {{- $_ := set $annotations "helm.sh/hook-delete-policy" "before-hook-creation,hook-succeeded" }}
    # {{- $_ := set $annotations "argocd.argoproj.io/hook" "Sync" }}
    # {{- $_ := set $annotations "argocd.argoproj.io/sync-wave" "3" }}
    # {{- $_ := set $annotations "argocd.argoproj.io/hook-delete-policy" "BeforeHookCreation,HookSucceeded" }}
  {{- end }}
  {{- with $annotations := merge $annotations .Values.createMetatableJob.jobAnnotations }}
  annotations:
    {{- $annotations | toYaml | nindent 4 }}
  {{- end }}
spec:
  template:
    metadata:
      labels:
        tier: airflow
        component: run-create-metatable
        release: {{ .Release.Name }}
{{- with .Values.labels }}
{{ toYaml . | indent 8 }}
{{- end }}
      {{- if or .Values.airflowPodAnnotations .Values.createMetatableJob.annotations }}
      annotations:
        {{- if .Values.airflowPodAnnotations }}
        {{- toYaml .Values.airflowPodAnnotations | nindent 8 }}
        {{- end }}
        {{- if .Values.createMetatableJob.annotations }}
        {{- toYaml .Values.createMetatableJob.annotations | nindent 8 }}
        {{- end }}
      {{- end }}
    spec:
      securityContext: {{ $securityContext | nindent 8 }}
      restartPolicy: OnFailure
      nodeSelector:
{{ toYaml $nodeSelector | indent 8 }}
      affinity:
{{ toYaml $affinity | indent 8 }}
      tolerations:
{{ toYaml $tolerations | indent 8 }}
      serviceAccountName: {{ include "createMetatableJob.serviceAccountName" . }}
      {{- if or .Values.registry.secretName .Values.registry.connection }}
      imagePullSecrets:
        - name: {{ template "registry_secret" . }}
      {{- end }}
      containers:
        - name: run-create-metatable
          image: {{ template "airflow_image_for_metatable" . }}
          imagePullPolicy: {{ .Values.images.airflow.pullPolicy }}
          args: ["bash", "-c", "until curl -fsI http://localhost:15021/healthz/ready; do echo \"Waiting for Sidecar...\"; sleep 3; done; echo \"Sidecar available. Running the command...\";
    python create_metatable.py; x=$(echo $?); curl -fsI -X POST http://localhost:15020/quitquitquit && exit $x"]
          #command: ["bash", "-c", "python create_metatable.py"]
          envFrom:
          {{- include "custom_airflow_environment_from" . | default "\n  []" | indent 10 }}
          env:
          {{- include "custom_airflow_environment" . | indent 10 }}
          {{- include "standard_airflow_environment" . | indent 10 }}
          resources:
{{ toYaml .Values.createMetatableJob.resources | indent 12 }}
          volumeMounts:
            - name: config
              mountPath: {{ template "airflow_config_path" . }}
              subPath: airflow.cfg
              readOnly: true
{{- if .Values.createMetatableJob.extraVolumeMounts }}
{{ toYaml .Values.createMetatableJob.extraVolumeMounts | nindent 12 }}
{{- end }}
{{- if .Values.extraVolumeMounts }}
{{ toYaml .Values.extraVolumeMounts | nindent 12 }}
{{- end }}
{{- if .Values.createMetatableJob.extraContainers }}
{{- toYaml .Values.createMetatableJob.extraContainers | nindent 8 }}
{{- end }}
      volumes:
        - name: config
          configMap:
            name: {{ template "airflow_config" . }}
{{- if .Values.createMetatableJob.extraVolumes }}
{{ toYaml .Values.createMetatableJob.extraVolumes | nindent 8 }}
{{- end }}
{{- if .Values.extraVolumes }}
{{ toYaml .Values.extraVolumes | nindent 8 }}
{{- end }}
