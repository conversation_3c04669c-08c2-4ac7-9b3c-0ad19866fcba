{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow Run Migrations
#################################
{{- if .Values.migrateDatabaseJob.enabled }}
{{- $nodeSelector := or .Values.migrateDatabaseJob.nodeSelector .Values.nodeSelector }}
{{- $affinity := or .Values.migrateDatabaseJob.affinity .Values.affinity }}
{{- $tolerations := or .Values.migrateDatabaseJob.tolerations .Values.tolerations }}
{{- $topologySpreadConstraints := or .Values.migrateDatabaseJob.topologySpreadConstraints .Values.topologySpreadConstraints }}
{{- $securityContext := include "airflowPodSecurityContext" (list . .Values.migrateDatabaseJob) }}
{{- $containerSecurityContext := include "containerSecurityContext" (list . .Values.migrateDatabaseJob) }}
{{- $containerLifecycleHooks := or .Values.migrateDatabaseJob.containerLifecycleHooks .Values.containerLifecycleHooks }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "airflow.fullname" . }}-run-airflow-migrations
  labels:
    tier: airflow
    component: run-airflow-migrations
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- with .Values.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- $annotations := dict }}
  {{- if .Values.migrateDatabaseJob.useHelmHooks }}
    {{- $_ := set $annotations "helm.sh/hook" "post-install,post-upgrade" }}
    {{- $_ := set $annotations "helm.sh/hook-weight" "1" }}
    {{- $_ := set $annotations "helm.sh/hook-delete-policy" "before-hook-creation,hook-succeeded" }}
  {{- end }}
  {{- with $annotations := merge $annotations .Values.migrateDatabaseJob.jobAnnotations }}
  annotations: {{- $annotations | toYaml | nindent 4 }}
  {{- end }}
spec:
  {{- if not (kindIs "invalid" .Values.migrateDatabaseJob.ttlSecondsAfterFinished) }}
  ttlSecondsAfterFinished: {{ .Values.migrateDatabaseJob.ttlSecondsAfterFinished }}
  {{- end }}
  template:
    metadata:
      labels:
        tier: airflow
        component: run-airflow-migrations
        release: {{ .Release.Name }}
        {{- if or (.Values.labels) (.Values.migrateDatabaseJob.labels) }}
          {{- mustMerge .Values.migrateDatabaseJob.labels .Values.labels | toYaml | nindent 8 }}
        {{- end }}
      {{- if or .Values.airflowPodAnnotations .Values.migrateDatabaseJob.annotations }}
      annotations:
        {{- if .Values.airflowPodAnnotations }}
          {{- toYaml .Values.airflowPodAnnotations | nindent 8 }}
        {{- end }}
        {{- if .Values.migrateDatabaseJob.annotations }}
          {{- toYaml .Values.migrateDatabaseJob.annotations | nindent 8 }}
        {{- end }}
      {{- end }}
    spec:
      securityContext: {{ $securityContext | nindent 8 }}
      restartPolicy: OnFailure
      {{- if .Values.migrateDatabaseJob.priorityClassName }}
      priorityClassName: {{ .Values.migrateDatabaseJob.priorityClassName }}
      {{- end }}
      nodeSelector: {{- toYaml $nodeSelector | nindent 8 }}
      affinity: {{- toYaml $affinity | nindent 8 }}
      {{- if .Values.schedulerName }}
      schedulerName: {{ .Values.schedulerName }}
      {{- end }}
      tolerations: {{- toYaml $tolerations | nindent 8 }}
      topologySpreadConstraints: {{- toYaml $topologySpreadConstraints | nindent 8 }}
      serviceAccountName: {{ include "migrateDatabaseJob.serviceAccountName" . }}
      {{- if or .Values.registry.secretName .Values.registry.connection }}
      imagePullSecrets:
        - name: {{ template "registry_secret" . }}
      {{- end }}
      {{- if .Values.migrateDatabaseJob.extraInitContainers }}
      initContainers:
        {{- tpl (toYaml .Values.migrateDatabaseJob.extraInitContainers) . | nindent 8 }}
      {{- end }}
      containers:
        - name: run-airflow-migrations
          image: {{ template "airflow_image_for_migrations" . }}
          imagePullPolicy: {{ .Values.images.airflow.pullPolicy }}
          securityContext: {{ $containerSecurityContext | nindent 12 }}
          {{- if $containerLifecycleHooks  }}
          lifecycle: {{- tpl (toYaml $containerLifecycleHooks) . | nindent 12 }}
          {{- end }}
          {{- if .Values.migrateDatabaseJob.command }}
          command: {{- tpl (toYaml .Values.migrateDatabaseJob.command) . | nindent 12 }}
          {{- end }}
          {{- if .Values.migrateDatabaseJob.args }}
          args: {{- tpl (toYaml .Values.migrateDatabaseJob.args) . | nindent 12 }}
          {{- end }}
          {{- if .Values.migrateDatabaseJob.applyCustomEnv }}
          envFrom: {{- include "custom_airflow_environment_from" . | default "\n  []" | indent 10 }}
          env: {{- include "custom_airflow_environment" . | indent 10 }}
          {{- else }}
          env:
          {{- end }}
            - name: PYTHONUNBUFFERED
              value: "1"
          {{- include "standard_airflow_environment" . | indent 10 }}
          {{- if .Values.migrateDatabaseJob.env }}
              {{- tpl (toYaml .Values.migrateDatabaseJob.env) $ | nindent 12 }}
          {{- end }}
          resources: {{- toYaml .Values.migrateDatabaseJob.resources | nindent 12 }}
          volumeMounts:
            {{- include "airflow_config_mount" . | nindent 12 }}
              {{- if .Values.volumeMounts }}
            {{- toYaml .Values.volumeMounts | nindent 12 }}
            {{- end }}
            {{- if .Values.migrateDatabaseJob.extraVolumeMounts }}
              {{- tpl (toYaml .Values.migrateDatabaseJob.extraVolumeMounts) . | nindent 12 }}
            {{- end }}
{{- if .Values.extraVolumeMounts }}
{{ toYaml .Values.extraVolumeMounts | nindent 12 }}
{{- end }}
        {{- if .Values.migrateDatabaseJob.extraContainers }}
          {{- tpl (toYaml .Values.migrateDatabaseJob.extraContainers) . | nindent 8 }}
        {{- end }}
      volumes:
        - name: config
          configMap:
            name: {{ template "airflow_config" . }}
        {{- if .Values.volumes }}
          {{- toYaml .Values.volumes | nindent 8 }}
        {{- end }}
        {{- if .Values.migrateDatabaseJob.extraVolumes }}
          {{- tpl (toYaml .Values.migrateDatabaseJob.extraVolumes) . | nindent 8 }}
        {{- end }}
{{- if .Values.extraVolumes }}
{{ toYaml .Values.extraVolumes | nindent 8 }}
{{- end }}
{{- end }}
