# Role for spark-on-k8s-operator to create resources on cluster
{{- if .Values.sparkaccess.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ .Release.Name }}-spark-cluster-cr
  labels:
    rbac.authorization.kubeflow.org/aggregate-to-kubeflow-edit: "true"
    tier: airflow
    component: airflow-spark-cluster-role
    release: {{ .Release.Name }}
rules:
  - apiGroups:
      - sparkoperator.k8s.io
    resources:
      - sparkapplications
    verbs:
      - '*'
{{- end }}
---
# Allow airflow-worker service account access for spark-on-k8s
{{- if .Values.sparkaccess.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ .Release.Name }}-spark-crb
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ .Release.Name }}-spark-cluster-cr
subjects:
  - kind: ServiceAccount
    name: {{ include "worker.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
{{- end }}

---
# Allow airflow-triggerer service account access for spark-on-k8s
{{- if .Values.sparkaccess.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ .Release.Name }}-triggerer-spark-crb
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ .Release.Name }}-spark-cluster-cr
subjects:
  - kind: ServiceAccount
    name: {{ include "triggerer.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
{{- end }}
