{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow Metadata Secret
#################################
{{- if not .Values.data.metadataSecretName }}
{{- $defaultMetadataHost :=  .Values.postgresql.nameOverride | default (printf "%s-%s.%s" .Release.Name "postgresql" .Release.Namespace) }}
{{- $metadataHost := .Values.data.metadataConnection.host | default $defaultMetadataHost }}
{{- $pgbouncerHost := (printf "%s-%s.%s" ( include "airflow.fullname" . ) "pgbouncer" .Release.Namespace) }}
{{- $host := ternary $pgbouncerHost $metadataHost .Values.pgbouncer.enabled }}
{{- $metadataPort := .Values.data.metadataConnection.port | toString }}
{{- $port := ((ternary .Values.ports.pgbouncer $metadataPort .Values.pgbouncer.enabled) | toString) }}
{{- $metadataDatabase := .Values.data.metadataConnection.db }}
{{- $database := (ternary (printf "%s-%s" .Release.Name "metadata") $metadataDatabase .Values.pgbouncer.enabled) }}
{{- $query := ternary (printf "sslmode=%s" .Values.data.metadataConnection.sslmode) "" (eq .Values.data.metadataConnection.protocol "postgresql") }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "airflow.fullname" . }}-metadata
  labels:
    tier: airflow
    release: {{ .Release.Name }}
    chart: {{ .Chart.Name }}
    heritage: {{ .Release.Service }}
    {{- with .Values.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
type: Opaque
data:
  {{- with .Values.data.metadataConnection }}
  connection: {{ urlJoin (dict "scheme" .protocol "userinfo" (printf "%s:%s" (.user | urlquery) (.pass | urlquery) ) "host" (printf "%s:%s" $host $port) "path" (printf "/%s" $database) "query" $query) | b64enc | quote }}
  {{- end }}
  {{- if and .Values.workers.keda.enabled .Values.pgbouncer.enabled (not .Values.workers.keda.usePgbouncer) }}
  {{- with .Values.data.metadataConnection }}
  kedaConnection: {{ urlJoin (dict "scheme" .protocol "userinfo" (printf "%s:%s" (.user | urlquery) (.pass | urlquery) ) "host" (printf "%s:%s" $metadataHost $metadataPort) "path" (printf "/%s" $metadataDatabase) "query" $query) | b64enc | quote }}
  {{- end }}
  {{- else if and (or .Values.workers.keda.enabled .Values.triggerer.keda.enabled) (eq .Values.data.metadataConnection.protocol "mysql") }}
  {{- with .Values.data.metadataConnection }}
  kedaConnection: {{ urlJoin (dict "userinfo" (printf "%s:%s" (.user | urlquery) (.pass | urlquery) ) "host" (printf "tcp(%s:%s)" $metadataHost $metadataPort) "path" (printf "/%s" $metadataDatabase) "query" $query) | trimPrefix "//" | b64enc | quote }}
  {{- end }}
  {{- end }}
{{- end }}
