{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

{{- if and .Values.dags.gitSync.sshKey .Values.dags.gitSync.enabled}}
apiVersion: v1
kind: Secret
metadata:
  labels:
    release: {{ .Release.Name }}
    chart: {{ .Chart.Name }}
    heritage: {{ .Release.Service }}
    {{- with .Values.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ template "git_sync_ssh_key" . }}
data:
  gitSshKey: {{ .Values.dags.gitSync.sshKey | b64enc | quote }}
{{- end }}
