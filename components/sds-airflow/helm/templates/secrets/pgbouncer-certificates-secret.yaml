{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Pgbouncer Certificate Secret
#################################
{{- if or .Values.pgbouncer.ssl.ca .Values.pgbouncer.ssl.cert .Values.pgbouncer.ssl.key }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "pgbouncer_certificates_secret" . }}
  labels:
    release: {{ .Release.Name }}
    chart: {{ .Chart.Name }}
    heritage: {{ .Release.Service }}
    {{- with .Values.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
type: Opaque
data:
  {{- if .Values.pgbouncer.ssl.ca }}
  root.crt: {{ .Values.pgbouncer.ssl.ca | b64enc }}
  {{- end }}
  {{- if .Values.pgbouncer.ssl.cert }}
  server.crt: {{ .Values.pgbouncer.ssl.cert | b64enc }}
  {{- end }}
  {{- if .Values.pgbouncer.ssl.key }}
  server.key: {{ .Values.pgbouncer.ssl.key | b64enc }}
  {{- end }}
{{- end }}
