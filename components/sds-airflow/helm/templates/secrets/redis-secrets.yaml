{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

# We will create these secrets (if necessary) _even if_ we aren't
# currently using CeleryExecutor or CeleryKubernetesExecutor. As we are
# relying on the "pre-install" hack to prevent changing randomly generated passwords,
# updating the executor later doesn't give us the opportunity to deploy them
# when we need them. We will always deploy them defensively to make the executor
# update path actually work.

################################
## Airflow Redis Password Secret
#################################
{{- $random_redis_password := randAlphaNum 10 }}
{{- if and .Values.redis.enabled (not .Values.redis.passwordSecretName) }}
# If passwordSecretName is not set, we will either use the set password, or use the generated one
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Release.Name }}-redis-password
  labels:
    tier: airflow
    component: redis
    release: {{ .Release.Name }}
    chart: {{ .Chart.Name }}
    heritage: {{ .Release.Service }}
    {{- with .Values.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  annotations:
    "helm.sh/hook": "pre-install"
    "helm.sh/hook-delete-policy": "before-hook-creation"
    "helm.sh/hook-weight": "0"
type: Opaque
data:
  password: {{ (default $random_redis_password .Values.redis.password) | b64enc | quote }}
---
{{- end }}
{{- if not .Values.data.brokerUrlSecretName }}
##################################
## Airflow Redis Connection Secret
##################################
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Release.Name }}-broker-url
  labels:
    tier: airflow
    component: redis
    release: {{ .Release.Name }}
    chart: {{ .Chart.Name }}
    heritage: {{ .Release.Service }}
    {{- with .Values.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  annotations:
    "helm.sh/hook": "pre-install"
    "helm.sh/hook-delete-policy": "before-hook-creation"
    "helm.sh/hook-weight": "0"
type: Opaque
data:
  {{- if .Values.redis.enabled }}
  connection: {{ urlJoin (dict "scheme" "redis" "userinfo" (printf ":%s" ((default $random_redis_password .Values.redis.password) | urlquery)) "host" (printf "%s-redis:6379" (include "airflow.fullname" .) ) "path" "/0") | b64enc | quote }}
  {{- else }}
  connection: {{ (printf "%s" .Values.data.brokerUrl) | b64enc | quote }}
  {{- end }}
{{- end }}
