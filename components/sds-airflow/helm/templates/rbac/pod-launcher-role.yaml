{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow Pod Launcher Role
#################################
{{- if and .Values.rbac.create .Values.allowPodLaunching }}
apiVersion: rbac.authorization.k8s.io/v1
{{- if .Values.multiNamespaceMode }}
kind: ClusterRole
{{- else }}
kind: Role
{{- end }}
metadata:
  {{- if not .Values.multiNamespaceMode }}
  name: {{ include "airflow.fullname" . }}-pod-launcher-role
  namespace: "{{ .Release.Namespace }}"
  {{- else }}
  name: {{ .Release.Namespace }}-{{ include "airflow.fullname" . }}-pod-launcher-role
  {{- end }}
  labels:
    tier: airflow
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- if .Values.multiNamespaceMode }}
    namespace: "{{ .Release.Namespace }}"
    {{- end }}
    {{- with .Values.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
rules:
  - apiGroups:
      - ""
    resources:
      - "pods"
    verbs:
      - "create"
      - "list"
      - "get"
      - "patch"
      - "watch"
      - "delete"
  - apiGroups:
      - ""
    resources:
      - "pods/log"
    verbs:
      - "get"
  - apiGroups:
      - ""
    resources:
      - "pods/exec"
    verbs:
      - "create"
      - "get"
  - apiGroups:
      - ""
    resources:
      - "events"
    verbs:
      - "list"
{{- end }}
