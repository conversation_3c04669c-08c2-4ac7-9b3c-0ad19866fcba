{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow SCC Role Binding
#################################
{{- if and .Values.rbac.create .Values.rbac.createSCCRoleBinding }}
{{- $hasWorkers := has .Values.executor (list "CeleryExecutor" "LocalKubernetesExecutor" "KubernetesExecutor" "CeleryKubernetesExecutor") }}
apiVersion: rbac.authorization.k8s.io/v1
{{- if .Values.multiNamespaceMode }}
kind: ClusterRoleBinding
{{- else }}
kind: RoleBinding
{{- end }}
metadata:
  {{- if not .Values.multiNamespaceMode }}
  name: {{ include "airflow.fullname" . }}-scc-rolebinding
  namespace: "{{ .Release.Namespace }}"
  {{- else }}
  name: {{ .Release.Namespace }}-{{ include "airflow.fullname" . }}-scc-rolebinding
  {{- end }}
  labels:
    tier: airflow
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- if .Values.multiNamespaceMode }}
    namespace: "{{ .Release.Namespace }}"
    {{- end }}
    {{- with .Values.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: system:openshift:scc:anyuid
subjects:
  - kind: ServiceAccount
    name: {{ include "webserver.serviceAccountName" . }}
    namespace: "{{ .Release.Namespace }}"
  {{- if $hasWorkers }}
  - kind: ServiceAccount
    name: {{ include "worker.serviceAccountName" . }}
    namespace: "{{ .Release.Namespace }}"
  {{- end }}
  - kind: ServiceAccount
    name: {{ include "scheduler.serviceAccountName" . }}
    namespace: "{{ .Release.Namespace }}"
  {{- if and .Values.statsd.enabled }}
  - kind: ServiceAccount
    name: {{ include "statsd.serviceAccountName" . }}
    namespace: "{{ .Release.Namespace }}"
  {{- end }}
  {{- if and .Values.flower.enabled (or (contains "CeleryExecutor" .Values.executor) (contains "CeleryKubernetesExecutor" .Values.executor)) }}
  - kind: ServiceAccount
    name: {{ include "flower.serviceAccountName" . }}
    namespace: "{{ .Release.Namespace }}"
  {{- end }}
  {{- if and (semverCompare ">=2.2.0" .Values.airflowVersion) }}
  - kind: ServiceAccount
    name: {{ include "triggerer.serviceAccountName" . }}
    namespace: "{{ .Release.Namespace }}"
  {{- end }}
  - kind: ServiceAccount
    name: {{ include "migrateDatabaseJob.serviceAccountName" . }}
    namespace: "{{ .Release.Namespace }}"
  {{- if .Values.webserver.defaultUser.enabled }}
  - kind: ServiceAccount
    name: {{ include "createUserJob.serviceAccountName" . }}
    namespace: "{{ .Release.Namespace }}"
  {{- end }}
  {{- if and .Values.cleanup.enabled }}
  - kind: ServiceAccount
    name: {{ include "cleanup.serviceAccountName" . }}
    namespace: "{{ .Release.Namespace }}"
  {{- end }}
{{- end }}
