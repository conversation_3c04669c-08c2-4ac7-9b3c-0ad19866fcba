{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

{{- /*
The sole purpose of this yaml file is it to check the values file is consistent for some complexe combinations.
*/ -}}

{{- /*
##############################
   Redis related checks
#############################
*/ -}}

  {{- if or (contains "CeleryExecutor" .Values.executor) (contains "CeleryKubernetesExecutor" .Values.executor) }}
    {{- if .Values.redis.enabled }}

      {{- if and .Values.redis.passwordSecretName (not .Values.data.brokerUrlSecretName) }}
        {{ required "When using the internal redis of the chart and setting the value redis.passwordSecretName, you must also set the value data.brokerUrlSecretName." nil }}
      {{- end }}

      {{- if and .Values.redis.passwordSecretName .Values.redis.password }}
        {{ required "You must not set both values redis.passwordSecretName and redis.password" nil }}
      {{- end }}

    {{- else }}

      {{- if not (or .Values.data.brokerUrlSecretName .Values.data.brokerUrl) }}
        {{ required "You must set one of the values data.brokerUrlSecretName or data.brokerUrl when using a Celery based executor with redis.enabled set to false (we need the url to the redis instance)." nil }}
      {{- end }}

    {{- end }}

    {{- if and .Values.data.brokerUrlSecretName .Values.data.brokerUrl }}
      {{ required "You must not set both values data.brokerUrlSecretName and data.brokerUrl" nil }}
    {{- end }}

  {{- end }}

  {{- if and .Values.elasticsearch.enabled .Values.opensearch.enabled  }}
    {{ required "You must not set both values elasticsearch.enabled and opensearch.enabled" nil }}
  {{- end }}

  {{- if .Values.elasticsearch.enabled  }}
    {{- if and .Values.elasticsearch.secretName .Values.elasticsearch.connection }}
      {{ required "You must not set both values elasticsearch.secretName and elasticsearch.connection" nil }}
    {{- end }}

    {{- if not (or .Values.elasticsearch.secretName .Values.elasticsearch.connection) }}
      {{ required "You must set one of the values elasticsearch.secretName or elasticsearch.connection when using a Elasticsearch" nil }}
    {{- end }}

  {{- end }}

  {{- if .Values.opensearch.enabled  }}
    {{- if and .Values.opensearch.secretName .Values.opensearch.connection }}
      {{ required "You must not set both values opensearch.secretName and opensearch.connection" nil }}
    {{- end }}

    {{- if not (or .Values.opensearch.secretName .Values.opensearch.connection) }}
      {{ required "You must set one of the values opensearch.secretName or opensearch.connection when using OpenSearch" nil }}
    {{- end }}

  {{- end }}
