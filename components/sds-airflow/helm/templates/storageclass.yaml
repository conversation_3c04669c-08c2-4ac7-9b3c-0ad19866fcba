{{- if eq ( tpl .Values.global.CLOUD_SERVICE_PROVIDER . ) "aws" }}
kind: StorageClass
apiVersion: storage.k8s.io/v1
metadata:
  name: efs-sc-{{ .Values.global.DEPLOY_NAMESPACE }}
provisioner: efs.csi.aws.com
parameters:
  provisioningMode: efs-ap
  fileSystemId: {{ .Values.global.EFS_ID }}
  directoryPerms: "700"
  gidRangeStart: "1000" # optional
  gidRangeEnd: "2000" # optional
  basePath: "/k8_airflow_data_{{ tpl .Values.clusterEnvironment . }}" # optional
{{- end }}