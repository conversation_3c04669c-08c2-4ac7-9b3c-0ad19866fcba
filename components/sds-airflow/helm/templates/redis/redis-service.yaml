{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow Redis Service
#################################
{{- if and .Values.redis.enabled (or (contains "CeleryExecutor" .Values.executor) (contains "CeleryKubernetesExecutor" .Values.executor)) }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "airflow.fullname" . }}-redis
  labels:
    tier: airflow
    component: redis
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- with .Values.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
{{- if eq .Values.redis.service.type "ClusterIP" }}
  type: ClusterIP
  {{- if .Values.redis.service.clusterIP }}
  clusterIP: {{ .Values.redis.service.clusterIP }}
  {{- end }}
{{- else }}
  type: {{ .Values.redis.service.type }}
{{- end }}
  selector:
    tier: airflow
    component: redis
    release: {{ .Release.Name }}
  ports:
    - name: redis-db
      protocol: TCP
      port: {{ .Values.ports.redisDB }}
      targetPort: {{ .Values.ports.redisDB }}
      {{- if (and (eq .Values.redis.service.type "NodePort") (not (empty .Values.redis.service.nodePort))) }}
      nodePort: {{ .Values.redis.service.nodePort }}
      {{- end }}
{{- end }}
