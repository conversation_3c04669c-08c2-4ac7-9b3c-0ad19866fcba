# Role for spark-on-k8s-operator to create resources on cluster
{{- if .Values.sparkaccess.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ .Release.Name }}-spark-cluster-cr
  labels:
    rbac.authorization.kubeflow.org/aggregate-to-kubeflow-edit: "true"
    component: airflow-spark-cluster-role
    release: {{ .Release.Name }}
rules:
  - apiGroups:
      - sparkoperator.k8s.io
    resources:
      - sparkapplications
    verbs:
      - '*'
{{- end }}