# Allow airflow-worker service account access for spark-on-k8s
{{- if .Values.sparkaccess.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ .Release.Name }}-spark-crb
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ .Release.Name }}-spark-cluster-cr
subjects:
  - kind: ServiceAccount
    name: {{ include "worker.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
{{- end }}
