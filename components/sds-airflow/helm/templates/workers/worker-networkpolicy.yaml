{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow Worker NetworkPolicy
#################################
{{- if and .Values.networkPolicies.enabled (or (contains "CeleryExecutor" .Values.executor) (contains "CeleryKubernetesExecutor" .Values.executor)) }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "airflow.fullname" . }}-worker-policy
  labels:
    tier: airflow
    component: airflow-worker-policy
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- if or (.Values.labels) (.Values.workers.labels) }}
      {{- mustMerge .Values.workers.labels .Values.labels | toYaml | nindent 4 }}
    {{- end }}
spec:
  podSelector:
    matchLabels:
      tier: airflow
      component: worker
      release: {{ .Release.Name }}
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          tier: airflow
          release: {{ .Release.Name }}
          component: webserver
    ports:
    - protocol: TCP
      port: {{ .Values.ports.workerLogs }}
{{- end }}
