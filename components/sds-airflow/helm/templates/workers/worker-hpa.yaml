{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow Worker HPA
#################################
{{- if and (and (not .Values.workers.keda.enabled) .Values.workers.hpa.enabled) (or (contains "CeleryExecutor" .Values.executor) (contains "CeleryKubernetesExecutor" .Values.executor)) }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "airflow.fullname" . }}-worker
  labels:
    tier: airflow
    component: worker-horizontalpodautoscaler
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    deploymentName: {{ .Release.Name }}-worker
    {{- if or (.Values.labels) (.Values.workers.labels) }}
      {{- mustMerge .Values.workers.labels .Values.labels | toYaml | nindent 4 }}
    {{- end }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: {{ ternary "StatefulSet" "Deployment" .Values.workers.persistence.enabled }}
    name: {{ include "airflow.fullname" . }}-worker
  minReplicas: {{ .Values.workers.hpa.minReplicaCount }}
  maxReplicas: {{ .Values.workers.hpa.maxReplicaCount }}
  metrics: {{- toYaml .Values.workers.hpa.metrics | nindent 4 }}
  {{- with .Values.workers.hpa.behavior }}
  behavior: {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
