apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: airflow-worker-role
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["*"]
- apiGroups: [""]
  resources: ["services"]
  verbs: ["*"]
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["*"]
- apiGroups: [""]
  resources: ["persistentvolumeclaims"]
  verbs: ["*"]
- apiGroups: [""]
  resources: ["sparkapplications"]
  verbs: [""]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["list","get"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["*"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["*"]
- apiGroups: ["apps"]
  resources: ["replicasets"]
  verbs: ["*"]
