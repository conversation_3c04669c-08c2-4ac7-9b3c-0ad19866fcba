{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow Cleanup ServiceAccount
#################################
{{- if and .Values.cleanup.serviceAccount.create .Values.cleanup.enabled }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.cleanup.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ include "cleanup.serviceAccountName" . }}
  labels:
    tier: airflow
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- if or (.Values.labels) (.Values.cleanup.labels) }}
      {{- mustMerge .Values.cleanup.labels .Values.labels | toYaml | nindent 4 }}
    {{- end }}
  annotations:
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
    eks.amazonaws.com/role-arn: "{{ .Values.global.SERVICE_ACCOUNT_ANNOTATION_ROLE_ARN }}"
  {{- else if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
    azure.workload.identity/client-id: "{{ .Values.global.AZURE_MANAGED_IDENTITY_ID }}"
  {{- end }}
{{- end }}
