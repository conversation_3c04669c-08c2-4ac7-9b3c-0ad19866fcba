{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow triggerer Service
#################################
{{- /* Airflow version 2.6.0 is when triggerer logs serve introduced */ -}}
{{- if semverCompare ">=2.6.0" .Values.airflowVersion }}
{{- if .Values.triggerer.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: airflow-triggerer
  labels:
    tier: airflow
    component: triggerer
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- if or (.Values.labels) (.Values.triggerer.labels) }}
      {{- mustMerge .Values.triggerer.labels .Values.labels | toYaml | nindent 4 }}
    {{- end }}
spec:
  clusterIP: None
  selector:
    tier: airflow
    component: triggerer
    release: {{ .Release.Name }}
  ports:
    - name: triggerer-logs
      protocol: TCP
      port: {{ .Values.ports.triggererLogs }}
      targetPort: {{ .Values.ports.triggererLogs }}
{{- end }}
{{- end }}
