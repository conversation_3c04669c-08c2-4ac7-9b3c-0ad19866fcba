{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow Pgbouncer Service
#################################
{{- if .Values.pgbouncer.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "airflow.fullname" . }}-pgbouncer
  labels:
    tier: airflow
    component: pgbouncer
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- if or (.Values.labels) (.Values.pgbouncer.labels) }}
      {{- mustMerge .Values.pgbouncer.labels .Values.labels | toYaml | nindent 4 }}
    {{- end }}
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: {{ .Values.ports.pgbouncerScrape | quote }}
    {{- if .Values.pgbouncer.service.extraAnnotations }}
      {{- toYaml .Values.pgbouncer.service.extraAnnotations | nindent 4 }}
    {{- end }}
spec:
  type: ClusterIP
  {{- if .Values.pgbouncer.service.clusterIp }}
  clusterIP: {{ .Values.pgbouncer.service.clusterIp }}
  {{- end }}
  selector:
    tier: airflow
    component: pgbouncer
    release: {{ .Release.Name }}
  ports:
    - name: pgbouncer
      protocol: TCP
      port: {{ .Values.ports.pgbouncer }}
    - name: pgb-metrics
      protocol: TCP
      port: {{ .Values.ports.pgbouncerScrape }}
{{- end }}
