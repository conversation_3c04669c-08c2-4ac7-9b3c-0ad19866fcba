{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow Pgbouncer Ingress
#################################
{{- if and .Values.pgbouncer.enabled .Values.ingress.pgbouncer.enabled }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "airflow.fullname" . }}-pgbouncer-ingress
  labels:
    tier: airflow
    component: pgbouncer-ingress
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- if or (.Values.labels) (.Values.pgbouncer.labels) }}
      {{- mustMerge .Values.pgbouncer.labels .Values.labels | toYaml | nindent 4 }}
    {{- end }}
  {{- with .Values.ingress.pgbouncer.annotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if and .Values.ingress.pgbouncer.hosts (.Values.ingress.pgbouncer.hosts | first | kindIs "string" | not) }}
  {{- $anyTlsHosts := false -}}
  {{- range .Values.ingress.pgbouncer.hosts }}
  {{- if .tls }}
  {{- if .tls.enabled }}
  {{- $anyTlsHosts = true -}}
  {{- end }}
  {{- end }}
  {{- end }}
  {{- if $anyTlsHosts }}
  tls:
  {{- range .Values.ingress.pgbouncer.hosts }}
  {{- if .tls }}
  {{- if .tls.enabled }}
    - hosts:
        - {{ .name | quote }}
      secretName: {{ .tls.secretName }}
  {{- end }}
  {{- end }}
  {{- end }}
  {{- end }}
  {{- end }}
  rules:
    {{- range .Values.ingress.pgbouncer.hosts | default (list .Values.ingress.pgbouncer.host) }}
    - http:
        paths:
          - backend:
              service:
                name: {{ $.Release.Name }}-pgbouncer
                port:
                  name: pgb-metrics
            {{- if $.Values.ingress.pgbouncer.path }}
            path: {{ $.Values.ingress.pgbouncer.path }}
            pathType: {{ $.Values.ingress.pgbouncer.pathType }}
            {{- end }}
      {{- $hostname := . -}}
      {{- if . | kindIs "string" | not }}
      {{- $hostname = .name -}}
      {{- end }}
      {{- if $hostname }}
      host: {{ tpl $hostname $ | quote }}
      {{- end }}
    {{- end }}
  {{- if .Values.ingress.pgbouncer.ingressClassName }}
  ingressClassName: {{ .Values.ingress.pgbouncer.ingressClassName }}
  {{- end }}
{{- end }}
