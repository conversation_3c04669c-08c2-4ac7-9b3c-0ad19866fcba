{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

#################################################
## Priority classes provisioned via the chart values
#################################################
{{- $Global := . }}
{{- range $e := .Values.priorityClasses }}
---
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: {{ $Global.Release.Name }}-{{ $e.name }}
  labels:
    release: {{ $Global.Release.Name }}
preemptionPolicy: {{ default "PreemptLowerPriority" $e.preemptionPolicy }}
value: {{ $e.value | required "value is required for priority classes" }}
description: "This priority class will not cause other pods to be preempted."
{{- end }}
