{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow Webserver Ingress
#################################
{{- if and .Values.webserver.enabled (semverCompare "<3.0.0" .Values.airflowVersion) }}
{{- if or .Values.ingress.web.enabled .Values.ingress.enabled }}
{{- $fullname := (include "airflow.fullname" .) }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullname }}-ingress
  labels:
    tier: airflow
    component: airflow-ingress
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- if or (.Values.labels) (.Values.webserver.labels) }}
      {{- mustMerge .Values.webserver.labels .Values.labels | toYaml | nindent 4 }}
    {{- end }}
  {{- with .Values.ingress.web.annotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if and .Values.ingress.web.hosts (.Values.ingress.web.hosts | first | kindIs "string" | not) }}
  {{- $anyTlsHosts := false -}}
  {{- range .Values.ingress.web.hosts }}
  {{- if .tls }}
  {{- if .tls.enabled }}
  {{- $anyTlsHosts = true -}}
  {{- end }}
  {{- end }}
  {{- end }}
  {{- if $anyTlsHosts }}
  tls:
  {{- range .Values.ingress.web.hosts }}
  {{- if .tls }}
  {{- if .tls.enabled }}
    - hosts:
        - {{ .name | quote }}
      secretName: {{ .tls.secretName }}
  {{- end }}
  {{- end }}
  {{- end }}
  {{- end }}
  {{- else if .Values.ingress.web.tls.enabled }}
  tls:
    - hosts:
        {{- .Values.ingress.web.hosts | default (list .Values.ingress.web.host) | toYaml | nindent 8 }}
      secretName: {{ .Values.ingress.web.tls.secretName }}
  {{- end }}
  rules:
    {{- range .Values.ingress.web.hosts | default (list .Values.ingress.web.host) }}
    - http:
        paths:
          {{- range $.Values.ingress.web.precedingPaths }}
          - path: {{ .path }}
            pathType: {{ .pathType }}
            backend:
              service:
                name: {{ .serviceName }}
                port:
                  name: {{ .servicePort }}
          {{- end }}
          - backend:
              service:
                name: {{ $fullname }}-webserver
                port:
                  name: airflow-ui
            {{- if $.Values.ingress.web.path }}
            path: {{ $.Values.ingress.web.path }}
            pathType: {{ $.Values.ingress.web.pathType }}
            {{- end }}
          {{- range $.Values.ingress.web.succeedingPaths }}
          - path: {{ .path }}
            pathType: {{ .pathType }}
            backend:
              service:
                name: {{ .serviceName }}
                port:
                  name: {{ .servicePort }}
          {{- end }}
      {{- $hostname := . -}}
      {{- if . | kindIs "string" | not }}
      {{- $hostname = .name -}}
      {{- end }}
      {{- if $hostname }}
      host: {{ tpl $hostname $ | quote }}
      {{- end }}
    {{- end }}
  {{- if .Values.ingress.web.ingressClassName }}
  ingressClassName: {{ .Values.ingress.web.ingressClassName }}
  {{- end }}
{{- end }}
{{- end }}
