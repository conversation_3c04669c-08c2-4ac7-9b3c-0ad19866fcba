{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow Webserver HPA
#################################
{{- if semverCompare "<3.0.0" .Values.airflowVersion }}
{{- if and .Values.webserver.enabled .Values.webserver.hpa.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "airflow.fullname" . }}-webserver
  labels:
    tier: airflow
    component: webserver-horizontalpodautoscaler
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    deploymentName: {{ .Release.Name }}-webserver
    {{- if or (.Values.labels) (.Values.webserver.labels) }}
      {{- mustMerge .Values.webserver.labels .Values.labels | toYaml | nindent 4 }}
    {{- end }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "airflow.fullname" . }}-webserver
  minReplicas: {{ .Values.webserver.hpa.minReplicaCount }}
  maxReplicas: {{ .Values.webserver.hpa.maxReplicaCount }}
  metrics: {{- toYaml .Values.webserver.hpa.metrics | nindent 4 }}
  {{- with .Values.webserver.hpa.behavior }}
  behavior: {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
{{- end }}
