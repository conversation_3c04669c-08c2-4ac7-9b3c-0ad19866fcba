{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow Webserver NetworkPolicy
#################################
{{- if and .Values.webserver.enabled (semverCompare "<3.0.0" .Values.airflowVersion) }}
{{- if .Values.networkPolicies.enabled }}
{{- $from := or .Values.webserver.networkPolicy.ingress.from .Values.webserver.extraNetworkPolicies }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "airflow.fullname" . }}-webserver-policy
  labels:
    tier: airflow
    component: airflow-webserver-policy
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- if or (.Values.labels) (.Values.webserver.labels) }}
      {{- mustMerge .Values.webserver.labels .Values.labels | toYaml | nindent 4 }}
    {{- end }}
spec:
  podSelector:
    matchLabels:
      tier: airflow
      component: webserver
      release: {{ .Release.Name }}
  policyTypes:
    - Ingress
  {{- if $from }}
  ingress:
    - from: {{- toYaml $from | nindent 6 }}
      ports:
      {{ range .Values.webserver.networkPolicy.ingress.ports }}
        -
          {{- range $key, $val := . }}
          {{ $key }}: {{ tpl (toString $val) $ }}
          {{- end }}
      {{- end }}
  {{- end }}
{{- end }}
{{- end }}
