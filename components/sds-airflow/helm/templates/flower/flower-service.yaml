{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow Flower Service Component
#################################
{{- if .Values.flower.enabled }}
{{- if or (contains "CeleryExecutor" .Values.executor) (contains "CeleryKubernetesExecutor" .Values.executor) }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "airflow.fullname" . }}-flower
  labels:
    tier: airflow
    component: flower
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- if or (.Values.labels) (.Values.flower.labels) }}
      {{- mustMerge .Values.flower.labels .Values.labels | toYaml | nindent 4 }}
    {{- end }}
  {{- with .Values.flower.service.annotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.flower.service.type }}
  selector:
    tier: airflow
    component: flower
    release: {{ .Release.Name }}
  ports:
  {{ range .Values.flower.service.ports }}
    -
      {{- range $key, $val := . }}
      {{ $key }}: {{ tpl (toString $val) $ }}
      {{- end }}
  {{- end }}
  {{- if .Values.flower.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.flower.service.loadBalancerIP }}
  {{- end }}
  {{- if .Values.flower.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges: {{- toYaml .Values.flower.service.loadBalancerSourceRanges | nindent 4 }}
  {{- end }}
{{- end }}
{{- end }}
