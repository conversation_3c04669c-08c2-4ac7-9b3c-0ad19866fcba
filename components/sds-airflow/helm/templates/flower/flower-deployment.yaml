{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow Flower Deployment
#################################
{{- if .Values.flower.enabled }}
{{- if or (contains "CeleryExecutor" .Values.executor) (contains "CeleryKubernetesExecutor" .Values.executor) }}
{{- $nodeSelector := or .Values.flower.nodeSelector .Values.nodeSelector }}
{{- $affinity := or .Values.flower.affinity .Values.affinity }}
{{- $tolerations := or .Values.flower.tolerations .Values.tolerations }}
{{- $topologySpreadConstraints := or .Values.flower.topologySpreadConstraints .Values.topologySpreadConstraints }}
{{- $revisionHistoryLimit := or .Values.flower.revisionHistoryLimit .Values.revisionHistoryLimit }}
{{- $securityContext := include "airflowPodSecurityContext" (list . .Values.flower) }}
{{- $containerSecurityContext := include "containerSecurityContext" (list . .Values.flower) }}
{{- $containerLifecycleHooks := or .Values.flower.containerLifecycleHooks .Values.containerLifecycleHooks }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "airflow.fullname" . }}-flower
  labels:
    tier: airflow
    component: flower
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- with .Values.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- if .Values.flower.annotations }}
  annotations: {{- toYaml .Values.flower.annotations | nindent 4 }}
  {{- end }}
spec:
  replicas: 1
  {{- if $revisionHistoryLimit }}
  revisionHistoryLimit: {{ $revisionHistoryLimit }}
  {{- end }}
  selector:
    matchLabels:
      tier: airflow
      component: flower
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        tier: airflow
        component: flower
        release: {{ .Release.Name }}
        {{- if or (.Values.labels) (.Values.flower.labels) }}
          {{- mustMerge .Values.flower.labels .Values.labels | toYaml | nindent 8 }}
        {{- end }}
      annotations:
        checksum/airflow-config: {{ include (print $.Template.BasePath "/configmaps/configmap.yaml") . | sha256sum }}
        checksum/flower-secret: {{ include (print $.Template.BasePath "/secrets/flower-secret.yaml") . | sha256sum }}
        {{- if or (.Values.airflowPodAnnotations) (.Values.flower.podAnnotations) }}
          {{- mustMerge .Values.flower.podAnnotations .Values.airflowPodAnnotations | toYaml | nindent 8 }}
        {{- end }}
    spec:
      nodeSelector: {{- toYaml $nodeSelector | nindent 8 }}
      affinity: {{- toYaml $affinity | nindent 8 }}
      {{- if .Values.schedulerName }}
      schedulerName: {{ .Values.schedulerName }}
      {{- end }}
      tolerations: {{- toYaml $tolerations | nindent 8 }}
      topologySpreadConstraints: {{- toYaml $topologySpreadConstraints | nindent 8 }}
      serviceAccountName: {{ include "flower.serviceAccountName" . }}
      {{- if .Values.flower.priorityClassName }}
      priorityClassName: {{ .Values.flower.priorityClassName }}
      {{- end }}
      restartPolicy: Always
      securityContext: {{ $securityContext | nindent 8 }}
      {{- if or .Values.registry.secretName .Values.registry.connection }}
      imagePullSecrets:
        - name: {{ template "registry_secret" . }}
      {{- end }}
      containers:
        - name: flower
          image: {{ template "flower_image" . }}
          imagePullPolicy: {{ .Values.images.flower.pullPolicy }}
          securityContext: {{ $containerSecurityContext | nindent 12 }}
          {{- if $containerLifecycleHooks  }}
          lifecycle: {{- tpl (toYaml $containerLifecycleHooks) . | nindent 12 }}
          {{- end }}
          {{- if .Values.flower.command }}
          command: {{ tpl (toYaml .Values.flower.command) . | nindent 12 }}
          {{- end }}
          {{- if .Values.flower.args }}
          args: {{ tpl (toYaml .Values.flower.args) . | nindent 12 }}
          {{- end }}
          resources: {{- toYaml .Values.flower.resources | nindent 12 }}
          volumeMounts:
            {{- include "airflow_config_mount" . | nindent 12 }}
            {{- if .Values.volumeMounts }}
              {{- toYaml .Values.volumeMounts | nindent 12 }}
            {{- end }}
            {{- if .Values.flower.extraVolumeMounts }}
              {{- tpl (toYaml .Values.flower.extraVolumeMounts) . | nindent 12 }}
            {{- end }}
          ports:
            - name: flower-ui
              containerPort: {{ .Values.ports.flowerUI }}
          livenessProbe:
            failureThreshold: {{ .Values.flower.livenessProbe.failureThreshold }}
            exec:
              command:
                - curl
                {{- if (or .Values.flower.secretName (and .Values.flower.username .Values.flower.password))}}
                - "--user"
                - $AIRFLOW__CELERY__FLOWER_BASIC_AUTH
                {{- end }}
                - {{ printf "localhost:%s" (.Values.ports.flowerUI | toString) }}
            initialDelaySeconds: {{ .Values.flower.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.flower.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.flower.livenessProbe.timeoutSeconds }}
          readinessProbe:
            failureThreshold: {{ .Values.flower.readinessProbe.failureThreshold }}
            exec:
              command:
                - curl
                {{- if (or .Values.flower.secretName (and .Values.flower.username .Values.flower.password))}}
                - "--user"
                - $AIRFLOW__CELERY__FLOWER_BASIC_AUTH
                {{- end }}
                - {{ printf "localhost:%s" (.Values.ports.flowerUI | toString) }}
            initialDelaySeconds: {{ .Values.flower.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.flower.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.flower.readinessProbe.timeoutSeconds }}
          startupProbe:
            failureThreshold: {{ .Values.flower.startupProbe.failureThreshold }}
            exec:
              command:
                - curl
                {{- if (or .Values.flower.secretName (and .Values.flower.username .Values.flower.password))}}
                - "--user"
                - $AIRFLOW__CELERY__FLOWER_BASIC_AUTH
                {{- end }}
                - {{ printf "localhost:%s" (.Values.ports.flowerUI | toString) }}
            periodSeconds: {{ .Values.flower.startupProbe.periodSeconds }}
            initialDelaySeconds: {{ .Values.flower.startupProbe.initialDelaySeconds }}
            timeoutSeconds: {{ .Values.flower.startupProbe.timeoutSeconds }}
          envFrom:
          {{- include "custom_airflow_environment_from" . | default "\n  []" | indent 10 }}
          env:
            {{- if (or .Values.flower.secretName (and .Values.flower.username .Values.flower.password))}}
            - name: AIRFLOW__CELERY__FLOWER_BASIC_AUTH
              valueFrom:
                secretKeyRef:
                  name: {{ template "flower_secret" . }}
                  key: basicAuth
            {{- end }}
            {{- include "standard_airflow_environment" . | indent 10 }}
            {{- include "custom_airflow_environment" . | indent 10 }}
            {{- include "container_extra_envs" (list . .Values.flower.env) | indent 10 }}
        {{- if .Values.flower.extraContainers }}
          {{- tpl (toYaml .Values.flower.extraContainers) . | nindent 8 }}
        {{- end }}
      volumes:
        - name: config
          configMap:
            name: {{ template "airflow_config" . }}
        {{- if .Values.volumes }}
          {{- toYaml .Values.volumes | nindent 8 }}
        {{- end }}
        {{- if .Values.flower.extraVolumes }}
          {{- tpl (toYaml .Values.flower.extraVolumes) . | nindent 8 }}
        {{- end }}
{{- end }}
{{- end }}
