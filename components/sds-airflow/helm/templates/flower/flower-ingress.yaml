{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow Flower Ingress
#################################
{{- if .Values.flower.enabled }}
{{- if and (or .Values.ingress.flower.enabled .Values.ingress.enabled) (or (contains "CeleryExecutor" .Values.executor) (contains "CeleryKubernetesExecutor" .Values.executor)) }}
{{- $fullname := (include "airflow.fullname" .) }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullname }}-flower-ingress
  labels:
    tier: airflow
    component: flower-ingress
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- if or (.Values.labels) (.Values.flower.labels) }}
      {{- mustMerge .Values.flower.labels .Values.labels | toYaml | nindent 4 }}
    {{- end }}
  {{- with .Values.ingress.flower.annotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if and .Values.ingress.flower.hosts (.Values.ingress.flower.hosts | first | kindIs "string" | not) }}
  {{- $anyTlsHosts := false -}}
  {{- range .Values.ingress.flower.hosts }}
  {{- if .tls }}
  {{- if .tls.enabled }}
  {{- $anyTlsHosts = true -}}
  {{- end }}
  {{- end }}
  {{- end }}
  {{- if $anyTlsHosts }}
  tls:
  {{- range .Values.ingress.flower.hosts }}
  {{- if .tls }}
  {{- if .tls.enabled }}
    - hosts:
        - {{ .name | quote }}
      secretName: {{ .tls.secretName }}
  {{- end }}
  {{- end }}
  {{- end }}
  {{- end }}
  {{- else if .Values.ingress.flower.tls.enabled }}
  tls:
    - hosts:
        {{- .Values.ingress.flower.hosts | default (list .Values.ingress.flower.host) | toYaml | nindent 8 }}
      secretName: {{ .Values.ingress.flower.tls.secretName }}
  {{- end }}
  rules:
    {{- range .Values.ingress.flower.hosts | default (list .Values.ingress.flower.host) }}
    - http:
        paths:
          - backend:
              service:
                name: {{ $fullname }}-flower
                port:
                  name: flower-ui
            {{- if $.Values.ingress.flower.path }}
            path: {{ $.Values.ingress.flower.path }}
            pathType: {{ $.Values.ingress.flower.pathType }}
            {{- end }}
      {{- $hostname := . -}}
      {{- if . | kindIs "string" | not }}
      {{- $hostname = .name -}}
      {{- end }}
      {{- if $hostname }}
      host: {{ tpl $hostname $ | quote }}
      {{- end }}
    {{- end }}
  {{- if .Values.ingress.flower.ingressClassName }}
  ingressClassName: {{ .Values.ingress.flower.ingressClassName }}
  {{- end }}
{{- end }}
{{- end }}
