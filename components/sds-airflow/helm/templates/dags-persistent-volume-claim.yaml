{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

######################################
## Airflow DAGs PersistentVolumeClaim
######################################
{{- if and (not .Values.dags.persistence.existingClaim ) .Values.dags.persistence.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ template "airflow_dags_volume_claim" . }}
  labels:
    tier: airflow
    component: dags-pvc
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- with .Values.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.dags.persistence.annotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  accessModes: [{{ .Values.dags.persistence.accessMode | quote }}]
  resources:
    requests:
      storage: {{ .Values.dags.persistence.size | quote }}
  {{- if .Values.dags.persistence.storageClassName }}
  {{- if (eq "-" .Values.dags.persistence.storageClassName) }}
  storageClassName: ""
  {{- else }}
  storageClassName: {{ tpl .Values.dags.persistence.storageClassName . | quote }}
  {{- end }}
  {{- end }}
{{- end }}
