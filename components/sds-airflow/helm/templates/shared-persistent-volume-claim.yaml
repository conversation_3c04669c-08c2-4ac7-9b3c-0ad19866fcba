#Added by <PERSON>
{{- if and (not .Values.shared.persistence.existingClaim ) .Values.shared.persistence.enabled }}
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: airflow-shared
  labels:
    tier: airflow
    component: shared-pvc
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- with .Values.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  accessModes: ["ReadWriteMany"]
  resources:
    requests:
      storage: {{ .Values.shared.persistence.size | quote }}
  {{- if .Values.shared.persistence.storageClassName }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
  storageClassName: "{{ tpl .Values.shared.persistence.storageClassName.aws . }}"
  {{- else if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure"}}
  storageClassName: "{{ .Values.shared.persistence.storageClassName.azure }}"
  {{- end }}
  {{- else }}
  storageClassName: ""
  {{- end }}
{{- end }}
