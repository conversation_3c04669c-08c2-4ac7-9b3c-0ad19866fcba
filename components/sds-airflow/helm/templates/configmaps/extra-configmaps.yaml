{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

####################################################
## Extra ConfigMaps provisioned via the chart values
####################################################
{{- $Global := . }}
{{- range $configMapName, $configMapContent := .Values.extraConfigMaps }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ tpl $configMapName $Global | quote }}
  labels:
    release: {{ $Global.Release.Name }}
    chart: "{{ $Global.Chart.Name }}-{{ $Global.Chart.Version }}"
    heritage: {{ $Global.Release.Service }}
    {{- with $Global.Values.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- if $configMapContent.labels }}
      {{- toYaml $configMapContent.labels | nindent 4 }}
    {{- end }}
  {{- $annotations := dict }}
  {{- if or $configMapContent.useHelmHooks (not (hasKey $configMapContent "useHelmHooks")) }}
    {{- $_ := set $annotations "helm.sh/hook" "pre-install,pre-upgrade" }}
    {{- $_ := set $annotations "helm.sh/hook-weight" "0" }}
    {{- $_ := set $annotations "helm.sh/hook-delete-policy" "before-hook-creation" }}
  {{- end }}
  {{- with $annotations := merge $annotations ($configMapContent.annotations | default dict) }}
  annotations: {{- $annotations | toYaml | nindent 4 }}
  {{- end }}
{{- if $configMapContent.data }}
data:
  {{- with $configMapContent.data }}
    {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}
{{- end }}
