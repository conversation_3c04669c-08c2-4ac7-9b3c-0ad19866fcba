{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow StatsD NetworkPolicy
#################################
{{- if and .Values.networkPolicies.enabled .Values.statsd.enabled }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "airflow.fullname" . }}-statsd-policy
  labels:
    tier: airflow
    component: statsd-policy
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- with .Values.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  podSelector:
    matchLabels:
      tier: airflow
      component: statsd
      release: {{ .Release.Name }}
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          tier: airflow
          release: {{ .Release.Name }}
    {{- if .Values.statsd.extraNetworkPolicies }}
      {{- toYaml .Values.statsd.extraNetworkPolicies | nindent 4 }}
    {{- end }}
    ports:
    - protocol: UDP
      port: {{ .Values.ports.statsdIngest }}
    - protocol: TCP
      port: {{ .Values.ports.statsdScrape }}
{{- end }}
