{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow StatsD Deployment
#################################
{{- if .Values.statsd.enabled }}
{{- $nodeSelector := or .Values.statsd.nodeSelector .Values.nodeSelector }}
{{- $affinity := or .Values.statsd.affinity .Values.affinity }}
{{- $tolerations := or .Values.statsd.tolerations .Values.tolerations }}
{{- $topologySpreadConstraints := or .Values.statsd.topologySpreadConstraints .Values.topologySpreadConstraints }}
{{- $revisionHistoryLimit := or .Values.statsd.revisionHistoryLimit .Values.revisionHistoryLimit }}
{{- $securityContext := include "localPodSecurityContext" .Values.statsd }}
{{- $containerSecurityContext := include "externalContainerSecurityContext" .Values.statsd }}
{{- $containerLifecycleHooks := .Values.statsd.containerLifecycleHooks }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "airflow.fullname" . }}-statsd
  labels:
    tier: airflow
    component: statsd
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- with .Values.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.statsd.annotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: 1
  {{- if $revisionHistoryLimit }}
  revisionHistoryLimit: {{ $revisionHistoryLimit }}
  {{- end }}
  selector:
    matchLabels:
      tier: airflow
      component: statsd
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        tier: airflow
        component: statsd
        release: {{ .Release.Name }}
        {{- with .Values.labels }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- include "custom.azureLabel" . | nindent 8 }}
      {{- if or .Values.statsd.extraMappings .Values.statsd.podAnnotations }}
      annotations:
        checksum/statsd-config: {{ include (print $.Template.BasePath "/configmaps/statsd-configmap.yaml") . | sha256sum }}
        {{- if .Values.statsd.podAnnotations }}
          {{- toYaml .Values.statsd.podAnnotations | nindent 8 }}
        {{- end }}
      {{- end }}
    spec:
      {{- if .Values.statsd.priorityClassName }}
      priorityClassName: {{ .Values.statsd.priorityClassName }}
      {{- end }}
      nodeSelector: {{- toYaml $nodeSelector | nindent 8 }}
      affinity: {{- toYaml $affinity | nindent 8 }}
      tolerations: {{- toYaml $tolerations | nindent 8 }}
      {{- if .Values.schedulerName }}
      schedulerName: {{ .Values.schedulerName }}
      {{- end }}
      topologySpreadConstraints: {{- toYaml $topologySpreadConstraints | nindent 8 }}
      terminationGracePeriodSeconds: {{ .Values.statsd.terminationGracePeriodSeconds }}
      serviceAccountName: {{ include "statsd.serviceAccountName" . }}
      securityContext: {{ $securityContext | nindent 8 }}
      restartPolicy: Always
      {{- if or .Values.registry.secretName .Values.registry.connection }}
      imagePullSecrets:
        - name: {{ template "registry_secret" . }}
      {{- end }}
      containers:
        - name: statsd
          image: {{ template "statsd_image" . }}
          imagePullPolicy: {{ .Values.images.statsd.pullPolicy }}
          securityContext: {{ $containerSecurityContext | nindent 12 }}
          {{- if $containerLifecycleHooks }}
          lifecycle: {{- tpl (toYaml $containerLifecycleHooks) . | nindent 12 }}
          {{- end }}
          {{- if .Values.statsd.args }}
          args: {{ tpl (toYaml .Values.statsd.args) . | nindent 12 }}
          {{- else}}
          args:
            - "--statsd.mapping-config=/etc/statsd-exporter/mappings.yml"
          {{- end }}
          resources: {{- toYaml .Values.statsd.resources | nindent 12 }}
          {{- with .Values.statsd.env }}
          env: {{- toYaml . | nindent 12 }}
          {{- end }}
          ports:
            - name: statsd-ingest
              protocol: UDP
              containerPort: {{ .Values.ports.statsdIngest }}
            - name: statsd-scrape
              containerPort: {{ .Values.ports.statsdScrape }}
          livenessProbe:
            httpGet:
              path: /metrics
              port: {{ .Values.ports.statsdScrape }}
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
          readinessProbe:
            httpGet:
              path: /metrics
              port: {{ .Values.ports.statsdScrape }}
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
          volumeMounts:
            - name: config
              mountPath: /etc/statsd-exporter/mappings.yml
              subPath: mappings.yml
      volumes:
        - name: config
          configMap:
            name: {{ include "airflow.fullname" . }}-statsd
{{- end }}
