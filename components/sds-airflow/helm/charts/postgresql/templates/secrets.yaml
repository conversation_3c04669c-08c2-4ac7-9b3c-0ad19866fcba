{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- $host := include "postgresql.v1.primary.fullname" . }}
{{- $port := include "postgresql.v1.service.port" . }}
{{- $customUser := include "postgresql.v1.username" . }}
{{- $postgresPassword := include "common.secrets.lookup" (dict "secret" (include "postgresql.v1.secretName" .) "key" (coalesce .Values.global.postgresql.auth.secretKeys.adminPasswordKey .Values.auth.secretKeys.adminPasswordKey) "defaultValue" (ternary (coalesce .Values.global.postgresql.auth.password .Values.auth.password .Values.global.postgresql.auth.postgresPassword .Values.auth.postgresPassword) (coalesce .Values.global.postgresql.auth.postgresPassword .Values.auth.postgresPassword) (or (empty $customUser) (eq $customUser "postgres"))) "context" $) | trimAll "\"" | b64dec }}
{{- if and (not $postgresPassword) .Values.auth.enablePostgresUser }}
{{- $postgresPassword = randAlphaNum 10 }}
{{- end }}
{{- $replicationPassword := "" }}
{{- if eq .Values.architecture "replication" }}
{{- $replicationPassword = include "common.secrets.passwords.manage" (dict "secret" (include "postgresql.v1.secretName" .) "key" (coalesce .Values.global.postgresql.auth.secretKeys.replicationPasswordKey .Values.auth.secretKeys.replicationPasswordKey) "providedValues" (list "auth.replicationPassword") "context" $) | trimAll "\"" | b64dec }}
{{- end }}
{{- $ldapPassword := "" }}
{{- if and .Values.ldap.enabled (or .Values.ldap.bind_password .Values.ldap.bindpw) }}
{{- $ldapPassword = coalesce .Values.ldap.bind_password .Values.ldap.bindpw }}
{{- end }}
{{- $password := "" }}
{{- if and (not (empty $customUser)) (ne $customUser "postgres") }}
{{- $password = include "common.secrets.passwords.manage" (dict "secret" (include "postgresql.v1.secretName" .) "key" (coalesce .Values.global.postgresql.auth.secretKeys.userPasswordKey .Values.auth.secretKeys.userPasswordKey) "providedValues" (list "global.postgresql.auth.password" "auth.password") "context" $) | trimAll "\"" | b64dec }}
{{- end }}
{{- $database := include "postgresql.v1.database" . }}
{{- if (include "postgresql.v1.createSecret" .) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{- if $postgresPassword }}
  postgres-password: {{ $postgresPassword | b64enc | quote }}
  {{- end }}
  {{- if $password }}
  password: {{ $password | b64enc | quote }}
  {{- end }}
  {{- if $replicationPassword }}
  replication-password: {{ $replicationPassword | b64enc | quote }}
  {{- end }}
  # We don't auto-generate LDAP password when it's not provided as we do for other passwords
  {{- if and .Values.ldap.enabled (or .Values.ldap.bind_password .Values.ldap.bindpw) }}
  ldap-password: {{ $ldapPassword  | b64enc | quote }}
  {{- end }}
{{- end }}
{{- if .Values.serviceBindings.enabled }}
{{- if $postgresPassword }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}-svcbind-postgres
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: servicebinding.io/postgresql
data:
  provider: {{ print "bitnami" | b64enc | quote }}
  type: {{ print "postgresql" | b64enc | quote }}
  host: {{ $host | b64enc | quote }}
  port: {{ $port | b64enc | quote }}
  username: {{ print "postgres" | b64enc | quote }}
  database: {{ print "postgres" | b64enc | quote }}
  password: {{ $postgresPassword | b64enc | quote }}
  uri: {{ printf "postgresql://postgres:%s@%s:%s/postgres" $postgresPassword $host $port | b64enc | quote }}
{{- end }}
{{- if $password }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}-svcbind-custom-user
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: servicebinding.io/postgresql
data:
  provider: {{ print "bitnami" | b64enc | quote }}
  type: {{ print "postgresql" | b64enc | quote }}
  host: {{ $host | b64enc | quote }}
  port: {{ $port | b64enc | quote }}
  username: {{ $customUser | b64enc | quote }}
  password: {{ $password | b64enc | quote }}
  {{- if $database }}
  database: {{ $database | b64enc | quote }}
  {{- end }}
  uri: {{ printf "postgresql://%s:%s@%s:%s/%s" $customUser $password $host $port $database | b64enc | quote }}
{{- end }}
{{- end }}
