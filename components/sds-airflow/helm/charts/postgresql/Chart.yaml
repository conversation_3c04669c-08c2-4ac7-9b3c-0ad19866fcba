annotations:
  category: Database
  images: |
    - name: os-shell
      image: docker.io/bitnami/os-shell:11-debian-11-r91
    - name: postgres-exporter
      image: docker.io/bitnami/postgres-exporter:0.15.0-debian-11-r2
    - name: postgresql
      image: docker.io/bitnami/postgresql:16.1.0-debian-11-r15
  licenses: Apache-2.0
apiVersion: v2
appVersion: 16.1.0
dependencies:
- name: common
  repository: oci://registry-1.docker.io/bitnamicharts
  tags:
  - bitnami-common
  version: 2.x.x
description: PostgreSQL (Postgres) is an open source object-relational database known
  for reliability and data integrity. ACID-compliant, it supports foreign keys, joins,
  views, triggers and stored procedures.
home: https://bitnami.com
icon: https://bitnami.com/assets/stacks/postgresql/img/postgresql-stack-220x234.png
keywords:
- postgresql
- postgres
- database
- sql
- replication
- cluster
maintainers:
- name: VMware, Inc.
  url: https://github.com/bitnami/charts
name: postgresql
sources:
- https://github.com/bitnami/charts/tree/main/bitnami/postgresql
version: 13.2.24
