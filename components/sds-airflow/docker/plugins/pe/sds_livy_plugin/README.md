## SDS Livy Plugin

This directory contains the custom livy hook and livy sensor operator that is used in SDS orchestration. The supported Livy version is [0.7.1](https://www.apache.org/dyn/closer.lua/incubator/livy/0.7.1-incubating/apache-livy-0.7.1-incubating-bin.zip).


### Hooks

* [SDSLivyHook](hooks/sds_livy_hook.py) - This is a custom implementation of [LivyHook](https://airflow.apache.org/docs/apache-airflow-providers-apache-livy/stable/_api/airflow/providers/apache/livy/hooks/livy/index.html). It creates a connection to the Livy server using the necessary parameters given as input. It inherits all the utility methods from LivyHook that is used to interact with the API. The Livy connection URL can be a string that is stored as an airflow [Variable](https://airflow.apache.org/docs/apache-airflow/stable/concepts/variables.html). </br> </br>
  Input parameters -
  * ```livy_connection_url``` - Templated airflow variable that stores the url of the livy server.
  * ```username``` - <PERSON>rna<PERSON> to connect to Livy server, if authentication is required. Default authentication is HTTPBasicAuth
  * ```password``` - Password to connect to Livy server
  * To check for other input parameters inherited from parent classes check the [livy hook](https://airflow.apache.org/docs/apache-airflow-providers-apache-livy/stable/_api/airflow/providers/apache/livy/hooks/livy/index.html) and [http hook](https://airflow.apache.org/docs/apache-airflow-providers-http/stable/_api/airflow/providers/http/hooks/http/index.html) which includes authentication types.

### Sensors
* [SDSLivySensorOperator](sensors/sds_livy_sensor_operator.py) - This sensor can be used to trigger spark jobs remotely via [Livy](https://livy.apache.org/). On instantiating the sensor, it triggers the spark job by interacting with the Livy server and on subsequent poke intervals checks for the spark job status and exits when it reaches a completed stage. All input fields of this sensor are templated </br> </br>
  By default the sensor works in **reschedule** mode. This is the preferred setting for long-running spark jobs. For small jobs, this can be overridden to **poke**. </br> </br>
  Input parameters
  * ```livy_connection_url``` - Templated airflow variable that stores the url of the livy server as string.
  * ```module_name``` - String that is provided to identify which module in SDS the sensor ins being used as
  * ```username``` - Username, as string, to connect to Livy server, if authentication is required. Default authentication is HTTPBasicAuth
  * ```password```  - Password to connect to Livy server
  * ```polling_interval```  - Time in seconds between polling for job completion. Don’t poll for values >=0
  * ```extra_options``` – A dictionary of options, where key is string and value depends on the option that’s being modified.
  * ```extra_headers``` – A dictionary of headers passed to the HTTP request to livy.
  * ```spark_params``` - A dictionary of spark parameters related to the spark job, such as driver and executor memory, configuration,arguments and jar files. To check all the options for keys that can be passed as arguments, refer to the livy documentation [here](https://livy.apache.org/docs/latest/rest-api.html)