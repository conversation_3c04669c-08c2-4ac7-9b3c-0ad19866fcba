from typing import Any, Dict, Optional

from airflow.exceptions import AirflowException
from airflow.models import Variable
from airflow.providers.apache.livy.hooks.livy import BatchState
from airflow.sensors.base import BaseSensorOperator

from plugins.pe.sds_livy_plugin.hooks.sds_livy_hook import SDSLivyHook


class SDSLivySensorOperator(BaseSensorOperator):
    """
    This sensor wraps the Apache Livy REST API, allowing to submit a Spark
    application to the underlying cluster. It executes a spark submit operation by sending a POST request
    via Livy and periodically pokes to check the status of the spark job and exits if completed.

    :param file: path of the file containing the application to execute (required).
    :type file: str
    :param class_name: name of the application Java/Spark main class.
    :type class_name: str
    :param args: application command line arguments.
    :type args: list
    :param jars: jars to be used in this sessions.
    :type jars: list
    :param py_files: python files to be used in this session.
    :type py_files: list
    :param files: files to be used in this session.
    :type files: list
    :param driver_memory: amount of memory to use for the driver process.
    :type driver_memory: str
    :param driver_cores: number of cores to use for the driver process.
    :type driver_cores: str, int
    :param executor_memory: amount of memory to use per executor process.
    :type executor_memory: str
    :param executor_cores: number of cores to use for each executor.
    :type executor_cores: str, int
    :param num_executors: number of executors to launch for this session.
    :type num_executors: str, int
    :param archives: archives to be used in this session.
    :type archives: list
    :param queue: name of the YARN queue to which the application is submitted.
    :type queue: str
    :param name: name of this session.
    :type name: str
    :param conf: Spark configuration properties.
    :type conf: dict
    :param proxy_user: user to impersonate when running the job.
    :type proxy_user: str
    :param livy_connection_url: livy server url.
    :type livy_connection_url: str
    :param username:login username
    :type username:str
    :param password: login password
    :type password: str
    :param polling_interval: time in seconds between polling for job completion. Don't poll for values >=0
    :type polling_interval: int
    :param extra_options: A dictionary of options, where key is string and value
        depends on the option that's being modified.
    :type extra_options: Dict[str, Any]
    :param extra_headers: A dictionary of headers passed to the HTTP request to livy.
    :type extra_headers: Dict[str, Any]
    """

    template_fields = (
        'livy_connection_url', 'spark_params', 'username', 'password', 'polling_interval', 'extra_options',
        'extra_headers', 'module_name')

    def __init__(self,
                 *,
                 spark_params: Dict = None,
                 livy_connection_url: str = None,
                 module_name: str = None,
                 username: Optional[str] = None,
                 password: Optional[str] = None,
                 polling_interval: int = 0,
                 extra_options: Optional[Dict[str, Any]] = None,
                 extra_headers: Optional[Dict[str, Any]] = None,
                 **kwargs: Any,
                 ) -> None:
        if "mode" not in kwargs:
            kwargs["mode"] = "reschedule"  # change the default value for mode to reschedule
        super().__init__(**kwargs)

        if not module_name:
            raise AirflowException("'module_name' was not provided")
        self.spark_params = spark_params
        self.livy_connection_url = livy_connection_url
        self.module_name = module_name
        self.username = username
        self.password = password
        self.polling_interval = polling_interval
        self.extra_options = extra_options or {}
        self.extra_headers = extra_headers or {}
        self._sds_livy_hook: Optional[SDSLivyHook] = None
        self.batch_id = None

        # unique key to identify the variable created in poke method. The task id and dag id uniquely identifies it
        self.variable_key = "livy_job_id_key_{}_{}".format(self.dag_id, self.task_id)

    def get_hook(self) -> SDSLivyHook:
        """
        Get a valid SDSLivyHook
        :return: hook
        :rtype: SDSLivyHook
        """

        if self._sds_livy_hook is None or not isinstance(self._sds_livy_hook, SDSLivyHook):
            self._sds_livy_hook = SDSLivyHook(
                livy_connection_url=self.livy_connection_url,
                username=self.username,
                password=self.password,
                extra_headers=self.extra_headers,
                extra_options=self.extra_options
            )

        return self._sds_livy_hook

    def poke(self, context: Dict) -> bool:
        self.variable_key = f"{self.variable_key}_{context['data_interval_start'].strftime('%Y-%m-%d-%H-%M-%S')}"
        self.batch_id = Variable.get(key=self.variable_key, default_var=None)
        if not self.batch_id:
            self.batch_id = self.get_hook().post_batch(**self.spark_params)
            self.log.info("Livy batch id for job- %s" % self.batch_id)
            Variable.set(self.variable_key, self.batch_id)
            self.log.info(f"Batch id has been set with value {self.batch_id} ")
            return False

        
        self.log.info(f"Poking livy batch with batch_id '{self.batch_id}' for task '{self.task_id}'")
        state = self.get_hook().get_batch_state(self.batch_id)
        self.log.info(f"Batch with id '{self.batch_id}' is in state: '{state.value}'")

        if state in self._sds_livy_hook.TERMINAL_STATES:  # success, killed, dead, error states
            Variable.delete(self.variable_key)
            if state != BatchState.SUCCESS:  # success state
                raise AirflowException(f"Spark job has failed and exited with status - {state.value} " )
            return True

        else:
            self.log.info(
                f"Task '{self.task_id}' - Spark job has not terminated. It is in state: {state.value}")
            return False
