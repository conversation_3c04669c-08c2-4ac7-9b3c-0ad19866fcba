"""This module contains the custom SDS Livy Hook"""
import requests
from time import sleep
from typing import Optional, Any

from airflow.exceptions import AirflowException
from airflow.providers.apache.livy.hooks.livy import LivyHook


class SDSLivyHook(LivyHook):

    def __init__(self,
                 livy_connection_url: str,
                 username: Optional[str] = None,
                 password: Optional[str] = None,
                 **kwargs) -> None:
        super().__init__(**kwargs)
        self.base_url = livy_connection_url
        self.username = username
        self.password = password
        self.default_no_retries = kwargs.get("LIVY_SESSSION_REQUEST_RETRY_LIMIT", 5)
        self.default_waiting_period = kwargs.get("LIVY_SESSSION_REQUEST_RETRY_LIMIT", 10)

    def get_conn(self, headers: dict[Any, Any] | None = None, extra_options: dict[str, Any] | None = None) -> requests.Session:
        """
        Return HTTP session with <PERSON><PERSON> for sending requests
        :param headers:additional headers to be passed through as a dictionary
        :type headers: dict
        :return: requests session
        :rtype: requests.Session
        """

        session = requests.Session()
        tmp_headers = self.default_headers.copy()  # setting default header from parent class
        self._validate_connection_url(self.base_url)
        if headers:
            tmp_headers.update(headers)
        session.headers.update(tmp_headers)

        if self.username:
            session.auth = self.auth_type(self.username, self.password)

        return session

    @staticmethod
    def _validate_connection_url(livy_connection_url: str) -> None:
        """
        Validate the livy connection URL
        :param livy_connection_url: livy connection url
        :type livy_connection_url: str
        """
        from urllib.parse import urlparse
        import validators

        if urlparse(livy_connection_url).scheme not in ["https", "http"]:
            raise AirflowException("'livy_connection_url' does not have required scheme specified. Please specify url "
                                   "of the format http://{host}:{port}' or 'https://{host}:{port}'.")

        if not validators.url(livy_connection_url):
            raise AirflowException("'livy_connection_url' must be a valid url."
                                   " It must be of the format '{schema}://{"
                                   "host}:{port}'")

    def run_method(
        self,
        endpoint: str,
        method: str = 'GET',
        data: Any | None = None,
        headers: dict[str, Any] | None = None,
        retry_args: dict[str, Any] | None = None,
    ) -> Any:
        result = super().run_method(endpoint, method, data, headers, retry_args)
        while "Rejected, too many sessions are being created" in result.text and self.default_no_retries != 0:
            self.log.info(f"Adding waiting period of of {self.default_waiting_period} to create livy sessions")
            sleep(self.default_waiting_period)
            result = super().run_method(endpoint, method, data, headers, retry_args)
            self.default_no_retries -= 1
        return result
