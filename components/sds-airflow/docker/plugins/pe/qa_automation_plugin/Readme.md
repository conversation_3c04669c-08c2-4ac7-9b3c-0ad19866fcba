# Config-driven Orchestration Builder for QA Automation

A simplified approach to generate orchestration DAGs for QA automation testing using configuration files.

## Components

### BehaveTestExecutor Operator

A custom operator extending KubernetesPodOperator to simplify behave test execution. The operator can be instantiated via DAG generator using YAML configuration.

#### Features

1. **Prerequisites Execution**
   - Executes prerequisite test cases before main test execution
   - Configurable prerequisite tags and feature files

2. **Report Generation**
   - Generates test execution reports
   - Supports uploading to S3 or Azure storage

3. **Test Rerun**
   - Automatically reruns failed scenarios
   - Configurable rerun behavior

4. **Parallel Execution**
   - Supports parallel test execution
   - Configurable number of parallel processes

#### Parameters

- `feature_files`: List of feature files to execute
- `tags`: List of behave tags to include
- `negated_tags`: List of behave tags to exclude
- `pre_req_tag`: Tag to identify prerequisite tests (default: @Discover-UI-001)
- `pre_req_feature_files`: List of prerequisite feature files
- `parallel_processes`: Number of parallel processes
- `rerun`: Enable/disable failed test rerun
- `run_prerequisites`: Enable/disable prerequisite execution
- `generate_report`: Enable/disable report generation

#### Configuration Override

Parameters can be overridden during DAG trigger using configuration: This can be used via trigger DAG with config 
option in Airflow UI.
### Configuration Example

```json
{
  "default": {
    "parallel_processes": 3,
    "rerun": false
  },
  "task_id": {
    "feature_files": ["new.feature"],
    "tags": ["new_tag"]
  }
} 
```

### DAG Generator

A generic DAG builder using YAML configuration files, built on top of [Apache Airflow DAG Factory](https://github.com/apache/airflow/tree/main/airflow/utils/dag_factory).

#### Features

1. **Task Defaults**
   - Enables setting default configurations for all tasks using the same operator
   - Reduces configuration duplication across multiple tasks
   - Supports operator-specific defaults that apply to all instances

2. **SDSPipelineDAG Support**
   - Creates pipeline-compatible DAGs for mediator pattern integration
   - Enables automatic dependency management between DAGs
#### Writing DAG generator files
* We can write multiple dag generator files which loads DAGs from all the yaml files inside configured folder name in
shared file system path.
* Please refer the sample dag generator file [qa_automation_dag_generator.py](../../../dags/pe/qa_automation/qa_automation_dag_generator.py)
* We need to call the load_dags_from_yaml function as written in [qa_automation_dag_generator.py](../../../dags/pe/qa_automation/qa_automation_dag_generator.py)
#### Configuration Examples

##### 1. Example configurations with task defaults

```yaml
host_enrichment_dag:
  default_args:
    owner: 'example_owner'
    retries: 1
    start_date: '2024-01-01'
  task_defaults:
    plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator:
      image: 'prevalentai/sds-qa-automation:latest'
      feature_files: [src/common/features/base.feature]
  tasks:
    smoke_test:
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags: ["@smoke"]
      parallel_processes: 2
      pre_req_feature_files: ["src/common/features/base.feature"]
    regression_test:
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags: ["@regression"]
      pre_req_feature_files: ["src/common/features/base.feature"]
      feature_files: [src/specific/features/test.feature]
      dependencies: [smoke_test]
```
* Here we have two tasks `some_test` & `regression_test`. Parameters `image` & `feature_files` is put inside `task_defaults`
key so that we don't need to mention those again in tasks.

##### 2. Example configurations with Environment variable & Secrets

```yaml
host_enrichment_dag:
  default_args:
    owner: 'example_owner'
    retries: 1
    start_date: '2024-01-01'
  task_defaults:
    plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator:
      image: 'prevalentai/sds-qa-automation:latest'
      feature_files: [src/common/features/base.feature]
      env_vars:
        TEST_ENV: "staging"
        DEBUG_MODE: "true"
      secrets:
        - deploy_type: env  # Maps to Secret constructor first argument
          deploy_target: SELENIUM_USERNAME  # Maps to Secret constructor second argument
          secret: airflow-secret-vault  # Maps to Secret constructor third argument
          key: SELENIUM_USERNAME  # Maps to Secret constructor fourth argument
        - deploy_type: env
          deploy_target: SELENIUM_PASSWORD
          secret: airflow-secret-vault
          key: SELENIUM_PASSWORD
  tasks:
    smoke_test:
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags: ["@smoke"]
      parallel_processes: 2
      pre_req_feature_files: ["src/common/features/base.feature"]
    regression_test:
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags: ["@regression"]
      pre_req_feature_files: ["src/common/features/base.feature"]
      feature_files: [src/specific/features/test.feature]
      dependencies: [smoke_test]
```
##### 3. Example configurations to upload reports to object store path

* See the report_base_path parameter configured in below yaml. This will upload the output report & rerun report as tar file.
to the s3 base path under key ${trigger mode}(Manual/Automatic)/{{dag_run.dag_id}}/{{task_instance.task_id}}/{{data_interval_start.strftime('%Y-%m-%d-%H:%M:%S')}}
* We can use the existing templating feature to inject values from context variable in generate report and all the parameters in yaml.

```yaml
host_enrichment_dag:
  default_args:
    owner: 'example_owner'
    retries: 1
    start_date: '2024-01-01'
  task_defaults:
    plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator:
      image: 'prevalentai/sds-qa-automation:latest'
      feature_files: [src/common/features/base.feature]
      generate_report: true
      report_base_path: "s3://<%DREMIO_DATALAKE%>/behave/{{dag_run.dag_id}}/{{task_instance.task_id}}/{{data_interval_start.strftime('%Y-%m-%d-%H:%M:%S')}}"
      env_vars:
        TEST_ENV: "staging"
        DEBUG_MODE: "true"
      secrets:
        - deploy_type: env
          deploy_target: SELENIUM_USERNAME
          secret: airflow-secret-vault
          key: SELENIUM_USERNAME
        - deploy_type: env
          deploy_target: SELENIUM_PASSWORD
          secret: airflow-secret-vault
          key: SELENIUM_PASSWORD
  tasks:
    smoke_test:
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags: ["@smoke"]
      parallel_processes: 2
      pre_req_feature_files: ["src/common/features/base.feature"]
    regression_test:
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags: ["@regression"]
      pre_req_feature_files: ["src/common/features/base.feature"]
      feature_files: [src/specific/features/test.feature]
      dependencies: [smoke_test]

```
##### 4. Example configurations with another task with BashOperator

```yaml
host_enrichment_dag:
  default_args:
    owner: 'example_owner'
    retries: 1
    start_date: '2024-01-01'
  task_defaults:
    plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator:
      image: 'prevalentai/sds-qa-automation:latest'
      feature_files: [src/common/features/base.feature]
      generate_report: true
      report_base_path: "s3://<%DREMIO_DATALAKE%>/behave/{{dag_run.dag_id}}/{{task_instance.task_id}}/{{data_interval_start.strftime('%Y-%m-%d-%H:%M:%S')}}"
      env_vars:
        TEST_ENV: "staging"
        DEBUG_MODE: "true"
      secrets:
        - deploy_type: env
          deploy_target: SELENIUM_USERNAME
          secret: airflow-secret-vault
          key: SELENIUM_USERNAME
        - deploy_type: env
          deploy_target: SELENIUM_PASSWORD
          secret: airflow-secret-vault
          key: SELENIUM_PASSWORD
  tasks:
    setup_test_env:
      operator: airflow.operators.bash.BashOperator
      bash_command: |
        echo "Setting up test environment"
        mkdir -p /tmp/test_data
        echo "Test setup completed"
    smoke_test:
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags: ["@smoke"]
      parallel_processes: 2
      pre_req_feature_files: ["src/common/features/base.feature"]
      dependencies: [setup_test_env]
    regression_test:
      operator: plugins.pe.qa_automation_plugin.behave_test_execution_operator.BehaveTestExecutorOperator
      tags: ["@regression"]
      pre_req_feature_files: ["src/common/features/base.feature"]
      feature_files: [src/specific/features/test.feature]
      dependencies: [setup_test_env]
    cleanup_test_env:
      operator: airflow.operators.bash.BashOperator
      dependencies: [smoke_test,regression_test]
      bash_command: |
        echo "Cleaning up test environment"
        rm -rf /tmp/test_data
        echo "Cleanup completed"
```
#### Integrating with platform configuration management module.
* yaml configurations can be deployed via `config` component of any mono repository.
* The yaml defined in those configuration gets platforms template rendering feature from context variables ( Refer generate report example above)
* The configurations must be placed inside the folder `orchestration_shared_fs` of the corresponding config component.
* The same can be placed inside client mono repository as well.
* Path inside orchestration_shared must be the argument `config_path` given for `load_dags_from_yaml` function of dag generator file. 
Refer `load_dags_from_yaml` function in [qa_automation_dag_generator.py](../../../dags/pe/qa_automation/qa_automation_dag_generator.py)
* Please make sure to configure generator script to use different folder across mono repositories.

