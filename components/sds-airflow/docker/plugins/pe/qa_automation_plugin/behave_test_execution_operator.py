import logging
from typing import Optional
from airflow.providers.cncf.kubernetes.operators.spark_kubernetes import KubernetesPodOperator
from airflow.models import Variable
import os
from urllib.parse import urlparse
from commons.pe.constants.airflow_constants import AirflowConstants

class BehaveCommandBuilder:

    PARALLEL_PRCS_ARG = "--parallel-processes"
    TAG_ARG = "-t"
    RERUN_ARG = "-rf"
    BEHAVEX_CMD = "behavex"
    RERUN_OUTPUT_PATH = "./rerun_output"
    FAILED_SCENARIO_PATH = "./output/failing_scenarios.txt"
    DEFAULT_PRE_REQ_TAG = "@Discover-UI-001"

    OUTPUT_DIR = "./output"
    OUTPUT_TAR = "output.tar.gz"
    RERUN_OUTPUT_TAR = "rerun_output.tar.gz"
    BEHAVE_JOB_STATUS_KEY = "RUN_STATUS"
    REPORT_UPLOAD_JOB_STATUS_KEY = "REPORT_UPLOAD_STATUS"
    JOB_EXIT_CMD = f"if [ ${BEHAVE_JOB_STATUS_KEY} -eq 0 ]; then exit 0; else  exit 1; fi"
    def __init__(self):
        self.feature_files = []
        self.tags = []
        self.negated_tags = []
        self.rerun = True
        self.parallel_processes = None
        self.prerequisites_tag = []
        self.prerequisites_feature_file = []
        self.rerun_output_path = BehaveCommandBuilder.RERUN_OUTPUT_PATH

        self.report_path = None
        self.storage_type = None

    def set_report_config(self, report_path: str):
        self.report_path = report_path
        self.storage_type = 'azure' if report_path.startswith(('azure://', 'abfs://', 'https://')) else 's3'
        return self

    def build_report_upload_command(self) -> str:
        if not self.report_path:
            return ""

        # Commands to tar the folders
        tar_commands = [
            f"tar -czf {self.OUTPUT_TAR} -C {self.OUTPUT_DIR} .",
        ]

        if self.rerun:
            tar_commands.append(f"tar -czf {self.RERUN_OUTPUT_TAR} -C {self.RERUN_OUTPUT_PATH} .")

        # Upload commands based on storage type
        if self.storage_type == 's3':
            upload_commands = [f"aws s3 cp {self.OUTPUT_TAR} {self.report_path}/{self.OUTPUT_TAR}"]
            if self.rerun:
                upload_commands.append(f"aws s3 cp {self.RERUN_OUTPUT_TAR} {self.report_path}/{self.RERUN_OUTPUT_TAR}")
        else:  # azure
            upload_commands = [
                f"""az login --federated-token "$(cat $AZURE_FEDERATED_TOKEN_FILE)" --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID && \
           az storage blob upload \
             --overwrite \
             --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} \
             --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} \
             --name "{self.OUTPUT_TAR}" \
             --file "{self.report_path}/{self.OUTPUT_TAR}" \
             --auth-mode login && \
           echo "Upload complete." """
            ]
            if self.rerun:
                upload_commands.append(f"""az storage blob upload \
            --overwrite \
            --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} \
            --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} \
            --name "{self.RERUN_OUTPUT_TAR}" \
            --file "{self.report_path}/{self.RERUN_OUTPUT_TAR}" \
            --auth-mode login && \
            echo "Rerun Upload complete." """)
        return " ; ".join(tar_commands + upload_commands)

    def add_feature_files(self, files: list):
        self.feature_files.extend(files)
        return self

    def add_pre_req_tag(self, tags: []):
        self.prerequisites_tag.extend(tags)
        return self

    def add_pre_req_feature_files(self, files: list):
        self.prerequisites_feature_file.extend(files)
        return self

    def add_tags(self, tags: list):
        self.tags.extend(tags)
        return self

    def add_negated_tags(self, tags: list):
        self.negated_tags.extend(tags)
        return self

    def set_rerun(self, rerun: bool):
        self.rerun = rerun
        return self

    def set_parallel_processes(self, processes: int):
        self.parallel_processes = processes
        return self

    def build_prerequisites_command(self) -> str:
        cmd_parts = [BehaveCommandBuilder.BEHAVEX_CMD]
        if self.prerequisites_tag:
            cmd_parts.append(f"{BehaveCommandBuilder.TAG_ARG}={','.join(self.prerequisites_tag)}")
        cmd_parts.extend(self.prerequisites_feature_file)
        return " ".join(cmd_parts)

    def build_rerun_command(self) -> str:
        return f"{BehaveCommandBuilder.BEHAVEX_CMD} {BehaveCommandBuilder.RERUN_ARG} {BehaveCommandBuilder.FAILED_SCENARIO_PATH} -o {BehaveCommandBuilder.RERUN_OUTPUT_PATH}"
    def build_main_command(self) -> str:
        cmd_parts = [BehaveCommandBuilder.BEHAVEX_CMD]

        if self.feature_files:
            cmd_parts.extend(self.feature_files)

        if self.tags:
            cmd_parts.append(f"{BehaveCommandBuilder.TAG_ARG}={','.join(self.tags)}")

        if self.negated_tags:
            cmd_parts.append(f"{BehaveCommandBuilder.TAG_ARG}={','.join([f'~{tag}' for tag in self.negated_tags])}")

        if self.parallel_processes:
            cmd_parts.append(f"{BehaveCommandBuilder.PARALLEL_PRCS_ARG}={self.parallel_processes}")

        return " ".join(cmd_parts)


class BehaveTestExecutorOperator(KubernetesPodOperator):
    template_fields = ('feature_files', 'tags', 'negated_tags', 'parallel_processes',
                       'rerun', 'report_base_path', *KubernetesPodOperator.template_fields)

    def __init__(
            self,
            feature_files: list= None,
            report_base_path: str = None,
            tags: Optional[list] = None,
            negated_tags: Optional[list] = None,
            pre_req_tag: Optional[list] = None,
            pre_req_feature_files: list = None,
            parallel_processes: Optional[int] = None,
            rerun: bool = True,
            run_prerequisites: bool = True,
            storage_conn_id: str = "s3_default",
            generate_report: bool = False,
            **kwargs
    ):
        if not kwargs.get('arguments'):

            if not feature_files:
                raise ValueError("feature_files parameter is required")

            if generate_report :
                if not report_base_path:
                    raise ValueError("Report path  not set properly set argument report_base_path")
                self._validate_storage_path(report_base_path)

            if run_prerequisites:
                if not pre_req_feature_files:
                    raise ValueError("Pre requisites feature files should be specified")

        super().__init__(name=kwargs['task_id'],**kwargs)
        self.feature_files = feature_files
        self.tags = tags or []
        self.negated_tags = negated_tags or []
        self.parallel_processes = parallel_processes
        self.rerun = rerun
        self.report_base_path = report_base_path
        self.run_prerequisites = run_prerequisites
        self.storage_conn_id = storage_conn_id
        self.generate_report = generate_report
        self.pre_req_tag = pre_req_tag or []
        self.pre_req_feature_files = pre_req_feature_files or []

    def _get_report_path(self, context):
        timestamp = context['data_interval_start'].strftime('%Y-%m-%d-%H:%M:%S')
        return os.path.join(
            self.report_base_path,
            context.get('triggerer','Manual'),
            context['dag'].dag_id,
            self.task_id,
            timestamp
        )

    def _validate_storage_path(self, path: str):
        parsed = urlparse(path)
        if not parsed.scheme and parsed.scheme not in ('s3', 'azure', 'abfs', 'https'):
            raise ValueError(f"Storage path must start with s3:// or azure:// or abfs:// or https://, got {path}")

    def execute(self, context):

        logging.info("Starting BehaveTestExecutorOperator execution")

        # Get DAG run configuration
        dag_run_conf = context.get('dag_run').conf if context.get('dag_run') else {}

        if dag_run_conf:
            logging.info("Found DAG run configuration")
            # Check for default configuration that applies to all tasks
            default_conf = dag_run_conf.get('default', {})
            # Check for task-specific configuration
            task_conf = dag_run_conf.get(self.task_id, {})

            # Merge configurations, task-specific takes precedence over default
            override_conf = {**default_conf, **task_conf}

            if override_conf:
                logging.info(f"Applying configuration overrides for task {self.task_id}")
                # List of parameters that can be overridden
                override_params = [
                    'feature_files', 'tags', 'negated_tags', 'pre_req_tag',
                    'pre_req_feature_files', 'parallel_processes', 'rerun',
                    'run_prerequisites', 'generate_report'
                ]

                # Apply overrides
                for param in override_params:
                    if param in override_conf:
                        original_value = getattr(self, param)
                        setattr(self, param, override_conf[param])
                        logging.info(f"Overriding {param}: {original_value} -> {override_conf[param]}")

        self.cmds = ["/bin/sh","-c"]
        if not self.arguments:
            logging.info("Building behave command")
            command_builder = BehaveCommandBuilder()
            command_builder.add_feature_files(self.feature_files) \
                .add_tags(self.tags) \
                .add_negated_tags(self.negated_tags) \
                .set_parallel_processes(self.parallel_processes) \
                .set_rerun(self.rerun)

            commands = []
            if self.run_prerequisites:
                if self.pre_req_tag:
                    command_builder.add_pre_req_tag(self.pre_req_tag)
                command_builder.add_pre_req_feature_files(self.pre_req_feature_files)
                commands.append(command_builder.build_prerequisites_command())
            commands.append(command_builder.build_main_command())

            if self.rerun:
                commands.append(command_builder.build_rerun_command())
                commands.append(f"{BehaveCommandBuilder.BEHAVE_JOB_STATUS_KEY}=$?")
            else:
                commands.append(f"{BehaveCommandBuilder.BEHAVE_JOB_STATUS_KEY}=$?")

            if self.generate_report:
                logging.info("Building generate report command")
                report_path = self._get_report_path(context)
                command_builder.set_report_config(report_path)
                commands.append(command_builder.build_report_upload_command())
                commands.append(f"{BehaveCommandBuilder.REPORT_UPLOAD_JOB_STATUS_KEY}=$?")
            commands.append(BehaveCommandBuilder.JOB_EXIT_CMD)
            self.arguments = [" ; ".join(commands)]
            logging.info(f"Final commands built is {self.arguments}")

        return super().execute(context)
