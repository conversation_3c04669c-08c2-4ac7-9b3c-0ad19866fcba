from airflow.sensors.base import BaseSensorOperator
from airflow.providers.http.hooks.http import HttpHook
from commons.pe.constants.airflow_constants import PuppyGraphConstants
from airflow.exceptions import AirflowException
from airflow.models import TaskReschedule
import json
from typing import Sequence
import os
import requests
from urllib.parse import urljoin

class SDSPuppyGraphCacheOperator(BaseSensorOperator):
    template_fields: Sequence[str] = ("data",)
    template_fields_renderers = {"data": "json"}

    def __init__(
            self,
            *,
            schema_name: str,
            table_name: str,
            headers: dict,
            data: dict,
            request_params:dict,
            puppy_graph_conn_id=PuppyGraphConstants.PUPPY_GRAPH_DEFAULT_CONNECTION_ID,
            **kwargs,
    ):
        if "mode" not in kwargs:
            kwargs["mode"] = "reschedule"
        self.schema_name = schema_name
        self.table_name = table_name
        self.puppy_graph_conn_id = puppy_graph_conn_id
        self.endpoint = PuppyGraphConstants.CACHE_STATUS
        self.data = data
        self.method = PuppyGraphConstants.GET
        self.request_params = request_params or {}
        self.headers = headers
        self.http_hook = HttpHook(http_conn_id=self.puppy_graph_conn_id)
        
        super().__init__(**kwargs)
    
    def _make_request(self, method, endpoint, data=None, headers=None):
        """Helper function to make HTTP requests"""
        self.http_hook.get_conn()
        extra_base_endpoint = os.getenv("PUPPY_GRAPH_BASE_URL_PREFIX", "/")
        host = self.http_hook.base_url
        final_endpoint = os.path.join(extra_base_endpoint, endpoint)
        url = urljoin(host, final_endpoint)
        
        if method.upper() == "GET":
            response = requests.get(url, params=data, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, data=data, headers=headers)
        else:
            raise AirflowException(f"Unsupported HTTP method: {method}")

        if response.status_code != 200:
            raise AirflowException(f"Request to {url} failed with status code {response.status_code}: {response.text}")

        return response

    def poke(self, context):
        ti = context['ti']
        task_reschedules = TaskReschedule.find_for_task_instance(ti)
        poke_number = len(task_reschedules) + 1
        
        xcom_value  = ti.xcom_pull(key=f"{self.schema_name}.{self.table_name}")
        if xcom_value:
            self.node_label = json.loads(xcom_value[0]).get("label")
        else:
            raise AirflowException(f"XCOM {self.schema_name}.{self.table_name} returned: None")
        
        response = self._make_request("GET", PuppyGraphConstants.CACHE_STATUS, data=self.request_params, headers=self.headers)
        
        data = response.json().get("GremlinServer", {})
        items = data.get("LocalCacheStatusDetail", None)
        cache_status = data.get("LocalCacheStatus", "")
        
        if items is None:
            self.log.error(f"Cache not available: {cache_status}")
            raise AirflowException(f"Cache not available: {cache_status}")
                
        node_label = next((item for item in items if item.get("Name") == self.node_label), None)
        if not node_label:
            raise AirflowException(f"Label {self.node_label} not found in puppy graph cache")
        view_id = node_label.get("Id")
        self.data.update({"viewIds": [view_id]})
        self.log.info(f"Data: {json.dumps(self.data)}")

        if poke_number == 1:
            refresh_response = self._make_request("POST", PuppyGraphConstants.REFRESH_CACHE, data=json.dumps(self.data), headers=self.headers)
            refresh_response.raise_for_status()
            self.log.info(f"Starting to refresh cache for label {self.node_label}")
        else:
            state = node_label.get("State")
            analyze_status = node_label.get("AnalyzeDataStatus")
            self.log.info(f"Node label {self.node_label} found in cache with state {state}")
            if state == "SUCCESS" and analyze_status == "SUCCESS" and cache_status == "READY":
                self.log.info(f"Cache refreshed successfully for Label: {self.node_label}")
                return True
            elif state == "FAILED":
                error = node_label.get("ErrorMessage")
                raise AirflowException(f"Cache refresh for label {self.node_label} Failed, ERROR: {error}")
            else:
                return False
