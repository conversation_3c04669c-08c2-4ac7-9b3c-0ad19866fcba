from typing import Any, Dict, Optional

from airflow import settings
from airflow.plugins_manager import AirflowPlugin
from airflow.timetables.base import DagRunInfo, DataInterval, TimeRestriction, Timetable
from airflow.timetables.interval import CronDataIntervalTimetable
from pendulum import DateTime
from pendulum.tz.timezone import Timezone
from airflow.serialization.serialized_objects import decode_timezone
from airflow.serialization.serialized_objects import encode_timezone


class DelayedIntervalTimetable(CronDataIntervalTimetable):
    """Timetable that schedules data intervals with a cron expression and adds a delay to the data interval.
    
    Example: For a DAG with a cron expression for a trigger at the start of every hour, and a delay of 3 hours
             If it is manually triggered at 4:25pm, the data interval returned will be [12pm-1pm], i.e the previous data interval minus the delay provided
             For a scheduled run of 3-4pm the data interval returned will be 12-1pm
    
    """

    def __init__(self, cron: str, timezone: Timezone=settings.TIMEZONE, delay_hour: int=0) -> None:
        super().__init__(cron, timezone)
        self._delay_hour = delay_hour

    @classmethod
    def deserialize(cls, data: Dict[str, Any]) -> "Timetable":
        return cls(data["expression"], decode_timezone(data["timezone"]), data["delay_hour"])
    

    def serialize(self) -> Dict[str, Any]:
        return {
            "expression": self._expression,
            "timezone": encode_timezone(self._timezone),
            "delay_hour": self._delay_hour,
        }

    def next_dagrun_info(
        self,
        *,
        last_automated_data_interval: Optional[DataInterval],
        restriction: TimeRestriction,
    ) -> Optional[DagRunInfo]:
        earliest = restriction.earliest
        if not restriction.catchup:
            earliest = self._skip_to_latest(earliest)
        elif earliest is not None:
            earliest = self._align(earliest)
        if last_automated_data_interval is None:
            # First run; schedule the run at the first available time matching
            # the schedule, and retrospectively create a data interval for it.
            if earliest is None:
                return None
            start = earliest
        else:  # There's a previous run.
            if earliest is not None:
                # Catchup is False or DAG has new start date in the future.
                # Make sure we get the later one.
                start = max(last_automated_data_interval.end, earliest)
            else:
                # Data interval starts from the end of the previous interval.
                start = last_automated_data_interval.end
        if restriction.latest is not None and start > restriction.latest:
            return None
        end = self._get_next(start)
        # Return the data interval after subtracting the delayed period
        return DagRunInfo.interval(
            start=start.subtract(hours=self._delay_hour),
            end=end.subtract(hours=self._delay_hour),
        )

    def infer_manual_data_interval(self, *, run_after: DateTime) -> DataInterval:
        # Get the last complete period before run_after, e.g. if a DAG run is
        # scheduled at each midnight, the data interval of a manually triggered
        # run at 1am 25th is between 0am 24th and 0am 25th.
        end = self._get_prev(self._align(run_after))
        # Return the data interval after subtracting the delayed period
        return DataInterval(
            start=self._get_prev(end).subtract(hours=self._delay_hour),
            end=end.subtract(hours=self._delay_hour),
        )


class DelayedIntervalTimetablePlugin(AirflowPlugin):
    name = "delayed_interval_timetable"
    timetables = [DelayedIntervalTimetable]
