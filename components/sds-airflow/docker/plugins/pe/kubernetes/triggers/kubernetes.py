import asyncio
from kubernetes.client.exceptions import ApiException
from kubernetes_asyncio.client.exceptions import ApiException as AsyncApiException
from typing import AsyncIterator, Any

from airflow.providers.cncf.kubernetes.hooks.kubernetes import Async<PERSON><PERSON>rnetesHook, <PERSON>bernet<PERSON>Hook
from airflow.triggers.base import Trigger<PERSON>vent

from commons.pe.operator.base import TaskStatus
from commons.pe.triggers.base import SDSBaseTrigger
from plugins.pe.kubernetes.constants import SPARK_OPERATOR_API_GROUP, SPARK_OPERATOR_API_VERSION, SPARK_OPERATOR_PLURAL
from plugins.pe.kubernetes.kubernetes_mixin import KubernetesMixin


class KubernetesSparkTrigger(SDSBaseTrigger, KubernetesMixin):
    FAILURE_STATES = ("FAILED", "SUBMISSION_FAILED", "FAILING", "UNKNOWN",)
    SUCCESS_STATES = ("COMPLETED", "SUCCEEDING",)

    def __init__(
            self,
            spark_application_name: str,
            **kwargs
    ):
        self.spark_application_name = spark_application_name
        KubernetesMixin.__init__(self, **kwargs)
        SDSBaseTrigger.__init__(self, **kwargs)

    def sync_run(self) -> TriggerEvent:
        self.log.info(f"Going to check status of spark application {self.spark_application_name}")
        manifest: dict = self.sync_hook.get_custom_object(
            name=self.spark_application_name,
            namespace=self.namespace,
            group=SPARK_OPERATOR_API_GROUP,
            version=SPARK_OPERATOR_API_VERSION,
            plural=SPARK_OPERATOR_PLURAL
        )

        app_state: str = manifest.get("status", {}).get("applicationState", {}).get("state")
        if app_state is None:
            # Application not yet submitted
            self.log.info(f"Application {self.spark_application_name} not yet submitted")
            return TriggerEvent(payload={"status": TaskStatus.RUNNING})

        self.log.info(f"Application {self.spark_application_name} is in state {app_state}")
        if app_state in self.FAILURE_STATES:
            return TriggerEvent(payload={"status": TaskStatus.FAILED, "message": f"Application state is {app_state}"})
        elif app_state in self.SUCCESS_STATES:
            return TriggerEvent(payload={"status": TaskStatus.SUCCESS,
                                         "message": f"Application state is {app_state}"})
        else:
            return TriggerEvent(payload={"status": TaskStatus.RUNNING,
                                         "message": f"{self.spark_application_name} still running with state {app_state}"})


    def serialize(self) -> tuple[str, dict[str, Any]]:
        return (
            "plugins.pe.kubernetes.triggers.kubernetes.KubernetesSparkTrigger",
            {
                "spark_application_name": self.spark_application_name,
                "kubernetes_conn_id": self.kubernetes_conn_id,
                "namespace": self.namespace
            }
        )

    async def run(self) -> AsyncIterator[TriggerEvent]:
        while True:
            self.log.info(f"Going to asynchronously check status of spark application {self.spark_application_name}")
            manifest: dict = await self.async_hook.get_custom_object_async(
                name=self.spark_application_name,
                namespace=self.namespace,
                group=SPARK_OPERATOR_API_GROUP,
                version=SPARK_OPERATOR_API_VERSION,
                plural=SPARK_OPERATOR_PLURAL
            )

            app_state: str = manifest.get("status", {}).get("applicationState", {}).get("state")
            if app_state is None:
                # Application not yet submitted
                self.log.info(f"Application {self.spark_application_name} not yet submitted")
                await asyncio.sleep(5.0)
            else:
                if app_state in self.FAILURE_STATES:
                    yield TriggerEvent(payload={"status": TaskStatus.FAILED, "message": f"Application state is {app_state}"})
                    return
                elif app_state in self.SUCCESS_STATES:
                    yield TriggerEvent(payload={"status": TaskStatus.SUCCESS, "message": f"Application state is {app_state}"})
                    return
                else:
                    await asyncio.sleep(5.0)
