from kubernetes_asyncio import client as async_client

from airflow.providers.cncf.kubernetes.hooks.kubernetes import AsyncKubernetesHook


class SDSAsyncKubernetesHook(AsyncKubernetesHook):

    async def get_custom_object_async(self, group: str, version: str, plural: str, name: str, namespace: str):
        async with self.get_conn() as connection:
            v1_api = async_client.CustomObjectsApi(connection)
            custom_object = await v1_api.get_namespaced_custom_object(
                name=name,
                namespace=namespace,
                group=group,
                plural=plural,
                version=version
            )

        return custom_object

    async def get_pod_logs_async(self, pod: str, container: str, namespace: str):
        async with self.get_conn() as connection:
            v1_api = async_client.CoreV1Api(connection)
            logs = await v1_api.read_namespaced_pod_log(
                name=pod,
                namespace=namespace,
                container=container,
                follow=False,
                timestamps=False,
            )
            if logs:
                return logs
