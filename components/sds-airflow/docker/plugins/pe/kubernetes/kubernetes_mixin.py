import hashlib
from functools import cached_property
from typing import Sequence

from airflow.providers.cncf.kubernetes.hooks.kubernetes import KubernetesHook

from plugins.pe.kubernetes.hooks.kubernetes import SDSAsyncKubernetesHook


class KubernetesMixin:
    template_fields: Sequence[str] = ("namespace",)

    def __init__(
            self,
            namespace: str = "default",
            kubernetes_conn_id: str = "kubernetes_default",
            **kwargs
    ):
        self.namespace = namespace
        self.kubernetes_conn_id = kubernetes_conn_id

        super().__init__(**kwargs)

    @cached_property
    def async_hook(self) -> SDSAsyncKubernetesHook:
        return SDSAsyncKubernetesHook(
            conn_id=self.kubernetes_conn_id
        )

    @cached_property
    def sync_hook(self) -> KubernetesHook:
        return KubernetesHook(
            conn_id=self.kubernetes_conn_id
        )

    @staticmethod
    def make_name_compatible(name: str, character_limit: int = 63) -> str:
        """Makes name compatible with kubernetes specifications
        Args:
            name(str):name of the application
            character_limit(int): Max limit for the name, Default is 63. Some CRD's have a smaller limit and this value can be leveraged for that
        """
        application_name = "".join([char for char in name if char.isalnum()])
        application_name = hashlib.sha256(application_name.encode()).hexdigest()[:character_limit - 64] if len(
            application_name) > character_limit else application_name
        if application_name[0].isdigit():
            application_name = application_name.replace(application_name[0], "a", 1)
        return application_name.lower()
