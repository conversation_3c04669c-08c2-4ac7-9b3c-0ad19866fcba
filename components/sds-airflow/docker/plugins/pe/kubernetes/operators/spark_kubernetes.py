import json
import os
from datetime import timedelta
from typing import Any, Optional, Dict

from airflow.exceptions import AirflowException
from airflow.utils.context import Context
from kubernetes.client.exceptions import ApiException

from commons.pe.common_utils import Utils
from commons.pe.operator.base import TaskStatus
from plugins.pe.kubernetes.constants import SPARK_OPERATOR_API_GROUP, SPARK_OPERATOR_PLURAL, SPARK_OPERATOR_API_VERSION
from plugins.pe.kubernetes.kubernetes_mixin import KubernetesMixin
from plugins.pe.kubernetes.triggers.kubernetes import KubernetesSparkTrigger
from plugins.pe.spark.base import SparkOperatorBase


class SparkKubernetesOperator(KubernetesMixin, SparkOperatorBase):
    trigger_class = KubernetesSparkTrigger
    global_attributes = SparkOperatorBase.global_attributes | {
        "runtime_config": "DEFAULT_SPARK_K8S_RUNTIME_CONFIGURATIONS"}
    template_fields = SparkOperatorBase.template_fields + KubernetesMixin.template_fields

    ui_color = "Orange"

    def __init__(
            self,
            **kwargs
    ):

        if "retries" not in kwargs:
            kwargs["retries"] = int(os.getenv("SDS_KUBERNETES_SPARK_OPERATOR_RETRY", "0"))
            kwargs["retry_delay"] = timedelta(seconds=int(os.getenv("SDS_KUBERNETES_SPARK_OPERATOR_RETRY_DELAY", "60")))

        super().__init__(**kwargs)

    def pre_execute(self, context: Any):
        super().pre_execute(context)
        self.namespace = self.runtime_config.get("metadata", {}).get("namespace", "default")
        # Handle app_name to make it kubernetes compatible
        spark_app_name = self.make_name_compatible(name=self.app_name, character_limit=55)
        self.log.info(f"Setting application name to {spark_app_name}")
        self.app_name = spark_app_name
        self.labels.update({"job_name": self.app_name})

    def prepare(self, context: Context):
        self.log.info(f"Creating SparkApplication spec with application name - '{self.app_name}'")
        self.job_submit_body = {"spec": {}, "metadata": {}}
        self.job_submit_body["metadata"].update({"name": self.app_name, "namespace": self.namespace})

        k8s_spec_dict = {
            "arguments": self.args,
            "sparkConf": self.conf,
            "mainApplicationFile": self.application_file,
            "mainClass": self.class_name,
            "sparkVersion": self.spark_version
        }
        # Add spark application dependencies to submission body
        deps_dict = {
            "pyFiles": self.py_files,
            "files": self.files,
            "packages": self.packages,
            "repositories": self.repositories,
            "jars": self.jars
        }

        if [value for value in deps_dict.values() if value]:
            k8s_spec_dict.update({"deps": deps_dict})

        k8s_spec_dict.update({
            "driver": {
                "cores": self.driver_cores,
                "memory": self.driver_memory,
                "labels": self.labels
            },
            "executor": {
                "cores": self.executor_cores,
                "memory": self.executor_memory,
                "instances": self.executor_instances,
                "labels": self.labels,
                "coreRequest": str(int(self.executor_cores * 1000 / 2)) + "m"
            }
        })

        self.job_submit_body.update({"spec": k8s_spec_dict})
        self.job_submit_body = Utils.merge_dictionaries(self.runtime_config, self.job_submit_body)
        self.log.info("Successfully completed creating spark application manifest")

    def submit(self, context: Context):
        job_exists = self.check_application_exists()
        if job_exists:
            self.log.info(f"Found existing spark application with name {self.app_name} in namespace {self.namespace} ")
            if self.force_replace_existing:
                # Delete existing running application before submitting again
                self.log.info("Attempting to kill existing spark application")
                self.delete_spark_application()

            else:
                # Go straight to tracking job status
                return

        self.log.info(f"Submitting spark application with manifest - {json.dumps(self.job_submit_body)}")
        self.sync_hook.create_custom_object(
            group=SPARK_OPERATOR_API_GROUP,
            version=SPARK_OPERATOR_API_VERSION,
            plural=SPARK_OPERATOR_PLURAL,
            body=self.job_submit_body,
            namespace=self.namespace
        )

    def on_operator_complete(self, context: Context, event: Optional[Dict[str, Any]]):
        is_success = event.get("status") == TaskStatus.SUCCESS
        log_method = self.log.info if is_success else self.log.error
        pod_logs = self.get_pod_logs()
        log_method(pod_logs)
        self.delete_spark_application()
        if is_success:
            self.log.info(f"Spark application {self.app_name} successfully completed execution")
        else:
            # Handle failure case
            raise AirflowException(f"Spark application {self.app_name} failed ")

    def get_pod_logs(self):
        try:
            pod_logs = self.sync_hook.get_pod_logs(
                pod_name=f"{self.app_name}-driver",
                container="spark-kubernetes-driver",
                namespace=self.namespace
            )
        except ApiException:
            self.log.error("The job has completed but the driver pod seems to be missing. Skipping displaying the logs")
            return
        return pod_logs.data.decode("utf-8")

    def check_application_exists(self):
        """To check if the application with the same name already exists

        Returns: True if spark application exists else False

        """
        try:
            self.sync_hook.get_custom_object(
                name=self.app_name,
                group=SPARK_OPERATOR_API_GROUP,
                version=SPARK_OPERATOR_API_VERSION,
                plural=SPARK_OPERATOR_PLURAL,
                namespace=self.namespace
            )
            return True
        except ApiException:
            return False

    def delete_spark_application(self):
        self.sync_hook.delete_custom_object(
            name=self.app_name,
            group=SPARK_OPERATOR_API_GROUP,
            version=SPARK_OPERATOR_API_VERSION,
            plural=SPARK_OPERATOR_PLURAL,
            namespace=self.namespace
        )
        self.log.info(f"Successfully deleted spark application {self.app_name} in namespace {self.namespace}")

    def _get_trigger_params(self, context: Context, **kwargs) -> dict[str, Any]:
        return {
            "spark_application_name": self.app_name,
            "namespace": self.namespace,
            "kubernetes_conn_id": self.kubernetes_conn_id
        }
