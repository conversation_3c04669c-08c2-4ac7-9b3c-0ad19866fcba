from __future__ import annotations

import json
import urllib.parse

import pendulum
import requests
from airflow.exceptions import AirflowException
from requests.auth import HTTPBasicAuth
from airflow.hooks.base import BaseHook


class DruidConstants:
    INDEXING_ENDPOINT = "druid/indexer/v1/task/"


class SDSDruidHook(BaseHook):

    def __init__(self,
                 druid_conn_id: str,
                 headers: dict = None,
                 **kwargs
                 ):
        self.druid_conn_id = druid_conn_id
        self.headers = headers
        super().__init__(**kwargs)

    def get_druid_connection_url(self) -> str:
        conn = self.get_connection(self.druid_conn_id)
        host = conn.host
        port = conn.port
        conn_type = conn.conn_type or "http"
        endpoint = conn.extra_dejson.get("endpoint", DruidConstants.INDEXING_ENDPOINT)
        return f"{conn_type}://{host}:{port}/{endpoint}"

    def get_auth(self) -> HTTPBasicAuth | None:
        conn = self.get_connection(self.druid_conn_id)
        user = conn.login
        password = conn.password
        if user is not None and password is not None:
            return HTTPBasicAuth(user, password)
        else:
            return None

    def submit_indexing_job(self, data: dict):
        url = self.get_druid_connection_url()
        response = requests.post(url, data=json.dumps(data), headers=self.headers, auth=self.get_auth())
        code = response.status_code
        if code != 200:
            self.log.error(f"Error submitting the Druid job to {url}. Returned ({code}) -> {response.content}")
            raise AirflowException(f"Indexing job failed with status code {code}, error message - {response.content}")

        response_body = response.json()
        return response_body["task"]

    def get_indexing_job(self, druid_task_id: str):
        get_status_url = urllib.parse.urljoin(self.get_druid_connection_url(), f"{druid_task_id}/status")
        response = requests.get(
            url=get_status_url,
            headers=self.headers,
            auth=self.get_auth()
        )
        if response.status_code == 200:
            return response.json()
        else:
            raise AirflowException(f"Failed to get indexing task details - {response.text}")

    def get_indexing_job_time(self, druid_task_id: str):
        response = self.get_indexing_job(druid_task_id)

        created_time = pendulum.parse(response.get("status").get("createdTime"))
        now = pendulum.now(tz=created_time.tz)
        difference = now.diff(created_time).in_seconds()

        return difference

    def get_logs(self,druid_task_id:str):
        log_url = urllib.parse.urljoin(self.get_druid_connection_url(), f"{druid_task_id}/log")
        response = requests.get(
            url=log_url,
            headers=self.headers,
            auth=self.get_auth()
        )
        if response.status_code == 200:
            return response.text
        else:
            return response.status_code

    def kill_indexing_task(self, druid_task_id: str):
        requests.post(f"{self.get_druid_connection_url()}/{druid_task_id}/shutdown", auth=self.get_auth())
