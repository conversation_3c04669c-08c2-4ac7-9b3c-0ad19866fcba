from __future__ import annotations

import json
from typing import Sequence

import pendulum
from airflow.exceptions import AirflowException
from airflow.sensors.base import BaseSensorOperator, PokeReturnValue
from airflow.utils.context import Context

from plugins.pe.druid.sds_druid_hook import SDSDruidHook


class DruidIndexingOperator(BaseSensorOperator):
    template_fields: Sequence[str] = ("data",)

    def __init__(self,
                 data: dict,
                 druid_conn_id: str = "druid_conn",
                 headers=None,
                 max_ingestion_time: int | None = None,
                 **kwargs
                 ):
        if headers is None:
            headers = {}
        self.druid_conn_id = druid_conn_id
        self.data = data
        self.max_ingestion_time = max_ingestion_time
        self.headers = headers
        self.headers = {**{"content-type": "application/json"}, **headers}

        kwargs["mode"] = "poke"  # Always set the mode to poke
        super().__init__(**kwargs)

    def poke(self, context: Context) -> bool | PokeReturnValue:
        hook = SDSDruidHook(druid_conn_id=self.druid_conn_id, headers=self.headers)
        datasource = self.data.get("spec", {}).get("dataSchema", {}).get("dataSource")
        dagrun = context["dag_run"]
        start_date_iso = pendulum.instance(dagrun.start_date).to_iso8601_string()

        # Create unique task id for druid with index type datasource and data interval start
        druid_task_id = f"{self.data.get('type')}_{datasource}_{context['data_interval_start'].to_iso8601_string()}_{start_date_iso}_{context['ti'].try_number}"
        self.data.update({"id": druid_task_id})
        context["ti"].xcom_push(key="indexing_spec", value=json.dumps(self.data))
        context["ti"].xcom_push(key="druid_task_id", value=druid_task_id)
        self.log.info(f"Druid ingestion spec - {json.dumps(self.data)}")

        poke_number = context["ti"].xcom_pull(key="poke_number", task_ids=self.task_id, dag_id=self.dag_id, default=1)
        context["ti"].xcom_push(key="poke_number", value=poke_number + 1)

        # If first poke, submit indexing job
        if poke_number == 1:
            self.log.info(f"Submitting indexing job for datasource {datasource} with id {druid_task_id}")
            druid_task_id = hook.submit_indexing_job(data=self.data)
            context["ti"].xcom_push(key="druid_task_id", value=druid_task_id)

            self.log.info(f"Druid indexing job submitted with task_id {druid_task_id}")

            return False

        # Check status of druid indexing job
        druid_task_id = context["ti"].xcom_pull(key="druid_task_id", task_ids=self.task_id, dag_id=self.dag_id)
        task_response = hook.get_indexing_job(druid_task_id)
        context["ti"].xcom_push(key="job_response", value=task_response)
        state = task_response.get("status").get("status")
        self.log.info(f"Indexing task {druid_task_id} is in state {state}")

        if state == "RUNNING":
            duration = hook.get_indexing_job_time(druid_task_id)
            self.log.info(f"Indexing task has been running for {duration} seconds")
            if self.max_ingestion_time and duration > self.max_ingestion_time:
                # ensure that the job gets killed if the max ingestion time is exceeded
                self.log.info(f"Indexing task has been running for more than {self.max_ingestion_time}")
                hook.kill_indexing_task(druid_task_id)
                raise AirflowException(
                    f"Task {druid_task_id} has run for more than the limit {self.max_ingestion_time}")

            return False

        elif state == "SUCCESS":
            logs = hook.get_logs(druid_task_id)
            if isinstance(logs, int):
                self.log.error(f"Failed to get logs for task {druid_task_id}. Status code {logs} returned")
            else:
                self.log.info(logs)
            self.log.info(f"Successfully indexed task for datasource {datasource}")
            return True

        elif state == "FAILED":
            error_msg = task_response.get("status").get("errorMsg","")
            logs = hook.get_logs(druid_task_id)
            if isinstance(logs,int):
                self.log.error(f"Failed to get logs for task {druid_task_id}. Status code {logs} returned")
            else:
                self.log.error(logs)
            raise AirflowException(error_msg)
