from abc import ABC
from typing import Any, Sequence

from airflow.lineage import prepare_lineage

from commons.pe.operator.base import SDSBaseOperator, BaseJobOperator


class SparkOperatorBase(BaseJobOperator, ABC):
    global_attributes = SDSBaseOperator.global_attributes | {"conf": "DEFAULT_SPARK_CONFIGURATIONS"}

    template_fields: Sequence[str] = BaseJobOperator.template_fields + ("args",
                                      "conf",
                                      "class_name",
                                      "application_file",
                                      "py_files",
                                      "files",
                                      "jars",
                                      "packages",
                                      "repositories",
                                      "driver_memory",
                                      "driver_cores",
                                      "executor_memory",
                                      "executor_cores",
                                      "executor_instances",
                                      "runtime_config",
                                      "spark_version",
                                      )

    def __init__(
            self,
            args: list[str] = None,
            conf: dict[str, str] | str = None,
            class_name: str = None,
            application_file: str = None,
            jars: list[str] = None,
            driver_memory: str = "1g",
            driver_cores: int = 1,
            executor_memory: str = "1g",
            executor_cores: int = 1,
            executor_instances=1,
            py_files: list[str] = None,
            files: list[str] = None,
            packages: list[str] = None,
            repositories: list[str] = None,
            runtime_config: dict = None,
            spark_version: str = "3.5.1",
            **kwargs
    ):
        self.runtime_config = runtime_config or {}
        self.args = args or []
        self.conf = conf or {}
        self.class_name = class_name
        self.application_file = application_file
        self.jars = jars or []
        self.driver_memory = driver_memory
        self.driver_cores = driver_cores
        self.executor_memory = executor_memory
        self.executor_cores = executor_cores
        self.executor_instances = executor_instances
        self.spark_version = spark_version
        self.py_files = py_files or []
        self.files = files or []
        self.repositories = repositories or []
        self.packages = packages or []

        super().__init__(**kwargs)

    @prepare_lineage
    def pre_execute(self, context: Any):
        # Execute the mixin pre execute first
        super().pre_execute(context)
        self.conf["spark.app.name"] = self.conf.get("spark.app.name", self.app_name)
