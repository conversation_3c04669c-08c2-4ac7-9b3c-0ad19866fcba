from typing import Any
from airflow.models import BaseOperator
from airflow.providers.redis.hooks.redis import RedisHook


class SDSRedisDeleteKey(BaseOperator):
    """
    Operator to delete key pattern from redis.
    """

    template_fields = ('key',)

    def __init__(self, *, key: list, check_for_patterns: bool = True, redis_conn_id: str = 'redis_default',
                 **kwargs) -> None:
        """
        @param key Key or Key pattern to delete
        @param check_for_patterns Specify delete all patterns or just key
        @param redis_conn_id: str Redis connection string
        @rtype: None
        """
        super().__init__(**kwargs)
        self.key = key
        self.check_for_patterns = check_for_patterns
        self.redis_conn_id = redis_conn_id
        

    def delete_keys(self, redis_key):
        total_keys = 0
        if self.check_for_patterns:
            for key in self.redis_client.scan_iter(redis_key):
                self.redis_client.delete(key)
                total_keys = total_keys+1
            self.log.info(F"Deleted {total_keys} keys for pattern {redis_key} from redis")
        else:
            self.redis_client.delete(redis_key)
            self.log.info(F"Deleted key {self.key} from redis")

    def execute(self, context: Any):
        self.redis_client = RedisHook(self.redis_conn_id).get_conn()
        self.log.info(f"Deleting keys {self.key} from redis")
        [self.delete_keys(each_redis_key) for each_redis_key in self.key]
