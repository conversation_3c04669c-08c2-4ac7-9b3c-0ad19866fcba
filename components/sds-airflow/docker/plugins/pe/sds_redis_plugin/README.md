## SDS Redis Plugin
Plugin to manage Redis server communication. Uses [RedisHook](https://airflow.apache.org/docs/apache-airflow-providers-redis/stable/_api/airflow/providers/redis/hooks/redis/index.html) available in airflow.

## Setting Redis connection
* Set redis connection with id passed in redis_con_id in below classes to connect to redis server.
* [Set connection using environment variable](https://airflow.apache.org/docs/apache-airflow/stable/howto/connection.html#connection-uri-format)
* SSL enabled redis can be set using parameter in connection variable as per this [doc](https://airflow.apache.org/docs/apache-airflow-providers-redis/stable/_api/airflow/providers/redis/hooks/redis/index.html#airflow.providers.redis.hooks.redis.RedisHook)
## Operators
* [SDSRedisDeleteKey](sds_redis_operator.py) - Operator to delete keys in redis server.
Input parameters
  key: str, check_for_patterns: bool = True ,redis_conn_id: str = 'redis_default', **kwargs
  * ```key``` - Key to delete from redis server. Use regex with ``check_for_patterns`` `True`
  remove keys with the pattern.
  * ``check_for_patterns`` Boolean value to specify delete all keys coming in that pattern or just key.
  * ``redis_con_id`` Redis connection ID.

