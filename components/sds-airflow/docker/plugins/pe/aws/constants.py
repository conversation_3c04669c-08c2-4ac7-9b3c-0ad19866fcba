class EmrConstants:
    SERVERLESS_APPLICATION_NAME_PREFIX_VAR = "SERVERLESS_APPLICATION_PREFIX"
    SERVERLESS_OVERRIDE_DEFAULT_CONF = {
        "spark.authenticate": False,
        "spark.dynamicAllocation.enabled": False
    }
    SERVERLESS_SPARK_MAP = {
        "3.2.3": "emr-6.7.0"
    }
    JOB_INTERMEDIATE_STATES = ['PENDING', 'RUNNING', 'SCHEDULED', 'SUBMITTED']
    JOB_FAILURE_STATES = ['FAILED', 'CANCELLING', 'CANCELLED']
    JOB_SUCCESS_STATES = ['SUCCESS']
    JOB_TERMINAL_STATES = JOB_SUCCESS_STATES + JOB_FAILURE_STATES
