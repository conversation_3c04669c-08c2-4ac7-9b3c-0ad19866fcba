from functools import cached_property
from uuid import uuid4

from plugins.pe.aws.hooks.emr import SDSEmrServerlessHook


class EmrServerlessMixin:

    def __init__(
            self,
            aws_conn_id: str = "aws_default",
            release_label: str = "emr-6.7.0",
            application_id: str = None,
            execution_role_arn: str = None,
            application_configuration: dict = None,
            monitoring_configuration: dict = None,
            client_request_token: str = '',
            **kwargs
    ):
        self.aws_conn_id = aws_conn_id
        self.release_label = release_label
        self.application_id = application_id
        self.execution_role_arn = execution_role_arn
        self.application_configuration = application_configuration
        self.monitoring_configuration = monitoring_configuration
        self.client_request_token = client_request_token or str(uuid4())

        super().__init__(**kwargs)

    @cached_property
    def sync_hook(self) -> SDSEmrServerlessHook:
        """Create and return an EmrServerlessHook."""
        return SDSEmrServerlessHook(aws_conn_id=self.aws_conn_id)
