import json
import os
from typing import Optional, Dict, Any
from uuid import uuid4

from airflow.exceptions import AirflowException
from airflow.utils.context import Context

from commons.pe.operator.base import TaskStatus
from plugins.pe.aws.constants import EmrConstants
from plugins.pe.aws.mixins import EmrServerlessMixin
from plugins.pe.spark.base import SparkOperatorBase


class EmrServerlessSparkOperator(EmrServerlessMixin, SparkOperatorBase):

    global_attributes = SparkOperatorBase.global_attributes | {
        "runtime_config": "DEFAULT_SPARK_EMR_SERVERLESS_RUNTIME_CONFIGURATIONS"}

    def __init__(
            self,
            **kwargs
    ):
        super().__init__(**kwargs)
        self.job_run_id = None

    def _get_trigger_params(self, context: Context, **kwargs) -> dict[str, Any]:
        return {
            "aws_conn_id": self.aws_conn_id,
            "monitoring_configuration": self.monitoring_configuration,
            "application_id": self.application_id,
            "job_id": self.job_run_id
        }

    def pre_execute(self, context: Context):
        super().pre_execute(context)
        if not self.release_label:
            rel_label_from_runtime = self.runtime_config.get("release_label", None)
            if not rel_label_from_runtime:
                self.release_label = EmrConstants.SERVERLESS_SPARK_MAP.get(self.spark_version, "emr-6.7.0")
            else:
                self.release_label = rel_label_from_runtime
        self.execution_role_arn = self.execution_role_arn if self.execution_role_arn else self.runtime_config.get(
            "execution_role_arn", None)
        self.application_configuration = self.application_configuration if self.application_configuration else self.runtime_config.get(
            "application_configuration", None)
        self.monitoring_configuration = self.monitoring_configuration if self.monitoring_configuration else self.runtime_config.get(
            "monitoring_configuration", None)
        self.application_id = self.get_application_id()

    def prepare(self, context: Context):
        self.labels.update({"application_id": self.application_id,
                            "job_uuid": f"{self.dag_id}-{self.task_id}-{context['data_interval_start'].strftime('%Y-%m-%d-%H-%M-%S')}"})
        self.log.info(f"Obtained EMR Serverless application with id '{self.application_id}'")
        if not self.execution_role_arn:
            raise AirflowException("EMR Serverless job has not been provided an execution role ARN.")
        job_driver = {"sparkSubmit": {}}
        job_driver["sparkSubmit"].update({"entryPointArguments": self.args})
        job_driver["sparkSubmit"].update({"entryPoint": self.application_file})
        spark_submit_params = (
            f"--class {self.class_name} "
            f"--driver-cores {str(self.driver_cores)} "
            f"--driver-memory {self.driver_memory} "
            f"--executor-cores {str(self.executor_cores)} "
            f"--executor-memory {self.executor_memory} "
            f"--num-executors {str(self.executor_instances)} "
        )
        for conf, conf_value in EmrConstants.SERVERLESS_OVERRIDE_DEFAULT_CONF.items():
            if conf not in self.conf:
                self.conf.update({conf: conf_value})
        spark_submit_params = spark_submit_params + "--conf " + " --conf ".join(
            [f"{key}={str(value)}" for key, value in self.conf.items()])
        for attribute in ["py_files", "files", "jars", "packages", "repositories"]:
            if getattr(self, attribute):
                spark_submit_params = spark_submit_params + f" --{attribute.replace('_', '-')} {','.join(getattr(self, attribute))} "

        job_driver["sparkSubmit"].update({"sparkSubmitParameters": spark_submit_params})
        self.log.info(
            f"Going to submit emr serverless job '{self.app_name}' with sparkSubmit command - {spark_submit_params}")

        configuration_overrides = {}
        if self.application_configuration:
            configuration_overrides.update({"applicationConfiguration": self.application_configuration})

        if self.monitoring_configuration:
            configuration_overrides.update({"monitoringConfiguration": self.monitoring_configuration})

        self.job_submit_body = {"job_driver": job_driver, "configuration_overrides": configuration_overrides}

    def submit(self, context: Context):
        job_tags = self.get_job_tags(context)
        job_uuid = f"{self.dag_id}-{self.task_id}-{context['data_interval_start'].strftime('%Y-%m-%d-%H-%M-%S')}"
        if job_uuid in [tag.get("job_uuid") for tag in job_tags.values()]:
            self.log.info(f"Job run '{self.app_name} already exists with job id {self.job_run_id}'")
        else:
            submission_response = self.sync_hook.start_job_run(
                application_id=self.application_id,
                client_token=self.client_request_token,
                execution_role_arn=self.execution_role_arn,
                tags=self.labels,
                name=self.app_name,
                job_driver_params=self.job_submit_body.get("job_driver"),
                configuration_overrides=self.job_submit_body.get("configuration_overrides", {})
            )
            if submission_response['ResponseMetadata']['HTTPStatusCode'] != 200:
                raise AirflowException(f'EMR serverless job failed to start: {submission_response}')
            self.job_run_id = submission_response["jobRunId"]
            self.log.info(f"Job run '{self.app_name}' created with job run id - '{self.job_run_id}'")

    def on_operator_complete(self, context: Context, event: Optional[Dict[str, Any]]):
        is_success = event.get("status") == TaskStatus.SUCCESS
        log_method = self.log.info if is_success else self.log.error
        log_path = self.monitoring_configuration.get("s3MonitoringConfiguration", {}).get("logUri")
        logs = self.sync_hook.get_job_logs(application_id=self.application_id, job_id=self.job_run_id,
                                           log_path=log_path)
        log_method(logs)
        job_response = self.sync_hook.get_job_response(application_id=self.application_id, job_run_id=self.job_run_id)
        self.log.info(
            f"Resources utilized for this job is - {json.dumps(job_response.get('jobRun').get('totalResourceUtilization'))}")
        arn = job_response.get("jobRun").get("arn")
        self.sync_hook.untag_resource(resource_arn=arn, tag_keys=["job_uuid"])
        if is_success:
            self.log.info(f"Spark application {self.app_name} successfully completed execution")
        else:
            # Handle failure case
            raise AirflowException(f"Spark application {self.app_name} failed ")

    def get_application_id(self):
        applications = [app for app in self.sync_hook.list_applications() if app.get("releaseLabel") == self.release_label]
        if not applications:
            raise AirflowException(f"EMR Serverless application with release version '{self.release_label}' not found")
        target_app = f"{os.getenv(EmrConstants.SERVERLESS_APPLICATION_NAME_PREFIX_VAR, 'SDS-DEV-SPARK')}-{self.release_label}"
        if target_app not in [app.get("name") for app in applications]:
            raise AirflowException(
                f"EMR Serverless application with name '{target_app}' not found for applications with release version '{self.release_label}'")
        return next(app.get("id") for app in applications if app.get("name") == target_app)

    def get_job_id(self, context: Context):
        job_tags = self.get_job_tags(context=context)
        job_id_list = [job_id for job_id, job_tag in job_tags.items() if job_tag.get(
            "job_uuid") == f"{self.dag_id}-{self.task_id}-{context['data_interval_start'].strftime('%Y-%m-%d-%H-%M-%S')}"]
        if not job_id_list:
            return None
        else:
            return job_id_list[0]

    def get_job_tags(self, context: Context):
        job_list = self.sync_hook.list_job_runs(application_id=self.application_id,
                                           created_after=context["data_interval_start"])
        job_tags = {job.get("id"): self.sync_hook.list_tags_for_resource(resource_arn=job.get("arn"))['tags'] for job in
                    job_list.get('jobRuns')}

        return job_tags
