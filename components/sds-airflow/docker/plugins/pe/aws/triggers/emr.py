from typing import AsyncIterator, Any

from airflow.triggers.base import Trigger<PERSON>vent

from commons.pe.operator.base import TaskStatus
from commons.pe.triggers.base import SDSBaseTrigger
from plugins.pe.aws.constants import EmrConstants
from plugins.pe.aws.mixins import EmrServerlessMixin


class EmrServerlessJobCheckTrigger(SDSBaseTrigger, EmrServerlessMixin):

    def __init__(
            self,
            job_id: str,
            **kwargs
    ):
        self.job_id = job_id
        EmrServerlessMixin.__init__(self, **kwargs)
        SDSBaseTrigger.__init__(self, **kwargs)

    def sync_run(self) -> TriggerEvent:
        job_response: dict = self.sync_hook.get_job_response(application_id=self.application_id, job_run_id=self.job_id)
        state: str = job_response.get('jobRun', {}).get("state")
        self.log.info(f"Job run '{self.job_id}' is in state '{state}'")
        if state in EmrConstants.JOB_SUCCESS_STATES:
            return TriggerEvent(payload={"status": TaskStatus.SUCCESS, "message": f"Application state is {state}"})
        elif state in EmrConstants.JOB_FAILURE_STATES:
            return TriggerEvent(payload={"status": TaskStatus.FAILED, "message": f"Application state is {state}"})
        else:
            self.log.info(job_response.get("jobRun").get("stateDetails"))
            return TriggerEvent(payload={"status": TaskStatus.RUNNING})

    def serialize(self) -> tuple[str, dict[str, Any]]:
        return (
            "plugins.pe.aws.triggers.emr.EmrServerlessJobCheckTrigger",
            {
                "job_id": self.job_id,
                "aws_conn_id": self.aws_conn_id,
                "monitoring_configuration": self.monitoring_configuration,
                "application_id": self.application_id
            }
        )

    async def run(self) -> AsyncIterator[TriggerEvent]:
        pass
