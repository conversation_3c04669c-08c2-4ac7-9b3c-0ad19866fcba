import gzip
import os
from urllib.parse import urlparse

import boto3
from botocore.exceptions import ClientError
from functools import cached_property

import pendulum
from airflow.providers.amazon.aws.hooks.base_aws import AwsBaseHook

from commons.pe.sds_date_utils import SDSDateUtils


class SDSEmrServerlessHook(AwsBaseHook):

    def __init__(self,*args,**kwargs)->None:
        kwargs["client_type"] = "emr-serverless"
        super().__init__(*args, **kwargs)

    @cached_property
    def conn(self):
        """Get the underlying boto3 EmrServerlessAPIService client (cached)"""
        return super().conn

    def list_applications(self):
        response = self.conn.list_applications()
        return response["applications"]

    def start_job_run(
            self,
            application_id:str,
            client_token:str,
            execution_role_arn:str,
            job_driver_params:dict,
            configuration_overrides=None,
            tags:dict=None,
            name:str="default"
    ):
        if configuration_overrides is None:
            configuration_overrides = {}
        if tags is None:
            tags={}
        response = self.conn.start_job_run(
            clientToken=client_token,
            applicationId=application_id,
            executionRoleArn=execution_role_arn,
            jobDriver=job_driver_params,
            configurationOverrides=configuration_overrides,
            name=name,
            tags=tags
        )
        return response

    def get_job_response(self,application_id:str,job_run_id:str):
        return self.conn.get_job_run(
            applicationId=application_id,
            jobRunId=job_run_id
        )

    def list_tags_for_resource(self,resource_arn:str):
        return self.conn.list_tags_for_resource(resourceArn=resource_arn)

    def list_job_runs(self,application_id:str,created_after:pendulum.DateTime,**kwargs):
        return self.conn.list_job_runs(
            applicationId=application_id,
            createdAtAfter=SDSDateUtils.pendulum_to_datetime(created_after),
            **kwargs
        )

    def untag_resource(self, resource_arn:str, tag_keys=None):
        if tag_keys is None:
            tag_keys = []
        self.conn.untag_resource(resourceArn=resource_arn,tagKeys=tag_keys)

    def get_job_logs(self, application_id: str, job_id:str, log_path: str):
        try:
            driver_log_path = os.path.join(log_path,
                                           f"applications/{application_id}/jobs/{job_id}/SPARK_DRIVER/stderr.gz")
            s3 = boto3.resource("s3")
            bucket = urlparse(driver_log_path, allow_fragments=False).netloc
            path = urlparse(driver_log_path, allow_fragments=False).path.strip("/")
            file_obj = s3.Object(bucket, path)
            logs = []
            with gzip.GzipFile(fileobj=file_obj.get()["Body"]) as gzipfile:
                for line in gzipfile:
                    logs.append(line.decode().strip())
            return "\n".join(logs)
        except ClientError as ex:
            if ex.response['Error']['Code'] == 'NoSuchKey':
                job_response = self.get_job_response(application_id=application_id,
                                                          job_run_id=job_id)
                self.log.error(job_response.get("jobRun").get("stateDetails"))
            else:
                raise

