from __future__ import annotations
from airflow.exceptions import AirflowException
from airflow.models import Variable
from airflow.providers.cncf.kubernetes.operators.spark_kubernetes import KubernetesPodOperator
from airflow.utils.context import Context
from kubernetes.client import models as k8s

from commons.pe.constants.airflow_constants import AirflowConstants


class SDSPerformanceTestKubernetesOperator(KubernetesPodOperator):
    template_fields = KubernetesPodOperator.template_fields + (
        "init_container_image", "annotations", "auth_type", "input_path", "output_path",
        "args")
    mount_path = "/input/queries/"

    def __init__(self,
                 init_container_image: str,
                 input_path: str,
                 output_path: str,
                 args: str,
                 auth_type: str = None,
                 **kwargs
                 ):

        self.upload_command = None
        self.init_container_image = init_container_image
        self.auth_type = auth_type
        self.input_path = input_path
        self.input_path_within_pod = self.mount_path
        self.output_path_within_pod = "/opt/sds-pe-deployment-utils/output"
        self.output_path = output_path
        self.args = args
        kwargs["image_pull_secrets"] = [k8s.V1LocalObjectReference("docker-secret")]
        super().__init__(**kwargs)

    def execute(self, context: Context):
        volume_mount = k8s.V1VolumeMount(
            name='shared-volume', mount_path=self.mount_path, sub_path=None, read_only=False
        )

        volume = k8s.V1Volume(
            name='shared-volume',
            empty_dir=k8s.V1EmptyDirVolumeSource()
        )

        self.volumes = [volume]
        self.volume_mounts = [volume_mount]

        if self.input_path.startswith("s3"):
            init_args = f"aws s3 cp {self.input_path} {self.input_path_within_pod} --recursive"
            init_environments = []
        elif self.input_path.startswith("https"):
            if self.auth_type == "sas_token":
                init_args = f"azcopy cp '{self.input_path}?'\"$AZURE_BLOB_SAS_TOKEN\" '{self.input_path_within_pod}'"

                init_environments = [k8s.V1EnvVar(name="AZURE_BLOB_SAS_TOKEN", value_from=k8s.V1EnvVarSource(
                    secret_key_ref=k8s.V1SecretKeySelector(key="azure-blob-sas-key", name="azure-secrets")))]
            else:
                init_args = f"azcopy login --identity --identity-client-id {Variable.get(key=AirflowConstants.AZURE_CLIENT_ID)}; azcopy copy '{self.input_path}' '{self.input_path_within_pod}'"
                init_environments = []

        else:
            raise AirflowException(
                f"Invalid path {self.input_path}. Path must either be s3 path or https(for azure abfs) path")

        init_container = k8s.V1Container(
            name="init-container",
            image=self.init_container_image,
            env=init_environments,
            volume_mounts=self.volume_mounts,
            command=["/bin/bash", "-c"],
            args=[init_args],
        )
        if self.output_path.startswith("s3"):
            self.upload_command = f"aws s3 cp {self.output_path_within_pod} {self.output_path} --recursive"
        elif self.output_path.startswith("https"):
            self.upload_command = f"{{{{azcopy login --identity --identity-client-id var.value.{AirflowConstants.AZURE_CLIENT_ID}; azcopy copy '{self.output_path_within_pod}' '{self.output_path}' }}}}"
        else:
            self.upload_command = ""

        self.arguments = [self.args + self.upload_command]
        self.init_containers = [init_container]
        super().execute(context=context)
