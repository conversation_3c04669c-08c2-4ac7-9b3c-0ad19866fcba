from typing import Any

from airflow.models import BaseOperator, Variable
from airflow.utils.context import Context
from airflow.utils.session import provide_session
from sqlalchemy.orm import Session

from commons.pe.common_utils import Utils
from plugins.pe.sds_dag_orchestrator_plugin.operators.orchestrator_utils import OrchestrationConstants as OC, \
    PipelineOrchestrator


class PipelineOrchestrationOperator(BaseOperator):

    @provide_session
    def execute(self, context: Context, session: Session = None) -> Any:

        # Read the orchestrator config and metatable entries and create Orchestrator object
        orchestrator_config = Variable.get(key=OC.ORCHESTRATION_VARIABLE, deserialize_json=True)
        orchestration_metatable_entry_list = PipelineOrchestrator.get_metatable_entry_list()
        orchestrator = PipelineOrchestrator(
            orchestrator_config=orchestrator_config,
            metatable_entries=orchestration_metatable_entry_list
        )

        for pipeline_id, pipeline_runs in orchestrator.get_sequence_pipelines().items():  # Orchestrate each sequential pipeline
            head_dag_id = orchestrator_config.get(pipeline_id).get(OC.HEAD_DAG_ID)
            active_backfills = orchestrator.active_backfill_runs(pipeline_id)

            if active_backfills:  # Backfill is in progress. Wait for it to complete
                self.log.info(
                    f"Pipeline '{pipeline_id}' with Head DAG {head_dag_id} has backfill dagruns running for dates - {','.join([orchestrator.timestamp_to_readable_string(dagrun.execution_date) for dagrun in active_backfills])}")
                self.log.info("Going to wait until backfill runs are complete before beginning sequential runs")

            else:  # No backfill runs in progress
                self.log.info(f"All backfill runs for pipeline {pipeline_id} is completed. Going to start sequence run")
                sequence_run_action_items = []
                for pipeline_run in pipeline_runs:
                    is_complete, message = orchestrator.check_sequence_run_trigger_condition(
                        pipeline_id=pipeline_id,
                        head_dag_id=head_dag_id,
                        logical_date=pipeline_run.get(OC.LOGICAL_DATE)
                    )
                    if is_complete:  # Ready to start pipeline run
                        message = orchestrator.orchestrate_pipeline(
                            pipeline_id=pipeline_id,
                            head_dag_id=head_dag_id,
                            logical_date=pipeline_run.get(OC.LOGICAL_DATE),
                            context=context
                        )

                    sequence_run_action_items.append(
                        [pipeline_id, pipeline_run.get(OC.LOGICAL_DATE).strftime("%Y-%m-%d %H:%M:%S"),
                         message])

                self.log.info(
                    Utils.tabular_print(
                        data=sequence_run_action_items,
                        field_names=["Pipeline ID", "Logical Date", "Message"],
                    )
                )

        self.log.info("Successfully completed all sequence pipelines. Going to start the non sequence runs")
        non_sequence_action_items = []
        for entry in orchestrator.get_non_sequence_runs():
            message = orchestrator.orchestrate_pipeline(
                pipeline_id=entry.get(OC.PIPELINE_ID),
                head_dag_id=entry.get(OC.HEAD_DAG_ID),
                logical_date=entry.get(OC.LOGICAL_DATE),
                context=context
            )
            non_sequence_action_items.append([entry.get(OC.PIPELINE_ID),
                                              entry.get(OC.LOGICAL_DATE).strftime(
                                                  "%Y-%m-%d %H:%M:%S"),
                                              message])
        self.log.info(
            Utils.tabular_print(
                data=non_sequence_action_items,
                field_names=["Pipeline ID", "Logical Date", "Message"],
            )
        )
