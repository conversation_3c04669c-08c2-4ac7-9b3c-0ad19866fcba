from typing import Any, List
from collections import Counter
from itertools import chain

from airflow.models.dag import DAG
from airflow.models import DagBag
from airflow.models.baseoperator import BaseOperator
from airflow.models.variable import Variable
from airflow.exceptions import AirflowFailException

from plugins.pe.sds_dag_orchestrator_plugin.operators.orchestrator_utils import OrchestrationConstants


class SDSConfigurationValidatorOperator(BaseOperator):
    """
    * Check for cyclical dependencies
    * Check if dependencies are a list
    * Check if dependent dags are string
    * Check tail dag is not in dependency(end)
    * Check head dag is not dependant
    """

    def __init__(self, **kwargs):
        super(SDSConfigurationValidatorOperator, self).__init__(**kwargs)

    def execute(self, context: Any):
        orchestration_config = Variable.get(key=OrchestrationConstants.ORCHESTRATION_VARIABLE, default_var=[],
                                            deserialize_json=True)

        if not orchestration_config:
            raise AirflowFailException("Configuration file not found in Airflow Variables")
        self.log.info("Validating orchestration configuration variable")
        for pipeline in orchestration_config:

            pipeline_config = orchestration_config.get(pipeline)
            dependency_config = pipeline_config.get(OrchestrationConstants.DEPENDENCIES_KEY, None)
            head_dag = pipeline_config.get(OrchestrationConstants.HEAD_DAG_ID, None)
            tail_dag = pipeline_config.get(OrchestrationConstants.TAIL_DAG_ID, None)
            dependant_dag_ids = [config.get(OrchestrationConstants.DEPENDENT_KEY, None) for config in
                                 dependency_config]
            dependency_dag_ids = [config.get(OrchestrationConstants.DEPENDENCIES_KEY, None) for config in
                                  dependency_config]

            if head_dag is None:
                raise AirflowFailException(
                    f"Key {OrchestrationConstants.HEAD_DAG_ID} not found in config variable for pipeline {pipeline}")

            if not tail_dag:
                raise AirflowFailException(
                    f"Tail DAG not found in config variable for pipeline {pipeline}")

            if None in dependant_dag_ids:
                missing_key_index = dependant_dag_ids.index(None) + 1
                raise AirflowFailException(
                    f"Key {OrchestrationConstants.DEPENDENT_KEY} not found in dependency configuration index {str(missing_key_index)} for pipeline {pipeline}")

            if None in dependency_dag_ids:
                missing_key_index = dependency_dag_ids.index(None) + 1
                raise AirflowFailException(
                    f"Key {OrchestrationConstants.DEPENDENCIES_KEY} not found in dependency configuration index {str(missing_key_index)} for pipeline {pipeline}")

            # Check if dependencies are specified as a list
            if not all(isinstance(x, List) for x in dependency_dag_ids):
                raise AirflowFailException("Dependencies dag must be specified as a List. Please check the "
                                           "dependencies specified in orchestration config variable")

            # Check if dependent dag ids are specified as a string
            if not all(isinstance(x, str) for x in dependant_dag_ids):
                raise AirflowFailException("Dependent dags must be specified as a string(dag_id). Please check the "
                                           "dependent dags specified in orchestration config variable")

            # Check if head DAG is not a dependent DAG
            if head_dag in dependant_dag_ids:
                raise AirflowFailException("Head DAG must not be a dependent DAG. Please check Head DAG "
                                           "specified under pipeline '{}'".format(pipeline))

            # Check if tail dag is present under dependencies
            if tail_dag in chain(*dependency_dag_ids):
                raise AirflowFailException("Tail DAG must not be a dependency. Please check Tail DAG specified under "
                                           "pipeline '{}'".format(pipeline))

            if len(dependency_dag_ids) == len(dependency_dag_ids):
                dependency_dict = {dependant_dag_ids[i]: dependency_dag_ids[i] for i in range(len(dependency_dag_ids))}
                tmp = [[tuple(sorted((key, v))) for v in value] for key, value in dependency_dict.items()]
                dependency_list = list(chain(*tmp))
                counts = Counter(dependency_list)
                if not all(count == 1 for count in counts.values()):
                    raise AirflowFailException("Cyclical dependency found for pipeline '{}'".format(pipeline))

            dependency_dag_ids = [dag_id for dag_id_list in dependency_dag_ids for dag_id in dag_id_list]
            all_dependencies = list(set(dependant_dag_ids + dependency_dag_ids))
            dagbag = DagBag()
            for dependency in all_dependencies:
                if ":" in dependency:
                    task_id = dependency.split(":")[1]
                    dag_id = dependency.split(":")[0]
                    if dag_id not in dagbag.dag_ids:
                        raise AirflowFailException(f"Dag id {dag_id} does not exist. Invalid dependency {dependency}")
                    else:
                        dag: DAG = dagbag.get_dag(dag_id)
                        if task_id not in dag.task_ids:
                            raise AirflowFailException(f"Task id {task_id} not present in dag {dag_id}. Invalid dependency {dependency}")
                else:
                    if dependency not in dagbag.dag_ids:
                        raise AirflowFailException(f"Dag id {dependency} does not exist. Invalid dependency {dependency}")

