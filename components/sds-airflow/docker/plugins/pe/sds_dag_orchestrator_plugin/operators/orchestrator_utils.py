import datetime
import itertools
from collections import defaultdict
from typing import List, Dict

import pendulum
from airflow.exceptions import DagRunAlreadyExists
from airflow.models import DagRun
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.context import Context
from airflow.utils.session import provide_session
from airflow.utils.state import Dag<PERSON>unState, TaskInstanceState
from sqlalchemy.orm import Session

from commons.pe.logger_base import SDSLoggerBase
from commons.pe.sds_dag_orchestrator import SDSOrchestrationMeta


class OrchestrationConstants:
    ORCHESTRATION_VARIABLE = "SDS_ORCHESTRATOR_CONFIG"
    DEPENDENCIES_KEY = "dependencies"
    DEPENDENT_KEY = "dependent_dag"
    HEAD_DAG_ID = "head_dag_id"
    TAIL_DAG_ID = "tail_dag_id"
    PIPELINE_ID = "pipeline_id"
    LOGICAL_DATE = "logical_date"
    SEQUENCE_RUN = "sequential_pipeline_run"
    DAG_RUN_TYPE = "dag_run_type"


class PipelineOrchestrator(SDSLoggerBase):
    SUCCESS_STATES = [DagRunState.SUCCESS, TaskInstanceState.SUCCESS]
    FAILURE_STATES = [DagRunState.FAILED, TaskInstanceState.FAILED]

    def __init__(self,
                 orchestrator_config: dict,
                 metatable_entries: List[Dict[str, str | datetime.datetime]]
                 ):

        self.orchestrator_config = orchestrator_config
        self.metatable_entries = metatable_entries

    @classmethod
    def get_dag_state(cls, dag_id, logical_date):
        """Get the state of a certain DAG, 'dag_id', run on date, 'execution_date'
        Args:
            dag_id (string): DAG id
            logical_date (datetime.datetime): Execution date on which the dag 'dag_id' is run
        Returns:
            string: State of the dag run on date, 'execution_date'
        """
        dag_run = DagRun.find(dag_id=dag_id, execution_date=logical_date)
        if dag_run:
            return dag_run[0].get_state()
        else:
            return None

    @classmethod
    def get_task_state(cls, task_id, dag_id, logical_date):
        """Get the state of a certain task, 'task_id', run on date, 'execution_date'
        Args:
            task_id (string): Task ID of the task
            dag_id (string): DAG id of the task
            logical_date (datetime.datetime): Execution date on which the task 'task_id' is run
        Returns:
            string: State of the task, 'task_id' of dag, 'dag_id', run on, 'execution_date'
        """
        dag_run = DagRun.find(dag_id=dag_id, execution_date=logical_date)
        if dag_run:
            ti = dag_run[0].get_task_instance(task_id=task_id)
            return ti.current_state()
        else:
            return None

    @staticmethod
    @provide_session
    def get_metatable_entry_list(session: Session = None):
        orchestration_metatable_entry_list = [
            {OrchestrationConstants.PIPELINE_ID: item.pipeline_id,
             OrchestrationConstants.HEAD_DAG_ID: item.head_dag_id,
             OrchestrationConstants.LOGICAL_DATE: item.logical_date,
             OrchestrationConstants.DAG_RUN_TYPE: item.dag_run_type} for
            item in SDSOrchestrationMeta.find_all(session)
        ]
        return orchestration_metatable_entry_list

    def get_sequence_pipelines(self):
        """
        Returns: A dictionary where key is the pipeline ID for a sequential execution pipeline (pipelines where
        'sequential_pipeline_run' is True) and value will be the metatable entries in dictionary format where each
        entry is a backfill run for the sequential pipeline

      """
        sequence_run_pipeline_ids = [pipeline_id for pipeline_id, pipeline_config in self.orchestrator_config.items() if
                                     pipeline_config.get(OrchestrationConstants.SEQUENCE_RUN, False)]
        sequence_runs_in_metatable = [entry for entry in self.metatable_entries if
                                      entry.get(OrchestrationConstants.PIPELINE_ID) in sequence_run_pipeline_ids and
                                      entry.get(OrchestrationConstants.DAG_RUN_TYPE) == "backfill"]
        grouped_sequence_runs = itertools.groupby(sequence_runs_in_metatable,
                                                  key=lambda x: x.get(OrchestrationConstants.PIPELINE_ID))
        return {pipeline_id: list(entries) for pipeline_id, entries in grouped_sequence_runs}

    def get_non_sequence_runs(self) -> list:
        non_sequence_pipeline_ids = [pipeline_id for pipeline_id, pipeline_config in self.orchestrator_config.items() if
                                     not pipeline_config.get(OrchestrationConstants.SEQUENCE_RUN, False)]
        sequence_run_pipeline_ids = [pipeline_id for pipeline_id, pipeline_config in self.orchestrator_config.items() if
                                     pipeline_config.get(OrchestrationConstants.SEQUENCE_RUN, False)]

        non_sequence_runs = [entry for entry in self.metatable_entries if entry.get(
            OrchestrationConstants.PIPELINE_ID) in non_sequence_pipeline_ids]  # List of all non sequence runs in metatable

        sequence_runs_non_backfill_items = [entry for entry in self.metatable_entries if entry.get(
            OrchestrationConstants.PIPELINE_ID) in sequence_run_pipeline_ids and entry.get(
            OrchestrationConstants.DAG_RUN_TYPE) != "backfill"]  # List of all non backfill runs in sequence run pipelines

        return non_sequence_runs + sequence_runs_non_backfill_items

    def active_backfill_runs(self, pipeline_id: str):
        """

        Args:
            pipeline_id (str): Name of the pipeline

        Returns: List of backfill dag runs that are currently active

        """
        dag_runs = DagRun.find(dag_id=self.orchestrator_config.get(
            pipeline_id).get(OrchestrationConstants.HEAD_DAG_ID))
        return [dag_run for dag_run in dag_runs if
                dag_run.run_type == "backfill" and dag_run.state not in [DagRunState.SUCCESS, DagRunState.FAILED]]

    @staticmethod
    def timestamp_to_readable_string(timestamp: datetime.datetime) -> str:
        return timestamp.strftime("%Y-%m-%d %H:%M:%S")

    def check_sequence_run_trigger_condition(self, pipeline_id: str, head_dag_id: str,
                                             logical_date: datetime.datetime) -> tuple[bool, str]:
        head_dag_run = DagRun.find(
            dag_id=head_dag_id, execution_date=logical_date)
        if not head_dag_run:  # Head dag entry in metatable but no run in airflow. This could be an old entry that
            # didn't get deleted
            self.log.warning(
                f"Head DAG run of {head_dag_id} for data interval start {self.timestamp_to_readable_string(logical_date)} is not found. This could be a stale metatable entry")
            self.log.info("Going to clear entry from orchestration metatable")
            SDSOrchestrationMeta.delete(
                head_dag_id=head_dag_id, logical_date=logical_date)
            return False, "Stale entry, deleted from metatable"

        else:
            head_dag_previous_run = head_dag_run[0].get_previous_dagrun(head_dag_run[0])
            if head_dag_previous_run:  # Previous head dag run exists
                # Previous dag run is backfilled. Check for completion of previous pipeline
                if head_dag_previous_run.run_type == "backfill":
                    incomplete_dict = self.get_pipeline_dag_runs(pipeline_id=pipeline_id,
                                                                 logical_date=head_dag_previous_run.execution_date,
                                                                 include_success_runs=False)

                    if incomplete_dict:
                        message = "Previous backfill run is incomplete with dag runs \n" + self.message_string_from_state_dict(
                            state_dict=incomplete_dict)
                        return False, message
                    else:
                        return True, "Previous backfill run completed"
                else:
                    return True, "Previous dag run is not a backfill run"
            else:
                return True, "No previous dag run"

    def get_pipeline_dag_runs(self, pipeline_id, logical_date, include_success_runs: bool = False):
        pipeline_config = self.orchestrator_config.get(pipeline_id, {})
        head_dag_id = pipeline_config.get(OrchestrationConstants.HEAD_DAG_ID)
        tail_dag_id = pipeline_config.get(OrchestrationConstants.TAIL_DAG_ID)
        dependent_dag_ids = list(
            set([dependency_config.get(OrchestrationConstants.DEPENDENT_KEY) for dependency_config in
                 pipeline_config.get(OrchestrationConstants.DEPENDENCIES_KEY, [])]))
        dependency_dag_ids = list(set([dependency_dag_id for dependency_config in
                                       pipeline_config.get(OrchestrationConstants.DEPENDENCIES_KEY, []) for
                                       dependency_dag_id in
                                       dependency_config.get(OrchestrationConstants.DEPENDENCIES_KEY)]))

        head_dag_state = self.get_dag_state(
            dag_id=head_dag_id, logical_date=logical_date)
        if head_dag_state != DagRunState.SUCCESS:  # No dependencies are met, return state of all dags
            return {**{dag_id: None for dag_id in list(set([tail_dag_id] + dependency_dag_ids + dependent_dag_ids))},
                    **{head_dag_id: head_dag_state}}

        else:
            dag_state_dict = self.get_dependency_state_dict(dependencies=dependency_dag_ids + dependent_dag_ids,
                                                            logical_date=logical_date)
            if include_success_runs:
                return dag_state_dict
            else:  # Filter by dependencies not success
                return {dag_id: state for dag_id, state in dag_state_dict.items() if state not in self.SUCCESS_STATES}

    @classmethod
    def get_dependency_state_dict(cls, dependencies: list, logical_date: datetime.datetime) -> Dict[str, str]:
        dependencies = list(set(dependencies))
        dependency_state_dict = {}
        for dependency in dependencies:
            if ":" in dependency:  # It is a task dependency
                dag_id, task_id = dependency.split(":")
                dependency_state_dict[dependency] = cls.get_task_state(task_id=task_id, dag_id=dag_id,
                                                                       logical_date=logical_date)
            else:  # It is a dag dependency
                dependency_state_dict[dependency] = cls.get_dag_state(
                    dag_id=dependency, logical_date=logical_date)

        return dependency_state_dict

    def orchestrate_pipeline(self, pipeline_id: str, head_dag_id: str, logical_date: datetime.datetime,
                             context: Context):
        pipeline_config = self.orchestrator_config.get(pipeline_id, {})
        pipeline_dag_states = self.get_pipeline_dag_runs(pipeline_id=pipeline_id, logical_date=logical_date,
                                                         include_success_runs=True)  # Get all dag run states for pipeline

        # Head DAG is not complete
        if pipeline_dag_states.get(head_dag_id) != DagRunState.SUCCESS:
            return "Head DAG is not complete \n" + self.message_string_from_state_dict(state_dict=pipeline_dag_states)

        if pipeline_dag_states.get(pipeline_config.get(
                OrchestrationConstants.TAIL_DAG_ID)) in self.SUCCESS_STATES:  # Pipeline complete since tail dag is success, delete metatable entry
            SDSOrchestrationMeta.delete(
                head_dag_id=head_dag_id, logical_date=logical_date)
            return "Pipeline complete. Deleting metatable entry"

        self.log.info(
            f"Going for dependency checks for pipeline {pipeline_id} of logical date {self.timestamp_to_readable_string(logical_date)}")
        for dependency_config in pipeline_config.get(OrchestrationConstants.DEPENDENCIES_KEY):
            # Iterate through each dependency and check for incomplete dependencies
            dependant_dag_id = dependency_config.get(
                OrchestrationConstants.DEPENDENT_KEY)
            dependency_dag_ids = dependency_config.get(
                OrchestrationConstants.DEPENDENCIES_KEY)
            dependency_dag_states = {dependency: state for dependency, state in pipeline_dag_states.items() if
                                     dependency in dependency_dag_ids}
            if not pipeline_dag_states.get(dependant_dag_id) and set(
                    dependency_dag_states.values()).issubset(set(self.SUCCESS_STATES)):  # Dependencies are met, trigger DAG
                data_interval_end_dt = DagRun.find(dag_id=head_dag_id, execution_date=logical_date)[
                    0].data_interval_end  # Get data interval end to pass to dag run conf of pipeline dag.
                # This can be accesses using template "{{ dagrun.conf.get('data_interval_end')}}"
                from sqlalchemy.exc import IntegrityError
                try:
                    trigger_dag_op = TriggerDagRunOperator(
                        task_id="trigger_dag",
                        trigger_dag_id=dependant_dag_id,
                        execution_date=logical_date,
                        conf={"data_interval_end": pendulum.instance(
                            data_interval_end_dt).to_iso8601_string()}
                    )
                    trigger_dag_op.execute(context=context)
                    pipeline_dag_states[dependant_dag_id] = "Triggered"
                except (DagRunAlreadyExists, IntegrityError):
                    pipeline_dag_states[dependant_dag_id] = self.get_dag_state(dag_id=dependant_dag_id,
                                                                               logical_date=logical_date)
        return self.message_string_from_state_dict(state_dict=pipeline_dag_states)

    def message_string_from_state_dict(self, state_dict: dict) -> str:
        ordered_dict = self.order_state_dict(state_dict=state_dict)
        return "\n".join([f"{status} --> [{','.join(dependencies)}]" for status, dependencies in ordered_dict.items()])

    def order_state_dict(self, state_dict: dict) -> Dict[str, list]:
        ordered_list = defaultdict(list)
        for dependency, state in sorted(state_dict.items()):
            if not state:
                ordered_list["Pending trigger"].append(dependency)
            elif state not in self.SUCCESS_STATES + self.FAILURE_STATES + ["Triggered"]:
                ordered_list["In progress"].append(
                    f"{dependency} is in state {state}")
            else:
                ordered_list[state].append(dependency)
        return dict(ordered_list)
