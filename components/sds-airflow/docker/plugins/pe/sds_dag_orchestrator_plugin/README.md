## SDS DAG Orchestrator Plugin

This contains the required plugins for implementation of the mediator pattern

### Operators

* [SDSConfigurationValidatorOperator](operators/sds_configuration_validation_operator.py) - This operator is used to validate whether the dependency configuration stored as an Airflow Variable is specified correctly or not. It overrides the BaseOperator of Airflow. </br>
    The checks involve - 
  * Checking if the configuration exists as an Airflow Variable
  * Checking for cyclical dependencies.
  * Checking if dependency dags for a dependent dag are specified as a List of Strings, where each string must be a dependency dag
  * Checking if dependent dags are provided as String.
  * Checking if tail dag is not mentioned as a dependency dag. This is because the tail dag is the end of a pipeline and no dags in the pipeline should be dependent on it.
  * Checking if head dag is not a dependant dag. This is because head dag is at the start of a pipeline, and it must not be dependent on any DAG in the pipeline. </br> </br>

* [DependencyCheckerOperator](operators/sds_dag_orchestrator_operator.py) - This operator is used to orchestrate the pipeline and perform the mediator action. The working of this operator is as follows - 
  * Checks for entries in the [Orchestration metatable](../../../commons/sds_dag_orchestrator.py) for any head dag entries
  * For each entry it checks the dependencies for each dependent DAG in the pipeline
  * If all dependencies are met, it triggers the DAG, otherwise it skips triggering for the current execution
  * If all DAGS are triggered and all dependencies are met, it clears the entry from the metatable. </br> </br>

### User Guide

This section involves the steps needed to incorporate the mediator pattern to orchestrate SDS data pipelines. For more information on the mediator pattern refer the doc [here](https://prevalentai.atlassian.net/l/c/qRk2Y1ae).

#### DAG Development
While writing DAGs to create pipelines using the mediator pattern,
* The first DAG of every pipeline must be an instance of **SDSHeadDAG**. It contains an argument called ```pipeline_id``` which specifies which pipeline the DAG should be a part of. Head DAG object can be imported using <br>
```from commons.pe.sds_dag_orchestrator import SDSHeadDAG```.<br> This is the only DAG in the pipeline that contains a schedule interval
* All DAGs other than the Head DAG must be an instance of **SDSPipelineDAG**. It also contains the ```pipeline_id``` argument which specifies which pipeline the DAG should be a part of. It can be imported using <br>
```from commons.pe.sds_dag_orchestrator import SDSPipelineDAG``` <br> Pipeline DAGs have no schedule interval

#### Cross DAG Dependency

For specifying dependencies between dags and tasks, an Airflow Variable with key ```SDS_ORCHESTRATOR_CONFIG``` contains a JSON object as value which holds the dependency relations between DAGs and tasks in a pipeline.<br> <br> An example of the orchestration variable is - 
```{
    "SAMPLE_PIPELINE_NAME": {
        "head_dag_id": "pipeline_head_dag",
        "tail_dag_id": "pipeline_tail_dag",
        "dependencies": [
          {
            "dependent_dag": "pipeline_dag_1",
            "dependencies": [
              "pipeline_head_dag"
            ]
          },
          {
            "dependent_dag": "pipeline_dag_2",
            "dependencies": [
              "pipeline_head_dag"
            ]
          },
          {
            "dependent_dag": "pipeline_tail_dag",
            "dependencies": [
              "pipeline_dag_1",
              "pipeline_dag_2:example_task"
            ]
          }
        ]
      }
    }
```

Each key is a pipeline_id name and value is a JSON object containing dependencies. The contents of each pipeline dependency object must contain - 
* ```head_dag_id``` - The DAG id of the first DAG (SDSHeadDAG) of the pipeline
* ```tail_dag_id``` - The DAG id of the last DAG of the pipeline
* ```dependencies``` - Array containing JSON objects that determine a specific dependency of a pipeline. Each JSON object contains two keys - 
  * ```dependent_dag``` - DAG id of a DAG in a pipeline which has dependencies. This DAG will only be triggered only after the dependencies have been met.
  * ```dependencies``` - Array containing the dependencies of the corresponding ```dependent_dag```. Elements of this array can be either a DAG - ```dag_id``` or a task, ```dag_id:task_id``` where ```dag_id``` is the dag in which the task ```task_id``` is present. Once all the dependencies are successfully run, then the ```dependent_dag``` will run.

On specifying the dependencies of a pipeline, after each scheduled trigger of the head dag of the pipeline, the mediator will automatically periodically check for the necesssary dependencies and trigger the DAGs as and when the depdencies are met.
