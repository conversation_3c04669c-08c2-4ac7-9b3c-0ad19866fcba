from __future__ import annotations

import os

from airflow.exceptions import AirflowException
from airflow.models.variable import Variable
from airflow.providers.cncf.kubernetes.backcompat.backwards_compat_converters import convert_env_vars
from airflow.providers.cncf.kubernetes.operators.spark_kubernetes import KubernetesPodOperator
from airflow.utils.context import Context
from kubernetes.client import models as k8s

from commons.pe.constants.airflow_constants import AirflowConstants
from plugins.pe.kubernetes.constants import SECRET_PROVIDER_CLASS_CSI_DRIVER


class JavaKubernetesOperator(KubernetesPodOperator):
    template_fields = KubernetesPodOperator.template_fields + (
        "jar", "class_name", "init_container_image", "job_args", "env_from", "annotations", "environment_variables", "container_resources_dict", )

    def __init__(self,
                 jar: str,
                 class_name: str,
                 init_container_image: str,
                 job_args: list = None,
                 environment_variables: dict = None,
                 auth_type:str= None,
                 container_resources_dict: dict = None,
                 **kwargs
                 ):
        self.jar = jar
        self.class_name = class_name
        self.job_args = [] if not job_args else job_args
        self.init_container_image = init_container_image
        self.environment_variables = environment_variables
        self.auth_type = auth_type

        kwargs["image_pull_secrets"] = [k8s.V1LocalObjectReference("docker-secret")]
        if "image_pull_policy" not in kwargs:
            kwargs["image_pull_policy"] = "IfNotPresent"
        super().__init__(**kwargs)
        self.do_xcom_push = False
        self.container_resources_dict = container_resources_dict

        cluster_context = os.getenv("JAVA_KUBERNETES_POD_CLUSTER_CONTEXT", None)
        if not cluster_context:
            self.in_cluster = True
        else:
            self.cluster_context = cluster_context

        self.pod_template_file = os.path.join(os.getenv("AIRFLOW_HOME", "/opt/airflow"),
                                              "commons/pe/k8s_pod_template_file.yaml")

        self.log_events_on_failure = True
        self.shared_path = "/exec/jar_files"

    def execute(self, context: Context):
        self.container_resources = k8s.V1ResourceRequirements(**self.container_resources_dict)
        volume_mount = k8s.V1VolumeMount(
            name='shared-volume', mount_path=self.shared_path, sub_path=None, read_only=False
        )

        volume = k8s.V1Volume(
            name='shared-volume',
            empty_dir={}
        )

        self.volumes = [volume]
        self.volume_mounts = [volume_mount]
        self.env_vars = convert_env_vars(self.environment_variables) if self.environment_variables else []
        self.env_vars.extend([
            k8s.V1EnvVar(name="DB_PASSWORD", value_from=k8s.V1EnvVarSource(
                secret_key_ref=k8s.V1SecretKeySelector(name=f"external-secret-vault-{self.namespace}",
                                                       key="managementDbPassword"))),
            k8s.V1EnvVar(name="CLIENT_ID", value_from=k8s.V1EnvVarSource(
                secret_key_ref=k8s.V1SecretKeySelector(name=f"external-secret-vault-{self.namespace}",
                                                       key="clientId"))),
            k8s.V1EnvVar(name="CLIENT_SECRET", value_from=k8s.V1EnvVarSource(
                secret_key_ref=k8s.V1SecretKeySelector(name=f"external-secret-vault-{self.namespace}",
                                                       key="clientSecret")))
        ])
        self.env_from = [k8s.V1EnvFromSource(secret_ref=k8s.V1SecretEnvSource(name=f"external-secret-vault-{self.namespace}"))]
        jar_path = os.path.join(self.shared_path, 'executable.jar')
        if self.jar.startswith("s3"):
            init_args = f"aws s3 cp {self.jar} {jar_path}"
            init_environments = []
        elif self.jar.startswith("https"):
            if self.auth_type == "sas_token":
                init_args = f"azcopy cp '{self.jar}?'\"$AZURE_BLOB_SAS_TOKEN\" '{jar_path}'"

                init_environments = [k8s.V1EnvVar(name="AZURE_BLOB_SAS_TOKEN", value_from=k8s.V1EnvVarSource(
                    secret_key_ref=k8s.V1SecretKeySelector(key="azure-blob-sas-key", name="azure-secrets")))]
            else:
                init_args = f"azcopy login --identity --identity-client-id {Variable.get(key=AirflowConstants.AZURE_CLIENT_ID)}; azcopy copy '{self.jar}' '{jar_path}'"
                init_environments = []

        else:
            raise AirflowException(
                f"Invalid path {self.jar}. Path must either be s3 path or https(for azure abfs) path")

        init_container = k8s.V1Container(
            name="init-container",
            image=self.init_container_image,
            env=init_environments,
            volume_mounts=self.volume_mounts,
            command=["/bin/bash", "-c"],
            args=[init_args],
        )

        self.init_containers = [init_container]
        self.cmds = ["java", "-cp", f"{os.path.join(self.shared_path, 'executable.jar')}:/opt/spark/jars/*",
                     self.class_name] + self.job_args
        super().execute(context=context)
