from typing import Optional

from airflow.plugins_manager import AirflowPlugin
from airflow.timetables.base import DagRunInfo, DataInterval, TimeRestriction, Timetable
from pendulum import DateTime, timezone



class ManualOnlyTimetable(Timetable):
    """Timetable that sets dags to be only manually triggered
    This ensures DAG will not be triggered by scheduler and only runs when manually triggered. 
    Data interval start will be the same time of trigger in the timezone specified in the configuration or at DAG level
    """

    def infer_manual_data_interval(self, run_after: DateTime) -> DataInterval:
        data_interval_start = run_after.replace(tzinfo=timezone("utc"))
        return DataInterval(start=data_interval_start, end=data_interval_start)

    def next_dagrun_info(
        self,
        *,
        last_automated_data_interval: Optional[DataInterval],
        restriction: TimeRestriction,
    ) -> DagRunInfo | None:
        return None


class ManualOnlyPlugin(AirflowPlugin):
    """Plugin to regsiter timetable"""

    name = "manual_only_timetable_plugin"
    timetables = [ManualOnlyTimetable]
