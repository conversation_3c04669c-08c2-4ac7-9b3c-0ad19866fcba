# Custom Airflow Plugins

This repo consists of custom airflow plugins that can be used as operators in tasks. To define a custom plugin create a subfolder inside the ```plugins``` folder and define your operator there

# Operator Factory User Guide

This guide explains how to create new operators using the factory pattern implemented in the SDS (Spark Data Services) framework. The framework provides a hierarchical structure for creating both synchronous and asynchronous operators with built-in support for Airflow integration.




## Step 1: Creating Your Concrete Operator

### Basic Structure

Create a new operator class that inherits from `SparkOperatorBase`. Following the pattern used in the Kubernetes implementation:

```python
class YourPlatformSparkOperator(YourMixin, SparkOperatorBase):
    # Define the trigger class for async execution
    trigger_class = YourPlatformTrigger
    
    # Merge global attributes with platform-specific ones
    global_attributes = SparkOperatorBase.global_attributes | {
        "runtime_config": "YOUR_PLATFORM_DEFAULT_CONFIGS"
    }
    
    # Extend template fields with platform-specific fields
    template_fields = SparkOperatorBase.template_fields + YourMixin.template_fields
```

### Constructor <PERSON>

Based on the Kubernetes reference, initialize platform-specific settings:

```python
def __init__(self, **kwargs):
    # Set default retry configuration from environment variables
    if "retries" not in kwargs:
        kwargs["retries"] = int(os.getenv("YOUR_PLATFORM_RETRY", "0"))
        kwargs["retry_delay"] = timedelta(seconds=int(os.getenv("YOUR_PLATFORM_RETRY_DELAY", "60")))
    
    super().__init__(**kwargs)
```

## Step 2: Implementing Required Abstract Methods

### 1. `pre_execute(self, context: Any)`

Following the Kubernetes pattern, customize platform-specific preprocessing:

```python
def pre_execute(self, context: Any):
    super().pre_execute(context)
    
    # Extract platform-specific settings from runtime_config
    self.platform_namespace = self.runtime_config.get("metadata", {}).get("namespace", "default")
    
    # Make application name compatible with platform requirements
    platform_compatible_name = self.make_name_platform_compatible(
        name=self.app_name, 
        character_limit=55
    )
    self.app_name = platform_compatible_name
    self.labels.update({"job_name": self.app_name})
```

### 2. `prepare(self, context: Context)`

Create the job submission manifest similar to how Kubernetes creates SparkApplication spec:

```python
def prepare(self, context: Context):
    self.log.info(f"Creating job submission spec for '{self.app_name}'")
    
    # Initialize submission body structure
    self.job_submit_body = {"spec": {}, "metadata": {}}
    
    # Set metadata
    self.job_submit_body["metadata"].update({
        "name": self.app_name, 
        "namespace": self.platform_namespace
    })
    
    # Create platform-specific job specification
    platform_spec = {
        "arguments": self.args,
        "sparkConf": self.conf,
        "mainApplicationFile": self.application_file,
        "mainClass": self.class_name,
        "sparkVersion": self.spark_version
    }
    
    # Add dependencies if they exist
    dependencies = {
        "pyFiles": self.py_files,
        "files": self.files,
        "packages": self.packages,
        "repositories": self.repositories,
        "jars": self.jars
    }
    
    if any(dependencies.values()):
        platform_spec.update({"deps": dependencies})
    
    # Add resource specifications
    platform_spec.update({
        "driver": {
            "cores": self.driver_cores,
            "memory": self.driver_memory,
            "labels": self.labels
        },
        "executor": {
            "cores": self.executor_cores,
            "memory": self.executor_memory,
            "instances": self.executor_instances,
            "labels": self.labels
        }
    })
    
    # Merge with runtime configuration
    self.job_submit_body.update({"spec": platform_spec})
    self.job_submit_body = Utils.merge_dictionaries(self.runtime_config, self.job_submit_body)
```

### 3. `submit(self, context: Context)`

Handle job submission with existence checking, similar to the Kubernetes approach:

```python
def submit(self, context: Context):
    # Check if job already exists
    job_exists = self.check_job_exists()
    
    if job_exists:
        self.log.info(f"Found existing job {self.app_name}")
        if self.force_replace_existing:
            self.log.info("Deleting existing job before resubmission")
            self.delete_existing_job()
        else:
            # Skip submission, go to monitoring
            return
    
    # Submit the job to your platform
    self.log.info(f"Submitting job with manifest: {json.dumps(self.job_submit_body)}")
    
    # Use platform-specific client/hook to submit
    self.platform_hook.create_job(
        job_specification=self.job_submit_body,
        namespace=self.platform_namespace
    )
```

### 4. `on_operator_complete(self, context: Context, event: Optional[Dict[str, Any]])`

Handle completion similar to the Kubernetes cleanup pattern:

```python
def on_operator_complete(self, context: Context, event: Optional[Dict[str, Any]]):
    is_success = event.get("status") == TaskStatus.SUCCESS
    message = event.get("message", "")
    
    # Log appropriate message
    log_method = self.log.info if is_success else self.log.error
    log_method(message)
    
    # Clean up job resources
    self.delete_job_resources()
    
    if is_success:
        self.log.info(f"Job {self.app_name} completed successfully")
    else:
        raise AirflowException(f"Job {self.app_name} failed: {message}")
```

### 5. `_get_trigger_params(self, context: Context, **kwargs)`

Provide parameters for async trigger following the pattern:

```python
def _get_trigger_params(self, context: Context, **kwargs) -> dict[str, Any]:
    return {
        "job_name": self.app_name,
        "namespace": self.platform_namespace,
        "platform_connection_id": self.platform_connection_id
    }
```


## Step 3: Creating Your Trigger

Create a trigger class following the Kubernetes trigger pattern:

```python
class YourPlatformTrigger(SDSBaseTrigger, YourMixin):
    FAILURE_STATES = ("FAILED", "ERROR", "TERMINATED")
    SUCCESS_STATES = ("COMPLETED", "SUCCEEDED")
    
    def __init__(self, job_name: str, **kwargs):
        self.job_name = job_name
        YourMixin.__init__(self, **kwargs)
        SDSBaseTrigger.__init__(self, **kwargs)
    
    def sync_run(self) -> TriggerEvent:
        """Synchronous status checking"""
        self.log.info(f"Checking status of job {self.job_name}")
        
        # Get job manifest/status from platform
        job_manifest = self.platform_hook.get_job_status(
            name=self.job_name,
            namespace=self.namespace
        )
        
        # Extract current state
        job_state = job_manifest.get("status", {}).get("state")
        
        if job_state is None:
            return TriggerEvent(payload={"status": TaskStatus.RUNNING})
        
        if job_state in self.FAILURE_STATES:
            return TriggerEvent(payload={
                "status": TaskStatus.FAILED, 
                "message": self.get_job_logs()
            })
        elif job_state in self.SUCCESS_STATES:
            return TriggerEvent(payload={
                "status": TaskStatus.SUCCESS,
                "message": self.get_job_logs()
            })
        else:
            return TriggerEvent(payload={"status": TaskStatus.RUNNING})
    
    async def run(self) -> AsyncIterator[TriggerEvent]:
        """Asynchronous monitoring loop"""
        while True:
            # Similar logic to sync_run but with async calls
            # and sleep intervals between checks
            await asyncio.sleep(5.0)
```

## Step 4: Factory Registration

### Update SparkOperatorFactory

Add your operator to the factory's operator map:

```python
operator_map = {
    "KUBERNETES": SparkKubernetesOperator,
    "EMR-SERVERLESS": EmrServerlessSparkOperator,
    "YOUR-PLATFORM": YourPlatformSparkOperator,  # Add here
}
```



## Step 5: Usage Examples

### Direct Usage
```python
task = YourPlatformSparkOperator(
    task_id="my_spark_job",
    app_name="my-application",
    application_file="path/to/app.py",
    conf={"spark.sql.adaptive.enabled": "true"},
    deferrable=True
)
```

### Factory Usage
```python
# By platform key
OperatorClass = SparkOperatorFactory.get_operator("YOUR-PLATFORM")
task = OperatorClass(task_id="factory_task", ...)

# By task mapping
task = SparkOperatorFactory.get(task_id="your_platform_task", ...)
```

## Key Implementation Notes

1. **Follow the inheritance pattern** - Always call `super()` methods to maintain the chain
2. **Use mixins for platform-specific functionality** - Like connection management and utility methods
3. **Implement both sync and async modes** - The `sync_run()` and `run()` methods in triggers
4. **Handle job lifecycle properly** - Check existence, submit, monitor, cleanup
5. **Merge configurations appropriately** - Use `Utils.merge_dictionaries()` for combining configs
6. **Make names platform-compatible** - Transform application names to meet platform requirements
7. **Provide meaningful logging** - At all major steps for troubleshooting
8. **Handle resource cleanup** - Always clean up jobs and resources on completion

This pattern ensures consistency across different execution platforms while allowing platform-specific customizations through mixins and configuration management.
