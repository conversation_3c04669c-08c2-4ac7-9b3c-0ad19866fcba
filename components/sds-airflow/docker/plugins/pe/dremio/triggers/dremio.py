import json
from functools import cached_property
from typing import AsyncIterator, Any

from airflow.triggers.base import TriggerEvent

from commons.pe.operator.base import TaskStatus
from commons.pe.triggers.base import SDSBaseTrigger
from plugins.pe.dremio.hooks.dremio import DremioHook


class DremioReflectionCheckTrigger(SDSBaseTrigger):

    def __init__(
            self,
            source: str,
            dremio_url: str,
            **kwargs

    ):
        self.source = source
        self.dremio_url = dremio_url
        super().__init__(**kwargs)

    @cached_property
    def sync_hook(self):
        return DremioHook(dremio_url=self.dremio_url)

    def sync_run(self) -> TriggerEvent:
        reflections_for_source = self.sync_hook.get_reflections_for_source(self.source)
        reflection_status_dict = {reflection.get("name"): reflection.get("status").get("combinedStatus") for reflection
                                  in reflections_for_source}
        reflection_refresh_dict = {reflection.get("name"): reflection.get("status").get("refresh") for reflection
                                   in reflections_for_source}
        if all(status == "CAN_ACCELERATE" for status in reflection_status_dict.values()) and all(
                status in ["SCHEDULED", "MANUAL"] for status in reflection_refresh_dict.values()):
            self.log.info(f"Combined status is - {json.dumps(reflection_status_dict)}")
            self.log.info(f"Refresh status is - {json.dumps(reflection_refresh_dict)}")
            self.log.info("All reflections have run successfully")
            return TriggerEvent(
                payload={"status": TaskStatus.SUCCESS, "message": "All reflections have run successfully"})
        for reflection in reflections_for_source:
            status = reflection.get("status").get("combinedStatus")
            if status in ["FAILED", "CAN_ACCELERATE_WITH_FAILURES", "CANNOT_ACCELERATE_MANUAL",
                          "CANNOT_ACCELERATE_SCHEDULED"]:
                return TriggerEvent(payload={"status": TaskStatus.FAILED,
                                             "message": f"Refresh for reflection {reflection.get('name')} has failed with status {json.dumps(reflection.get('status'))}"})

            else:
                self.log.info(
                    f"Refresh for reflection {reflection.get('name')} is incomplete with status {json.dumps(reflection.get('status'))}")
                return TriggerEvent(payload={"status": TaskStatus.RUNNING})

    def serialize(self) -> tuple[str, dict[str, Any]]:
        return (
            "plugins.pe.dremio.triggers.dremio.DremioReflectionCheckTrigger",
            {
                "dremio_url": self.dremio_url,
                "source": self.source
            }
        )

    async def run(self) -> AsyncIterator[TriggerEvent]:
        pass


class DremioViewTrigger(SDSBaseTrigger):

    def __init__(
            self,
            query_job_id: str,
            dremio_url: str,
            **kwargs
    ):
        self.query_job_id = query_job_id
        self.dremio_url = dremio_url
        super().__init__(**kwargs)

    @cached_property
    def sync_hook(self):
        return DremioHook(dremio_url=self.dremio_url)

    def sync_run(self) -> TriggerEvent:
        # job_id = context["ti"].xcom_pull(key="query_job_id", task_ids=self.task_id)
        status = self.sync_hook.get_job_report(self.query_job_id)
        if status:
            return TriggerEvent(payload={'status': TaskStatus.SUCCESS, "message": "View created"})
        else:
            return TriggerEvent(payload={'status': TaskStatus.RUNNING})

    def serialize(self) -> tuple[str, dict[str, Any]]:
        return (
            "plugins.pe.dremio.triggers.dremio.DremioViewTrigger",
            {
                "dremio_url": self.dremio_url,
                "query_job_id": self.query_job_id
            }
        )

    async def run(self) -> AsyncIterator[TriggerEvent]:
        pass
