import json
import os
from functools import cached_property
from time import sleep
from typing import List
from urllib.error import HTTPError
from urllib.parse import urljoin

import requests
from airflow.hooks.base import BaseHook

from commons.pe.constants.airflow_constants import HTTPConstants, DremioConstants
from commons.pe.iceberg_utils import IcebergUtils


class DremioJobException(Exception):
    pass


class Dr<PERSON>ioHook(BaseHook):

    def __init__(self, dremio_url: str, headers: dict = None, ssl_verify: bool = False, **kwargs):
        super().__init__(**kwargs)
        self.dremio_url = dremio_url
        self.headers = {} if headers is None else headers
        self.ssl_verify = ssl_verify
        self.iceberg_utils = IcebergUtils()

    def get_url(self, endpoint: str):
        return urljoin(self.dremio_url, f"{DremioConstants.DREMIO_BASE_ENDPOINT}/{endpoint}")

    @cached_property
    def token(self):
        url = urljoin(self.dremio_url, "apiv2/login")
        data = json.dumps({
            "userName": os.getenv("DREMIO_USERNAME"),
            "password": os.getenv("DREMIO_PASSWORD")
        })
        headers = {
            'Content-Type': 'application/json'
        }
        response = requests.post(url=url, data=data, headers=headers)
        self.check_response(response)
        return response.json().get("token", "")

    def check_response(self, response: requests.Response):
        try:
            response.raise_for_status()
        except requests.exceptions.HTTPError:
            self.log.error("HTTP error: %s", response.reason)
            self.log.error(response.text)
            raise DremioJobException(str(response.status_code) + ":" + response.reason + "- " + response.text)

    def api_response(self, endpoint: str, method: str, body: dict = None):
        if body is None:
            body = {}
        url = self.get_url(endpoint)
        headers = {"authorization": self.token, "Content-Type": "application/json"}
        if method == HTTPConstants.GET:
            response = requests.get(url=url, headers=headers)
        elif method == HTTPConstants.POST:
            response = requests.post(url=url, headers=headers, data=json.dumps(body))
        elif method == HTTPConstants.PUT:
            response = requests.put(url=url, headers=headers, data=json.dumps(body))
        elif method == HTTPConstants.DELETE:
            response = requests.delete(url=url, headers=headers, data=json.dumps(body))
        else:
            raise HTTPError(f"Invalid method {method} called on API")

        self.check_response(response)
        if response.text:
            return response.json()

    def execute_sql_query(self, query: str, context: list = None):
        if not context:
            context = []
        body = {
            "sql": query,
            "context": context
        }
        self.log.info(f"Executing query {query}")
        return self.api_response(endpoint="sql", method=HTTPConstants.POST, body=body)

    def get_catalog_by_path(self, path: str):
        endpoint = f"catalog/by-path/{path}"
        return self.api_response(endpoint, method=HTTPConstants.GET)

    def get_reflections_for_source(self, source: str) -> List[dict]:
        source_id = self.get_catalog_by_path(source.replace(".", "/")).get("id")
        endpoint = f"dataset/{source_id}/reflection"
        response = self.api_response(endpoint, method=HTTPConstants.GET)
        return response.get("data")

    def get_reflection(self, reflection_id: int) -> dict:
        endpoint = f"reflection/{reflection_id}"
        return self.api_response(endpoint, method=HTTPConstants.GET)

    def refresh_metadata(self, table_name: str):
        query = f"ALTER TABLE {table_name} REFRESH METADATA"
        response = self.execute_sql_query(query=query)
        job_id = response.get("id")
        while True:
            result = self.get_job_report(job_id)
            if result:
                break
            else:
                self.log.info("Waiting for refresh metadata query to complete")
                sleep(5)

    def get_job_report(self, job_id: str) -> bool | str:
        endpoint = f"job/{job_id}"
        response = self.api_response(endpoint=endpoint, method=HTTPConstants.GET)
        state = response.get("jobState")
        if state == "CANCELED":
            raise DremioJobException(f"Job {job_id} has been CANCELED")
        elif state == "FAILED":
            error_message = response.get("errorMessage", "")
            raise DremioJobException(f"Job {job_id} has failed with error - {error_message}")
        elif state == "COMPLETED":
            self.log.info(f"Job {job_id} has completed")
            return response
        else:
            self.log.info(f"Job {job_id} is not completed")
            return False

    def check_space_exists(self, space: str) -> bool:
        endpoint = f"catalog/by-path/{space}"
        try:
            response = self.api_response(endpoint=endpoint, method=HTTPConstants.GET)
            if response.get("entityType") != "space":
                raise DremioJobException(
                    f"The provided path {space} is not a space, Virtual datasets can only be created in space.")
            elif response.get("name") == space:
                return True
            else:
                return False
        except (requests.exceptions.HTTPError, DremioJobException):
            self.log.info(f"{space} does not exist.")
            return False

    def create_space_if_not_exists(self, space: str):
        if not self.check_space_exists(space):
            endpoint = "catalog"
            body = {
                "entityType": "space",
                "name": space
            }
            response = self.api_response(endpoint=endpoint, method=HTTPConstants.POST, body=body)
            self.log.info(f"Created space with name {space} and id {response.get('id')}")
        else:
            self.log.info(f"Space {space} already exists")

    def get_source_schema(self, source: str) -> list:
        source_path = source.replace(".", "/")
        catalog = self.get_catalog_by_path(source_path)
        schema = [field.get("name") for field in catalog.get("fields")]
        return schema

    def update_reflection(self, reflection_spec: dict):
        endpoint = f"reflection/{reflection_spec.get('id')}"
        self.api_response(endpoint=endpoint, method=HTTPConstants.PUT, body=reflection_spec)

    def set_system_property(self, property_name: str, value: str):
        sql = f"ALTER SYSTEM SET {property_name} = {value}"
        self.execute_sql_query(query=sql)

    def reset_system_property(self, property_name: str):
        sql = f"ALTER SYSTEM RESET {property_name}"
        self.execute_sql_query(query=sql)
