import itertools
from typing import Optional, Dict, Any

from airflow.exceptions import AirflowException
from airflow.models import Variable
from airflow.utils.context import Context
from pyiceberg.types import PrimitiveType

from commons.pe.constants.airflow_constants import DremioConstants
from commons.pe.operator.base import TaskStatus, BaseJobOperator
from plugins.pe.dremio.hooks.dremio import Dr<PERSON>ioHook
from plugins.pe.dremio.triggers.dremio import DremioViewTrigger


class DremioViewCreationOperator(BaseJobOperator):

    trigger_class = DremioViewTrigger
    ui_color = " #7dcea0"
    template_fields = BaseJobOperator.template_fields + (
        "view_type",
        "view_name",
        "query",
        "base_tables",
    )

    def __init__(self,
                 view_type: str,
                 dremio_cluster_url: str,
                 view_name: str,
                 query: str = None,
                 base_tables: list = None,
                 **kwargs):
        super().__init__(**kwargs)
        self.view_type = view_type
        self.view_name = view_name
        self.query = query
        self.base_tables = base_tables
        self.dremio_cluster_url = dremio_cluster_url
        self.dremio_hook = DremioHook(dremio_url=self.dremio_cluster_url)

        if self.view_type == "view" and not self.query:
            raise AirflowException(f"View {self.view_name} need to specify query")
        elif self.view_type == "unionView" and not self.base_tables:
            raise AirflowException(f"Union view {self.view_name} needs to specify base tables to select view")

        self.space = None
        self.table_name = None
        self.mode = "poke"
        self.poke_interval = 10

    def _get_trigger_params(self, context: Context, **kwargs) -> dict[str, Any]:
        return {
            "query_job_id": self.query_job_id,
            "dremio_url": self.dremio_cluster_url
        }

    def pre_execute(self, context: Any):
        super().pre_execute(context)
        self.log.info(f"Using dremio url {self.dremio_cluster_url}")
        view_path = self.view_name.split(".")
        if len(view_path) == 2:
            self.space: str = view_path[0]
            self.table_name: str = view_path[1]
        else:
            raise AirflowException(f"View name {self.view_name} provided is not in the correct format space.table_name")

    def prepare(self, context: Context):
        self.log.info("Checking if the provided dremio space exists.")
        self.dremio_hook.create_space_if_not_exists(space=self.space)
        if self.view_type == "unionView":
            catalog = Variable.get(key=DremioConstants.DREMIO_SOURCE_NAME)
            for table in self.base_tables:
                self.dremio_hook.refresh_metadata(table_name=f"{catalog}.{table}")
            self.log.info("Checking if schemas are compatible")
            self.query = self.create_sql_statement()
        context["ti"].xcom_push(key="sql", value=self.query)

    def submit(self, context: Context):
        self.log.info(f"Going to create view {self.space}.{self.table_name}")
        response = self.create_or_replace_view()
        job_id = response.get("id")
        self.query_job_id = job_id
        context["ti"].xcom_push(key="query_job_id", value=job_id)
        if job_id:
            self.dremio_hook.get_job_report(job_id)
        self.log.info(f"Successfully created {self.space}.{self.table_name}.")

    def check_schema(self):
        schema_list = [self.dremio_hook.iceberg_utils.get_table_schema(table) for table in self.base_tables]
        schema_list = [{name: field_type for name, field_type in schema.items() if
                        isinstance(field_type, PrimitiveType)} for schema in schema_list]
        all_columns = list(set(itertools.chain(*schema_list)))
        missing = {table: list(set(all_columns).difference(self.dremio_hook.iceberg_utils.get_table_schema(table))) for
                   table in self.base_tables}
        return all_columns, missing

    def create_sql_statement(self) -> str:
        catalog = Variable.get(key=DremioConstants.DREMIO_SOURCE_NAME)
        all_columns, _ = self.check_schema()
        table_columns = {table: self.dremio_hook.iceberg_utils.get_table_schema(table_name=table) for table in
                         self.base_tables}
        table_queries = {}
        for table in self.base_tables:
            query = ",".join(
                [column if column in table_columns[table] else f"null as {column}" for column in all_columns])
            table_queries[table] = query
        final_query = " UNION ".join(
            [f"SELECT {table_queries[table]} FROM {catalog}.{table}" for table in self.base_tables])
        return final_query

    def create_or_replace_view(self):
        query = f"CREATE OR REPLACE VIEW {self.space}.{self.table_name} AS ({self.query})"
        catalog = Variable.get(key=DremioConstants.DREMIO_SOURCE_NAME)
        response = self.dremio_hook.execute_sql_query(query=query, context=[catalog])
        return response


    def on_operator_complete(self, context: Context, event: Optional[Dict[str, Any]]):
        pass
