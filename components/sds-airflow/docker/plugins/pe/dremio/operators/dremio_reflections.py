import json
from time import sleep
from typing import List, Sequence, Optional, Dict, Any

from airflow.exceptions import AirflowException
from airflow.models import Variable
from airflow.utils.context import Context

from commons.pe.common_utils import Utils
from commons.pe.constants.airflow_constants import HTTPConstants, DremioConstants
from commons.pe.operator.base import TaskStatus, BaseJobOperator
from plugins.pe.dremio.hooks.dremio import Dr<PERSON>ioHook, DremioJobException
from plugins.pe.dremio.triggers.dremio import DremioReflectionCheckTrigger


class DremioReflectionCreationOperator(BaseJobOperator):

    trigger_class = DremioReflectionCheckTrigger

    ui_color = "#43BFC7"
    template_fields: Sequence[str] = BaseJobOperator.template_fields + (
        "refresh_settings",
        "reflections_spec",
        "exclude_fields",
        "include_fields",
        "source",
        "query",
    )

    def __init__(
            self,
            source: str,
            dremio_cluster_url: str,
            source_type: str = None,
            reflections_spec: List[dict] = None,
            create_raw_reflection: bool = True,
            refresh_settings: dict = None,
            exclude_fields: str = None,
            include_fields: str = None,
            query: str = None,
            **kwargs
    ):
        super().__init__(**kwargs)
        self.source = source
        self.reflections_spec = [] if reflections_spec is None else reflections_spec
        self.create_raw_reflection = create_raw_reflection
        self.refresh_settings = refresh_settings if refresh_settings else {}
        self.exclude_fields = exclude_fields if exclude_fields else []
        self.include_fields = include_fields if include_fields else []
        self.query = query
        self.dremio_cluster_url = dremio_cluster_url
        self.dremio_hook = DremioHook(dremio_url=self.dremio_cluster_url)
        if self.include_fields and self.exclude_fields:
            raise AirflowException("Cannot set both 'include fields' and 'exclude fields' for a datasource. "
                                   "Please specify either one")
        self.mode = "reschedule"
        self.source_id = None
        self.source_type = source_type
        self.source_path = None
        self.raw_reflection_name = None

    def _get_trigger_params(self, context: Context, **kwargs) -> dict[str, Any]:
        return {
            "source": self.source,
            "dremio_url": self.dremio_cluster_url
        }

    def pre_execute(self, context: Context):
        super().pre_execute(context)
        self.log.info(f"Using dremio url {self.dremio_cluster_url}")
        self.raw_reflection_name = f"full_schema_raw_reflection_{self.source.replace('.', '_')}"
        self.source_path = self.source.replace(".", "/")
        if self.source_type == "PHYSICAL_DATASET":
            self.dremio_hook.refresh_metadata(table_name=self.source)
        source_details = self.dremio_hook.get_catalog_by_path(self.source_path)
        self.source_id = source_details.get("id")
        self.source_type = source_details.get("type")
        if self.source_type == "VIRTUAL_DATASET" and not self.query:
            raise DremioJobException(f"{self.source} is a view, but 'sql' is not defined which is mandatory for a view")

    def prepare(self, context: Context):
        if self.create_raw_reflection:
            reflections_for_source = self.dremio_hook.get_reflections_for_source(self.source)
            if self.raw_reflection_name in [reflection.get("name") for reflection in reflections_for_source]:
                self.log.info(f"'{self.raw_reflection_name}' already exists")

            self.log.info(f"Determining spec for automatic raw reflection creation for {self.source}")
            full_schema_reflection_spec = self.get_raw_reflection_spec()
            self.reflections_spec.append(full_schema_reflection_spec)

        self.set_refresh_method()

    def submit(self, context: Context):
        reflections_for_source = {spec.get("name"): spec for spec in
                                  self.dremio_hook.get_reflections_for_source(self.source)}
        if len(reflections_for_source) == 0:
            self.log.info(f"No reflections found for source {self.source}")
            new_reflections = self.reflections_spec

        else:  # Reflections already existing for source. Check for any new reflections and create.
            new_reflections = [reflection for reflection in self.reflections_spec if
                               reflection.get("name") not in reflections_for_source]
            existing_reflections = [reflection for reflection in self.reflections_spec if
                                    reflection.get("name") in reflections_for_source]

            for spec in existing_reflections:
                reflection_name = spec.get("name")
                reflection_id = reflections_for_source.get(reflection_name).get("id")
                spec["id"] = reflection_id
                current_spec = self.dremio_hook.get_reflection(reflection_id)
                updates = self.compare_reflections(current_spec, spec)
                if updates:
                    self.log.info(f"Updates available in {reflection_name} - {json.dumps(updates)}")
                    spec.update({key: value for key, value in current_spec.items() if
                                 key in ["id", "name", "tag", "type", "enabled"]})
                    spec.update({"datasetId": self.source_id})
                    self.dremio_hook.update_reflection(spec)
                else:
                    self.log.info(f"No updates for reflection {reflection_name}")

        for spec in new_reflections:
            self.create_reflection(reflection_spec=spec)
            self.log.info(f"Successfully created reflection {spec.get('name')} with spec {json.dumps(spec)}")

        self.log.info(f"Going to refresh all available reflections for source {self.source}")
        self.execute_refresh()
        sleep(5)

    def check(self, context: Context) -> TaskStatus:
        reflections_for_source = self.dremio_hook.get_reflections_for_source(self.source)
        reflection_status_dict = {reflection.get("name"): reflection.get("status").get("combinedStatus") for reflection
                                  in reflections_for_source}
        reflection_refresh_dict = {reflection.get("name"): reflection.get("status").get("refresh") for reflection
                                   in reflections_for_source}
        if all(status == "CAN_ACCELERATE" for status in reflection_status_dict.values()) and all(
                status in ["SCHEDULED", "MANUAL"] for status in reflection_refresh_dict.values()):
            self.log.info(f"Combined status is - {json.dumps(reflection_status_dict)}")
            self.log.info(f"Refresh status is - {json.dumps(reflection_refresh_dict)}")
            self.log.info("All reflections have run successfully")
            return TaskStatus.SUCCESS
        for reflection in reflections_for_source:
            status = reflection.get("status").get("combinedStatus")
            if status in ["FAILED", "CAN_ACCELERATE_WITH_FAILURES", "CANNOT_ACCELERATE_MANUAL",
                          "CANNOT_ACCELERATE_SCHEDULED"]:
                self.log.error(f"Entire status - {json.dumps(reflection.get('status'))}")
                raise AirflowException(
                    f"Refresh for reflection {reflection.get('name')} has failed with status {status}")
            else:
                self.log.info(f"Refresh for reflection {reflection.get('name')} is incomplete with status {status}")
                self.log.info(f"Entire status - {json.dumps(reflection.get('status'))}")

    def create_reflection(self, reflection_spec: dict):
        endpoint = "reflection"
        reflection_spec["datasetId"] = self.source_id
        final_spec = {**DremioConstants.REFLECTION_SPEC_DEFAULT_VALUES, **reflection_spec}
        response = self.dremio_hook.api_response(endpoint, method=HTTPConstants.POST, body=final_spec)
        self.refresh_reflection()
        return response

    def execute_refresh(self):
        if self.source_type == "PHYSICAL_DATASET":
            self.dremio_hook.refresh_metadata(table_name=self.source)
        self.refresh_reflection()
        self.log.info("Updated refresh details")
        self.dremio_hook.api_response(endpoint=f"catalog/{self.source_id}/refresh", method=HTTPConstants.POST, body={})
        self.log.info(f"Successfully triggered reflection refresh for {self.source}")

    def refresh_reflection(self):
        dataset_details = self.dremio_hook.get_catalog_by_path(path=self.source_path)
        refresh_body = {
            "id": self.source_id,
            "entityType": dataset_details.get("entityType"),
            "type": dataset_details.get("type"),
            "path": dataset_details.get("path"),
            "accelerationRefreshPolicy": {
                **{"neverRefresh": True,
                   "neverExpire": False
                   }, **self.refresh_settings
            }
        }
        if self.source_type == "VIRTUAL_DATASET":
            refresh_body["sql"] = self.query
            refresh_body["sqlContext"] = [Variable.get(key=DremioConstants.DREMIO_SOURCE_NAME)]
        self.dremio_hook.api_response(endpoint=f"catalog/{self.source_id}", method=HTTPConstants.PUT, body=refresh_body)

    def get_raw_reflection_spec(self):
        schema = self.dremio_hook.get_source_schema(self.source)
        reflection_spec = {**{
            "type": "RAW",
            "name": self.raw_reflection_name},
                           **DremioConstants.REFLECTION_SPEC_DEFAULT_VALUES
                           }
        display_fields = [{"name": name} for name in schema]
        if self.exclude_fields:
            display_fields = [field for field in display_fields if field.get("name") not in self.exclude_fields]
        elif self.include_fields:
            display_fields = [field for field in display_fields if field.get("name") in self.include_fields]
        else:
            self.log.info(f"Using all fields from source {self.source} schema")

        reflection_spec["displayFields"] = display_fields
        reflection_spec["datasetId"] = self.source_id
        if self.source_type == "PHYSICAL_DATASET":
            table_name = ".".join(self.source.split(".")[1:])
            partition_spec = self.dremio_hook.iceberg_utils.get_partition_spec(table_name=table_name)
        else:
            partition_spec = []
        partition_fields = [{
            "name": spec.get("name"), "transform": {"type": spec.get("transform", "identity").upper()}
        } for spec in partition_spec]
        reflection_spec["partitionFields"] = partition_fields

        custom_raw_reflection_spec = next(
            (spec for spec in self.reflections_spec if spec.get("name") == self.raw_reflection_name), None)
        if custom_raw_reflection_spec:
            for key in ["id", "type", "tag", "datasetId", "displayFields", "partitionFields"]:
                custom_raw_reflection_spec.pop(key, None)
            final_merged_spec = Utils.merge_dictionaries(reflection_spec, custom_raw_reflection_spec)
            self.reflections_spec = [spec for spec in self.reflections_spec if
                                     spec.get("name") != self.raw_reflection_name]

            return final_merged_spec
        else:
            return reflection_spec

    @staticmethod
    def compare_reflections(first_reflection: dict, second_reflection: dict) -> dict:
        """
        Compare the difference in second dict with respect to first
        """
        if first_reflection.get("id", None) is None or first_reflection.get("id") != second_reflection.get("id"):
            raise AirflowException("Reflection ids do not match.")
        keys_to_remove = ["status", "currentSizeBytes", "totalSizeBytes", "createdAt", "updatedAt", "entity_type",
                          "datasetId"]
        for key in keys_to_remove:
            first_reflection.pop(key, None)
            second_reflection.pop(key, None)
        second_reflection["tag"] = first_reflection["tag"]
        second_reflection["id"] = first_reflection["id"]
        second_reflection["entityType"] = first_reflection["entityType"]

        diff_dict = {}
        for key, value in second_reflection.items():
            first_value = first_reflection.get(key)
            if isinstance(value, list):
                if first_value is None:
                    first_value = []
                first_value = sorted(first_value, key=lambda item: item["name"])
                value = sorted(value, key=lambda item: item["name"])
            if value != first_value:
                diff_dict[key] = value

        return diff_dict

    def set_refresh_method(self):
        if not self.refresh_settings:
            self.refresh_settings = {
                "neverRefresh": True,
                "neverExpire": True
            }

        if not self.refresh_settings.get("method", None):
            self.log.info("No refresh method provided in refresh settings. Going to determine method automatically")
            if self.source_type == "PHYSICAL_DATASET":
                self.log.info(f"{self.source} is an iceberg table")
                table_name = ".".join(self.source.split(".")[1:])
                partition_spec = self.dremio_hook.iceberg_utils.get_partition_spec(table_name)
                if not partition_spec:
                    self.log.info(
                        f"Iceberg table {self.source} has no partition columns and no incremental field provided. "
                        f" Going to apply full refresh to refresh settings")
                    self.refresh_settings["method"] = "FULL"
                else:
                    partition_column = partition_spec[0].get("name")
                    self.log.info(f"Setting refresh type as INCREMENTAL with incremental column {partition_column}")
                    self.refresh_settings["method"] = "INCREMENTAL"
                    self.refresh_settings["refreshField"] = partition_column
            else:
                self.log.info(f"{self.source} is a view. Going to apply full refresh")
                self.refresh_settings["method"] = "FULL"

    def on_operator_complete(self, context: Context, event: Optional[Dict[str, Any]]):
        is_success = event.get("status") == TaskStatus.SUCCESS
        log_method = self.log.info if is_success else self.log.error
        log_method(event.get("message"))
        if is_success:
            self.log.info("Completed reflection refresh for datasource")
        else:
            # Handle failure case
            raise AirflowException(f"Dremio reflection refresh for {self.app_name} failed ")

