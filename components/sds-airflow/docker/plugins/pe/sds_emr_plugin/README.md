## SDS EMR Plugin
Plugins to manage EMR clusters for data pipeline jobs to run.
## Operators
* [SDSClusterSpawnOperator](/plugins/pe/sds_emr_plugingin/operators/sds_emr_operator.py)
* [SDSClusterDestroyOperator](/plugins/pe/sds_emr_plugingin/operators/sds_emr_operator.py)
### Environment Variables
Following environment variables should be set for the working of this operators.

| Variable  | Details |Default Value|
| ------------- | ------------------------ |------------- |
| AWS_REGION  | AWS Region  |`us-west-2` |
| S3_BOOTSTRAP_BUCKET_NAME  | Bucket name for the terraform scripts |`sds-qa-apps` |
| TF_VAR_tf_state_bucket  | Bucket name to store terraform state  |`terraform-pai-sdsqa-state` |
| TF_VAR_tf_dynamo_table  | Terraform dynamo db S3 bucket name  |`sdsqa-up-and-running-locks` |
| TF_VAR_AWS_REGION  | Terraform region to which resource should be created  |`us-west-2` |
| enable_sync_hadoop_conf  | Control variable for Druid settings copy|`False` |

* In addition to above for running locally AWS credentials can also be set using 
  environment variables (`AWS_ACCESS_KEY_ID`,`AWS_SECRET_ACCESS_KEY`,`AWS_DEFAULT_REGION` )
### SDSClusterSpawnOperator
* Sensor operator for managing cluster creation by running Terraform scripts.
* Clusters created are identified using cluster key which combosed of cluster_name,cluster_id & cluster_type seperated by ``_`` 
It will be ``${cluster_name}_${cluster_id}_${cluster_type}``
* If a cluster already exist with the same key operator will not create it again instead will be tracked by cluster counter variable.
* Operator downloads terraform script and runs it which will create the cluster if it does'nt already exists
* It is developer's responsibility to call SDSClusterDestroyOperator on error case & After the jobs are completed
#### Constructor arguments
| Argument  | Details |Default Value|Jinja Template supported |
| ------------- | ------------------------ |------------- |------------- |
| cluster_name  | Name field in cluster key  |DEFAULT |Yes|
| cluster_id  | Cluster Id in cluster key  |CLUSTER|Yes|
| cluster_type  | Size of the cluster. Supported values are SMALL,MEDIUM,LARGE  |SMALL|No|
| idle_timeout_enabled  | Enable Idle timeout for cluster  |'True'|No|
| tf_s3_key  | S3 Key for terraform script|`bootstrap/tf-scripts/emr-bootstrap.zip`|Yes|
| aws_conn_id  | AWS Connection ID  |`aws_default`|No|
### SDSClusterDestroyOperator
* Sensor operator for managing cluster termination by running Terraform scripts
* Clusters are identified using cluster key which combosed of cluster_name,cluster_id & cluster_type seperated by ``_`` 
It will be ``${cluster_name}_${cluster_id}_${cluster_type}``
* If a cluster already exist and if more consumers are using it ( In other DAGs in case of shared cluster) this operator will decrement the counter variable instead of deleting cluster
* Operator downloads terraform script and runs it which will destroy the cluster if it exists and no consumers are not using it ( Using counter variable)
#### Constructor arguments
| Argument  | Details |Default Value|Jinja Template supported |
| ------------- | ------------------------ |------------- |------------- |
| cluster_name  | Name field in cluster key  |DEFAULT |Yes|
| cluster_id  | Cluster Id in cluster key  |CLUSTER|Yes|
| cluster_type  | Size of the cluster. Supported values are SMALL,MEDIUM,LARGE  |SMALL|No|
| idle_timeout_enabled  | Enable Idle timeout for cluster  |'True'|No|
| tf_s3_key  | S3 Key for terraform script|`bootstrap/tf-scripts/emr-bootstrap.zip`|Yes|
| aws_conn_id  | AWS Connection ID  |`aws_default`|No|
