
from typing import Dict

from airflow.exceptions import AirflowException
from airflow.models import Variable
from airflow.providers.redis.hooks.redis import RedisHook
from airflow.sensors.base import BaseSensorOperator
from redis.exceptions import LockError

from plugins.pe.sds_emr_plugin.operators.sds_emr_utils import AWSUtils, ClusterType, EMRConstants, ClusterService, \
    TerraformUtils


class SDSClusterSpawnOperator(BaseSensorOperator):
    """
    Sensor operator to create EMR cluster using terraform script uploaded in S3 location.
    Cluster will be identified by cluster key with cluster_name,cluster_id & cluster_type.
    If multiple SpawnOperators are called with same cluster key same cluster will be reused
    using internal counter.LIVY url will be set in context & variable using key
    ${cluster_name}_${cluster_id}_${cluster_type}_LIVY_URL.
    :param cluster_name - Component in cluster key string
    :param cluster_id - Component in cluster key string
    :param cluster_type - Specify size of cluster allowed values are SMALL,MEDIUM & LARGE
    :param idle_timeout_enabled - Specify idle timeout for cluster to auto shutdown
    :param tf_s3_key - S3 Key location for terraform script. S3 Bucket name can be set
    using environment variable as explained in README.
    :param aws_conn_id - AWS connection ID
    """

    template_fields = ('cluster_name', 'cluster_id', 'cluster_type', 'tf_s3_key', 'hadoop_sync')

    def __init__(self,
                 cluster_name: str = EMRConstants.CLUSTER_NAME_DEFAULT,
                 cluster_id: str = EMRConstants.CLUSTER_ID_DEFAULT,
                 cluster_type: ClusterType = ClusterType.SMALL,
                 idle_timeout_enabled: str = 'true',
                 hadoop_sync: str = 'false',
                 tf_s3_key=EMRConstants.TF_S3_DEFAULT,
                 aws_conn_id: str = EMRConstants.AWS_CON_ID_DEFAULT,
                 redis_conn_id: str = EMRConstants.REDIS_CON_ID_DEFAULT,
                 **kwargs) -> None:

        if "mode" not in kwargs:
            kwargs["mode"] = "reschedule"  # change the default value for mode to reschedule

        super().__init__(**kwargs)
        self.cluster_name = cluster_name
        self.cluster_id = cluster_id
        self.cluster_type = cluster_type
        self.tf_s3_key = tf_s3_key
        self.idle_timeout_enabled = idle_timeout_enabled
        self.cluster_key = '{}_{}_{}'.format(cluster_name, cluster_id, cluster_type)
        self.aws_conn_id = aws_conn_id
        self.redis_hook = RedisHook(redis_conn_id=redis_conn_id)
        self.hadoop_sync = hadoop_sync

    def handle_cluster_service(self, context):
        cluster_tech = Variable.get(F"{self.cluster_name}_{self.cluster_id}_{EMRConstants.CLUSTER_SVC_KEY}",
                                    default_var=ClusterService.AWS_EMR)
        if cluster_tech == ClusterService.ONPREM:
            livy_url = Variable.get(F"{self.cluster_name}_{self.cluster_id}_{EMRConstants.LIVY_URL_KEY}",
                                    default_var=None)
            if not livy_url:
                raise AirflowException('LIVY_URL variable should be set for ONPREM cluster')
            context['task_instance'].xcom_push(key='{}_{}'.format(self.cluster_key, EMRConstants.LIVY_URL_KEY), value=livy_url)
            Variable.set('{}_{}'.format(self.cluster_key, EMRConstants.LIVY_URL_KEY), livy_url)
            return True
        if cluster_tech == ClusterService.AWS_EMR:
            AWSUtils.validate_credentials()
            self.log.info("AWS credentials validated")

    def poke(self, context: Dict) -> bool:
        """
        Poke method for sensor operator to create EMR using terraform script. Uses Redis lock to control
        concurrency.
        @param context:
        @return:
        """
        if self.handle_cluster_service(context):
            return True
        try:
            with self.redis_hook.get_conn().lock(self.cluster_key, timeout=EMRConstants.REDIS_LOCK_MAX_TIMEOUT,
                                                 blocking_timeout=EMRConstants.REDIS_WAIT_TIMEOUT):
                cluster_available = AWSUtils.check_cluster_available(self.cluster_key)
                self.log.info("Cluster availability check returned - {} for cluster {}".format(cluster_available,
                                                                                               self.cluster_key))
                cluster_counter = int(
                    Variable.get('{}_{}'.format(self.cluster_key, EMRConstants.COUNTER_KEY), default_var=0))
                self.log.info("Cluster consumer counter is {} for {}".format(cluster_counter, self.cluster_key))
                if (cluster_counter > 0 and cluster_available):
                    self.log.info('Cluster with key {} already available setting livy URL & Counter incrementing'
                                  .format(self.cluster_key))
                    Variable.set('{}_{}'.format(self.cluster_key, EMRConstants.COUNTER_KEY), cluster_counter + 1)
                    livy_url = Variable.get('{}_{}'.format(self.cluster_key, EMRConstants.LIVY_URL_KEY))
                    context['task_instance'].xcom_push(key='{}_{}'.format(self.cluster_key, EMRConstants.LIVY_URL_KEY),
                                            value=livy_url)
                    return True
                else:
                    self.log.error(
                        'Cluster {} is not available with cluster count greater than 0'.format(self.cluster_key))
                    self.log.error(
                        'Resetting variables for {}'.format(self.cluster_key))
                    Variable.set('{}_{}'.format(self.cluster_key, EMRConstants.COUNTER_KEY), 0)
                    Variable.delete('{}_{}'.format(self.cluster_key, EMRConstants.LIVY_URL_KEY))
                self.log.info('Starting to create cluster {} running terraform'.format(self.cluster_key))
                TerraformUtils(self.cluster_name, self.cluster_id, self.tf_s3_key, self.cluster_type,
                               self.idle_timeout_enabled, self.hadoop_sync, self.aws_conn_id).create_cluster(context)
                self.log.info('Loading livy URL from TF output path for cluster {}'.format(self.cluster_key))
                livy_url = TerraformUtils(self.cluster_name, self.cluster_id, self.tf_s3_key, self.cluster_type,
                                          self.hadoop_sync, self.aws_conn_id).get_livy_url()
                Variable.set('{}_{}'.format(self.cluster_key, EMRConstants.LIVY_URL_KEY), livy_url)
                context['task_instance'].xcom_push(key='{}_{}'.format(self.cluster_key, EMRConstants.LIVY_URL_KEY),
                                        value=livy_url)
                Variable.set('{}_{}'.format(self.cluster_key, EMRConstants.COUNTER_KEY), 1)
                return True
        except LockError as e:
            self.log.error(f"Cannot acquire cluster creation lock for {self.cluster_key}")
            return False
        except Exception as e:
            self.log.error(f"Exception while running cluster creation for cluster {self.cluster_key}", e)
            raise e


class SDSClusterDestroyOperator(BaseSensorOperator):
    """
        Sensor operator to destroy EMR cluster using terraform script uploaded in S3 location.
        Cluster will be identified by cluster key with cluster_name,cluster_id & cluster_type.
        If multiple SpawnOperators are called with same cluster key same cluster will be reused
        using internal counter. Uses redis lock for controlling concurrency.

        :@param cluster_name - Component in cluster key string
        :@param cluster_id - Component in cluster key string
        :@param cluster_type - Specify size of cluster allowed values are SMALL,MEDIUM & LARGE
        :@param idle_timeout_enabled - Specify idle timeout for cluster to auto shutdown
        :@param tf_s3_key - S3 Key location for terraform script. S3 Bucket name can be set
        using environment variable as explained in README.
        :@param aws_conn_id - AWS connection ID
        :@param redis_conn_id Redis connection ID
        """

    template_fields = ('cluster_name', 'cluster_id', 'cluster_type', 'tf_s3_key', 'hadoop_sync')

    def __init__(self,
                 cluster_name: str = 'DEFAULT',
                 cluster_id: str = 'CLUSTER',
                 cluster_type: ClusterType = ClusterType.SMALL,
                 tf_s3_key=EMRConstants.TF_S3_DEFAULT,
                 idle_timeout_enabled: str = 'true',
                 hadoop_sync: str = 'false',
                 aws_conn_id: str = EMRConstants.AWS_CON_ID_DEFAULT,
                 redis_conn_id: str = EMRConstants.REDIS_CON_ID_DEFAULT,
                 **kwargs) -> None:
        if "mode" not in kwargs:
            kwargs["mode"] = "reschedule"  # change the default value for mode to reschedule
        super().__init__(**kwargs)
        self.cluster_name = cluster_name
        self.cluster_id = cluster_id
        self.cluster_type = cluster_type
        self.idle_timeout_enabled = idle_timeout_enabled
        self.tf_s3_key = tf_s3_key
        self.aws_conn_id = aws_conn_id
        self.cluster_key = '{}_{}_{}'.format(cluster_name, cluster_id, cluster_type)
        self.redis_hook = RedisHook(redis_conn_id=redis_conn_id)
        self.hadoop_sync = hadoop_sync

    def poke(self, context: Dict) -> bool:
        cluster_tech = Variable.get(F"{self.cluster_name}_{self.cluster_id}_{EMRConstants.CLUSTER_SVC_KEY}",
                                    default_var=ClusterService.AWS_EMR)
        if cluster_tech == ClusterService.ONPREM:
            return True
        try:
            with self.redis_hook.get_conn().lock(self.cluster_key, timeout=EMRConstants.REDIS_LOCK_MAX_TIMEOUT,
                                                 blocking_timeout=EMRConstants.REDIS_WAIT_TIMEOUT):
                cluster_counter = int(
                    Variable.get('{}_{}'.format(self.cluster_key, EMRConstants.COUNTER_KEY), default_var=0))
                if cluster_counter > 1:
                    self.log.info('Decrementing cluster counter for {}'.format(self.cluster_key))
                    Variable.set('{}_{}'.format(self.cluster_key, EMRConstants.COUNTER_KEY), cluster_counter - 1)
                    return True
                elif cluster_counter == 1:
                    self.log.info(
                        'Cluster counter for key {} is {} and going to terminate cluster'.format(self.cluster_key,
                                                                                                 cluster_counter))
                else:
                    self.log.error(
                        'Abnormal cluster counter for cluster key {} found - {} - Going to terminate cluster'.format(
                            self.cluster_key, cluster_counter))
                TerraformUtils(self.cluster_name, self.cluster_id, self.tf_s3_key, self.cluster_type,
                               self.idle_timeout_enabled, self.hadoop_sync, self.aws_conn_id).destroy_emr_cluster(
                    context)
                Variable.delete('{}_{}'.format(self.cluster_key, EMRConstants.COUNTER_KEY))
                Variable.delete('{}_{}'.format(self.cluster_key, EMRConstants.LIVY_URL_KEY))
                return True
        except LockError as e:
            self.log.info(f"Cannot acquire cluster creation lock for {self.cluster_key}")
            return False
        except Exception as e:
            self.log.error(f"Exception while running cluster creation for cluster {self.cluster_key}", e)
            raise e
