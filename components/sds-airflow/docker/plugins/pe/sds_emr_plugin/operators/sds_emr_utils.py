import json
import os
import zipfile
from enum import Enum
from tempfile import NamedTemporaryFile, mkdtemp

import boto3
from airflow.exceptions import AirflowException
from airflow.operators.bash import BashOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook


class ClusterType(str, Enum):
    SMALL = 'SMALL'
    MEDIUM = 'MEDIUM'
    LARGE = 'LARGE'


class ClusterService(str, Enum):
    AWS_EMR = 'AWS_EMR'
    ONPREM = 'ONPREM'


class EMRConstants:
    CLUSTER_SVC_KEY = 'CLUSTER_SVC'
    CONCURRENCY_LOCK_KEY = 'LOCK'
    COUNTER_KEY = 'COUNTER'
    LIVY_URL_KEY = 'LIVY_URL'
    TF_STATUS_KEY = 'TF_STAT'
    TF_S3_DEFAULT = 'bootstrap/tf-scripts/emr-bootstrap_v2.zip'
    CLUSTER_NAME_DEFAULT = 'DEFAULT'
    CLUSTER_ID_DEFAULT = 'CLUSTER'
    AWS_CON_ID_DEFAULT = 'aws_default'
    S3_TF_BUCKET_NAME_DEFAULT = 'sds-qa-apps'
    S3_TF_STATE_BUCKET_NAME_DEFAULT = 'terraform-pai-sdsqa-state'
    TF_DYNAMO_TABLE_DEFAULT = 'sdsqa-up-and-running-locks'
    AWS_REGION_DEFAULT = 'us-west-2'
    PROJECT = os.getenv('PROJECT', default='SDS')
    ENVIRONMENT = os.getenv('ENVIRONMENT', default='QA')
    EMR_NAME_PREFIX = F"{PROJECT}_{ENVIRONMENT}"
    REDIS_LOCK_MAX_TIMEOUT = 1800
    REDIS_WAIT_TIMEOUT = 30
    REDIS_CON_ID_DEFAULT = 'redis_default'


class TerraformUtils:
    import logging
    LOGGER = logging.getLogger("airflow.task")

    def __init__(self,
                 cluster_name: str = EMRConstants.CLUSTER_NAME_DEFAULT,
                 cluster_id: str = EMRConstants.CLUSTER_ID_DEFAULT,
                 source_s3_key: str = EMRConstants.TF_S3_DEFAULT,
                 cluster_type: ClusterType = ClusterType.SMALL,
                 idle_timeout_enabled: str = 'true',
                 hadoop_sync: str = 'false',
                 aws_conn_id: str = EMRConstants.AWS_CON_ID_DEFAULT,
                 **kwargs) -> None:
        self.cluster_name = cluster_name
        self.cluster_id = cluster_id
        self.cluster_type = cluster_type
        self.idle_timeout_enabled = idle_timeout_enabled
        self.cluster_key = '{}_{}_{}'.format(cluster_name, cluster_id, cluster_type)
        self.aws_conn_id = aws_conn_id
        self.bootstrap_bucket_name = os.getenv('S3_BOOTSTRAP_BUCKET_NAME', EMRConstants.S3_TF_BUCKET_NAME_DEFAULT)
        self.source_s3_key = source_s3_key
        self.hadoop_sync = hadoop_sync

    BOOTSTRAP_CLUSTER_TF_OUTPUT_S3_KEY = 'bootstrap/tf-output/{}/{}/{}/output.json'
    TF_INIT_BASH_COMMAND = 'cd $tf_path;export TF_VAR_project=$project;export TF_VAR_environment=$environment;export TF_VAR_cluster_name=$cluster_name;export TF_VAR_cluster_id=$cluster_id;export TF_VAR_cluster_type=$cluster_type;export TF_VAR_idle_timeout_enabled=$idle_timeout_enabled;export TF_VAR_enable_copying_hadoop_confs=$hadoop_sync;terraform init -backend-config \"bucket=$TF_VAR_tf_state_bucket\" -backend-config \"dynamodb_table=$TF_VAR_tf_dynamo_table\" -backend-config \"region=$TF_VAR_AWS_REGION\" -backend-config \"key=emr/$TF_VAR_environment/$TF_VAR_cluster_name/$TF_VAR_cluster_id/$TF_VAR_cluster_type/terraform.tfstate\"'
    TF_DESTROY_BASH_COMMAND = 'cd $tf_path;export TF_VAR_cluster_name=$cluster_name;export TF_VAR_project=$project;export TF_VAR_environment=$environment;export TF_VAR_cluster_id=$cluster_id;export TF_VAR_cluster_type=$cluster_type;export TF_VAR_idle_timeout_enabled=$idle_timeout_enabled;export TF_VAR_enable_copying_hadoop_confs=$hadoop_sync;terraform destroy -auto-approve'
    TF_APPLY_BASH_COMMAND = 'cd $tf_path;export TF_VAR_project=$project;export TF_VAR_environment=$environment;export TF_VAR_cluster_name=$cluster_name;export TF_VAR_cluster_id=$cluster_id;export TF_VAR_cluster_type=$cluster_type;export TF_VAR_idle_timeout_enabled=$idle_timeout_enabled;export TF_VAR_enable_sync_hadoop_confs=$hadoop_sync;terraform apply -auto-approve'

    TF_ENV = {
        'AWS_REGION': os.getenv('AWS_REGION', default=EMRConstants.AWS_REGION_DEFAULT),
        'S3_BOOTSTRAP_BUCKET_NAME': os.getenv('S3_BOOTSTRAP_BUCKET_NAME',
                                              default=EMRConstants.S3_TF_BUCKET_NAME_DEFAULT),
        'TF_VAR_tf_state_bucket': os.getenv('S3_BOOTSTRAP_BUCKET_NAME',
                                            default=EMRConstants.S3_TF_STATE_BUCKET_NAME_DEFAULT),
        'TF_VAR_tf_dynamo_table': os.getenv('TF_VAR_tf_dynamo_table', default=EMRConstants.TF_DYNAMO_TABLE_DEFAULT),
        'TF_VAR_AWS_REGION': os.getenv('TF_VAR_AWS_REGION', default=EMRConstants.AWS_REGION_DEFAULT)
    }

    def download_tf_script(self):

        source_s3 = S3Hook(aws_conn_id=self.aws_conn_id)

        self.LOGGER.info("Downloading source S3 file %s", self.source_s3_key)
        if not source_s3.check_for_key(self.source_s3_key, bucket_name=self.bootstrap_bucket_name):
            raise AirflowException(f"The source key {self.source_s3_key} does not exist")
        source_s3_key_object = source_s3.get_key(self.source_s3_key, bucket_name=self.bootstrap_bucket_name)
        with NamedTemporaryFile("wb", delete=False) as f_source:
            self.LOGGER.info("Dumping S3 file %s contents to local file %s", self.source_s3_key, f_source.name)
            source_s3_key_object.download_fileobj(Fileobj=f_source)
            f_source.flush()
            self.LOGGER.info('File downloaded is %s' % f_source.name)

        with zipfile.ZipFile(f_source.name, 'r') as zip_ref:
            temp_dir = mkdtemp(prefix='%s_%s' % (self.cluster_name, self.cluster_id))
            zip_ref.extractall(temp_dir)
        self.LOGGER.info('Terraform file extracted to location %s ' % temp_dir)

        return temp_dir

    def create_cluster(self, context):
        tf_dir = self.download_tf_script()
        tf_env = dict(self.TF_ENV)
        tf_env.update({'tf_path': tf_dir, 'cluster_name': self.cluster_name,
                       'cluster_id': self.cluster_id, 'cluster_type': self.cluster_type,
                       'idle_timeout_enabled': self.idle_timeout_enabled,
                       'project':EMRConstants.PROJECT,
                       'environment':EMRConstants.ENVIRONMENT,
                       'hadoop_sync':self.hadoop_sync})
        tf_env.update(os.environ.copy())
        tf_init_bash_op = BashOperator(task_id='tf_init', bash_command=self.TF_INIT_BASH_COMMAND,
                                       env=tf_env)
        tf_init_bash_op.execute(context)
        tf_destroy_bash_op = BashOperator(task_id='tf_destroy', bash_command=self.TF_DESTROY_BASH_COMMAND,
                                          env=tf_env)
        tf_destroy_bash_op.execute(context)
        tf_apply_bash_op = BashOperator(task_id='tf_apply', bash_command=self.TF_APPLY_BASH_COMMAND,
                                        env=tf_env)
        tf_apply_bash_op.execute(context)

    def destroy_emr_cluster(self, context):
        tf_dir = self.download_tf_script()
        tf_env = dict(self.TF_ENV)
        tf_env.update({'tf_path': tf_dir, 'cluster_name': self.cluster_name,
                       'cluster_id': self.cluster_id, 'cluster_type': self.cluster_type,
                       'idle_timeout_enabled': self.idle_timeout_enabled,
                       'project': EMRConstants.PROJECT,
                       'environment': EMRConstants.ENVIRONMENT,
                       'hadoop_sync': self.hadoop_sync
                       })
        tf_env.update(os.environ.copy())
        tf_init_bash_op = BashOperator(task_id='tf_init', bash_command=self.TF_INIT_BASH_COMMAND,
                                       env=tf_env)
        tf_init_bash_op.execute(context)
        tf_destroy_bash_op = BashOperator(task_id='tf_destroy', bash_command=self.TF_DESTROY_BASH_COMMAND,
                                          env=tf_env)
        tf_destroy_bash_op.execute(context)

    def get_livy_url(self):
        source_s3 = S3Hook(aws_conn_id=self.aws_conn_id)
        bootstrap_output_key = self.BOOTSTRAP_CLUSTER_TF_OUTPUT_S3_KEY.format(self.cluster_name, self.cluster_id,self.cluster_type)
        if not source_s3.check_for_key(bootstrap_output_key, bucket_name=self.bootstrap_bucket_name):
            raise AirflowException(f"The source key {bootstrap_output_key} does not exist")
        source_s3_key_object = source_s3.read_key(bootstrap_output_key,
                                                  bucket_name=self.bootstrap_bucket_name)
        cluster_details = json.loads(source_s3_key_object)
        self.LOGGER.info('TF output file content is %s' % cluster_details)
        return cluster_details.get('livy_url')

class AWSUtils:
    if 'AWS_DEFAULT_REGION' not in os.environ:
        os.environ['AWS_DEFAULT_REGION'] = EMRConstants.AWS_REGION_DEFAULT
    sts_client = boto3.client('sts')
    emr_client = boto3.client('emr')

    @staticmethod
    def check_cluster_available(cluster_key):
        """
        Check EMR cluster status using EMR client.
        cluster_key will be prefixed with ${PROJECT}_${ENV} as that is the cluster name
        set from TF script for the cluster name.
        @param cluster_key:
        @return:
        """
        page_iterator = AWSUtils.emr_client.get_paginator('list_clusters').paginate(
            ClusterStates=['RUNNING', 'WAITING'])
        cluster_id = None
        for page in page_iterator:
            for item in page.get('Clusters'):
                if F"{EMRConstants.EMR_NAME_PREFIX}_{cluster_key}" == item.get('Name'):
                    cluster_id = item.get('Id')
                    break
        return True if cluster_id else False

    @staticmethod
    def validate_credentials():
        '''
        Utility method to validate credentials are set. This method will raise exception if credentials are not valid.
        '''

        AWSUtils.sts_client.get_caller_identity()
