from airflow.models import Dag<PERSON>un
from airflow.settings import Session
from sqlalchemy import text
from sqlalchemy.engine import Inspector

from commons.pe.sds_dag_orchestrator import SDSOrchestrationMeta

session = Session()
engine = session.get_bind(mapper=None, clause=None)
inspector = Inspector.from_engine(engine)
table_list = inspector.get_table_names()
if "orchestration_meta" not in table_list:
    SDSOrchestrationMeta.create_table(session=session)
else:
    table_columns = inspector.get_columns("orchestration_meta")
    if "dag_run_type" not in [column.get("name") for column in table_columns]:
        conn = engine.connect()
        entries = conn.execute("SELECT orchestration_meta.id AS orchestration_meta_id, orchestration_meta.logical_date AS orchestration_meta_logical_date, orchestration_meta.head_dag_id AS orchestration_meta_head_dag_id, orchestration_meta.pipeline_id AS orchestration_meta_pipeline_id FROM orchestration_meta ORDER BY orchestration_meta.logical_date ASC").fetchall()

        update_entries = []
        for entry in entries:
            head_dag_id = entry.orchestration_meta_head_dag_id
            logical_date = entry.orchestration_meta_logical_date
            pipeline_id = entry.orchestration_meta_pipeline_id
            dag_run = DagRun.find(dag_id = head_dag_id,execution_date = logical_date)
            if dag_run:
                dag_run_type = dag_run[0].run_type
                update_entries.append({"pipeline_id":pipeline_id,"head_dag_id":head_dag_id,"logical_date":logical_date,"dag_run_type":dag_run_type})

        conn = engine.connect()
        conn.execute(text("DROP TABLE IF EXISTS orchestration_meta"))

        SDSOrchestrationMeta.create_table(session=session)
        for entry in update_entries:
            SDSOrchestrationMeta.insert_into_metatable(
                pipeline_id=entry.get("pipeline_id"),
                head_dag_id=entry.get("head_dag_id"),
                logical_date=entry.get("logical_date"),
                dag_run_type=entry.get("dag_run_type"),
                session=session
            )
