from dagfactory import DagFactory
import logging
from typing import Any, List

from airflow import DAG, AirflowException
from dagfactory import DagFactory
from itertools import chain
from pathlib import Path


from commons.pe.constants.airflow_constants import AirflowConfigurationConstants
from typing import Dict

from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
import os

SYSTEM_PARAMS: List[str] = ["default", "task_groups"]

class CustomDagFactory(DagFactory):

    def build_dags(self) -> Dict[str, DAG]:
        """
        Override the build_dags method to create SDSDAG objects instead of default DAGs.
        """
        # Step 1: Generate default DAGs using the parent method
        default_dags = super().build_dags()

        # Step 2: Replace each default DAG with an instance of SDSDAG
        custom_dags = {}
        for dag_id, dag in default_dags.items():
            if isinstance(dag, DAG):
                # Create SDSDAG using all attributes of the original DAG
                dag.__class__ = SDSPipelineDAG
                dag._callback_set = False
                custom_dags[dag_id] = dag
            else:
                custom_dags[dag_id] = dag  # Retain non-DAG objects as is

        return custom_dags

    def get_dag_configs(self) -> Dict[str, Dict[str, Any]]:
        """
        Returns configuration for each DAG in the factory, with task_default_args
        applied to all tasks in the DAG configuration. Task-specific configurations
        take precedence over task_defaults.

        :returns: dict with configuration for dags
        """

        updated_configs = {}

        for dag_id, dag_config in self.config.items():
            # Get task_defaults from dag_config
            task_defaults = dag_config.get('task_defaults', {})

            # Apply task_defaults to all tasks
            tasks = dag_config.get('tasks', [])
            for task_id,task in tasks.items():
                # Ensure task-specific settings take priority
                operator = task.get('operator')
                if ( task_defaults.get(operator)):
                    logging.info(f"Found default arguments for operator {operator}")
                    combined_config = {**task_defaults.get(operator), **task}  #  task values override defaults
                    task.clear()  # Clear the task dictionary
                    task.update(combined_config)  # Update with merged config

            # Store the updated dag_config
            updated_configs[dag_id] = dag_config

        # Exclude system parameters and return the updated configuration
        return {dag: config for dag, config in updated_configs.items() if dag not in SYSTEM_PARAMS}

def load_dags_from_yaml(
        globals_dict: Dict[str, Any],
        config_path: str,
        loader_name="QA_AUTOMATION",
        suffix=None,
):
    """
    Loads all the yaml/yml files in the dags folder

    The dags folder is defaulted to the airflow dags folder if unspecified.
    And the prefix is set to yaml/yml by default. However, it can be
    interesting to load only a subset by setting a different suffix.

    :param config_path: Folder path which will be appended with airflow shared filesystem base path
    :param globals_dict: The globals() from the file used to generate DAGs
    :dags_folder: Path to the folder you want to get recursively scanned
    :suffix: file suffix to filter `in` what files to scan for dags
    """
    config_base_path = os.getenv(key=AirflowConfigurationConstants.SHARED_VOLUME_PATH_ENV_VARIABLE,
                                 default="/opt/airflow/shared/")
    config_file_path = os.path.join(config_base_path, config_path)
    # chain all file suffixes in a single iterator

    logging.info(f"Loading DAGs for loader {loader_name} from %s", config_file_path)
    if suffix is None:
        suffix = [".yaml", ".yml"]
    candidate_dag_files = []
    for suf in suffix:
        candidate_dag_files = list(chain(candidate_dag_files, Path(config_file_path).rglob(f"*{suf}")))
    for config_file_path in candidate_dag_files:
        config_file_abs_path = str(config_file_path.absolute())
        logging.info("Loading %s", config_file_abs_path)
        try:
            factory = CustomDagFactory(config_file_abs_path)
            factory.generate_dags(globals_dict)
        except Exception as e:  # pylint: disable=broad-except
            logging.exception("Failed to load dag from %s", config_file_path)
            raise AirflowException(e)
        else:
            logging.info("DAG loaded: %s", config_file_path)
