import os
import socket

from commons.pe.constants.airflow_constants import DremioConstants
from commons.pe.logger_base import SDSLoggerBase


class DremioUtils(SDSLoggerBase):

    @staticmethod
    def get_dremio_cluster_ips():
        dremio_service_name = os.getenv("DREMIO_SERVICE_NAME")
        mode = os.getenv("MODE", "PROD")
        if mode == "PROD":
            dremio_hostnames = socket.gethostbyname_ex(dremio_service_name)[2]
            return [f"{DremioConstants.HTTP_SCHEME}://{hostname}:9047" for hostname in dremio_hostnames ]
        else:
            local_dremio_cluster_ips = os.getenv("DREMIO_CLUSTER_IPS", "").split(",")
            return local_dremio_cluster_ips
