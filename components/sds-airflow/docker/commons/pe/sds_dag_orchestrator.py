from datetime import datetime
from functools import partial
from typing import List

from airflow import plugins_manager
from airflow.models import ID_LEN, Base, Operator, MappedOperator
from airflow.models.base import COLLATION_ARGS
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils import timezone
from airflow.utils.log.logging_mixin import LoggingMixin
from airflow.utils.session import provide_session
from airflow.utils.sqlalchemy import UtcDateTime
from sqlalchemy import Column, Index, Integer, String, UniqueConstraint
from sqlalchemy.engine.reflection import Inspector
from sqlalchemy.orm import Session
from airflow.settings import TIMEZONE

from commons.pe.models.sds_dags import SDSDAG


class SDSOrchestrationMetaUtils:
    """Class containing util methods for mediator pattern orchestration"""

    @staticmethod
    def trigger_mediator(context):
        pipeline_id = context["dag"].pipeline_id
        from airflow.models import Variable
        pipeline_dependencies = Variable.get(key="SDS_ORCHESTRATOR_CONFIG",deserialize_json=True,default_var={}).get(pipeline_id,{}).get("dependencies",[])
        dependency_dag_ids = list(set([dependency_dag_id for dependency_config in
                                       pipeline_dependencies for
                                       dependency_dag_id in
                                       dependency_config.get("dependencies")]))
        if f"{context['dag'].dag_id}:{context['task'].task_id}" in dependency_dag_ids or not context["task"].downstream_task_ids:
            TriggerDagRunOperator(task_id="trigger_mediator", trigger_dag_id="mediator",
                                  execution_date=datetime.now().replace(tzinfo=TIMEZONE)).execute(context)

    @staticmethod
    def add_to_metatable(context):
        """Adds head dag entry to orchestration metatable if the task currently is the last one of the dag

        Args:
            context (Dict): Airflow DAG context object
        """
        if not context["task"].downstream_task_ids:
            SDSOrchestrationMeta.insert_into_metatable(
                logical_date=context["dag_run"].execution_date,
                head_dag_id=context["dag"].dag_id,
                pipeline_id=context["dag"].pipeline_id,
                dag_run_type=context["dag_run"].run_type
            )
        SDSOrchestrationMetaUtils.trigger_mediator(context)



class SDSOrchestrationMeta(Base, LoggingMixin):
    """
    DagRun describes an instance of a Dag. It can be created
    by the scheduler (for regular runs) or by an external trigger
    """

    __tablename__ = "orchestration_meta"
    id = Column(Integer, primary_key=True)
    logical_date = Column(UtcDateTime, default=timezone.utcnow, nullable=False)
    head_dag_id = Column(String(ID_LEN, **COLLATION_ARGS), nullable=False)
    pipeline_id = Column(String(ID_LEN), nullable=False)
    dag_run_type = Column(String(ID_LEN),nullable=False)

    __table_args__ = (
        Index(
            "logical_date_head_dag_id_pipeline_id",
            logical_date,
            head_dag_id,
            pipeline_id,
            dag_run_type
        ),
        UniqueConstraint(
            "pipeline_id",
            "head_dag_id",
            "logical_date",
            name="orchestration_meta_unique_key",
        ),
    )

    def __init__(self, head_dag_id, pipeline_id, logical_date,dag_run_type):
        self.head_dag_id = head_dag_id
        self.pipeline_id = pipeline_id
        self.logical_date = logical_date
        self.dag_run_type = dag_run_type
        super().__init__()

    @classmethod
    @provide_session
    def find_all(
            cls,
            session: Session = None,
    ) -> List["SDSOrchestrationMeta"]:
        """Returns all entries in the SDS orchestration metatable

        Returns:
            [List]: List of entries in the SDS Orchestration metatable
        """
        engine = session.get_bind(mapper=None, clause=None)
        inspector = Inspector.from_engine(engine)
        if "orchestration_meta" not in inspector.get_table_names():
            Base.metadata.create_all(engine)
            session.commit()
        qry = session.query(cls)
        return qry.order_by(SDSOrchestrationMeta.logical_date.asc()).all()

    @classmethod
    @provide_session
    def create_table(
            cls,
            session: Session = None,
    ):
        """Creates orchestration table if it doesn't exists

        Args:
            session (Session, optional): SQLAlchemy session. Defaults to None.
        """
        engine = session.get_bind(mapper=None, clause=None)
        inspector = Inspector.from_engine(engine)
        if "orchestration_meta" not in inspector.get_table_names():
            Base.metadata.create_all(engine)
        session.commit()

    @classmethod
    @provide_session
    def insert_into_metatable(
            cls, head_dag_id: str, pipeline_id: str, logical_date, dag_run_type:str, session: Session = None
    ):
        """Adds entry to the orchestration metatable

        Args:
            dag_run_type (str): Type of the DAG run, eg: backfill, scheduled, manual
            head_dag_id (str): Head dag id
            pipeline_id (str): Pipeline id
            logical_date (pendulum.DateTime): Logical date(data_interval_start)
            session (Session, optional): SQLAlchemy session. Defaults to None.
        """
        SDSOrchestrationMeta.delete(head_dag_id=head_dag_id,logical_date=logical_date,session=session)
        session.add(
            SDSOrchestrationMeta(
                head_dag_id=head_dag_id,
                pipeline_id=pipeline_id,
                logical_date=logical_date,
                dag_run_type=dag_run_type
            )
        )
        session.flush()

    @classmethod
    @provide_session
    def delete(cls, head_dag_id: str, logical_date, session: Session = None):
        """Deletes entry from orchestration metatable

        Args:
            head_dag_id (str): Head DAG id
            logical_date ([type]): Pipeline id
            session (Session, optional): SQLAlchemy session. Defaults to None.
        """
        session.query(cls).filter(
            cls.head_dag_id == head_dag_id, cls.logical_date == logical_date
        ).delete()
        session.commit()


def handle_custom_callback(callback_fn,custom_fn,context):
    callback_fn(context)
    custom_fn(context)

class SDSHeadDAG(SDSDAG):
    """
    Class that is to be used for instantiating first(head) DAG of every data processing pipeline
    """

    def __init__(self,pipeline_id: str, **kwargs):
        self.pipeline_id = pipeline_id
        super().__init__(**kwargs)
        if not self.default_args.get("on_success_callback", None):
            self.default_args.update({"on_success_callback": SDSOrchestrationMetaUtils.add_to_metatable})
        else:
            self.default_args.update({"on_success_callback": partial(handle_custom_callback,
                                                                     self.default_args.get("on_success_callback"),SDSOrchestrationMetaUtils.add_to_metatable)})


class SDSPipelineDAG(SDSDAG):
    """
    Class that is to be used for instantiating every member of the pipeline other than head dag
    """

    def __init__(self, pipeline_id: str, **kwargs):
        # plugins_manager.initialize_timetables_plugins()
        from manual_only_timetable import ManualOnlyTimetable
        self.pipeline_id = pipeline_id
        kwargs.pop("catchup", False)
        super().__init__(
            timetable=ManualOnlyTimetable(),catchup=False, **kwargs
        )
        self._callback_set = False

    def add_task(self, task: Operator) -> None:
        super().add_task(task)
        # New task is added, set the callback set to False since we need to add again
        self._callback_set = False

    @property
    def tasks(self) -> list[Operator]:
        tasks = super().tasks
        # If callback has not been set, assign it to the last task
        if tasks and not self._callback_set:
            self._assign_mediator_trigger_success_callback_to_tasks(tasks)
            self._callback_set = True
        return tasks
    
    def _assign_mediator_trigger_success_callback_to_tasks(self, tasks:list[Operator]):
        start_tasks = [task for task in tasks if not task.upstream_task_ids]
        # Find all non start tasks and assign mediator callback function as on_success_callback
        non_start_tasks = [task for task in tasks if task not in start_tasks and not isinstance(task, MappedOperator)]
        for task in non_start_tasks:
            if task.on_success_callback:
                task.on_success_callback = partial(handle_custom_callback, task.on_success_callback, SDSOrchestrationMetaUtils.trigger_mediator)
            else:
                task.on_success_callback = SDSOrchestrationMetaUtils.trigger_mediator

