import os

from airflow.models import DAG
import inspect
import copy
class SDSDAG(DAG):

    def __init__(self,**kwargs):
        default_args = kwargs.get("default_args",{})

        # Updating default_args dict to include email functionality on failure
        if os.getenv(key="AIRFLOW_EMAIL_NOTIFICATION_ENABLED",default="true").lower() == "true":
            email_receivers_str = os.getenv("AIRFLOW_EMAIL_NOTIFICATION_RECEIVERS","<EMAIL>")
            email_receivers = email_receivers_str.split(",")
            default_args.update({
                "email":email_receivers,
                "email_on_failure":True,
                "email_on_retry":os.getenv(key="AIRFLOW_EMAIL_ON_RETRY",default="false").lower() == "true"
            })

        # Updating user defined macros to support for rendering fields in spark/dbt orchestration operators in SDS
        user_defined_macros = kwargs.get("user_defined_macros") or {}
        from commons.pe.common_utils import Utils
        user_defined_macros.update({
            "str":str,
            "eval": eval,
            "os": os,
            "render_variable":Utils.render_variable,
        })
        kwargs["default_args"] = default_args
        kwargs["user_defined_macros"] = user_defined_macros
        kwargs["render_template_as_native_obj"] = True
        
        super().__init__(**kwargs)
