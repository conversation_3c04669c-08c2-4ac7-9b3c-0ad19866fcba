import json
import logging
import os
from functools import cached_property
from typing import Dict

import pendulum
import requests
from jinja2 import Template

from airflow.exceptions import AirflowException
from airflow.models.variable import Variable
from oauthlib.oauth2 import BackendApplicationClient
from prettytable import PrettyTable, ALL
from requests_oauthlib import OAuth2Session
from airflow.utils.session import provide_session
from sqlalchemy.orm import Session
from airflow.models.dagrun import DagRun

from commons.pe.constants.airflow_constants import AirflowConfigurationConstants
from commons.pe.constants.airflow_constants import MaintenanceJobConstants as MJC
from copy import deepcopy

class Utils:
    """
    Class that contains Util methods common to SDS orchestration
    """
    LOGGER = logging.getLogger("Utils")
    SSL_VERIFY = bool(os.getenv("SSL_VERIFY", default=False))

    @staticmethod
    def merge_dictionaries(parent_dict: Dict, child_dict: Dict):
        """
        Recursively merges two dictionaries on the same keys. The values in the 'parent_dict' are overridden by the
        values of the corresponding keys in 'child_dict'
        :param parent_dict:
        :param child_dict:
        :return:
        """
        child_dict = {} if not child_dict else child_dict
        parent_dict = {} if not parent_dict else parent_dict
        for key in child_dict:
            if key in parent_dict:
                if not isinstance(child_dict[key], Dict) and not isinstance(parent_dict[key], Dict):
                    parent_dict[key] = child_dict[key]
                else:
                    parent_dict[key] = Utils.merge_dictionaries(parent_dict[key], child_dict[key])

            else:
                parent_dict[key] = child_dict[key]

        return parent_dict

    @staticmethod
    def get_keycloak_token(headers=None, body=None):
        is_auth_enabled = os.getenv('IS_HTTP_AUTH_ENABLED', default=False)
        if is_auth_enabled:
            try:
                if body is None:
                    body = {}
                if headers is None:
                    headers = {}
                get_token_headers = {**{"Content-Type": "application/x-www-form-urlencoded"}, **headers}
                default_body = {
                    "client_id": os.getenv("KC_CLIENT_ID", "admin-cli"),
                    "grant_type": "client_credentials",
                    "client_secret": os.getenv("KC_CLIENT_SECRET", "admin-cli")
                }
                keycloak_host = os.getenv("KEYCLOAK_HOST")
                realm = os.getenv("KEYCLOAK_REALM")

                get_token_body = {**default_body, **body}
                response = requests.post(url=f"{keycloak_host}/realms/{realm}/protocol/openid-connect/token", data = get_token_body, headers=get_token_headers,verify=Utils.SSL_VERIFY)
                return f"Bearer {response.json().get('access_token')}"
            except Exception:
                Utils.LOGGER.error('Exception while getting Token')
                raise AirflowException('Failed to get token from endpoint')
        return None

    @staticmethod
    def get_oidc_token():
        """Returns OIDC token required for API authentication, obtained from Airflow secrets backend

        Raises:
            AirflowException: If token cannot be fetched

        Returns:
            [string]: OIDC token
        """
        is_auth_enabled = Variable.get(key='is_http_auth_enabled', default_var=False)
        if is_auth_enabled:
            try:
                sds_secrets = Variable.get(Variable.get('SDS_SECRET_ID', 'SDS_DEV_SECRETS'), deserialize_json=True)
                operator_conf = {'client_id_secret_key': 'OIDC_CLIENT_ID',
                                 'client_secret_secret_key': 'OIDC_CLIENT_SECRET'}
                client_id = sds_secrets[operator_conf.get('client_id_secret_key', 'OIDC_CLIENT_ID')]
                client_secret = sds_secrets[operator_conf.get('client_secret_secret_key', 'OIDC_CLIENT_SECRET')]
                oidc_url = Variable.get('OIDC_TOKEN_URL')
                client = BackendApplicationClient(client_id=client_id)
                oauth = OAuth2Session(client=client)
                token = oauth.fetch_token(token_url=oidc_url, client_id=client_id, client_secret=client_secret,verify=Utils.SSL_VERIFY).get(
                    'access_token')
                return '%s %s' % (operator_conf.get('token_header_prefix', 'Bearer'), token)
            except Exception:
                Utils.LOGGER.error('Exception while getting OIDC Token ')
                raise AirflowException('Failed to get token from OIDC endpoint')
        else:
            return None

    @staticmethod
    def generate_oidc_token():
        """Returns OIDC token required for API authentication, obtained from environment variables

        Raises:
            AirflowException: If token cannot be fetched

        Returns:
            [string]: OIDC token
        """
        is_auth_enabled = Variable.get(key='is_http_auth_enabled', default_var=False)
        if is_auth_enabled:
            try:
                operator_conf = {}
                client_id_value=os.getenv("OIDC_AUTH_CLIENT_ID","")
                client_secret_value=os.getenv("OIDC_AUTH_CLIENT_SECRET","")
                token_url_value=os.getenv("OIDC_AUTH_URL","")
                client = BackendApplicationClient(client_id=client_id_value)
                oauth = OAuth2Session(client=client)
                token = oauth.fetch_token(token_url=token_url_value, client_id=client_id_value,client_secret=client_secret_value).get('access_token')
                return '%s %s' % (operator_conf.get('token_header_prefix', 'Bearer'), token)
            except Exception:
                Utils.LOGGER.error('Exception while getting OIDC Token ')
                raise AirflowException('Failed to get token from OIDC endpoint')
        else:
            return None
    @staticmethod
    def tabular_print(data, field_names):
        """
        Function to print data in a tabular form
        """
        x = PrettyTable()
        x.hrules=ALL
        x.align="c"
        x.field_names = field_names
        x.add_rows(data)
        return x

    @staticmethod
    def write_data_to_shared_filesystem(data: Dict, file_path: str):
        """
        Function to write data as a dictionary to the shared filesystem as a single JSON object.
        The 'file_path' argument takes the relative path from the shared filesystem path and
        will create the folder if it doesnt exists
        :param data: Dict: Dict that contains the data to be written as a JSON object to the shared filesystem
        :param file_path: String: Absolute path where the payload file must be placed
        :return:
        """
        directory = os.path.dirname(file_path)
        os.makedirs(directory, exist_ok=True)
        file_name = os.path.basename(file_path)
        if not file_name.endswith(".json"):
            raise ValueError("'file_path' argument must be path to JSON file with '.json' extension")
        with open(file_path, "w+") as payload_file:
            json.dump(data, payload_file)

    @staticmethod
    def read_data_from_shared_filesystem(relative_file_path):
        """
        Function to read data stored in JSON file from the share filesystem and return it as a dictionary.
        If not file is found it returns a blank dictionary
        :param relative_file_path: String: Relative path from the shared filesystem where the JSON file is stored
        """
        shared_volume_path = os.getenv(key=AirflowConfigurationConstants.SHARED_VOLUME_PATH_ENV_VARIABLE, default="/opt/airflow/shared")
        try:
            payloads_file = open(os.path.join(shared_volume_path, relative_file_path), "r")
            parser_job_configs_for_all_sources = json.load(payloads_file)
        except FileNotFoundError:
            parser_job_configs_for_all_sources = dict()

        return parser_job_configs_for_all_sources
    
    @staticmethod
    def render_variable(variable, task_instance):
        """Renders Jinja templating over the 'variable' parameter passed to the function by applying templates 
        available in airflow and the user defined macros defined in a DAG

        Args:
            variable (string): Jinja format templated string
            task_instance (TaskInstance): Task instance object for a given task

        Returns:
            [string]: Returns templated object as a string
        """
        jn = Template(variable ,autoescape=True)
        macros = task_instance.get_template_context()['dag'].user_defined_macros
        return jn.render(**task_instance.get_template_context(), **macros)

    @staticmethod
    def cleanup_name(name:str) -> str:
        return "".join(char.lower() for char in name if char.isalnum())

    @staticmethod
    @provide_session
    def clear_dag_run_for_pipeline(session: Session = None):
        backfill_config = Variable.get(
            key="SDS_BACKFILL_CONFIG",
            default_var=None,
            deserialize_json=True
        )

        mediator_config = Variable.get(
            key="SDS_ORCHESTRATOR_CONFIG",
            default_var=None,
            deserialize_json=True
        )

        if not backfill_config:
            raise AirflowException("The backfill variable SDS_BACKFILL_CONFIG is not set")

        if not mediator_config:
            raise AirflowException("The mediator variable SDS_ORCHESTRATOR_CONFIG is not set")

        if not all(item in backfill_config.keys() for item in ['start_date', 'end_date', 'pipeline_id', 'dag_id']):
            raise AirflowException("The backfill config must contain keys {{'start_date', 'end_date', 'pipeline_id', "
                                   "'dag_id'}}")

        pipeline_dependency_dag_list = mediator_config.get("{pipeline_id}".format(**backfill_config)).get(
            "dependencies"
        )

        qry = session.query(DagRun)

        try:
            for each_dag in pipeline_dependency_dag_list:
                Utils.LOGGER.info("Deleting list of dag runs for {dependent_dag} between {start_date} and {end_date}".format(**each_dag, **backfill_config))
                dag_runs = qry.filter(
                    DagRun.dag_id == "{dependent_dag}".format(**each_dag),
                    DagRun.execution_date >= pendulum.parse("{start_date}".format(**backfill_config)),
                    DagRun.execution_date <= pendulum.parse("{end_date}".format(**backfill_config))
                )
                Utils.LOGGER.info(dag_runs.all())
                dag_runs.delete()
            session.commit()
        except Exception as error:
            session.rollback()
            Utils.LOGGER.exception(error)
            raise AirflowException(F"Error while deleting dag runs due to exception {error}")

    @classmethod
    def get_maintenance_configs(cls, interval_end: pendulum.DateTime):
        extra_args = []
        delete_orphan_config = Variable.get(key=MJC.ICEBERG_DELETE_ORPHAN_FILES_CONFIG, default_var={}, deserialize_json=True)
        older_than_timestamp = interval_end
        extra_args.extend([
            "--filesOlderThan",
            older_than_timestamp.subtract(hours=24).to_iso8601_string().split(".")[0].split('T')[0]
        ])
        final_config = deepcopy(delete_orphan_config)
        final_config["args"].extend(extra_args)

        return final_config

    @staticmethod
    def get_iceberg_compaction_maintenance_configs(params:Dict):
        extra_args = []
        compaction_config = Variable.get(key=MJC.ICEBERG_COMPACTION_TABLE_CONFIG, deserialize_json=True)
        extra_args.extend([
            "--table",
            params.get("table_name"),
            "--strategy",
            params.get("strategy"),
            "--options",
            params.get("options"),
            "--where",
            params.get("where"),
            "--sort_order",
            params.get("sort_order")
        ])
        final_config = deepcopy(compaction_config)
        final_config["args"].extend(extra_args)
        return final_config

    @classmethod
    def get_custom_configuration(cls, default_conf_var, job_conf_var, source_name, global_merge=None):
        if global_merge is None:
            global_merge = {}
        default_configuration = Variable.get(key=default_conf_var, deserialize_json=True, default_var={})
        custom_configuration = Variable.get(key=job_conf_var, deserialize_json=True, default_var={})
        if source_name not in custom_configuration:
            cls.LOGGER.info(f"Config for source '{source_name}' not found. Taking default configuration")
        merged_conf = cls.merge_dictionaries(default_configuration, custom_configuration.get(source_name, {}))
        for key, value in global_merge.items():
            final_value = cls.merge_dictionaries(
                parent_dict=Variable.get(key=value, default_var={}, deserialize_json=True),
                child_dict=merged_conf.get(key, {})
            )

            merged_conf[key] = final_value
        return merged_conf
