import pendulum
import logging
from airflow.configuration import conf
from datetime import datetime, timedelta
from dateutil import parser
from commons.pe.constants.airflow_constants import AirflowConfigurationConstants


class SDSDateUtils:
    """Class containing util methods relating to date functionality.
    """
    LOGGER = logging.getLogger("DateUtils")

    @classmethod
    def convert_iso_dt_string_to_local_dt(cls, date_time, timezone):
        """Converts a 'date_time' to local time specified in the timezone argument

        Args:
            date_time (pendulum.DateTime): pendulum datetime object
            timezone (string): timezone as string

        Returns:
            pendulum.DateTime: pendulum.DateTime object in local timezone provided by timezone argument
        """
        local_timezone = pendulum.timezone(timezone)
        local_time = local_timezone.convert(date_time)
        return local_time

    @classmethod
    def get_past_local_date_interval(cls, data_interval_start, unit: str, timezone: str = None):
        """Returns tuple of start and end date of a given interval specified by unit argument for a given time zone specified by timezone argument

        Args:
            data_interval_start (pendulum.DateTime): DateTime object with which the interval start and end is calculated
            unit (str): Data interval unit to be calculated. E.g. 'day', 'week', 'month', 'year'
            timezone (str, optional): Timezone to be used for the calculation. Defaults to None and uses whatever is specified in Airflow configuration file

        Raises:
            ValueError: If unit is not among the required values - 'day', 'week', 'month', 'year'

        Returns:
            [tuple]: Tuple containing the interval start and end dates in local time
        """
        if timezone is None:
            timezone = conf.get(section=AirflowConfigurationConstants.AIRFLOW_CONF_CORE_KEY,
                                key=AirflowConfigurationConstants.AIRFLOW_CONF_CORE_TIMEZONE_KEY)
            cls.LOGGER.info("Timezone received from airflow.cfg - '%s'" % (timezone))
        dag_execution_date_local_time = cls.convert_iso_dt_string_to_local_dt(data_interval_start, timezone)
        cls.LOGGER.info("DAG was executed at %s in local timezone %s" % (dag_execution_date_local_time, timezone))

        valid_units = ["day", "week", "month", "year"]
        if unit not in valid_units:
            raise ValueError(
                "Expected values for 'unit' - {} , instead received '{}'".format(",".join(valid_units), unit))

        return dag_execution_date_local_time.start_of(unit), dag_execution_date_local_time.end_of(unit)

    @classmethod
    def get_start_date(cls, data_interval_start, unit: str, timezone: str = None):
        """Returns start date of a given time interval specified by unit argument in a given timezone

        Args:
            data_interval_start (pendulum.DateTime): DateTime object with which the interval start is calculated
            unit (str): Data interval unit to be calculated. E.g. 'day', 'week', 'month', 'year'
            timezone (str, optional): Timezone to be used for the calculation. Defaults to None and uses whatever is specified in Airflow configuration file

        Returns:
            [pendulum.DateTime]: Start date of the unit interval
        """
        start_date = cls.get_past_local_date_interval(
            data_interval_start, unit, timezone
        )[0]
        return start_date.int_timestamp * 1000

    @classmethod
    def get_analysis_period_details(cls, data_interval_start, unit: str = "day", timezone: str = 'utc') -> dict:
        """
        Function to get year month day from data interval
        @return dictionary containing year,month,day,analysis_week start and end epochs
        """
        date_time = pendulum.from_timestamp(SDSDateUtils.get_end_date(data_interval_start, unit, timezone) // 1000)
        date = datetime.strptime(date_time.strftime("%y/%m/%d"), '%y/%m/%d')
        week_start = date - timedelta(days=date.weekday())
        week_end = week_start + timedelta(days=7)
        start_date = parser.parse(str(week_start) + ' ' + str.upper(timezone))
        end_date = parser.parse(str(week_end) + ' ' + str.upper(timezone))
        week_start_epoch = int(start_date.timestamp()) * 1000
        week_end_epoch = ((int(end_date.timestamp())) * 1000) - 1
        day_of_the_week = date.weekday() + 1
        year = date_time.year
        month = date_time.month
        day = date_time.day
        return {"year": year, "month": month, "day": day, "week_start_epoch": week_start_epoch,
                "week_end_epoch": week_end_epoch, "day_of_the_week": day_of_the_week}

    @classmethod
    def get_end_date(cls, data_interval_start, unit: str, timezone: str = None):
        """Returns end date of a given time interval specified by unit argument in a given timezone

        Args:
            data_interval_start (pendulum.DateTime): DateTime object with which the interval end is calculated
            unit (str): Data interval unit to be calculated. E.g. 'day', 'week', 'month', 'year'
            timezone (str, optional): Timezone to be used for the calculation. Defaults to None and uses whatever is specified in Airflow configuration file

        Returns:
            [pendulum.DateTime]: End date of the unit interval
        """
        end_date = cls.get_past_local_date_interval(
            data_interval_start, unit, timezone
        )[1]
        return end_date.int_timestamp * 1000 + 999


    @staticmethod
    def pendulum_to_datetime(pendulum_dt:pendulum.DateTime):
        return datetime.fromtimestamp(pendulum_dt.timestamp(),tz=pendulum_dt.tzinfo)
