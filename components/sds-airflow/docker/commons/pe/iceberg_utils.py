from functools import cached_property

from pyiceberg.catalog import load_catalog
from pyiceberg.exceptions import NoSuchTableError
from pyiceberg.table import Table


class IcebergUtils:

    def __init__(self, catalog_name: str = "default"):
        self.catalog_name = catalog_name

    def load_table(self, table_name: str) -> Table:
        if not self.check_table_exists(table_name):
            raise NoSuchTableError(f"Table {table_name} does not exist")
        return self.catalog.load_table(table_name)

    @cached_property
    def catalog(self):
        return load_catalog(self.catalog_name)

    def check_table_exists(self, table_name: str):
        table_split = table_name.split(".")
        if len(table_split) != 2:
            raise NameError(f"Table {table_name} is invalid. Please provide it as schema.table format")
        schema = table_split[0]
        table = table_split[1]
        tables_in_schema = self.catalog.list_tables(schema)
        return True if table in [identifier[1] for identifier in tables_in_schema] else False

    def get_table_schema(self, table_name: str):
        schema = self.load_table(table_name).schema()
        schema_dict = {column.name: column.field_type for column in schema.columns}
        return schema_dict
    
    def get_table_lists(self, schema):
        table_list = [table[1] for table in self.catalog.list_tables(schema)]
        return table_list

    def get_partition_spec(self, table_name: str):
        table = self.load_table(table_name=table_name)
        partition_spec = []
        for spec in table.metadata.partition_spec:
            transform = spec.get("transform", "identity")
            name = spec.get("name")
            if transform != "identity":
                name = name.rstrip(f"_{transform}")
            partition_spec.append({"name": name, "transform": transform})

        return partition_spec
