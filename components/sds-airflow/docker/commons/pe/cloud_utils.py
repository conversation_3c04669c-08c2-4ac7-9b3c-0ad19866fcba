from abc import ABC, abstractmethod
from urllib.parse import urlparse

import boto3
import pendulum
from airflow.models import Variable
from azure.core.paging import ItemPaged
from azure.identity import ManagedIdentityCredential
from azure.storage.blob import BlobProperties
from azure.storage.filedatalake import DataLakeServiceClient


class BaseCloudUtils(ABC):

    def urlparser(self, path:str):
        parsed = urlparse(path)
        if not parsed:
            raise ValueError(f"Unable to parse the path provided - {path}")
        return parsed

    @abstractmethod
    def list_files(self, storage_path: str, file_extension: str = None, modified_after: pendulum.DateTime = None,
                   modified_before: pendulum.DateTime = None, **kwargs):
        pass


class CloudFactory(BaseCloudUtils):
    def list_files(self, storage_path: str, file_extension: str = None, modified_after: pendulum.DateTime = None,
                   modified_before: pendulum.DateTime = None, **kwargs):
        return self.instance.list_files(storage_path, file_extension, modified_after, modified_before)

    def __init__(self, path: str):
        self.path = path
        self.parsed_url = self.urlparser(path)

        scheme = self.parsed_url.scheme
        if scheme.startswith("s3"):
            self.instance = AwsUtils()
        elif scheme.startswith("abfs"):
            self.instance = AzureUtils()


class AwsUtils(BaseCloudUtils):

    def list_files(self, storage_path: str, file_extension: str = None, modified_after: pendulum.DateTime = None,
                   modified_before: pendulum.DateTime = None, **kwargs):
        s3_client = boto3.client("s3")
        parsed_url = self.urlparser(storage_path)
        bucket_name = parsed_url.netloc
        prefix = parsed_url.path.lstrip("/")
        if not prefix.endswith("/"):
            prefix = prefix + "/"
        paginator = s3_client.get_paginator("list_objects_v2")
        file_extension = file_extension or ""
        modified_before = modified_before or pendulum.now(tz=pendulum.tz.UTC)
        modified_after = modified_after or pendulum.datetime(2000, 0, 0, 0, 0, 0)
        pages = paginator.paginate(Bucket=bucket_name, Prefix=prefix)
        files = [f"s3a://{bucket_name}/{obj['Key']}"
                 for page in pages
                 for obj in page.get("Contents", [])
                 if obj["StorageClass"] not in ("GLACIER", "DEEP_ARCHIVE")
                 and obj["Key"].endswith(file_extension)
                 and modified_after <= obj["LastModified"] < modified_before
                 ]

        return files


class AzureUtils(BaseCloudUtils):

    def parse_abfs_path(self, abfs_path: str):
        parsed_url = self.urlparser(abfs_path)
        netloc = parsed_url.netloc
        container = netloc.split("@")[0]
        storage_account = netloc.split("@")[1].split(".")[0]
        path = parsed_url.path.lstrip("/")
        if not path.endswith("/"):
            path = path + "/"

        return storage_account, container, path

    def list_files(self, storage_path: str, file_extension: str = None, modified_after: pendulum.DateTime = None,
                   modified_before: pendulum.DateTime = None, **kwargs):
        if not storage_path.startswith("abfs"):
            raise ValueError("Azure storage path must start with abfs://")
        storage_account, container, path = self.parse_abfs_path(storage_path)
        file_extension = file_extension or ""
        modified_before = modified_before or pendulum.now(tz=pendulum.tz.UTC)
        modified_before = pendulum.instance(modified_before)
        modified_after = modified_after or pendulum.datetime(2000, 0, 0, 0, 0, 0)
        modified_after = pendulum.instance(modified_after)
        account_url = f"https://{storage_account}.dfs.core.windows.net"
        client_id = Variable.get(key="AZURE_MANAGED_IDENTITY_CLIENT_ID")
        credential = ManagedIdentityCredential(client_id=client_id)
        service_client = DataLakeServiceClient(account_url=account_url,credential=credential)
        file_system_client = service_client.get_file_system_client(file_system=container)
        blobs: ItemPaged[BlobProperties] = file_system_client.get_paths(path=path, recursive=True)
        files = [
            f"abfs://{container}@{storage_account}.dfs.core.windows.net/{blob.name}"
            for blob in blobs
            if blob.name.endswith(file_extension) and
               modified_after <= pendulum.instance(blob.last_modified) < modified_before
        ]
        return files
