from __future__ import annotations

import os
from typing import Iterable

from airflow.providers.amazon.aws.hooks.sns import SnsHook
from commons.pe.logger_base import SDSLoggerBase

def send_sns_mail(
        to: list[str] | Iterable[str],
        subject: str,
        html_content: str,
        **kwargs
):
    logger = SDSLoggerBase()
    if to:
        logger.log.info(f"The emails present in {','.join(to)} will be ignored. The emails to be used as recepients must be configured in the SNS topic")
    conn_id = kwargs.get("conn_id") if "conn_id" in kwargs else "aws_default"
    sns = SnsHook(aws_conn_id=conn_id)

    sns.publish_to_target(
        target_arn=os.getenv(key="AWS_EMAIL_SNS_ARN"),
        message=html_content,
        subject=subject,
        message_attributes=None,
    )
