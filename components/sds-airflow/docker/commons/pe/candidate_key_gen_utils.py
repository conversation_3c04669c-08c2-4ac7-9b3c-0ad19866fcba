import json
from airflow.exceptions import AirflowFailException
from airflow.models.variable import Variable
from commons.pe.logger_base import SDSLoggerBase
from commons.pe.constants.airflow_constants import CandidateKeyGenConstants
import os


class CandidateKeyGenUtils(SDSLoggerBase):
    CANDIDATE_KEY_CONFIG_VARIABLE = CandidateKeyGenConstants.CANDIDATE_KEY_CONFIG_VARIABLE

    @staticmethod
    def prepare_job_config(**kwargs):

        if 'preprocess_out' not in kwargs['dag_run'].conf or 'hash_key' not in kwargs['dag_run'].conf:
            raise AirflowFailException("The necessary parameters are not provided")

        pre_processing_output = kwargs['dag_run'].conf.get('preprocess_out')
        hash_key = kwargs['dag_run'].conf.get('hash_key')
        
        candidate_key_config_variable = Variable.get(key=CandidateKeyGenUtils.CANDIDATE_KEY_CONFIG_VARIABLE, deserialize_json=True)

        api_base_url = os.getenv("DOMAIN_URL").rstrip("/")
        

        candidate_key_config_variable["args"].extend([
            "--pre-processing-output",
            json.dumps(pre_processing_output),
            "--hash-key",
            hash_key,
            "--api-url",
            f"{api_base_url}/sds_mgmnt/api/v1/config/"
        ])

        candidate_key_config_variable["app_name"] = f"sds-studio-ckbuilder_{hash_key[:5]}"

        

        return candidate_key_config_variable