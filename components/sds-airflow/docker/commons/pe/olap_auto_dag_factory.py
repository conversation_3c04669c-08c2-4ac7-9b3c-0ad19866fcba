import json
import os.path
from copy import deepcopy
from datetime import datetime
from typing import Dict, List

import pendulum
from airflow.models import BaseOperator
from airflow.models.baseoperator import chain
from airflow.models.dag import DAG
from airflow.utils.task_group import TaskGroup

from commons.pe.common_utils import Utils
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
from commons.pe.sds_autoparser_utils import AutoParserUtils

from airflow.operators.empty import EmptyOperator

from commons.pe.constants.airflow_constants import OlapAutoGenConstants, AirflowConfigurationConstants
from commons.pe.druid_utils import DruidUtils
from commons.pe.sds_date_utils import SDSDateUtils


class OlapDAGFactory:
    DEFAULT_RDBMS_AIRFLOW_VARIABLE = "rdbms_default_spark_job_config"
    CUSTOM_RDBMS_AIRFLOW_VARIABLE = "rdbms_custom_spark_job_config"
    RDBMS_JOB_CONFIG_FILES_BASE_PATH = os.getenv("RDBMS_LOADER_SPARK_JOB_CONFIGS_BASE_BATH","")

    @classmethod
    def create_dag(
            cls, dag_name, pipeline_id, tags, default_dag_args):
        """
        params:
            dag_name(str): dag_id
            tags(List): list of tag names associated with dag
        returns:
            DAG object
        """

        dag_args = {
            'start_date': datetime(2020, 1, 1),
            "catchup": False,
            "user_defined_macros": {"json": json, "render_variable": Utils.render_variable, "pendulum": pendulum,
                                    "get_custom_druid_config": DruidUtils.get_configs_after_variable_merge,
                                    "get_end_date": SDSDateUtils.get_end_date,
                                    "get_final_config": OlapDAGFactory.get_rdbms_final_spark_config},
            "tags": tags,
            "render_template_as_native_obj": True
        }
        dag_args.update(default_dag_args)
        return SDSPipelineDAG(dag_id=dag_name, pipeline_id=pipeline_id, **dag_args)

    @classmethod
    def create_task_group(cls, task_group_name: str, task_list: List[Dict[str, str]], dag_id: str):
        with TaskGroup(group_id=f"execute_module_{task_group_name}") as tg:
            indexing_task_list = []
            for task in task_list:
                source_indexing_tasks=[]
                indexing_config_key = task.get("config_key")
                olap_engine = task.get("olap_engine","druid")

                if task.get("source_type", None) == "iceberg" and olap_engine == "druid":
                    iceberg_to_parquet_task = SparkOperatorFactory.get(
                        task_id=f"{indexing_config_key}_iceberg_to_parquet",
                        from_conf=f"get_custom_druid_config('{OlapAutoGenConstants.DRUID_INDEXING_DEFAULT_SPARK_JOB_CONFIG}','{OlapAutoGenConstants.DRUID_INDEXING_CUSTOM_SPARK_JOB_CONFIG}','{indexing_config_key}')"
                    )
                    source_indexing_tasks.append(iceberg_to_parquet_task)

                indexing_task = OlapDAGFactory.get_olap_indexing_task(task, dag_id)
                source_indexing_tasks.append(indexing_task)

                if task.get("indexing_operation", None) == "overwrite" and olap_engine=="druid":
                    source_indexing_tasks.append(DruidUtils.delete_olap_operator_sensors(f"{indexing_config_key}_delete"))

                for index in range(len(source_indexing_tasks)-1):
                    source_indexing_tasks[index] >> source_indexing_tasks[index+1]
                indexing_task_list.append(source_indexing_tasks[0])

            chain(indexing_task_list)

        return tg

    @staticmethod
    def get_olap_indexing_task(task_config: Dict[str, str], dag_id:str):
        source_name = task_config.get("config_key")
        olap_engine = task_config.get("olap_engine","druid")
        if olap_engine == "rdbms":
            return SparkOperatorFactory.get(
                task_id=f"{task_config.get('config_key')}_rdbms_index",
                from_conf=f"get_final_config('{source_name}')"
            )
        elif olap_engine=="druid":
            return DruidUtils.create_olap_operators_sensor(
                    druid_indexing_config_variable=dag_id.replace("dag", "config"),
                    indexing_config_key=source_name
                )


    @staticmethod
    def get_rdbms_final_spark_config(source_name: str):
        merged_configs = Utils.get_custom_configuration(
            default_conf_var=OlapDAGFactory.DEFAULT_RDBMS_AIRFLOW_VARIABLE,
            job_conf_var=OlapDAGFactory.CUSTOM_RDBMS_AIRFLOW_VARIABLE,
            source_name=source_name
        )
        config_path = os.path.join(OlapDAGFactory.RDBMS_JOB_CONFIG_FILES_BASE_PATH, f"{source_name}.json")
        extra_args = ["--config-path", config_path]
        final_config = deepcopy(merged_configs)
        final_config["args"].extend(extra_args)
        final_config["app_name"] = f"{source_name}_rdbms_index"

        return final_config

    @classmethod
    def add_tasks_to_dag(cls, dag: DAG, task_configs: List[Dict[str, List[Dict[str, str]]]]):
        with dag as dag:
            start_task = EmptyOperator(task_id="start_tasks")
            end_task = EmptyOperator(task_id="end_tasks")
            task_groups = [
                cls.create_task_group(task_group_name=config.get("task_group_name"), task_list=config.get("job_list"),
                                      dag_id=dag.dag_id)
                for config in task_configs]
            start_task >> task_groups[0]
            task_groups[-1] >> end_task
            for index, tg in enumerate(task_groups[:-1]):
                task_groups[index] >> task_groups[index + 1]
        return dag

    @classmethod
    def get_airflow_dag(
            cls, dag_id, tasks, tags, pipeline_id, default_dag_args=None
    ):
        """
        The actual method that has to be called by a DAG file to get the dag.
        params:
            dag_id(str): dag_id
            tasks(dict): task list associated with dag
            tags(List): list of tag names associated with dag
            pipeline_id(str):name of pipeline_id
        returns:
            DAG object
        """
        if default_dag_args is None:
            default_dag_args = {}
        if not tasks:
            return
        dag = cls.create_dag(
            dag_id,
            tags=tags + ["OLAP"],
            pipeline_id=pipeline_id,
            default_dag_args=default_dag_args
        )
        dag = cls.add_tasks_to_dag(dag, tasks)
        return dag
