from airflow.exceptions import AirflowSkipException
from airflow.providers.cncf.kubernetes.operators.pod import KubernetesPodOperator
from kubernetes.client import models as k8s_models
from airflow.providers.cncf.kubernetes.secret import Secret
from airflow.operators.trigger_dagrun import TriggerDagRunOperator

from commons.pe.qa_automation.qa_automation_constants.constants import QAAutomationConstants
from plugins.pe.sds_performane_test_plugin.sds_performance_test_kubernetes_operator import \
    SDSPerformanceTestKubernetesOperator
import os

selenium_username = Secret("env", "SELENIUM_USERNAME", "airflow-secret-vault", "SELENIUM_USERNAME")
selenium_pwd = Secret("env", "SELENIUM_PASSWORD", "airflow-secret-vault", "SELENIUM_PASSWORD")
kc_client_id = Secret("env", "KC_CLIENT_ID", "airflow-secret-vault", "kc_client_id")
kc_client_secret = Secret("env", "KC_CLIENT_SECRET", "airflow-secret-vault", "kc_client_secret")
git_password = Secret("env", "GIT_PASSWORD", "airflow-secret-vault", "GIT_PASSWORD")


class UserBehaviourUtils:

    def create_pods(task, config):
        return KubernetesPodOperator(
            env_vars=[
                k8s_models.V1EnvVar(name="MANAGEMENT_API_UI_MAPPING_ENDPOINT",
                                    value=os.getenv('MANAGEMENT_API_UI_MAPPING_ENDPOINT')),
                k8s_models.V1EnvVar(name="KEYCLOAK_HOST",
                                    value=os.getenv('KEYCLOAK_HOST')),
                k8s_models.V1EnvVar(name="KEYCLOAK_REALM",
                                    value=os.getenv('KEYCLOAK_REALM')),
                k8s_models.V1EnvVar(name="SELENIUM_SDS3_URL",
                                    value=os.getenv('SELENIUM_SDS3_URL'))
            ],
            namespace=f"{{{{var.json.{QAAutomationConstants.USER_BEHAVIOUR_RECORDER_VARIABLE}.get('namespace') }}}}",
            image=f"{{{{var.json.{QAAutomationConstants.USER_BEHAVIOUR_RECORDER_VARIABLE}.get('image') }}}}",
            cmds=["/bin/sh", "-c"],
            arguments=[task['command']],
            name=task['job_name'],
            task_id=task['job_name'],
            secrets=[selenium_username, selenium_pwd, kc_client_id, kc_client_secret, git_password],
            is_delete_operator_pod=True,
            hostnetwork=False,
            in_cluster=True,
            startup_timeout_seconds=10000,
            image_pull_secrets="docker-secret",
            annotations={"sidecar.istio.io/inject": "false"}
        )

    @staticmethod
    def generate_analytical_tasks(configs, job):
        if job == "user_behaviour_recorder":
            tasks = [UserBehaviourUtils.create_pods(tasks, configs) for tasks in
                     configs.get("user_behaviour_recorder_configs", [])]
            return tasks

    @staticmethod
    def check_if_perf_test_must_run(**kwargs):
        if kwargs["params"]["run_load_test"]:
            TriggerDagRunOperator(task_id="trigger_performance_test",
                                  trigger_dag_id="sds_load_script_executor_dag", conf=kwargs["params"]).execute(
                context=kwargs)
        else:
            raise AirflowSkipException("Skipping performance test run")

    @staticmethod
    def execute_locust_test(endpoint, runtime, input_path, output_path, users):
        output_path_within_pod = "output/results"
        return SDSPerformanceTestKubernetesOperator(
            env_vars=[
                k8s_models.V1EnvVar(name="KEYCLOAK_HOST",
                                    value=os.getenv('KEYCLOAK_HOST')),
                k8s_models.V1EnvVar(name="KEYCLOAK_REALM",
                                    value=os.getenv('KEYCLOAK_REALM')),
                k8s_models.V1EnvVar(name="IS_INSIGHTS_AUTH_ENABLED",
                                    value="\""+str(os.getenv('IS_INSIGHTS_AUTH_ENABLED'))+"\""),
                k8s_models.V1EnvVar(name="IS_CACHE_ENABLED",
                                    value="\""+str(os.getenv('IS_CACHE_ENABLED'))+"\"")
            ],
            namespace=f"{{{{var.json.{QAAutomationConstants.LOAD_SCRIPT_EXECUTOR_VARIABLE}.get('namespace') }}}}",
            image=f"{{{{var.json.{QAAutomationConstants.LOAD_SCRIPT_EXECUTOR_VARIABLE}.get('image') }}}}",
            cmds=["/bin/sh", "-c"],
            args=f"cd /opt/sds-pe-deployment-utils;locust -f /opt/sds-pe-deployment-utils/load_script_generator/load_script_user_creation.py --input-path /input/queries/ --endpoint {endpoint} --host {os.getenv('KEYCLOAK_HOST')} --users {users} --csv {output_path_within_pod} --run-time {runtime} --autostart --autoquit 2 ; ",
            name="execute-load-script",
            task_id="EXECUTE_LOAD_SCRIPT",
            secrets=[kc_client_id, kc_client_secret],
            is_delete_operator_pod=True,
            hostnetwork=False,
            in_cluster=True,
            startup_timeout_seconds=1000,
            input_path=input_path,
            output_path=output_path,
            init_container_image=f"{{{{var.json.{QAAutomationConstants.LOAD_SCRIPT_EXECUTOR_VARIABLE}.get('init_container_image',{{}}) }}}}",
            annotations=f"{{{{var.json.{QAAutomationConstants.LOAD_SCRIPT_EXECUTOR_VARIABLE}.get('annotations',{{}}) }}}}",
            service_account_name="default"

        )
