import logging
import os

from airflow.providers.cncf.kubernetes.operators.pod import KubernetesPodOperator
from kubernetes.client import models as k8s_models
from airflow.providers.cncf.kubernetes.secret import Secret
from kubernetes.client import models as k8s

class QAAutomationUtils:
    """
    Class that contains Util methods common to SDS orchestration
    """
    LOGGER = logging.getLogger(__name__)

    @staticmethod
    def create_pods(task, config):
        secret_env1 = Secret("env", "SELENIUM_USERNAME", "airflow-secret-vault", "SELENIUM_USERNAME")
        secret_env2 = Secret("env", "SELENIUM_PASSWORD", "airflow-secret-vault", "SELENIUM_PASSWORD")
        secret_env3 = Secret("env", "KC_CLIENT_ID", "airflow-secret-vault", "kc_client_id")
        secret_env4 = Secret("env", "KC_CLIENT_SECRET", "airflow-secret-vault", "kc_client_secret")
        secret_env5 = Secret("env", "AIRFLOW_API_USERNAME", "airflow-secret-vault", "airflowAdminUsername")
        secret_env6 = Secret("env", "AIRFLOW_API_PASSWORD", "airflow-secret-vault", "airflowAdminPassword")
        is_nifi_ssl_verify_enabled = "\""+str(os.getenv('IS_NIFI_SSL_VERIFY_ENABLED','false'))+"\""
        is_nifi_auth_enabled = "\""+str(os.getenv('IS_NIFI_AUTH_ENABLED','true'))+"\""
        tls_crt_secret_provider_volume = k8s.V1Volume(
            name='tls-crt',
            secret=k8s.V1SecretVolumeSource(secret_name='nifi-api-secret', items=[k8s.V1KeyToPath(key="tls.crt", path="tls.crt")])
        )
        tls_key_secret_provider_volume = k8s.V1Volume(
            name='tls-key',
            secret=k8s.V1SecretVolumeSource(secret_name='nifi-api-secret', items=[k8s.V1KeyToPath(key="tls.key", path="tls.key")])
        )
        tls_crt_secret_provider_volume_mount = k8s.V1VolumeMount(name='tls-crt', mount_path='/opt/tls.crt', sub_path='tls.crt', read_only=True)
        tls_key_secret_provider_volume_mount = k8s.V1VolumeMount(name='tls-key', mount_path='/opt/tls.key', sub_path='tls.key', read_only=True)
        return KubernetesPodOperator(
            namespace=config['namespace'],
            image=config['image'],
            env_vars=[
                k8s_models.V1EnvVar(name="POD_IP",value_from=k8s_models.V1EnvVarSource(field_ref=k8s_models.V1ObjectFieldSelector(field_path="status.podIP")),),
                k8s_models.V1EnvVar(name="NAMESPACE", value=config.get('namespace')),
                k8s_models.V1EnvVar(name="IMAGE", value=config.get('image')),
                k8s_models.V1EnvVar(name="UPDATED_AT", value="{{data_interval_end.end_of(str('day')).to_datetime_string()}}.999"),
                k8s_models.V1EnvVar(name="SELENIUM_SDS3_URL", value=os.getenv('SELENIUM_SDS3_URL')),
                k8s_models.V1EnvVar(name="NIFI_CERT_PATH", value=os.getenv('NIFI_CERT_PATH','/opt/tls.key')),
                k8s_models.V1EnvVar(name="NIFI_KEY_PATH", value=os.getenv('NIFI_KEY_PATH','/opt/tls.key')),
                k8s_models.V1EnvVar(name="IS_NIFI_SSL_VERIFY_ENABLED", value=is_nifi_ssl_verify_enabled),
                k8s_models.V1EnvVar(name="IS_NIFI_AUTH_ENABLED", value=is_nifi_auth_enabled),

                k8s_models.V1EnvVar(name="AIRFLOW_API_ENDPOINT", value=os.getenv('AIRFLOW_API_ENDPOINT')),
                k8s_models.V1EnvVar(name="NIFI_API_URL", value=os.getenv('NIFI_API_URL')),
                k8s_models.V1EnvVar(name="NIFI_REGISTRY_API_URL", value=os.getenv('NIFI_REGISTRY_API_URL')),
                k8s_models.V1EnvVar(name="S3_DATALAKE_BUCKET_NAME", value=os.getenv('S3_DATALAKE_BUCKET_NAME')),
                k8s_models.V1EnvVar(name="KEYCLOAK_HOST", value=os.getenv('KEYCLOAK_HOST')),
                k8s_models.V1EnvVar(name="KEYCLOAK_REALM", value=os.getenv('KEYCLOAK_REALM')),
                k8s_models.V1EnvVar(name="AWS_REGION", value=os.getenv('AWS_REGION'))
                ],
            cmds=["/bin/sh","-c"],
            arguments=[task['command']],
            name=task['job_name'],
            task_id=task['job_name'],
            volumes=[tls_key_secret_provider_volume, tls_crt_secret_provider_volume],
            volume_mounts=[tls_key_secret_provider_volume_mount,tls_crt_secret_provider_volume_mount],
            secrets = [secret_env1, secret_env2, secret_env3, secret_env4,secret_env5,secret_env6],
            is_delete_operator_pod=True,
            hostnetwork=False,
            in_cluster=True,
            startup_timeout_seconds=1000,
            image_pull_secrets=config['image_pull_secrets'],
            annotations={"sidecar.istio.io/inject": "false"}
        )

    @staticmethod
    def generate_analytical_tasks(configs, job):
        array_tasks = [QAAutomationUtils.create_pods(tasks, configs) for tasks in configs[job]]
        return array_tasks