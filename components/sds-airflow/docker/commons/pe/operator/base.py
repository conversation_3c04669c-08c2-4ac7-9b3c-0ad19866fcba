from abc import ABC, abstractmethod
from enum import Enum
import json
from typing import Any, ClassVar, Type, Optional, Dict, Sequence, TypedDict, NotRequired

from airflow.configuration import conf
from airflow.exceptions import AirflowFailException, AirflowException
from airflow.lineage import prepare_lineage
from airflow.models.taskreschedule import TaskReschedule
from airflow.models.variable import Variable
from airflow.sensors.base import BaseSensorOperator, PokeReturnValue
from airflow.utils.context import Context

from commons.pe.common_utils import Utils
from commons.pe.triggers.base import SDSBaseTrigger


class TaskStatus(str, Enum):
    FAILED = "failed"
    SUCCESS = "success"
    RUNNING = "running"


class PokeXcomVal(TypedDict):
    """
    Type for the xcom_value attribute of PokeReturnValue
    """
    status: TaskStatus
    message: str
    event_payload: Any
    metadata: NotRequired[dict]


class SDSBaseOperator(ABC, BaseSensorOperator):
    # Defines the trigger class being used in this operators
    trigger_class: ClassVar[Type[SDSBaseTrigger]]

    # Override in this format for adding template fields to child classes
    template_fields: Sequence[str] = ("from_var", "from_conf",)

    # Defines attributes that have a variable with globally defined values
    global_attributes = {}

    def __init__(
            self,
            from_var: str = None,
            from_conf: dict = None,
            deferrable: bool = conf.getboolean("operators", "default_deferrable", fallback=False),
            **kwargs
    ):
        self.from_conf = from_conf
        self.from_var = from_var
        self.deferrable = deferrable
        if "mode" not in kwargs:
            kwargs["mode"] = "reschedule"  # change the default value for mode to reschedule
        super().__init__(**kwargs)

        # Handle attribute value assignment from variable or conf
        params = [element for element in self.template_fields if element not in ("from_conf", "from_var")]
        if self.from_conf is None and self.from_var is None:
            return
        attr_values_from = "conf" if self.from_conf else "var"
        for param in params:
            self._set_attribute(attr_name=param, value_from=attr_values_from)

    def _set_attribute(self, attr_name: str, value_from: str):
        # Get attribute value set in constructor
        attr_val = getattr(self, attr_name, None)

        default_attr_val = f"'{attr_val}'" if isinstance(attr_val, str) else attr_val
        # Assign jinja template for attribute value based on whether to take from conf or variable
        if value_from == "conf":
            value = f"{{{{eval(render_variable(str({self.from_conf}),ti)).get('{attr_name}',{default_attr_val})}}}}"
        else:
            value = f"{{{{ render_variable(str(var.json.{self.from_var}.get('{attr_name}',{default_attr_val})),task_instance) }}}}"
        setattr(self, attr_name, value)

    def execute(self, context: Context) -> Any:
        self.prepare(context)  # Prepare the job conf
        if self.deferrable:
            # Not calling poke, since we are deferring
            self.execute_async(context)
        else:
            # Call the normal execute method with synchronous logic
            self.execute_sync(context)

    def execute_sync(self, context: Context) -> Any:
        if self.reschedule:  # Count poke number from task reschedule
            task_reschedules = TaskReschedule.find_for_task_instance(context['ti'])
            poke_number = len(task_reschedules) + 1
        else:  # Get poke number from xcom
            poke_number = context["task_instance"].xcom_pull(key="poke_number", default=1)

        if poke_number == 1:
            # Submit the job once on first poke
            self.log.info(f"Poke number {poke_number} with interval start {context['data_interval_start']}")
            self.submit(context)

        # Execute the sensor
        xcom_value: PokeXcomVal = BaseSensorOperator.execute(self, context=context)
        self.on_operator_complete(context=context, event=xcom_value.get("event_payload"))

    def poke(self, context: Context) -> bool | PokeReturnValue:
        trigger_params = self._get_trigger_params(context=context)
        trigger = self.trigger_class(**trigger_params)
        event = trigger.sync_run()
        status = event.payload.get("status")
        if not status:
            raise AirflowException(
                f"Payload of trigger event for sync_run method of trigger {trigger.__class__.__name__} does not have key 'status'")

        if status == TaskStatus.RUNNING:  # Return unfinished poke return value
            return PokeReturnValue(is_done=False, xcom_value=None)
        else:
            # Sensor operation complete, return the terminal poke return value
            xcom_value: PokeXcomVal = {
                "status": status,
                "message": event.payload.get("message"),
                "event_payload": event.payload
            }
            return PokeReturnValue(is_done=True, xcom_value=xcom_value)

    def execute_async(self, context: Context):
        if self.trigger_class is None:
            raise AirflowFailException(f"'trigger_class' not defined for class {self.__class__.__name__}")
        self.submit(context)
        trigger_params = self._get_trigger_params(context=context)
        self.defer(
            trigger=self.trigger_class(**trigger_params),
            method_name="on_operator_complete"
        )

    @prepare_lineage
    def pre_execute(self, context: Any):
        if self.from_conf and self.from_var:
            self.log.warning(
                f"'from_conf' and 'from_var' are both set. "
                f"Configuration present in Variable {self.from_var} wont be used.")

        for attr, var_key in self.global_attributes.items():
            global_var: dict = Variable.get(key=var_key, default_var={}, deserialize_json=True)
            global_var = json.loads(Utils.render_variable(json.dumps(global_var), context["ti"]))
            custom_var = getattr(self, attr)
            final_attribute_value = Utils.merge_dictionaries(global_var, custom_var)
            setattr(self, attr, final_attribute_value)

    @abstractmethod
    def on_operator_complete(self, context: Context, event: Optional[Dict[str, Any]]):
        """
        Method called after the operator task is done. This will run after the trigger is complete in async mode
        and after the check method in sync mode
        """
        pass

    @abstractmethod
    def _get_trigger_params(self, context: Context, **kwargs) -> dict[str, Any]:
        """
        Get parameters to pass to the trigger class. Child classes must override this
        based on the trigger_class being defined
        """
        pass

    @abstractmethod
    def prepare(self, context: Context):
        """
        Runs at the start of execute() method
        """
        pass

    @abstractmethod
    def submit(self, context: Context):
        """
        Submits the job or task based on the output for preprocess function
        """
        pass


class BaseJobOperator(SDSBaseOperator, ABC):
    template_fields: Sequence[str] = SDSBaseOperator.template_fields + ("app_name", "labels")

    def __init__(
            self,
            app_name: str = "default",
            labels: dict = None,
            append_date: bool = True,
            force_replace_existing: bool = False,
            **kwargs

    ):
        self.app_name = app_name
        self.append_date = append_date
        self.labels = labels or {}
        self.force_replace_existing = force_replace_existing

        # Job submit body which inheriting classes can override to create the conf
        # that can be used to submit the job
        self.job_submit_body: dict = {}
        super().__init__(**kwargs)

    @prepare_lineage
    def pre_execute(self, context: Any):
        if self.append_date:
            dag_run_type_prefix = context["dag_run"].run_type[0]
            dagrun_execution_date = context["data_interval_start"].strftime("%Y-%m-%d-%H-%M-%S")
            self.app_name = f"{self.app_name}-{dagrun_execution_date}-{dag_run_type_prefix}"
        self.labels.update({"job_name": self.app_name})

        super().pre_execute(context)
