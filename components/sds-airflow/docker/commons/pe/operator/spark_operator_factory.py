import os
from airflow.configuration import conf
from commons.pe.common_utils import Utils
from plugins.pe.aws.operators.emr import EmrServerlessSparkOperator
from plugins.pe.kubernetes.operators.spark_kubernetes import SparkKubernetesOperator
from plugins.pe.spark.base import SparkOperatorBase


class OperatorNotFoundException(Exception):
    pass


class SparkOperatorFactory:
    TASK_MAP_FILE = "spark_factory/spark_task_map.json"

    operator_map = {
        "KUBERNETES": SparkKubernetesOperator,
        "EMR-SERVERLESS": EmrServerlessSparkOperator
    }

    @classmethod
    def get_operator(cls, key: str = None):
        if not key:
            key = os.getenv(key="SPARK_OPERATOR_TYPE", default="KUBERNETES")
        # operator_map = {operator.operator_key(): operator for operator in OPERATOR_LIST}
        if key in cls.operator_map:
            return cls.operator_map.get(key)
        else:
            raise OperatorNotFoundException(
                f"Operator with key {key} not found. Either Operator key is invalid, operator does not exist, or the operator has not been registered in the SparkOperatorFactory")

    @classmethod
    def get(cls, task_id: str, **kwargs) -> SparkOperatorBase:
        task_map = Utils.read_data_from_shared_filesystem(relative_file_path=cls.TASK_MAP_FILE)
        deferrable_default = os.getenv("SPARK_OPERATOR_DEFAULT_DEFERRABLE", str(conf.getboolean("operators", "default_deferrable", fallback=False))).lower()
        deferrable_default = True if deferrable_default.lower() == "true" else False
        deferrable = kwargs.pop("deferrable", deferrable_default)
        key = task_map.get(task_id)
        operator = cls.get_operator(key=key)
        return operator(task_id=task_id,deferrable=deferrable, **kwargs)

    @staticmethod
    def get_default():
        return SparkKubernetesOperator
