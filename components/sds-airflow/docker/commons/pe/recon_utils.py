import json
import logging
import os.path
from functools import cached_property
from itertools import chain

import pendulum
import requests
from airflow.models import Variable
from airflow.providers.http.hooks.http import HttpHook

from commons.pe.common_utils import Utils
from commons.pe.constants.airflow_constants import (
    AutoParserConstants as AP
)
from commons.pe.sds_autoparser_utils import AutoParserUtils


class ReconException(Exception):
    pass


class ReconUtils:
    LOGGER = logging.getLogger(__name__)

    def __init__(self,
                 source: str,
                 conn_id: str = AP.RECON_REST_CONN_ID,
                 recon_type: str = "data-lake"
                 ):
        self.conn_id = conn_id
        self.recon_type = recon_type
        self.source = source

    @cached_property
    def url(self):
        hook = HttpHook(http_conn_id=AP.RECON_REST_CONN_ID)
        hook.get_conn()
        url = os.path.join(hook.base_url, f"sds_mgmnt/api/v1/recon/{self.recon_type}")
        return url

    @cached_property
    def headers(self):
        return {
            "Content-Type": "application/json",
            "Authorization": Utils.get_keycloak_token()
        }

    def get_params(self, start_time: str, end_time: str, data_model_type: str):
        time_prefix = "index_time_" if self.recon_type == "data-lake" else "event_timestamp_"
        params = {
            f"{time_prefix}start_date": start_time,
            f"{time_prefix}end_date": end_time,
            "data_source_name": self.source,
            "data_model_type": data_model_type
        }
        return params

    @staticmethod
    def populate_recon_ticket_data(*args, **context):
        """Function to generate payload for recon ticket.Payload is populated with the following fields source_name,
        datasource_count, srdm_count,start_time,end_time, created date, dag run id and reconciliation type. """
        source_name = args[0]
        recon_type = args[2]
        recon_status = args[1]
        srdm_count = context["ti"].xcom_pull(task_ids='get_srdm_count')
        datasource_count = context["ti"].xcom_pull(task_ids='get_splunk_count')
        start_time = context["data_interval_start"].to_iso8601_string().split(".")[0]
        end_time = context["data_interval_end"].to_iso8601_string().split(".")[0]
        dag_id = context["dag"].dag_id
        payload = {"data_source": source_name, "target_record_count": datasource_count,
                   "source_record_count": srdm_count,
                   "event_timestamp_start": start_time, "event_timestamp_end": end_time, "created_at": end_time,
                   "dag_run_id": dag_id, "recon_type": recon_type, "recon_status": recon_status}
        context["ti"].xcom_push(key="recon_ticket_payload", value=json.dumps(payload))

    @staticmethod
    def generate_ticket(*args, **context):
        """Function to compare the srdm and source count.Ticket is generated with status as new if the error
        percentage is greater than the preset error percentage else ticket with status as done is populated.

            """
        srdm_count = context["ti"].xcom_pull(task_ids='get_srdm_count')
        datasource_count = context["ti"].xcom_pull(task_ids='get_splunk_count')
        error_percent = args[1]
        if srdm_count == datasource_count:
            ds_error_percent = 0
        elif datasource_count == 0:
            ds_error_percent = 100
        else:
            ds_error_percent = ((datasource_count - srdm_count) / datasource_count * 100)
        if abs(ds_error_percent) > error_percent:
            return 'populate_new_ticket_payload'
        else:
            return 'populate_done_ticket_payload'

    def get_recon_count(self, start_time: str, end_time: str, data_model_type: str):
        params = self.get_params(start_time, end_time, data_model_type)
        response = requests.get(url=self.url, params=params, headers=self.headers)
        if response.status_code != 200:
            raise ReconException(f"Failed to list recon records for {self.recon_type} recon")
        data = response.json()
        if not data:
            return 0
        elif len(data) > 1:
            raise ValueError(
                f"Duplicate entries found in {self.recon_type} recon store for {self.source} between interval {start_time} and {end_time}")
        else:
            return data[0].get("record_count")

    def get_recon_job_args(self, data_model_type: str, table_name: str, interval_start: pendulum.DateTime,
                           interval_end: pendulum.DateTime, avro_files: list):
        job_args = Variable.get(key=AP.DATALAKE_RECON_VARIABLE, deserialize_json=True).get("job_args")
        job_args = job_args + [
            "--data-source-name", self.source,
            "--recon-type", self.recon_type,
            "--data-model-type",data_model_type,
            "--modified-after", interval_start.to_iso8601_string().split(".")[0],
            "--modified-before", interval_end.to_iso8601_string().split(".")[0],
            "--recon-api-url", self.url
        ]
        if data_model_type == AP.RDM:
            return job_args + list(chain.from_iterable(["--avro-files", file] for file in avro_files))
        else:
            schema = AutoParserUtils.get_table_schema(data_model_type)
            return job_args + ["--iceberg-table-name", f"{schema}.{table_name}"]

