from commons.pe.logger_base import SDSLoggerBase
import logging
import os
import json
from urllib.parse import urljoin
from functools import cached_property
import requests
from airflow.providers.http.hooks.http import HttpHook
from airflow.models.taskinstance import TaskInstance
from airflow.exceptions import AirflowSkipException
from airflow.models import Variable
from deepdiff import DeepDiff
from commons.pe.common_utils import Utils
from commons.pe.iceberg_utils import IcebergUtils
from commons.pe.constants.airflow_constants import PuppyGraphConstants
from datetime import datetime

TYPE_MAP = {"string": "String",
            "int": "Int",
            "long": "Long",
            "double": "Double",
            "boolean": "Boolean",
            "timestamptz": "DateTime",
            "list<string>": "Array<String>",
            "list<int>": "Array<Int>",
            "list<long>": "Array<Long>",
            }

class PuppyGraphSchemaUtils(SDSLoggerBase):

    def __init__(self):
        self.iceberg_utils = IcebergUtils()

    def get_attr(self, schema_dict):
        """
        This function is used for mapping the iceberg type to puppy supporting types
        
        :param schema_dict: contains the schema with attributes and their corresponding iceberg type
        :param primary_key: field for primary key
        :param source_primary_key: field for source primary key
        :param target_primary_key: field for target primary key
        """
        list_attr = []
        primary_key = [item.get("field") for item in self.pg_config.get("primary_key")]
        source_primary_key = [item.get("field") for item in self.pg_config.get("source_primary_key")]
        target_primary_key = [item.get("field") for item in self.pg_config.get("target_primary_key")]
        skip_fields = [*primary_key, *source_primary_key, *target_primary_key]
        self.log.info(f"Skipping fields: {skip_fields}")
        for attr, typ in schema_dict.items():
            if str(typ).find('struct') != -1 or attr in skip_fields:
                continue
            elif str(typ).startswith("decimal"):
                attr_type = str(typ).replace("decimal", "Decimal")
            else:
                attr_type = TYPE_MAP.get(str(typ), "Unknown")
            if attr_type == "Unknown":
                self.log.info(f"Skipping field {attr}, type {typ} is Unknown")
                continue
            list_attr.append({"field": attr, "alias": attr, "type": attr_type})

        return list_attr

    @cached_property
    def pg_config(self):
        """
        Extract and return relevant schema generation config values.
        Returns:
            dict: (SCHEMA, primary_key, source_primary_key, target_primary_key, cacheConfig, metastore_details)
        """
        config = Variable.get("puppy_graph_schema_generation_config", default_var={}, deserialize_json=True)
        cache_conf = config.get("cacheConfig")
        if cache_conf and "partitionTtl" in cache_conf:
            cache_conf["partitionTtl"]["amount"] = int(cache_conf["partitionTtl"].get("amount", 1))

        return {
            "primary_key": config.get("primary_key"),
            "source_primary_key": config.get("source_primary_key"),
            "target_primary_key": config.get("target_primary_key"),
            "cacheConfig": cache_conf,
            "catalog_details": config.get("catalog_details")
        }

    def exclude_none_empty(self, obj, path):
        return obj is None or obj == ""

    def check_schema_change(self, *args, **kwargs):
        """
        Function to check change in schema between current instance's graph schema with puppy graph uploaded schema
        """
        conn_id, headers, username, password = args
        current_schema = json.loads(kwargs["task_instance"].xcom_pull(key="PUPPY_GRAPH_SCHEMA", task_ids="puppy_graph_schema_generator"))
        hook = HttpHook(http_conn_id=conn_id)
        hook.get_conn()
        host = hook.base_url
        extra_base_endpoint = os.getenv("PUPPY_GRAPH_BASE_URL_PREFIX", "/")
        final_endpoint = os.path.join(extra_base_endpoint, PuppyGraphConstants.PUPPY_SCHEMA_GET_URL)
        endpoint = urljoin(host, final_endpoint)
        response = requests.get(endpoint, headers=headers, auth=(username, password))
        if response.status_code == 200:
            previous_success_schema = response.json()
        else:
            self.log.info(f"schemajson endpoint returned: {response.text}")
            return "update_puppy_graph_schema"
        diff = DeepDiff(previous_success_schema, current_schema, exclude_obj_callback=self.exclude_none_empty)
        if diff:
            self.log.info(f"Difference between previous schema: {diff}")
            return "update_puppy_graph_schema"
        else:
            return "cache_refresh_start"


    def cache_sense_check(self, response, **kwargs):
        """
        Function to check cache building status for entire schema.
        """
        nodes = response.json().get("GremlinServer", {}).get("LocalCacheStatusDetail", [])
        cache_status = response.json().get("GremlinServer", {}).get("LocalCacheStatus", [])
        if nodes:
            self.log.info(f"Cache status: {nodes}")
            for node in nodes:
                state=node.get("State")
                self.log.info(f"State for node {node}: {state}")
                if state != "SUCCESS":
                    table = node.get("Name")
                    self.log.error(f"Cache not set for Label: {table}")
                    return False
        
        if response.json().get("GremlinServer", {}).get("LocalCacheStatus", []) == "READY":
            return True
        else:
            self.log.info(f"Cache not available STATUS: {cache_status}, waiting for local cache status to be READY")
            return False


    def get_partition_interval(self, data_interval_start, data_interval_end, task_instance):
        """
        Function to return partition interval start and end
        """
        data_interval_end = Utils.render_variable("{{ dag_run.conf.get('data_interval_end') }}", task_instance) or data_interval_end
        try:
            start_dt = datetime.fromisoformat(data_interval_start)
            try:
                end_dt = datetime.strptime(data_interval_end, "%Y-%m-%dT%H:%M:%SZ")
            except ValueError:
                end_dt = datetime.strptime(data_interval_end, "%Y-%m-%d")
        except ValueError as e:
            self.log.error(f"Invalid date format: {e}")
            raise ValueError("Invalid date format. Expected format (YYYY-MM-DD)")

        if start_dt >= end_dt:
            self.log.error(
                f"Invalid interval: data_interval_start ({data_interval_start}) is not less than data_interval_end ({data_interval_end})")
            raise ValueError("data_interval_start must be less than data_interval_end")
        
        data = {
            "partitionStartDate": data_interval_start,
            "partitionEndDate": str(end_dt.date())
        }
        self.log.info(f"Partition interval: {data}")
        return data

    def get_table_list(self, schema, **kwargs):
        ti = kwargs["ti"]
        iceberg_utils = IcebergUtils()
        table_list = [{"table_name": table,
                      "schema_name": schema} for table in iceberg_utils.get_table_lists(schema)]
        ti.xcom_push(key="table_list", value=json.dumps(table_list))
        return table_list
    
    def get_table_list_from_xcom(self, schema, **kwargs):
        ti = kwargs["ti"]
        return json.loads(ti.xcom_pull(key="table_list", task_ids=f"get_table_list_for_{schema}"))

    @staticmethod
    def get_graphql_payload(task_instance):
        schema = json.loads(task_instance.xcom_pull(task_ids='puppy_graph_schema_generator', key='PUPPY_GRAPH_SCHEMA'))
        graph_schema = {"graph_schema": schema}
        return json.dumps(graph_schema)

    def load_table_and_schema(self, table_name, schema_name):
        """Helper to load table and schema from Iceberg."""
        table = self.iceberg_utils.load_table(f"{schema_name}.{table_name}")
        schema_dict = self.iceberg_utils.get_table_schema(f"{schema_name}.{table_name}")
        return table, schema_dict

    def generate_schema(self, table_name, schema_name, **kwargs):
        """
        This Function scans the icerberg table and find its attributes and its types and map these types with Puppy Graph supported types,
        Also, the created schema is pushed to XCOM with key {schema_name}.{table_name}
        """

        ti = kwargs["ti"]
        table, schema_dict = self.load_table_and_schema(table_name, schema_name)
        if table.properties.get("graph.vertex.name"):
            entity = table.properties.get("graph.vertex.name").lower().replace(" ", "_")
            vertex = {
                "label": entity,
                "oneToOne": {
                    "tableSource": {"catalog": "sds_datalake", "schema": schema_name, "table": table_name},
                    "id": {"fields": self.pg_config.get("primary_key")},
                    "attributes": self.get_attr(schema_dict),
                },
                "cacheConfig": self.pg_config.get("cacheConfig")
            }
            ti.xcom_push(key=f"{schema_name}.{table_name}", value=json.dumps(vertex))
        elif table.properties.get("graph.edge.name"):
            relationship = table.properties.get("graph.edge.name").lower().replace(" ", "_")
            edge = {
                "label": relationship,
                "fromVertex": table.properties.get("graph.edge.source.name").lower().replace(" ", "_"),
                "toVertex": table.properties.get("graph.edge.target.name").lower().replace(" ", "_"),
                "tableSource": {"catalog": "sds_datalake", "schema": schema_name, "table": table_name},
                "id": {"fields": self.pg_config.get("primary_key")},
                "fromId": {"fields": self.pg_config.get("source_primary_key")},
                "toId": {"fields": self.pg_config.get("target_primary_key")},
                "attributes": self.get_attr(schema_dict),
                "cacheConfig": self.pg_config.get("cacheConfig")
            }
            ti.xcom_push(key=f"{schema_name}.{table_name}", value=json.dumps(edge))
        else:
            raise AirflowSkipException("Skipped due to missing table properties")
    
    def generate_final_schema(self, *args, **kwargs):
        """
        This Function is fetches all the individual schema from XCOM and create the final SCHEMA JSON for puppy graph
        """
        schema_dict = [json.loads(item) for item in args]
        vertices = []
        edges = []

        for schema in schema_dict:
            map_indexes = list(range(0,len(schema)))
            for table_dict in schema:
                table = table_dict["table_name"]
                schema_name = table_dict["schema_name"]
                node_schema = [json.loads(item) for item in kwargs["task_instance"].xcom_pull(key=f"{schema_name}.{table}", task_ids=f"{schema_name}_task_map", default=[], map_indexes = map_indexes) if item]
                self.log.info(f"xcom_pull returned: {node_schema}")
                if node_schema and node_schema[0].get("oneToOne"):
                    vertices.extend(node_schema)
                else:
                    edges.extend(node_schema)
        graph_schema = {
            "catalogs": [self.pg_config.get("catalog_details")],
            "graph":{"vertices": vertices,
                     "edges": edges}}
        kwargs["task_instance"].xcom_push(key="PUPPY_GRAPH_SCHEMA", value=json.dumps(graph_schema))