import logging
from urllib.parse import urljoin

from airflow.models.variable import Variable
from airflow.providers.http.operators.http import HttpOperator
from commons.pe.common_utils import Utils
from commons.pe.constants.airflow_constants import OlapAutoGenConstants
from plugins.pe.druid.sds_druid_sensor import DruidIndexingOperator


class DruidUtils:
    @staticmethod
    def get_configs_after_variable_merge(*args):
        logger = logging.getLogger("merge two variables")
        default_confs = Variable.get(key=args[0], deserialize_json=True)
        druid_individual_confs = Variable.get(key=args[1], deserialize_json=True, default_var={})
        source_name = args[2]
        if source_name not in druid_individual_confs:
            logger.info(
                f"Spark config for source '{source_name}' not found. Taking default configuration"
            )

        spark_job_config = Utils.merge_dictionaries(
            default_confs, druid_individual_confs.get(source_name, {})
        )

        logger.info(f"Setting sparkapplication with name {source_name}")
        spark_job_config["app_name"] = f"{source_name}-loader"
        return spark_job_config

    @staticmethod
    def create_olap_operators_sensor(indexing_config_key, druid_indexing_config_variable):
        druid_indexing_task = DruidIndexingOperator(
            task_id=f"{indexing_config_key}_index",
            druid_conn_id="druid_coordinator_conn",
            data=f"{{{{ render_variable(str(var.json.{druid_indexing_config_variable}.{indexing_config_key}),ti) }}}}"
        )
        return druid_indexing_task

    @staticmethod
    def delete_olap_operator_sensors(datasource):
        delete_data_source = HttpOperator(task_id=f'delete_datasource_{datasource}',
                                                http_conn_id=OlapAutoGenConstants.DRUID_API_KEY,
                                                endpoint=urljoin(
                                                    OlapAutoGenConstants.DS_DELETE_URL,
                                                    datasource),
                                                method=OlapAutoGenConstants.DELETE,
                                                headers=OlapAutoGenConstants.CONTENT_TYPE_HEADER,
                                                do_xcom_push=True
                                                )
        return delete_data_source
