import json
import os

import requests
from airflow.exceptions import AirflowFailException, AirflowSkipException, AirflowException
from airflow.models import DagRun
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.models.variable import Variable
from airflow.utils.state import DagRunState

from commons.pe.common_utils import Utils
from commons.pe.constants.airflow_constants import AutoParserConstants
from commons.pe.iceberg_utils import IcebergUtils
from commons.pe.logger_base import SDSLoggerBase
from airflow.providers.http.hooks.http import HttpHook


class EdaReportUtils(SDSLoggerBase):
    EDA_CONFIG_VARIABLE = "sds_eda_report_generation_config"

    @staticmethod
    def get_eda_report_job_spec(*args, **kwargs):
        entity_name = kwargs.get("entity")
        entity_inventory_schema = kwargs.get("entity_inventory_schema")
        srdm_table = kwargs.get("srdm_table")
        data_source_feed_name = kwargs.get("data_source_feed_name")
        run_correlation = kwargs.get("run_correlation", "true")
        run_quality_check = kwargs.get("run_quality_check", "false")
        correlation_threshold = kwargs.get("correlation_threshold", "40")
        filter_condition = kwargs.get("filter_condition", "")
        partial_run = kwargs.get("partial_run", False)

        if not entity_name:
            raise AirflowFailException("No value for entity provided in kwargs")

        entity_inventory_table = f"{entity_inventory_schema}.sds_ei__{entity_name.replace(' ', '_').lower()}"

        if partial_run and isinstance(partial_run, list) and entity_name not in partial_run:
            raise AirflowSkipException(
                f"Skipping EDA report generation for {entity_name} entity as partial run was enabled with entities {json.dumps(partial_run)}")

        data_dictionary = Utils.read_data_from_shared_filesystem(
            relative_file_path=AutoParserConstants.EI_DATA_DICTIONARY_PATH)

        try:
            next(dict_entity for dict_entity in data_dictionary.keys() if dict_entity == entity_name)
        except StopIteration:
            raise AirflowFailException(f"No matching entity for {entity_name} found in data dictionary")

        dictionary_fields = [attribute for attribute, config in
                             data_dictionary[entity_name].get("attributes", {}).items()]

        iceberg_utils = IcebergUtils("default")
        if not iceberg_utils.check_table_exists(table_name=entity_inventory_table):
            inventory_fields = []
        else:
            inventory_fields = list(iceberg_utils.get_table_schema(entity_inventory_table).keys())
        final_field_list = list(set(dictionary_fields).intersection(set(inventory_fields)))

        eda_config = Variable.get(key=EdaReportUtils.EDA_CONFIG_VARIABLE, deserialize_json=True)
        auth_token_url = f"{os.getenv('KEYCLOAK_HOST')}/realms/{os.getenv('KEYCLOAK_REALM')}/protocol/openid-connect/token"
        eda_config["runtime_config"]["spec"]["driver"]["env"].append(
            {"name": "AUTH_TOKEN_URL", "value": auth_token_url})
        eda_config["runtime_config"]["spec"]["executor"]["env"].append(
            {"name": "AUTH_TOKEN_URL", "value": auth_token_url})
        hook = HttpHook(http_conn_id="management_api")
        hook.get_conn()
        host = hook.base_url
        eda_config["args"] = [
            "--srdm-path",
            srdm_table,
            "--inventory-table",
            entity_inventory_table,
            "--data-source-feed-name",
            data_source_feed_name,
            "--entity-name",
            entity_name.replace(' ', '_').lower(),
            "--inventory-column-names",
            ",".join(final_field_list),
            "--studio-api-host",
            host.rstrip("/"),
            "--run-correlation",
            run_correlation,
            "--run-quality-check",
            run_quality_check,
            "--correlation-threshold",
            str(correlation_threshold),
            "--filter-condition",
            filter_condition
        ]
        eda_config["app_name"] = f"{data_source_feed_name}_{Utils.cleanup_name(entity_name)}_eda_report"
        return eda_config

    def check_if_eda_must_run(self, **kwargs):
        
        srdm_table = kwargs.get("srdm_table")
        data_feed_name = kwargs.get("data_source_feed_name")
        hook = HttpHook(http_conn_id="management_api")
        hook.get_conn()
        host = hook.base_url
        endpoint = "sds_mgmnt/analytics/api/v1/ds/eda"
        url = os.path.join(host, endpoint)
        existing_reports = requests.get(url=url, params={"data_source": data_feed_name},
                                        headers={"Authorization": f"{Utils.get_keycloak_token()}"})
        if existing_reports.status_code != 200:
            raise AirflowException(f"Eda report list endpoint returned an error - {existing_reports.text}")

        if existing_reports.json():
            self.log.info(f"EDA report already present for {srdm_table} in EDA database. Skipping the trigger")
            raise AirflowSkipException
        incomplete_eda_runs = [dag_run for dag_run in DagRun.find(dag_id="eda_report_generation")
                               if dag_run.state in (DagRunState.RUNNING, DagRunState.QUEUED)
                               and dag_run.conf.get("trigger_from", "manual") == "autoparser"
                               and dag_run.conf.get("srdm_table") == srdm_table
                               ]
        if incomplete_eda_runs:
            self.log.info(
                f"Found incomplete dag runs for this same source triggered from autoparser with run ids - {''.join([dag_run.run_id for dag_run in incomplete_eda_runs])}")
            self.log.info(
                "Going to skip this trigger. Please track the status of the existing dag run for more info on EDA")
            raise AirflowSkipException

        if existing_reports.json() == [] and incomplete_eda_runs == []:
            self.log.info(
                "No eda report found in database and no dag run for eda triggered by autoparser found. Going to execute trigger")
            TriggerDagRunOperator(
                task_id="trigger_eda",
                trigger_dag_id="eda_report_generation",
                wait_for_completion=True,
                conf={
                    "srdm_table": srdm_table,
                    "data_source_feed_name": data_feed_name,
                    "trigger_from": "autoparser"
                },
            ).execute(context=kwargs)
