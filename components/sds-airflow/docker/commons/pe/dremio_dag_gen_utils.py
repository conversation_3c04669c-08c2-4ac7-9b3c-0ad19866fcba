import logging
from typing import List, Dict

from airflow import DAG, AirflowException
from airflow.utils.task_group import TaskGroup

from commons.pe.constants.airflow_constants import DremioConstants
from plugins.pe.dremio.operators.dremio_reflections import DremioReflectionCreationOperator
from plugins.pe.dremio.operators.dremio_views import DremioViewCreationOperator


class DremioDagGenUtils:
    log = logging.getLogger("dremio_dag_gen_utils")
    @staticmethod
    def generate_task_group(source: str, source_type: str, dremio_cluster_ips: List[str], dag: DAG,
                            create_raw_reflection: bool = True,
                            reflections_spec=None,
                            exclude_fields=None, include_fields=None, refresh_spec=None, query: str = "",
                            base_tables: list = None):
        if refresh_spec is None:
            refresh_spec = {}
        if include_fields is None:
            include_fields = []
        if reflections_spec is None:
            reflections_spec = []
        if exclude_fields is None:
            exclude_fields = []
        if base_tables is None:
            base_tables = []
        task_group_name = source.split(".")[-1]
        source_name = task_group_name
        with TaskGroup(group_id=task_group_name, ui_color="#43BFC7", dag=dag) as tg:
            for index, cluster_ip in enumerate(dremio_cluster_ips):
                task_suffix = f"{source_name}_{index}"
                if source_type == DremioConstants.REFLECTION:
                    reflection_task = DremioReflectionCreationOperator(
                        task_id=f"reflection_create_{task_suffix}",
                        dremio_cluster_url=cluster_ip,
                        source=f"{{{{ var.value.{DremioConstants.DREMIO_SOURCE_NAME} }}}}" + "." + source,
                        create_raw_reflection=create_raw_reflection,
                        reflections_spec=reflections_spec,
                        refresh_settings=refresh_spec,
                        exclude_fields=exclude_fields,
                        include_fields=include_fields,
                        source_type="PHYSICAL_DATASET"
                    )

                    view_on_table = DremioViewCreationOperator(
                        task_id=f"view_{task_suffix}",
                        view_type=DremioConstants.VIEW,
                        dremio_cluster_url=cluster_ip,
                        view_name=f"{{{{ var.value.{DremioConstants.DREMIO_SPACE_NAME} }}}}" + "." + source_name,
                        query=f"SELECT * FROM {source}"
                    )

                    reflection_task >> view_on_table

                else:
                    view_task_id = f"view_create_{task_suffix}"
                    create_view = DremioViewCreationOperator(
                        task_id=view_task_id,
                        dremio_cluster_url=cluster_ip,
                        view_type=source_type,
                        view_name=f"{{{{ var.value.{DremioConstants.DREMIO_SPACE_NAME} }}}}" + "." + source_name,
                        query=query,
                        base_tables=base_tables
                    )

                    if reflections_spec or create_raw_reflection:
                        reflection_on_view_task = DremioReflectionCreationOperator(
                            task_id=f"reflection_on_{task_suffix}",
                            source=f"{{{{ var.value.{DremioConstants.DREMIO_SPACE_NAME} }}}}" + "." + source_name,
                            source_type="VIRTUAL_DATASET",
                            dremio_cluster_url=cluster_ip,
                            exclude_fields=exclude_fields,
                            include_fields=include_fields,
                            create_raw_reflection=create_raw_reflection,
                            reflections_spec=reflections_spec,
                            refresh_settings=refresh_spec,
                            query=f"{{{{ ti.xcom_pull(key='sql', task_ids='{task_group_name}.{view_task_id}') }}}}"
                        )
                        create_view >> reflection_on_view_task

        return tg

    @staticmethod
    def validate_dremio_config(dremio_config: dict):
        dremio_type = dremio_config.get("type", None)
        dremio_source = dremio_config.get("dremioSourceName")
        if not dremio_type:
            raise AirflowException(
                f"Type is not present in config for {dremio_source}")
        elif dremio_type not in ["reflection", "view", "unionView"]:
            raise AirflowException(
                f"type must be either of 'reflection', 'view' or 'unionView' but value was {dremio_type}")

    @staticmethod
    def generate_dependency_dict(dremio_jobs_config: List[dict], dremio_cluster_ips: list, dag: DAG) -> Dict[
        str, TaskGroup]:
        task_group_dict = {}
        for dremio_conf in dremio_jobs_config:
            dremio_type = dremio_conf.get("type")
            if dremio_type == DremioConstants.REFLECTION:
                dremio_source = dremio_conf.get("sourceTable")
                dependency_dict = {dremio_source: dremio_conf}
            else:
                dremio_source = dremio_conf.get("viewName")
                dependency_dict = {dremio_source: dremio_conf}
                for dependency in dremio_conf.get("dependencies", []):
                    dependency_dict[dependency] = {"type": DremioConstants.REFLECTION, "sourceTable": dependency,
                                                   DremioConstants.AUTO_CREATE_RAW_REFLECTIONS: True}
            for dependency, dep_config in dependency_dict.items():
                if dependency not in task_group_dict:
                    source_type = dep_config.get("type")
                    create_raw_reflection_default = True if source_type == DremioConstants.REFLECTION else False
                    task_group_dict[dependency] = DremioDagGenUtils.generate_task_group(
                        source=dependency,
                        dag=dag,
                        source_type=source_type,
                        dremio_cluster_ips=dremio_cluster_ips,
                        create_raw_reflection=dep_config.get(DremioConstants.AUTO_CREATE_RAW_REFLECTIONS,
                                                             create_raw_reflection_default),
                        reflections_spec=dep_config.get(DremioConstants.REFLECTION_SPEC),
                        exclude_fields=dep_config.get("excludeFields"),
                        include_fields=dep_config.get("includeFields"),
                        refresh_spec=dep_config.get(DremioConstants.REFRESH_POLICY),
                        query=dep_config.get("query"),
                        base_tables=dep_config.get("baseTables")
                    )
        return task_group_dict