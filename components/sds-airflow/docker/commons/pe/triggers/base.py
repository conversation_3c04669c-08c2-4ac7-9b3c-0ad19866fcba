import os
import sys
from abc import abstractmethod
from typing import Any

from airflow.triggers.base import BaseTrigger, TriggerEvent
from airflow.providers.http.sensors.http import HttpSensor
from airflow.providers.http.operators.http import HttpOperator
from airflow.providers.cncf.kubernetes.operators.job import KubernetesJobOperator


class SDSBaseTrigger(BaseTrigger):

    @abstractmethod
    def sync_run(self) -> TriggerEvent:
        """
        This method defines the synchronous version of the run() method of the BaseTrigger
        This is the function that will be called for non-deferable mode of operators for whatever is the completion criteria
        """
        pass
