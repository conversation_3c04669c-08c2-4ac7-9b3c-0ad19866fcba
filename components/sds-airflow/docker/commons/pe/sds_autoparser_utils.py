import logging
import logging
import os
from copy import deepcopy
from typing import Tuple

import pendulum
from airflow.exceptions import AirflowSkipException, AirflowException
from airflow.models import DagRun
from airflow.models.variable import Variable
from airflow.utils.email import send_email
from airflow.utils.helpers import render_template_to_string

from commons.pe.cloud_utils import CloudFactory
from commons.pe.common_utils import Utils
from commons.pe.constants.airflow_constants import AutoParserConstants as AP
from commons.pe.constants.airflow_constants import DataMigrationConstants as DMC


class AutoParserUtils:
    LOGGER = logging.getLogger(__name__)

    @staticmethod
    def get_iceberg_recon_job_args(source: str, interval_start: pendulum.DateTime, interval_end: pendulum.DateTime,
                                   data_model_type: str, table_name: str, recon_table: str, recon_type: str):
        job_args = Variable.get(key=AP.DATALAKE_RECON_VARIABLE, deserialize_json=True).get("job_args")
        schema = AutoParserUtils.get_table_schema(data_model_type)
        return job_args + ["--iceberg-table-name", f"{schema}.{table_name}", "--data-model-type", data_model_type,
                           "--jdbc-table-name", recon_table,
                           "--data-source-name", source, "--recon-type", recon_type, "--modified-after",
                           interval_start.to_iso8601_string().split(".")[0],
                           "--modified-before", interval_end.to_iso8601_string().split(".")[0]]

    @staticmethod
    def get_table_schema(data_model_type: str):
        if data_model_type == AP.SRDM:
            return Variable.get(key=AP.SRDM_SCHEMA_VARIABLE)
        elif data_model_type == AP.SDM:
            return Variable.get(key=AP.SDM_SCHEMA_VARIABLE)
        else:
            raise AirflowException(f"Invalid data model type {data_model_type}")

    @staticmethod
    def check_if_rdm_data_exists(*args, **context):
        rdm_path = args[0]
        data_interval_start, data_interval_end = AutoParserUtils.get_data_interval(
            data_interval_start=context["data_interval_start"], data_interval_end=context["data_interval_end"],
            dag_run=context["dag_run"])
        cloud_utils = CloudFactory(path=rdm_path)
        avro_files = cloud_utils.list_files(storage_path=rdm_path, file_extension=".avro",
                                            modified_after=data_interval_start, modified_before=data_interval_end)
        context["task_instance"].xcom_push(key="avro_files", value=avro_files)
        AutoParserUtils.LOGGER.info(
            f"Found {str(len(avro_files))} for the interval '{data_interval_start.to_iso8601_string()}' - '{data_interval_end.to_iso8601_string()}'.")
        if not avro_files:
            AutoParserUtils.LOGGER.info(
                "No avro files found between the given interval. Skipping the rest of the pipeline")
            raise AirflowSkipException

    @staticmethod
    def send_email_on_skip(email_on_skip: bool, context):
        rdm_count = context["ti"].xcom_pull(key="rdm_count")
        if rdm_count == 0 and os.getenv(key="AIRFLOW_EMAIL_NOTIFICATION_ENABLED",
                                        default="true").lower() == "true" and email_on_skip:
            jinja_env = context["dag"].get_template_env(force_sandboxed=True)
            jinja_context = context["ti"].get_template_context()
            subject = os.getenv("AIRFLOW__EMAIL_SKIP_SUBJECT_TEMPLATE",
                                "/opt/airflow/shared/email/skip_task_email_subject.txt")
            content = os.getenv("AIRFLOW__EMAIL_SKIP_CONTENT_TEMPLATE",
                                "/opt/airflow/shared/email/skip_task_email_content.txt")
            skip_email_subject = open(subject).read()
            skip_email_subject = render_template_to_string(jinja_env.from_string(skip_email_subject), jinja_context)
            skip_email_content = open(content).read()
            skip_email_content = render_template_to_string(jinja_env.from_string(skip_email_content), jinja_context)
            send_email(context["task"].email, skip_email_subject, skip_email_content)

    @classmethod
    def get_autoparser_configs(cls, source: str, interval_start: pendulum.DateTime, interval_end: pendulum.DateTime,
                               rdm_path: str, srdm_table: str):
        default_job_var = AP.AUTOPARSER_DEFAULT_JOB_CONFIGURATION
        custom_job_var = AP.AUTOPARSER_CUSTOM_JOB_CONFIGURATION
        extra_args = []
        app_name = f"{source}-populate-srdm"

        merged_config = Utils.get_custom_configuration(default_job_var, custom_job_var, source)
        sdm_config_base_path = os.getenv(AP.AUTOPARSER_JOB_CONFIGS_BASE_PATH, "")
        suffix = source if sdm_config_base_path.startswith("http") else f"{source}.json"
        source_config_path = os.path.join(sdm_config_base_path, suffix)
        cls.LOGGER.info(f"Setting datasource config path as - {source_config_path}")
        schema = AutoParserUtils.get_table_schema(AP.SRDM)
        extra_args.extend([
            "--config-path",
            source_config_path,
            "--rdm-path",
            rdm_path,
            "--srdm-table",
            f"{schema}.{srdm_table}",
            "--modified-after",
            interval_start.to_iso8601_string().split(".")[0],
            "--modified-before",
            interval_end.to_iso8601_string().split(".")[0]
        ])

        final_config = deepcopy(merged_config)
        final_config["args"].extend(extra_args)
        final_config["app_name"] = app_name

        return final_config

    @classmethod
    def get_data_migration_configs(cls, source: str):
        default_job_var = DMC.DATA_MIGRATION_DEFAULT_CONFIG
        custom_job_var = DMC.DATA_MIGRATION_CUSTOM_CONFIG
        app_name = f"{source}-to-iceberg"

        default_config = Variable.get(key=default_job_var, default_var={}, deserialize_json=True)
        custom_config = Variable.get(key=custom_job_var, default_var={}, deserialize_json=True)
        if source not in custom_config:
            cls.LOGGER.info(f"Spark config for source '{source}' not found. Taking default configuration")
        merged_config = Utils.merge_dictionaries(default_config, custom_config.get(source, {}))

        final_config = deepcopy(merged_config)
        final_config["app_name"] = app_name

        return final_config

    @classmethod
    def get_data_interval(cls, data_interval_start: pendulum.DateTime, data_interval_end: pendulum.DateTime,
                          dag_run: DagRun) -> Tuple[pendulum.DateTime, pendulum.DateTime]:
        start_string = dag_run.conf.get("data_interval_start", None)
        end_string = dag_run.conf.get("data_interval_end", None)
        tz = dag_run.conf.get("timezone", "UTC")
        start = pendulum.parse(start_string, tz=tz).in_timezone("UTC") if start_string else data_interval_start
        end = pendulum.parse(end_string, tz=tz).in_timezone("UTC") if end_string else data_interval_end
        return start, end

    @classmethod
    def get_recon_trigger_dag_conf(cls, data_interval_start: pendulum.DateTime, data_interval_end: pendulum.DateTime, dag_run:DagRun):
        start, end = cls.get_data_interval(data_interval_start, data_interval_end, dag_run)
        return {
            "data_interval_start": start.to_iso8601_string().split(".")[0],
            "data_interval_end": end.to_iso8601_string().split(".")[0]
        }
