"""Constants relating to airflow variables and configurations"""


class AirflowConstants:
    DEFAULT_HTTP_HEADERS = {'Content-type': 'application/json', 'Accept': 'application/json'}
    SDS_BACKFILL_CONFIGURATION = "SDS_BACKFILL_CONFIG"
    AZURE_CLIENT_ID = "AZURE_MANAGED_IDENTITY_CLIENT_ID"


class AutoParserConstants:
    AUTOPARSER_DAG_GEN_FILE = "sds_autoparser_dag_gen/autoparser_dag_gen_configs.json"
    AUTOPARSER_DEFAULT_JOB_CONFIGURATION = "autoparser_default_job_configuration"
    AUTOPARSER_CUSTOM_JOB_CONFIGURATION = "autoparser_custom_job_configuration"
    RECON_DATALAKE_DEFAULT_JOB_CONFIGURATION = "recon_datalake_default_job_configuration"
    RECON_DATALAKE_CUSTOM_JOB_CONFIGURATION = "recon_datalake_custom_job_configuration"
    RECON_POSTGRES_CONN_ID = "psql_connection"
    DATA_LAKE_RECON_REDIS_KEY_VARIABLE = "data_lake_recon_redis_clear_key"
    RECON_DATASOURCE_DEFAULT_JOB_CONFIGURATION = "recon_datasource_default_job_configuration"
    RECON_DATASOURCE_CUSTOM_JOB_CONFIGURATION = "recon_datasource_custom_job_configuration"
    DATA_SOURCE_RECON_REDIS_KEY_VARIABLE = "data_source_recon_redis_clear_key"
    AUTOPARSER_JOB_CONFIGS_BASE_PATH = "SDS_SDM_CONFIGS_BASE_PATH"
    DBT_AUTO_DAG_GEN_FILE = "dbt_dag_dependency.json"
    SDM_DBT_DEFAULT_VARIABLE_NAME = "sdm_dbt_default_configuration"
    SDM_DBT_CUSTOM_VARIABLE_NAME = "sdm_dbt_custom_configuration"
    RECON_REST_CONN_ID = "recon_api"
    RDM = "RDM"
    SRDM = "SRDM"
    SDM = "SDM"
    SRDM_SCHEMA_VARIABLE = "SRDM_SCHEMA"
    SDM_SCHEMA_VARIABLE = "SDM_SCHEMA"
    DATALAKE_RECON_VARIABLE = "datalake_recon_config"
    DATA_LAKE_RECON_TABLE_NAME = "data_lake_recon_metric_store"
    DATA_SOURCE_RECON_TABLE_NAME = "data_source_recon_metric_store"
    EI_DATA_DICTIONARY_PATH = "sds-ei-configs/ui-configs/sds_ei_data_dictionary.json"


class AirflowConfigurationConstants:
    SHARED_VOLUME_PATH_ENV_VARIABLE = "AIRFLOW_DYNAMIC_TASK_PATH"
    SDS_OLAP_DYNAMIC_TASKS_FILE_PATH = "olap/olap_dag_factory.json"
    AIRFLOW_CONFIG_UPDATE_DAG_VARIABLE = "config_updation_variables"
    AIRFLOW_CONF_CORE_KEY = "core"
    AIRFLOW_CONF_CORE_TIMEZONE_KEY = "default_timezone"

class CandidateKeyGenConstants:
    CANDIDATE_KEY_CONFIG_VARIABLE = "candidate_key_generation_config"


class OlapAutoGenConstants:
    """Class containing constants used for Druid"""

    DRUID_INDEXING_DEFAULT_SPARK_JOB_CONFIG = "druid_indexing_default_spark_job_config"
    DRUID_INDEXING_CUSTOM_SPARK_JOB_CONFIG = "druid_indexing_custom_spark_job_config"
    SDS_OLAP_CONFIG_SCHEMA_VALIDATOR = {
        "tags": {
            "type": "list",
            "required": False,
            "schema": {
                "type": "string"
            }
        },
        "pipeline_id": {
            "type": "string",
            "required": True
        },
        "task_group": {
            "type": "list",
            "required": True,
            "schema": {
                "type": "dict",
                "schema": {
                    "task_group_name": {
                        "type": "string",
                        "required": True
                    },
                    "job_list": {
                        "type": "list",
                        "required": True,
                        "schema": {
                            "type": "dict",
                            "schema": {
                                "config_key": {
                                    "type": "string",
                                    "required": True
                                },
                                "indexing_operation": {
                                    "type": "string"
                                },
                                "olap_engine": {
                                    "type": "string"
                                },
                                "source_type": {
                                    "type": "string"
                                }
                            }
                        }
                    }
                }

            }
        }
    }

    GET = "GET"
    POST = "POST"
    DELETE = "DELETE",
    CONTENT_TYPE_HEADER = {"Content-Type": "application/json"}

    DRUID_API_KEY = "druid_api"
    DS_DELETE_URL = "/druid/coordinator/v1/datasources/%s"
    DS_DISABLE_URL = "/druid/coordinator/v1/datasources/%s/markUnused"
    DS_INDEXING_URL = "/druid/indexer/v1/task/"


class HTTPConstants:
    GET = "GET"
    POST = "POST"
    DELETE = "DELETE",
    PUT = "PUT"


class DataMigrationConstants:
    """Class containing constants used for Data Migration"""

    DATA_MIGRATION_SOURCES_FILE = "sds_data_migration/data_migration_sources.json"
    DATA_MIGRATION_DEFAULT_CONFIG = "data_migration_default_config"
    DATA_MIGRATION_CUSTOM_CONFIG = "data_migration_custom_config"


class MaintenanceJobConstants:
    """Class containing constants used for Maintenance Jobs"""

    ICEBERG_DELETE_ORPHAN_FILES_CONFIG = "iceberg_delete_orphan_files_config"
    ICEBERG_COMPACTION_TABLE_CONFIG = "iceberg_compaction_table_config"


class DremioConstants:
    REFLECTION = "reflection"
    VIEW = "view"
    HTTP_SCHEME = "http"
    DREMIO_SOURCE_NAME = "DREMIO_SOURCE_NAME"
    DREMIO_SPACE_NAME = "DREMIO_SPACE_NAME"
    AUTO_CREATE_RAW_REFLECTIONS = "autoCreateRawReflection"
    REFRESH_POLICY = "accelerationRefreshPolicy"
    REFLECTION_SPEC = "reflectionsSpec"
    DREMIO_DEFAULT_SOURCE_NAME = "iceberg"
    DREMIO_BASE_ENDPOINT = "api/v3"
    REFLECTION_SPEC_DEFAULT_VALUES = {
        "enabled": True,
        "arrowCachingEnabled": True,
        "partitionDistributionStrategy": "CONSOLIDATED",
    }

class PuppyGraphConstants:
    POST = "POST"
    GET = "GET"
    POST_SCHEMA = "schema"
    CACHE_STATUS = "status"
    PUPPY_GRAPH_DEFAULT_CONNECTION_ID="puppy_graph_url"
    UPDATE_GRAPHQL_API = "graphql/update-schema"
    REFRESH_CACHE = "ui-api/refreshLocalCache"
    PUPPY_SCHEMA_GET_URL = "schemajson"
    CONTEXT_VARIABLE_CONFIG = "sds-general-configs/context.json"
    CONTENT_TYPE_HEADER = {"Content-Type": "application/json"}
    SDS_REDIS_KEY_PREFIX = "sds_graphql_redis_clear_key"