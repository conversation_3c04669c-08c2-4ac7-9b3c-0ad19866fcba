ARG BASE_IMAGE
FROM $BASE_IMAGE

ARG AIRFLOW_HOME="/opt/airflow"
ARG AIRFLOW_UID="50000"

ENV PATH="${PATH}:/home/<USER>/.local/bin"
ENV VIRTUAL_ENV="/opt/airflow_env"

USER root

USER ${AIRFLOW_UID}

COPY requirements.txt ${AIRFLOW_HOME}

RUN pip download uv --no-deps -d wheels/ && \
    pip install wheels/uv*.whl && \
    uv pip install 'apache-airflow[celery,aws,redis,azure,amazon]==2.10.5' --target /home/<USER>/.local/lib/python3.12/site-packages \
    --no-cache-dir --constraint https://raw.githubusercontent.com/apache/airflow/constraints-2.10.5/constraints-3.12.txt && \
    uv pip install --target /home/<USER>/.local/lib/python3.12/site-packages \
    --no-cache-dir -r ${AIRFLOW_HOME}/requirements.txt

USER root

COPY --chown=airflow --chmod=755 ./dags/pe ${AIRFLOW_HOME}/sds/dags/pe
COPY --chown=airflow --chmod=755 ./plugins/pe ${AIRFLOW_HOME}/sds/plugins/pe
COPY --chown=airflow --chmod=755 ./commons/pe ${AIRFLOW_HOME}/sds/commons/pe
COPY --chown=airflow --chmod=755 ./create_metatable.py ${AIRFLOW_HOME}/
COPY --chown=airflow --chmod=755 ./config ${AIRFLOW_HOME}/config

USER ${AIRFLOW_UID}

ENV PYTHONPATH=${PYTHONPATH}:${AIRFLOW_HOME}:${AIRFLOW_HOME}/sds:${AIRFLOW_HOME}/sds/plugins

WORKDIR ${AIRFLOW_HOME}
EXPOSE 8080
ENTRYPOINT ["/usr/bin/dumb-init", "--", "/entrypoint"]
CMD []
