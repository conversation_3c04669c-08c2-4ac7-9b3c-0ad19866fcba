from copy import deepcopy
from airflow.config_templates.airflow_local_settings import DEFAULT_LOGGING_CONFIG
from airflow.configuration import conf
import os

# Flask appbuilder's info level log is very verbose,
# so it's set to 'WARN' by default.
BASE_LOG_FOLDER: str = conf.get('logging', 'BASE_LOG_FOLDER')
PROCESSOR_LOG_FOLDER: str = conf.get('scheduler', 'CHILD_PROCESS_LOG_DIRECTORY')
FILENAME_TEMPLATE: str = conf.get('logging', 'LOG_FILENAME_TEMPLATE')
PROCESSOR_FILENAME_TEMPLATE: str = conf.get('logging', 'LOG_PROCESSOR_FILENAME_TEMPLATE')
LOG_LEVEL: str = conf.get('logging', 'LOGGING_LEVEL').upper()
# Overriding default logging configuration object to route task logs to standard out for
#central logging.
# Reference - https://airflow.apache.org/docs/apache-airflow/stable/logging-monitoring/logging-tasks.html#advanced-configuration
LOGGING_CONFIG = deepcopy(DEFAULT_LOGGING_CONFIG)

LOGGING_CONFIG['handlers']['std_out_new'] = {
        'level': 'INFO',
        'formatter': 'airflow',
        'class': 'logging.StreamHandler',
        'stream': 'ext://sys.stdout',  # Default is stderr
        'filters': ['mask_secrets'],
    }


LOGGING_CONFIG['loggers']['airflow.task'] = {
    'handlers': ['task', 'std_out_new'],
    'level': LOG_LEVEL,
    'propagate': False,
    'filters': ['mask_secrets'],
}