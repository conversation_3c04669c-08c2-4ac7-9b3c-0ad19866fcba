from collections import Counter

from airflow.models import DAG
from airflow.exceptions import AirflowClusterPolicyViolation


def handle_timetable_plugins():
    from airflow.plugins_manager import initialize_timetables_plugins
    initialize_timetables_plugins()


def _dag_check_if_isinstance_of_sdsdag(dag:DAG):
    from commons.pe.models.sds_dags import SDSDAG
    if not isinstance(dag,SDSDAG):
        raise AirflowClusterPolicyViolation(f"DAG {dag.dag_id} is not an instance of SDSDAG. Please import it from commons.models.sds_dags.SDSDAG and use it as your dag object instead of default DAG object.")

def _dag_check_if_multiple_job_operator_has_same_app_name(dag:DAG):
    from commons.pe.operator.base import BaseJobOperator
    app_names = [task.task_id for task in dag.tasks if isinstance(task, BaseJobOperator)]
    app_name_counter = Counter(app_names)
    for app_name, count in app_name_counter.items():
        if count > 1:
            raise AirflowClusterPolicyViolation(f"{app_name} is defined {str(count)} times in SDSJobOperator tasks in dag {dag.dag_id}")

def dag_policy(dag:DAG):

    _dag_check_if_isinstance_of_sdsdag(dag)
    _dag_check_if_multiple_job_operator_has_same_app_name(dag)
