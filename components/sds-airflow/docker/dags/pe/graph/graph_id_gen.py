from copy import deepcopy
import datetime
import logging

from airflow.operators.empty import EmptyOperator
from airflow.utils.task_group import TaskGroup
from airflow.models.variable import Variable
from commons.pe.common_utils import Utils
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
from commons.pe.iceberg_utils import IcebergUtils
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory


logger = logging.getLogger("graph_id_population")

def get_graph_population_configs(table_name: str, entity_type: str, ):
    default_entity_job_var = "graph_population_entity_default_config"
    default_relationship_job_var = "graph_population_relationship_default_config"
    custom_job_var = "graph_population_custom_config"
    app_name =f"{table_name}_graph_id_generate"

    default_config = {}
    
    if entity_type == 'entity':
        default_config = Variable.get(key=default_entity_job_var, default_var={}, deserialize_json=True)
    else:
        default_config = Variable.get(key=default_relationship_job_var, default_var={}, deserialize_json=True)
    
    custom_config = Variable.get(key=custom_job_var, default_var={}, deserialize_json=True)
    
    extra_args = [
        "--entityType",
        f"{entity_type}",
        "--tableName",
        f"{table_name}"
    ]

    if table_name not in custom_config:
        logger.info(f"Spark config for table '{table_name}' not found. Taking default configuration")
    merged_config = Utils.merge_dictionaries(default_config, custom_config.get(table_name, {}))
    
    final_config = deepcopy(merged_config)
    final_config["app_name"] = app_name
    final_config["args"].extend(extra_args)

    logger.info(f"Final Config : {final_config}")

    return final_config    


with SDSPipelineDAG(
            dag_id="graph_id_population_dag",
            pipeline_id="graph",
            tags=["graph"],
            start_date=datetime.datetime(2021, 1, 1),
            render_template_as_native_obj=True,
            description="Dag to populate EI tables with graph IDs",
            user_defined_macros = {
                "get_graph_population_configs": get_graph_population_configs
            }

    ) as dag:
    start_task = EmptyOperator(task_id="start_graph_id_population")
    end_task = EmptyOperator(task_id="end_graph_id_population")

    graph_config = Utils.read_data_from_shared_filesystem("graph/schema.json")

    entity_tables = []
    relationship_tables = []

    # Extract entity (vertex) table names
    for vertex in graph_config.get('vertices', []):
        if 'mappedTableSource' in vertex and 'table' in vertex['mappedTableSource']:
            entity_tables.append(vertex['mappedTableSource']['table'])

    # Extract relationship (edge) table names
    for edge in graph_config.get('edges', []):
        if 'mappedTableSource' in edge and 'table' in edge['mappedTableSource']:
            relationship_tables.append(edge['mappedTableSource']['table'])

    logger.info(f"Vertex Tables : {entity_tables}")
    logger.info(f"Edge Tables: {relationship_tables}")

    with TaskGroup("vertex_population") as vertices:
        for table in entity_tables:
            SparkOperatorFactory.get(
                task_id=f"{table}_graph_id_generate",
                from_conf=f"get_graph_population_configs(table_name='{table}', entity_type='entity')"
            )

    with TaskGroup("edge_population") as edges:
        for table in relationship_tables:
            SparkOperatorFactory.get(
                task_id=f"{table}_graph_id_generate",
                from_conf=f"get_graph_population_configs(table_name='{table}', entity_type='relationship')"
            )

    start_task >> [vertices, edges] >> end_task