import json
import os
import base64
from datetime import datetime, timedelta
from commons.pe.common_utils import Utils
from commons.pe.iceberg_utils import IcebergUtils
from airflow.operators.python import PythonOperator, BranchPythonOperator
from airflow.operators.empty import EmptyOperator
from commons.pe.constants.airflow_constants import PuppyGraphConstants
from plugins.pe.puppy_graph.sds_puppy_graph_cache_sensor import SDSPuppyGraphCacheOperator
from airflow.providers.http.sensors.http import HttpSensor
from airflow.providers.http.operators.http import HttpOperator
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
from commons.pe.schema_generation_utils import PuppyGraphSchemaUtils
import pendulum
from plugins.pe.sds_redis_plugin.sds_redis_operator import SDSRedisDeleteKey

pg_utils = PuppyGraphSchemaUtils()

with SDSPipelineDAG(
        pipeline_id="SDS_EI_PIPELINE",
        dag_id="puppy_graph_olap_pipeline",
        description="PuppyGraph OLAP Pipeline",
        start_date=datetime(2024, 1, 1),
        render_template_as_native_obj=True,
        catchup=False,
        concurrency=60,
        tags=["SDS_EI_PIPELINE", "PUPPYGRAPH"],
        params={
            "data_interval_end": "2024-01-01"
        },
        user_defined_macros={
            "type": type,
            "json": json,
            "pendulum": pendulum,
            "get_keycloak_token": Utils.get_keycloak_token,
            "get_partition_interval": pg_utils.get_partition_interval,
            "get_graphql_payload": pg_utils.get_graphql_payload
        }
) as sds_graphql_schema_job:
    username = os.environ.get("PUPPYGRAPH_USERNAME", "admin")
    password = os.environ.get("PUPPYGRAPH_PASSWORD", "password")
    headers = {"Content-Type": "application/json"}
    credentials = f"{username}:{password}"
    encoded_credentials = base64.b64encode(credentials.encode("utf-8")).decode("utf-8")
    auth_header = {"Authorization": f"Basic {encoded_credentials}"}
    extra_base_endpoint = os.getenv("PUPPY_GRAPH_BASE_URL_PREFIX", "/")

    puppy_graph_schema_start = EmptyOperator(task_id="puppy_graph_schema_start")
    puppy_graph_schema_end = EmptyOperator(task_id="puppy_graph_schema_end", trigger_rule="one_success")

    schema_list = Utils.read_data_from_shared_filesystem(
        relative_file_path=PuppyGraphConstants.CONTEXT_VARIABLE_CONFIG).get("GRAPH_SCHEMA", "")
    schemas = [item.strip() for item in schema_list.split(",") if item.strip()]

    iceberg_utils = IcebergUtils()

    if not schemas:
        puppy_graph_schema_start >> puppy_graph_schema_end
    else:

        get_table_list_task_ids = [f"get_table_list_for_{item}" for item in schemas]
        puppy_graph_schema = PythonOperator(
            task_id="puppy_graph_schema_generator",
            python_callable=pg_utils.generate_final_schema,
            op_args=f"{{{{ ti.xcom_pull(key='table_list', task_ids={get_table_list_task_ids}, dag_id='puppy_graph_olap_pipeline') }}}}",
            trigger_rule="all_done",
            retries=3,
            retry_delay=timedelta(minutes=2))

        cache_refresh_start = EmptyOperator(task_id="cache_refresh_start")
        cache_refresh_end = EmptyOperator(task_id="cache_refresh_end", trigger_rule="none_failed_min_one_success")

        for schema in schemas:
            get_table_list = PythonOperator(
                task_id=f"get_table_list_for_{schema}",
                python_callable=pg_utils.get_table_list,
                op_kwargs={"schema": schema},
                retries=3,
                retry_delay=timedelta(minutes=2))

            schema_creation = PythonOperator.partial(
                task_id=f"{schema}_task_map",
                python_callable=pg_utils.generate_schema,
                retries=3,
                retry_delay=timedelta(minutes=2)).expand(op_kwargs=get_table_list.output)

            get_table_list_from_xcom = PythonOperator(
                task_id=f"get_table_list_from_xcom_for_{schema}",
                python_callable=pg_utils.get_table_list_from_xcom,
                op_kwargs={"schema": schema},
                retries=3,
                retry_delay=timedelta(minutes=2))

            cache_sensor = SDSPuppyGraphCacheOperator.partial(
                task_id=f"{schema}_cache_refresh_sensor",
                puppy_graph_conn_id="puppy_graph_url",
                request_params={},
                data="{{ get_partition_interval(ds, data_interval_end, task_instance) }}",
                headers={**auth_header, **headers},
                retries=3,
                retry_delay=timedelta(minutes=2)).expand_kwargs(get_table_list_from_xcom.output)

            cache_refresh_start >> get_table_list_from_xcom >> cache_sensor >> cache_refresh_end

            puppy_graph_schema_start >> get_table_list >> schema_creation >> puppy_graph_schema

        check_schema_change = BranchPythonOperator(
            task_id="check_change_in_schema",
            python_callable=pg_utils.check_schema_change,
            op_args=["puppy_graph_url", headers, username, password],
            retries=3,
            retry_delay=timedelta(minutes=2)
        )
        update_graphql_api = HttpOperator(
            task_id="update_graphql_api",
            http_conn_id="graphql_api_url",
            endpoint=PuppyGraphConstants.UPDATE_GRAPHQL_API,
            method=PuppyGraphConstants.POST,
            headers="{{ {'Authorization':  get_keycloak_token()} }}",
            data="{{ json.dumps(get_graphql_payload(task_instance)) }}",
            response_check=lambda response: response.status_code == 200,
            retries=3,
            retry_delay=timedelta(minutes=2))

        update_puppy_graph = HttpOperator(
            task_id="update_puppy_graph_schema",
            http_conn_id="puppy_graph_url",
            endpoint=os.path.join(extra_base_endpoint, PuppyGraphConstants.POST_SCHEMA),
            method=PuppyGraphConstants.POST,
            headers={**auth_header, **headers},
            log_response=True,
            data="{{ json.dumps(task_instance.xcom_pull(task_ids='puppy_graph_schema_generator', key='PUPPY_GRAPH_SCHEMA')) }}",
            response_check=lambda response: response.status_code == 200,
            retries=3,
            retry_delay=timedelta(minutes=2))

        cache_population_sensor = HttpSensor(
            task_id="cache_population_sensor",
            http_conn_id="puppy_graph_url",
            endpoint=os.path.join(extra_base_endpoint, PuppyGraphConstants.CACHE_STATUS),
            method=PuppyGraphConstants.GET,
            headers={**auth_header, **headers},
            response_check=pg_utils.cache_sense_check,
            retries=3,
            retry_delay=timedelta(minutes=2))

        sds_clear_graphql_cache = SDSRedisDeleteKey(
            task_id="sds_clear_graphql_cache",
            key="{{ var.value.%s }}" % PuppyGraphConstants.SDS_REDIS_KEY_PREFIX,
            redis_conn_id="graphql_redis",
            retries=3,
            retry_delay=timedelta(minutes=2)
        )

        check_schema_change >> cache_refresh_start
        cache_refresh_end >> puppy_graph_schema_end
        puppy_graph_schema >> sds_clear_graphql_cache >> update_graphql_api >> check_schema_change >> update_puppy_graph >> cache_population_sensor >> puppy_graph_schema_end
