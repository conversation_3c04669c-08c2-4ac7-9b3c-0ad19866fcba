from cerberus import Validator

from airflow.exceptions import AirflowException, AirflowConfigException


from commons.pe.common_utils import Utils

from commons.pe.constants.airflow_constants import AirflowConfigurationConstants, OlapAutoGenConstants
from commons.pe.olap_auto_dag_factory  import OlapDAGFactory

olap_dag_gen_config = Utils.read_data_from_shared_filesystem(AirflowConfigurationConstants.SDS_OLAP_DYNAMIC_TASKS_FILE_PATH)
validator = Validator()
if olap_dag_gen_config:
    for dag_id,dag_config in olap_dag_gen_config.items():
        validator.validate(dag_config, OlapAutoGenConstants.SDS_OLAP_CONFIG_SCHEMA_VALIDATOR)
        if validator.errors != {}:
            raise AirflowConfigException(f"Validation failed: for dag_id {dag_id} with error {validator.errors}")
        else:
            globals()[dag_id] = OlapDAGFactory.get_airflow_dag(dag_id=f"{dag_id}_dag",
                                                               tasks=dag_config.get("task_group",[]),
                                                               tags=dag_config.get("tags",[]),
                                                               pipeline_id=dag_config["pipeline_id"])


