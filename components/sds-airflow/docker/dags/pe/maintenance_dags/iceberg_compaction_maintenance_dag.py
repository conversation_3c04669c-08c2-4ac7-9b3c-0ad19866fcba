from airflow.operators.empty import EmptyOperator
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from commons.pe.common_utils import Utils
from commons.pe.models.sds_dags import SDSDAG
from datetime import datetime
from manual_only_timetable import ManualOnlyTimetable
from commons.pe.constants.airflow_constants import MaintenanceJobConstants as MJC

with SDSDAG(
    dag_id="iceberg_compaction_maintenance_dag",
    timetable=ManualOnlyTimetable(),
    catchup=False,
    render_template_as_native_obj=True,
    start_date=datetime(2020, 1, 1),
    description="Dag to compact iceberg tables",
    tags=["Iceberg maintenance"],
    params={"table_name": "", "strategy": "", "options": "", "where": "", "sort_order": ""},
    user_defined_macros={
                "get_maintenance_configs": Utils.get_iceberg_compaction_maintenance_configs
            }

) as iceberg_tables_compaction_dag:
    start_task = EmptyOperator(task_id="start")
    end_task = EmptyOperator(task_id="end")

    iceberg_table_compaction = SparkOperatorFactory.get(
        task_id="iceberg-compaction-job",
        from_conf="get_maintenance_configs(params)"

    )
    start_task >> iceberg_table_compaction >> end_task
