from datetime import datetime
from commons.pe.models.sds_dags import SDSDAG
from airflow.operators.empty import EmptyOperator
from airflow.models.variable import Variable
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from commons.pe.constants.airflow_constants import MaintenanceJobConstants as MJC
from commons.pe.common_utils import Utils

with SDSDAG(
        dag_id="iceberg_delete_orphan_files_dag",
        schedule_interval="0 0 * * *",
        catchup=False,
        render_template_as_native_obj=True,
        start_date=datetime(2020, 1, 1),
        description="Dag to delete orphan files in Iceberg",
        tags=["Iceberg maintenance"],
        user_defined_macros={
                "get_maintenance_configs": Utils.get_maintenance_configs
            }
) as delete_orphan_files_dag:
    start_task = EmptyOperator(task_id="start")
    end_task = EmptyOperator(task_id="end")

    iceberg_delete_orphan_files = SparkOperatorFactory.get(
        task_id = "iceberg-delete-orphan-files",
        from_conf="get_maintenance_configs(data_interval_end)"
        )
        
    start_task >> iceberg_delete_orphan_files >> end_task