# trigger dag reserialization
from commons.pe.models.sds_dags import SDSDAG
from airflow.operators.python import PythonOperator
from airflow.models.serialized_dag import SerializedDagModel
from airflow.utils.session import provide_session
from datetime import datetime, timedelta

@provide_session
def delete_serialized_dags(session,**kwargs,):
    session.query(SerializedDagModel).delete(synchronize_session=False)
    session.commit()

with SDSDAG(
    dag_id='reserialize_sharedFs',
    description='Delete Serialized DAGs in dagbag and trigger reserialization',
    start_date= datetime(2024, 1, 1),
    schedule_interval=None,
    tags=["RESERIALIZE_DAGBAG","RESERIALIZE_SCHEDULER"] 
) as dag:

    reserialize_sharedFs_task = PythonOperator(
    task_id='reserialize_dags_task',
    python_callable=delete_serialized_dags,
    provide_context=True,
)

