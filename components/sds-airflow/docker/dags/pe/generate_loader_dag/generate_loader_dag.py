import logging
import yaml
import os
from commons.pe.dag_factory.dag_factory import  load_dags_from_yaml
from commons.pe.common_utils import Utils
from commons.pe.constants.airflow_constants import AutoParserConstants, PuppyGraphConstants
import glob

data_dictionary = Utils.read_data_from_shared_filesystem(relative_file_path=AutoParserConstants.EI_DATA_DICTIONARY_PATH)
namespace = Utils.read_data_from_shared_filesystem(
        relative_file_path=PuppyGraphConstants.CONTEXT_VARIABLE_CONFIG).get("KUBE_NAMESPACE", "")

if os.path.exists("/opt/airflow/shared/generate_loader_dag_template/template_dag.yaml"):
    with open("/opt/airflow/shared/generate_loader_dag_template/template_dag.yaml", "r") as file:
        file_content = yaml.load(file, Loader=yaml.SafeLoader)

entity_list = ["generate_"+ entity.lower().replace(" ", "_") + "_loader_dag.yaml" for entity in data_dictionary]
files_list = [file.split("/")[-1] for file in glob.glob("/opt/airflow/shared/loader_with_ai_dags/generate_*.yaml")]

not_created = [entity for entity in entity_list if entity not in files_list]

logging.info(f"Found files difference: {files_list}, {entity_list} and not created: {not_created}")

for entity_name in not_created:
    entity_name = entity_name.replace(".yaml", "")
    new_file_content = {}
    new_file_content[entity_name] = file_content["loader_with_ai_template"]
    new_file_content[entity_name]["tasks"]["trigger_config_suite_in_k8s_pod"]["name"] = f"run-{entity_name}-pod"
    for secret in new_file_content[entity_name]["tasks"]["trigger_config_suite_in_k8s_pod"]["secrets"]:
        secret["secret"] = f"external-secret-vault-{namespace}"
    os.makedirs("/opt/airflow/shared/loader_with_ai_dags", exist_ok=True)
    with open(f"/opt/airflow/shared/loader_with_ai_dags/{entity_name}.yaml", "w") as new_file:
        yaml.dump(new_file_content, new_file)

load_dags_from_yaml(globals(), config_path="loader_with_ai_dags")
logging.info("DAG generator in loader_with_ai_dags.py has been executed")