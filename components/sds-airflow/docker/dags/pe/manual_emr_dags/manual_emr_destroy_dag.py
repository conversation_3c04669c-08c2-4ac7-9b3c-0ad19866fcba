from datetime import datetime

from commons.pe.models.sds_dags import SDSDAG
from plugins.pe.sds_emr_plugin.operators.sds_emr_operator import SDSClusterDestroyOperator
from plugins.pe.sds_emr_plugin.operators.sds_emr_utils import ClusterType
from plugins.pe.sds_emr_plugin.operators.sds_emr_utils import EMRConstants

MANUAL_EMR_DEFAULT_CLUSTER_NAME = "SDS_MANUAL"
MANUAL_EMR_DEFAULT_CLUSTER_ID = "SHARED_CLUSTER"
MANUAL_EMR_DEFAULT_CLUSTER_TYPE = ClusterType.LARGE.value
MANUAL_EMR_DEFAULT_TF_S3_LOC = EMRConstants.TF_S3_DEFAULT

with SDSDAG(dag_id="manual_emr_destroy_dag",
         schedule_interval=None,
         start_date=datetime(2000, 1, 1),
         concurrency=1,
         catchup=False,
         tags=['MANUAL_EMR', 'EMR'],
         params={"cluster_name": MANUAL_EMR_DEFAULT_CLUSTER_NAME, "cluster_id": MANUAL_EMR_DEFAULT_CLUSTER_ID,
                 "cluster_type": MANUAL_EMR_DEFAULT_CLUSTER_TYPE, "tf_s3_key": MANUAL_EMR_DEFAULT_TF_S3_LOC,
                 "hadoop_sync": "true"}
         ) as dag:
    SDSClusterDestroyOperator(
        task_id="destroy_manual_emr_cluster",
        cluster_name=("{{ dag_run.conf.get('cluster_name', '%s')}}" % MANUAL_EMR_DEFAULT_CLUSTER_NAME),
        cluster_id=("{{ dag_run.conf.get('cluster_id', '%s')}}" % MANUAL_EMR_DEFAULT_CLUSTER_ID),
        cluster_type=("{{ dag_run.conf.get('cluster_type', '%s')}}" % MANUAL_EMR_DEFAULT_CLUSTER_TYPE),
        tf_s3_key=("{{ dag_run.conf.get('tf_s3_key', '%s')}}" % MANUAL_EMR_DEFAULT_TF_S3_LOC),
        hadoop_sync="{{ dag_run.conf.get('hadoop_sync', 'true')}}"
    )
