from airflow.operators.empty import EmptyOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator

from commons.pe.constants.airflow_constants import AutoParserConstants
from commons.pe.common_utils import Utils
from commons.pe.models.sds_dags import SDSDAG
from datetime import datetime

with SDSDAG(
        dag_id="sds_trigger_on_demand_dag",
        description="SDS trigger on demand dag",
        start_date=datetime(2023, 1, 1),
        schedule_interval=None,
        render_template_as_native_obj=True,
        catchup=False,
        tags=["TRIGGER_ON_DEMAND"],

) as sds_trigger_on_demand_dag:
    sds_trigger_start = EmptyOperator(task_id="sds_trigger_start")
    sds_trigger_end = EmptyOperator(task_id="sds_trigger_end")
    autoparser_config = Utils.read_data_from_shared_filesystem(
        relative_file_path=AutoParserConstants.AUTOPARSER_DAG_GEN_FILE)
    for dag_name, config_value in autoparser_config.items():
        dag_id = f"{dag_name}-autoparser-dag"
        input_param = {
            "data_interval_start": "2023-01-01T10:00:00Z",
            "data_interval_end": "{{data_interval_start.to_iso8601_string()}}",
            "timezone": "UTC"

        }
        task= TriggerDagRunOperator(task_id=f"trigger_{dag_id}",
                                                  trigger_dag_id=dag_id, conf=input_param)

        sds_trigger_start >> task >> sds_trigger_end
