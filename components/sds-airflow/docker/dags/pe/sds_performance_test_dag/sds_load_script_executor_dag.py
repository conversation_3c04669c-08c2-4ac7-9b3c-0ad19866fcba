from datetime import datetime

from airflow.operators.empty import EmptyOperator
from commons.pe.models.sds_dags import SDSDAG
from commons.pe.qa_automation.qa_automation_constants.constants import QAAutomationConstants
from commons.pe.qa_automation.user_behaviour_recorder_utils import UserBehaviourUtils

with SDSDAG(
        dag_id="sds_load_script_executor_dag",
        description="SDS load_script_executor dag",
        start_date=datetime(2023, 1, 1),
        schedule_interval=None,
        render_template_as_native_obj=True,
        catchup=False,
        tags=[QAAutomationConstants.PERFORMANCE_TEST, QAAutomationConstants.LOAD_SCRIPT_EXECUTOR],
        params={
            "endpoint": "/insight-api/api/v3/dremio/query",
            "users": 1,
            "input_query_location": "<%DATALAKE_URI%>/reports/performance_test_input/",
            "performance_result_location": "<%DATALAKE_URI%>/reports/performance_test_output/results",
            "run_time_in_seconds": "20"

        }

) as sds_user_behaviour_recorder:
    sds_load_script_executor_start = EmptyOperator(task_id="sds_load_script_executor_start")
    sds_load_script_executor_end = EmptyOperator(task_id="sds_load_script_executor_end")
    run_locust_script = UserBehaviourUtils.execute_locust_test(endpoint="{{params.endpoint}}",
                                                               input_path="{{params.input_query_location}}",
                                                               output_path="{{params.performance_result_location.rstrip('/')}}"+"/{{data_interval_start.strftime('%Y-%m-%d-%H:%M:%S')}}",
                                                               users="{{params.users}}",
                                                               runtime="{{params.run_time_in_seconds}}")
    sds_load_script_executor_start >> run_locust_script >> sds_load_script_executor_end
