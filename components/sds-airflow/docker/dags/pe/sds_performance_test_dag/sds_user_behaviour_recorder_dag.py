from datetime import datetime

from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.utils.task_group import TaskGroup

from commons.pe.common_utils import Utils
from commons.pe.models.sds_dags import SDSDAG
from commons.pe.qa_automation.qa_automation_constants.constants import QAAutomationConstants
from commons.pe.qa_automation.user_behaviour_recorder_utils import UserBehaviourUtils

with SDSDAG(
        dag_id="sds_user_behaviour_recorder_dag.",
        description="SDS user behaviour recorder dag",
        start_date=datetime(2023, 1, 1),
        schedule_interval=None,
        render_template_as_native_obj=True,
        catchup=False,
        tags=[QAAutomationConstants.PERFORMANCE_TEST, QAAutomationConstants.USER_BEHAVIOUR_RECORDER],
        params={
            "run_load_test": False,
            "endpoint": "/insight-api/api/v3/dremio/query",
            "users": 1,
            "input_query_location": "<%DATALAKE_URI%>/reports/performance_test_input/",
            "performance_result_location": "<%DATALAKE_URI%>/reports/performance_test_output/results",
            "run_time_in_seconds": "20"

        }

) as sds_user_behaviour_recorder:
    sds_user_behaviour_recorder_start = EmptyOperator(task_id="sds_user_behaviour_recorder_start")
    sds_user_behaviour_recorder_end = EmptyOperator(task_id="sds_user_behaviour_recorder_end",
                                                    trigger_rule="none_failed")
    user_behavior_config = Utils.read_data_from_shared_filesystem(
        relative_file_path=QAAutomationConstants.SDS_USER_BEHAVIOUR_RECORDER_MODULE)
    with TaskGroup(group_id="sds_user_behaviour_recorder_tasks") as sds_user_behaviour_recorder_tasks:
        UserBehaviourUtils.generate_analytical_tasks(user_behavior_config, "user_behaviour_recorder")

    run_locust_test = PythonOperator(task_id="run_locust_test",
                                     python_callable=UserBehaviourUtils.check_if_perf_test_must_run,
                                     trigger_rule="all_success")

    sds_user_behaviour_recorder_start >> sds_user_behaviour_recorder_tasks >> run_locust_test >> sds_user_behaviour_recorder_end
