from datetime import datetime
import logging
from airflow.operators.python import Python<PERSON>perator
from airflow.utils.session import provide_session

from commons.pe.models.sds_dags import SDSDAG
from commons.pe.sds_dag_orchestrator import SDSOrchestrationMeta
from sqlalchemy.orm import Session
import pendulum
from airflow.exceptions import AirflowException


@provide_session
def delete_all_entries_from_metatable(session: Session = None, **kwargs):
    partial_clear = kwargs.get('partial_clear')
    delete_dag_id = kwargs.get('dag_id')
    logger = logging.getLogger("delete_entries_from_metatable")
    if partial_clear is None:
        raise AirflowException("Dag is triggered without config, please trigger the dag with config")
    try:
        if partial_clear:
            logger.info(f"Deleting {delete_dag_id} between dates {kwargs.get('start_date')} and {kwargs.get('end_date')}")
            rows_deleted = session.query(SDSOrchestrationMeta).filter(
                SDSOrchestrationMeta.head_dag_id == delete_dag_id,
                SDSOrchestrationMeta.logical_date >= pendulum.parse(kwargs.get("start_date")),
                SDSOrchestrationMeta.logical_date <= pendulum.parse(kwargs.get("end_date"))
            ).delete()
        else:
            logger.info("Deleting all the entries from meta")
            rows_deleted = session.query(SDSOrchestrationMeta).delete()
        session.commit()
        logger.info(f"{rows_deleted} rows deleted from orchestration meta table")
    except Exception as e:
        session.rollback()
        logger.info(f"Unable to delete rows from orchestration meta table. Exception raised - {e}")


with SDSDAG(
        dag_id="clear_orchestration_metatable_dag",
        description="DAG to delete all entries from orchestration metatable",
        start_date=datetime(2000, 1, 1),
        schedule_interval=None,
        render_template_as_native_obj=True,
        params={
            "partial_clear": True,
            "dag_id": "default_dag_id",
            "start_date": "2000-01-01T00:00:00",
            "end_date": "2000-01-01T00:00:00"
        },
        catchup=False,
        tags=["SDS_ORCHESTRATOR"]
) as dag:
    clear_metatable_operator = PythonOperator(task_id="delete_entries",
                                              python_callable=delete_all_entries_from_metatable,
                                              op_kwargs="{{ dag_run.conf }}"
                                              )
