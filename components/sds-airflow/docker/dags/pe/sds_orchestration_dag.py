from datetime import datetime, timedelta

from commons.pe.models.sds_dags import SDSDAG
from plugins.pe.sds_dag_orchestrator_plugin.operators.pipeline_orchestrator import PipelineOrchestrationOperator
from plugins.pe.sds_dag_orchestrator_plugin.operators.sds_configuration_validation_operator import \
    SDSConfigurationValidatorOperator

with SDSDAG(
        dag_id='mediator',
        description='DAG that orchestrates airflow pipeline based on the dependencies specified',
        start_date=datetime(2021, 1, 1),
        schedule_interval=None,
        catchup=False,
        tags=["SDS_ORCHESTRATOR","MEDIATOR"]
) as dag:
    config_validator = SDSConfigurationValidatorOperator(task_id="config_validator")

    dependency_checker = PipelineOrchestrationOperator(task_id='dependency_checker')

    config_validator >> dependency_checker