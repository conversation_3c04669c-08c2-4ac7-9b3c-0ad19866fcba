import json
import pendulum

from datetime import datetime
from airflow.operators.empty import EmptyOperator
from airflow.utils.task_group import TaskGroup
from commons.pe.common_utils import Utils
from commons.pe.models.sds_dags import SDSDAG
from commons.pe.qa_automation.qa_automation_constants.constants import QAAutomationConstants
from commons.pe.qa_automation.qa_automation_utils import QAAutomationUtils
from commons.pe.sds_date_utils import SDSDateUtils



with SDSDAG(
    dag_id="sds_platform_qa_automation_dag",
    description="SDS Platform QA automation DAG",
    start_date=datetime(2023, 1, 1),
    schedule_interval=None,
    render_template_as_native_obj=True,
    catchup=False,
    tags=[QAAutomationConstants.MODULE_NAME, QAAutomationConstants.PLATFORM_VALIDATION],
    user_defined_macros={
        "render_variable": Utils.render_variable,
        "get_start_date": SDSDateUtils.get_start_date,
        "get_end_date": SDSDateUtils.get_end_date,
        "json": json,
        "pendulum": pendulum,
        "get_analysis_period_details": SDSDateUtils.get_analysis_period_details,
    },
    params={
        "data_interval_start": "2023-01-01T10:00:00",
        "data_interval_end": "2023-01-01T12:00:00",
        "timezone": "UTC"
    }
) as sds_platform_validation:

    sds_platform_validation_start = EmptyOperator(task_id="sds_platform_qa_automation_start")
    sds_platform_validation_end = EmptyOperator(task_id="sds_platform_qa_automation_end")

    sds_platform_validation_tasks_config = Utils.read_data_from_shared_filesystem(
        relative_file_path=QAAutomationConstants.SDS_PLATFORM_QA_AUTOMATION_MODULE
    )
    
    if sds_platform_validation_tasks_config:
        with TaskGroup(group_id="sds_platform_validation_tasks") as sds_platform_validation_tasks:
            QAAutomationUtils.generate_analytical_tasks(sds_platform_validation_tasks_config, "sds_platfrom_validation_configs")

        sds_platform_validation_start >> sds_platform_validation_tasks >> sds_platform_validation_end
