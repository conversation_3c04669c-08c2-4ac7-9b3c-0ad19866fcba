from airflow.operators.empty import EmptyOperator
from datetime import datetime
from airflow.operators.bash import BashOperator
from airflow.operators.python import PythonOperator
from commons.pe.constants.airflow_constants import AirflowConstants
from commons.pe.common_utils import Utils
from commons.pe.models.sds_dags import SDSDAG

with SDSDAG(
        dag_id="sds_backfill_dag",
        schedule_interval=None,
        catchup=False,
        start_date=datetime(2000, 1, 1),
        description="Dag to run backfill for specified dag id",
        tags=["BACKFILL", "SDS_ORCHESTRATOR"]
) as sds_backfill_dag:
    start_task = EmptyOperator(
        task_id="start_sds_backfill_dag"
    )

    backfill_task = BashOperator(
        task_id="execute_backfill_command",
        bash_command="airflow dags backfill --reset-dagruns -y -s " \
                     F"{{{{ var.json.{AirflowConstants.SDS_BACKFILL_CONFIGURATION}.get('start_date') }}}} -e " \
                     F"{{{{ var.json.{AirflowConstants.SDS_BACKFILL_CONFIGURATION}.get('end_date') }}}} " \
                     F"{{{{ var.json.{AirflowConstants.SDS_BACKFILL_CONFIGURATION}.get('dag_id') }}}}"
    )

    clear_task = PythonOperator(
        task_id="clear_entries",
        python_callable=Utils.clear_dag_run_for_pipeline
    )

    start_task >> clear_task >> backfill_task
