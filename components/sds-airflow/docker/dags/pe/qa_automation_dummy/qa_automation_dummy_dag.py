from commons.pe.models.sds_dags import SDSDAG
from airflow.operators.bash import BashOperator
from airflow.utils.dates import days_ago

default_args = {
    'owner': 'airflow',
    'start_date': days_ago(1),
}

with SDSDAG(dag_id='qa_automation_dummy', default_args=default_args, schedule_interval=None, tags=["qa-auto-dummy", "devops"], is_paused_upon_creation=False) as dag:
    start = BashOperator(
        task_id='start',
        bash_command='echo "Starting the dummy dag run..."'
    )

    task_1 = BashOperator(
        task_id='task_1',
        bash_command=f"""
            set -e

            sleep 200
        """
    )

    task_2 = BashOperator(
        task_id='task_2',
        bash_command=f"""
            set -e

            sleep 300
        """
    )

    task_3 = BashOperator(
        task_id='task_3',
        bash_command=f"""
            set -e

            sleep 400
        """
    )

    start >> task_1 >> task_2 >> task_3
