from datetime import datetime

from airflow.operators.python import PythonOperator
from airflow.operators.empty import EmptyOperator

from commons.pe.common_utils import Utils
from commons.pe.constants.airflow_constants import AutoParserConstants
from commons.pe.models.sds_dags import SDSDAG
from manual_only_timetable import ManualOnlyTimetable

from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from commons.pe.candidate_key_gen_utils import CandidateKeyGenUtils
from commons.pe.sds_date_utils import SDSDateUtils

with SDSDAG(
    dag_id="candidate_key_generation",
    start_date=datetime(2021,1,1),
    timetable=ManualOnlyTimetable(),
    tags=["CANDIDATE-KEY-GENERATION", "DATA-SCIENCE", "SDS-STUDIO"],
    catchup=False,
    user_defined_macros={
        "get_end_date": SDSDateUtils.get_end_date,
        "get_start_date": SDSDateUtils.get_start_date
    },
    is_paused_upon_creation=False
) as dag:
    
    start_task = EmptyOperator(
        task_id='start_candidate_key_generation_job'
    )

    prepare_job_config = PythonOperator(
            task_id='prepare_job_config',
            python_callable=CandidateKeyGenUtils.prepare_job_config,
            provide_context=True,
        )

    generate_candidate_key_job = SparkOperatorFactory.get(
        task_id=f"generate_candidate_key_job",
        retries=3,
        from_conf=f"ti.xcom_pull(key='return_value', task_ids='prepare_job_config')"
    )

    end_task = EmptyOperator(
        task_id='end_candidate_key_generation_job'
    )

    start_task >> prepare_job_config >> generate_candidate_key_job >> end_task
