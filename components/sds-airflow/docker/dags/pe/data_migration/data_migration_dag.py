
from datetime import datetime
from commons.pe.common_utils import Utils
from airflow.utils.task_group import TaskGroup
from airflow.operators.empty import EmptyOperator
from commons.pe.sds_autoparser_utils import AutoParserUtils
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from commons.pe.constants.airflow_constants import DataMigrationConstants as DMC
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG

with SDSPipelineDAG(
        dag_id="sds_data_migration_dag",
        pipeline_id="DATA_MIGRATION",
        schedule_interval=None,
        catchup=False,
        render_template_as_native_obj=True,
        start_date=datetime(2000, 1, 1),
        description="Dag to migrate Parquet data to Iceberg",
        tags=["Data migration", "Parquet to Iceberg","History Data migration"],
        user_defined_macros={
            "get_data_migration_configs": AutoParserUtils.get_data_migration_configs
        }
) as sds_data_migration_dag:
    start_task = EmptyOperator(task_id="start")
    end_task = EmptyOperator(task_id="end")

    sources = Utils.read_data_from_shared_filesystem(DMC.DATA_MIGRATION_SOURCES_FILE)

    with TaskGroup("data_migration_list") as source_models:
        for data_source, enabled in sources.items():
            if enabled:
                    SparkOperatorFactory.get(
                         task_id=f"{data_source}-TO-ICEBERG",
                         from_conf=f"get_data_migration_configs('{data_source}')"
                    )

    start_task >> source_models >> end_task