import os
import logging
from datetime import datetime

from airflow import plugins_manager

from commons.pe.models.sds_dags import SDSDAG

plugins_manager.initialize_timetables_plugins()
from airflow.models.variable import Variable
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from commons.pe.common_utils import Utils
from commons.pe.constants.airflow_constants import AirflowConfigurationConstants
from manual_only_timetable import ManualOnlyTimetable

SHARED_VOLUME_PATH = os.getenv(
    key=AirflowConfigurationConstants.SHARED_VOLUME_PATH_ENV_VARIABLE, default="/shared"
)



def config_update_callable(**kwargs):
    logger = logging.getLogger("update-dag-factory-config")
    all_config = kwargs['dag_run'].conf if kwargs['dag_run'].conf else Variable.get(AirflowConfigurationConstants.AIRFLOW_CONFIG_UPDATE_DAG_VARIABLE, deserialize_json=True)
    logger.info(f"Obtained config: {all_config}")

    for key, value in all_config.items():
        target_data, target_path = (value, key) if kwargs["dag_run"].conf else (Variable.get(key=key, deserialize_json=True), value)
        path_to_write = os.path.join(SHARED_VOLUME_PATH, target_path)
        Utils.write_data_to_shared_filesystem(data=target_data, file_path=path_to_write)
        logger.info(f"Successfully wrote variable '{target_data}' to path '{path_to_write}'")
        

with SDSDAG(
    dag_id="config_updation_dag",
    description="Execution dag that uses to update the config after the change",
    start_date=datetime(2021, 1, 1),
    catchup=False,
    timetable=ManualOnlyTimetable(),
    tags=["CONFIGS"]
) as dag:
    start_execution = EmptyOperator(task_id="start_execution")
    analytics_config_write = PythonOperator(
        task_id="config_write",
        python_callable=config_update_callable,
        provide_context=True
    )

    start_execution >> analytics_config_write
