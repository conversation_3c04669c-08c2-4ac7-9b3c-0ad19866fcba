import json
from datetime import datetime
from typing import Dict

from airflow.operators.empty import EmptyOperator
from airflow.operators.python import <PERSON><PERSON>perator, BranchPythonOperator
from airflow.providers.http.operators.http import HttpOperator
from airflow.utils.trigger_rule import TriggerRule

from commons.pe.constants.endpoints import ApiEndpoints
from commons.pe.models.sds_dags import SDSDAG
from commons.pe.recon_utils import ReconUtils
from commons.pe.sds_autoparser_utils import AutoParserUtils
from commons.pe.constants.airflow_constants import AutoParserConstants as AP, AirflowConstants
from delayed_interval_timetable import DelayedIntervalTimetable

from plugins.pe.sds_redis_plugin.sds_redis_operator import SDSRedisDeleteKey
from commons.pe.common_utils import Utils
from plugins.pe.sds_redis_plugin.sds_redis_operator import SDSRedisDeleteKey
from plugins.pe.java.java_kubernetes_operator import JavaKubernetesOperator

def create_dag(source: str, config: Dict):
    SRDM_TABLE_NAME = config.get("srdm_table") if config.get("srdm_table", None) else source
    recon_utils = ReconUtils(conn_id=AP.RECON_REST_CONN_ID, source=source, recon_type="data-source")
    with SDSDAG(
            dag_id=f"{source}_recon_dag",
            timetable=DelayedIntervalTimetable(cron=config.get("cron", None),
                                               delay_hour=config.get("recon_buffer_period", 0)),
            start_date=datetime(2021, 1, 1),
            render_template_as_native_obj=True,
            catchup=False,
            user_defined_macros={
                "json": json,
                "generate_keycloak_token":Utils.get_keycloak_token,
                "get_data_interval": AutoParserUtils.get_data_interval,
                "get_recon_job_args": recon_utils.get_recon_job_args
            },
            tags=["RECON_MANAGER", "DATASOURCE-RECON", "RECON", source.upper()] + config.get("tags", []),
    ) as dag:

        start_task = EmptyOperator(task_id="start_task")
        end_task = EmptyOperator(task_id="end_task")

        execute_srdm_data_source_count = JavaKubernetesOperator(
                task_id=f"{source}-recon-data-source-{AP.SRDM}",
                namespace=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('namespace') }}}}",
                image=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('image') }}}}",
                init_container_image=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('init_container_image') }}}}",
                jar=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('jar') }}}}",
                class_name="ai.prevalent.recon.iceberg.IcebergReconciliation",
                name=f"{source}-RECON-data-source-{AP.SRDM}",
                env_from=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('env_from',[]) }}}}",
                environment_variables=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('environment_variables',{{}}) }}}}",
                labels=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('labels',{{}}) }}}}",
                container_resources_dict=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('container_resources',{{}}) }}}}",
                annotations=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('annotations',{{}}) }}}}",
                job_args=f"{{{{ get_recon_job_args('{source}', '{AP.SRDM}', '{SRDM_TABLE_NAME}' ,get_data_interval(data_interval_start,data_interval_end,dag_run)[0],get_data_interval(data_interval_start,data_interval_end,dag_run)[1],) }}}}",
            )

        redis_clear_task = SDSRedisDeleteKey(
            task_id="redis_clear",
            key=f"{{{{ var.value.{AP.DATA_SOURCE_RECON_REDIS_KEY_VARIABLE} }}}}",
            redis_conn_id="redis_default"
        )

        start_task >> execute_srdm_data_source_count

        if config.get("is_recovered", False):
            srdm_count_from_db = PythonOperator(
                task_id="get_srdm_count",
                python_callable=recon_utils.get_recon_count,
                op_args=["{{data_interval_start.to_iso8601_string().split('.')[0]}}","{{data_interval_end.to_iso8601_string().split('.')[0]}}" , "SRDM"]

            )
            splunk_count_from_db = PythonOperator(
                task_id="get_splunk_count",
                python_callable=recon_utils.get_recon_count,
                op_args=["{{data_interval_start.to_iso8601_string().split('.')[0]}}","{{data_interval_end.to_iso8601_string().split('.')[0]}}" , "splunk"]

            )
            compare_count = BranchPythonOperator(
                task_id='compare_srdm_splunk_count_task',
                python_callable=recon_utils.generate_ticket,
                op_args=[
                    source_name,
                    config.get("error_percentage")
                ]
            )

            generate_payload_with_status_new = PythonOperator(
                task_id="populate_new_ticket_payload",
                python_callable=recon_utils.populate_recon_ticket_data,
                op_args=[source_name, "New", "DataSource"]

            )
            generate_payload_with_status_done = PythonOperator(
                task_id="populate_done_ticket_payload",
                python_callable=recon_utils.populate_recon_ticket_data,
                op_args=[source_name, "Done", "DataSource"]

            )
            create_recon_ticket = HttpOperator(
                task_id='generate_recon_ticket',
                data="{{ json.dumps(ti.xcom_pull(key='recon_ticket_payload')) }}",
                headers={
                    **AirflowConstants.DEFAULT_HTTP_HEADERS,
                   **{"Authorization": "{{ generate_keycloak_token() }}"}},
                dag=dag,
                http_conn_id=AP.RECON_REST_CONN_ID,
                method="POST",
                endpoint=ApiEndpoints.RECON_CONFIG_ENDPOINT,
                do_xcom_push=True,
                trigger_rule=TriggerRule.ONE_SUCCESS
            )

            execute_srdm_data_source_count >> [srdm_count_from_db, splunk_count_from_db] >> compare_count >> [
                generate_payload_with_status_new,
                generate_payload_with_status_done] >> create_recon_ticket >> redis_clear_task >> end_task
        else:
            execute_srdm_data_source_count >> redis_clear_task >> end_task
    return dag


dag_gen_configs = Utils.read_data_from_shared_filesystem(AP.AUTOPARSER_DAG_GEN_FILE)
for source_name, dag_gen_config in dag_gen_configs.items():
    globals()[source_name] = create_dag(source_name, dag_gen_config)
