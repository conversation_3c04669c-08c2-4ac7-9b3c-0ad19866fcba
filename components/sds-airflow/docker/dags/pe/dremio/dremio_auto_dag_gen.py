import datetime
import logging

from airflow.operators.empty import EmptyOperator

from commons.pe.common_utils import Utils
from commons.pe.dremio_dag_gen_utils import DremioDagGenUtils
from commons.pe.dremio_utils import DremioUtils
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG

log = logging.getLogger("dremio_dag")

dremio_cluster_ips = DremioUtils.get_dremio_cluster_ips()
dremio_cluster_ips.sort()
log.info(f"Final dremio cluster ips are - {','.join(dremio_cluster_ips)}")
dremio_dag_gen_config = Utils.read_data_from_shared_filesystem("dremio/dremio_dag_gen_configs.json")

for dremio_module, module_config in dremio_dag_gen_config.items():
    dag_id = f"{dremio_module}_dremio_dag"
    tags = module_config.get("tags", [])
    pipeline_id = module_config.get("pipeline_id", "")
    job_list = module_config.get("jobs", [])
    with SDSPipelineDAG(
            dag_id=dag_id,
            pipeline_id=pipeline_id,
            tags=tags + ["DREMIO"],
            start_date=datetime.datetime(2021, 1, 1),
            render_template_as_native_obj=True,
    ) as dag:
        start_task = EmptyOperator(task_id=f"start_{dremio_module}")
        end_task = EmptyOperator(task_id=f"end_{dremio_module}")
        dremio_task_group_dict = DremioDagGenUtils.generate_dependency_dict(dremio_jobs_config=job_list, dremio_cluster_ips=dremio_cluster_ips, dag=dag)
        for dremio_config in job_list:
            dremio_type = dremio_config.get("type")
            dremio_source_name = dremio_config.get("sourceTable") if dremio_type == "reflection" else dremio_config.get(
                "viewName")
            source_dependencies = dremio_config.get("dependencies", [])
            [dremio_task_group_dict[dependency] for dependency in source_dependencies] >> dremio_task_group_dict[dremio_source_name]

        start_task >> list(dremio_task_group_dict.values()) >> end_task
