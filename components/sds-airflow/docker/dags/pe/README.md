## DAGs

This folder contains all the DAGs that are part of the SDS architecture. It contains the following DAG folders

* [SDS Data Parser DAGs](sds_data_parser) - This consists of the DAGs that orchestrate the data parsing jobs. It consists of two DAGs - 
  * [Controller DAG](sds_data_parser/sds_data_parser_controller_dag.py) - This is the main DAG that is scheduled every 30 minutes to generate payloads from data sources if there is data available to parse. 
  On every iteration, the controller DAG checks the offsets to be parsed for each individual data source and based on certain threshold conditions it will generate the payloads for the data to be parsed. The threshold conditions are the minimum data available to be parsed and schedule interval of each data source. Both these parameters are configurable and are stored as Airflow Variables.
  After payload generation, the execution DAG is triggered which will execute the spark jobs for data parsing. </br> </br>
  * [Execution DAG](sds_data_parser/sds_data_parser_execution_dag.py) - This DAG gets triggered by the controller DAG with the same execution date of the controller DAG. It receives the payloads generated by the controller DAG and dynamically generates the tasks to execute the spark jobs via Livy.

* [SDS Orchestration DAG](sds_orchestration_dag.py) - This DAG is used to orchestrate the pipelines and is the implementation of the mediator pattern logic.

* [Glue Job Utils](glue_job_utils.py) - In this DAG, get_glue_jobs_task_group will return task group of glue jobs based on the provided config file.

Kindly use this format to save config values required to create a glue job task group.
config file name: glue_job_config.json
```
{ "task_group_name": [{ "glue_job_name": "", "region_name": "", "script_s3_path": "", "num_of_dpus": "", "glue_job_extra_args": { } }, { "glue_job_name": "", "region_name": "", "script_s3_path": "", "num_of_dpus": "", "glue_job_extra_args": { } } ], "task_group_name_2": [] }
```

### Creating Pipelines

The SDS Orchestrator DAG is a sort of master DAG that controls and mediates the workflow of all the data processing pipelines. So while creating pipelines, the following practices must be followed in order to incorporate the mediator pattern working.
* The first DAG of the pipeline must always be an instance of **SDSHeadDAG**. Import the head DAG object by using </br> ```from commons.pe.sds_dag_orchestrator import SDSHeadDAG```. </br> The Head DAG takes a parameter, namely, `pipeline_id`. This is the pipeline the DAG is a part of, and it should be consistent with the pipeline configuration variable.
* The last task in the Head DAG must be a dummy operator.
* Every DAG in your pipeline other than the Head DAG must be an instance of **SDSPipelineDAG**. Import the head DAG object by using </br> ```from commons.pe.sds_dag_orchestrator import SDSPipelineDAG```. The pipeline DAG also takes the `pipeline_id` parameter. This is the pipeline the DAG is a part of, and it should be consistent with the pipeline configuration variable. The other feature of the Pipeline DAG class is that it has no schedule interval since it is the SDS Orchestration DAG that will trigger the pipeline DAGs as dependencies are met.


### Notification System

SDS orchestration engine has an email notification system in place in case of task or pipeline failures. It currently supports notification system for AWS. Notifications are enabled by the environment variable ```AIRFLOW_EMAIL_NOTIFICATION_ENABLED```. By default it is ```True```. In case you want to disable notifications while testing, set it to ```False```.

#### AWS
AWS uses SNS for sending emails on task failure. To set up email system in AWS
* Create an SNS topic and add the necessary emails to be subscribed for emails from Airflow
* Add the following environment variables
  ``` 
  AIRFLOW__EMAIL__EMAIL_BACKEND=commons.pe.email.sns.send_sns_mail
  AIRFLOW__EMAIL__EMAIL_CONN_ID=<airflow-connection-containing-aws-credentials>           
  AIRFLOW__EMAIL__HTML_CONTENT_TEMPLATE=${AIRFLOW_HOME}/shared/email/email_content.txt
  AIRFLOW__EMAIL__SUBJECT_TEMPLATE=${AIRFLOW_HOME}/shared/email/email_subject.txt
  AWS_EMAIL_SNS_ARN=<your-aws-sns-topic-arn-rolw>
  AIRFLOW_EMAIL_NOTIFICATION_ENABLED=True
  ```
* All DAGs defined in SDS 3.0 needs to be an instance of **SDSDAG**. It has notification feature enabled by default. Airflow cluster policies have been enabled to enforce this across all pipelines. Import this using ```from commons.pe.models.sds_dags import SDSDAG```
* If you want to enable notifications to be sent on task retry, set ```email_on_retry``` to ```True``` on a task level or in the default_args of a DAG to be set at DAG level.
* If you set ```email``` and ```email_on_failure``` to ```True``` on a task level, then even if ```AIRFLOW_EMAIL_NOTIFICATION_ENABLED``` is disabled, email will still be sent for that task.