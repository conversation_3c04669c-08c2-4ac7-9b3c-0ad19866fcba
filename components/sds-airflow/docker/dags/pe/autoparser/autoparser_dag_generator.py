import json
import os
from datetime import datetime
from functools import partial

import pendulum
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator

from commons.pe.constants.airflow_constants import AutoParserConstants as AP
from commons.pe.models.sds_dags import SDSDAG
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from commons.pe.sds_autoparser_utils import AutoParserUtils, Utils

DATA_MODEL_LIST = [AP.RDM, AP.SRDM, AP.SDM]


def create_dag(source: str, config: dict) -> SDSDAG:
    SRDM_TABLE_NAME = config.get("srdm_table") if config.get("srdm_table", None) else source
    RDM_PATH = config.get("rdm_path")
    with SDSDAG(
            dag_id=f"{source}-AUTOPARSER-dag",
            start_date=datetime(2022, 1, 1),
            schedule_interval=config.get("cron", None),
            render_template_as_native_obj=True,
            catchup=False,
            user_defined_macros={
                "get_custom_configuration": Utils.get_custom_configuration,
                "json": json,
                "pendulum": pendulum,
                "os": os,
                "str": str,
                "render_variable": Utils.render_variable,
                "get_autoparser_configs": AutoParserUtils.get_autoparser_configs,
                "get_data_interval": AutoParserUtils.get_data_interval,
                "get_recon_dag_conf": AutoParserUtils.get_recon_trigger_dag_conf
            },
            params={
                "data_interval_start": "2023-01-01T10:00:00",
                "data_interval_end": "2023-01-01T12:00:00",
                "timezone": "UTC"
            },
            tags=["AUTO-PARSER", "DBT", source.upper()] + config.get("tags", []),
            is_paused_upon_creation=False if config.get("unpause_dag_on_creation") else True
    ) as dag:

        start_task = EmptyOperator(task_id="start-tasks")
        end_task = EmptyOperator(task_id="end-tasks")

        check_if_data_exists_in_rdm = PythonOperator(
            task_id="check_data_exists_in_rdm",
            python_callable=AutoParserUtils.check_if_rdm_data_exists,
            on_success_callback=partial(AutoParserUtils.send_email_on_skip, config.get("email_on_skip", True)),
            op_args=[RDM_PATH]
        )

        populate_srdm_task = SparkOperatorFactory.get(
            task_id=f"{source}-POPULATE-SRDM",
            from_conf=f"get_autoparser_configs('{source}',get_data_interval(data_interval_start,data_interval_end,dag_run)[0],get_data_interval(data_interval_start,data_interval_end,dag_run)[1],"
                      f"'{RDM_PATH}','{SRDM_TABLE_NAME}')",
            poke_interval=30
        )

        trigger_recon_dag = TriggerDagRunOperator(
            task_id="trigger_data_lake_recon_dag",
            trigger_dag_id=f"{source}-DATA-LAKE-RECON-dag",
            reset_dag_run=True,
            trigger_run_id="{{ run_id }}",
            execution_date="{{ execution_date }}",
            conf="{{ get_recon_dag_conf(data_interval_start, data_interval_end, dag_run) }}"
        )

        start_task >> check_if_data_exists_in_rdm >> populate_srdm_task >> trigger_recon_dag >> end_task

    return dag


dag_gen_configs = Utils.read_data_from_shared_filesystem(AP.AUTOPARSER_DAG_GEN_FILE)
for source_name, dag_gen_config in dag_gen_configs.items():
    globals()[source_name] = create_dag(source_name, dag_gen_config)
