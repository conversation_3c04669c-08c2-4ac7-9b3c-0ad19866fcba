import json
import os
from datetime import datetime

import pendulum
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator

from commons.pe.common_utils import Utils
from commons.pe.constants.airflow_constants import AutoParserConstants as AP
from commons.pe.eda_report_utils import EdaReportUtils
from commons.pe.models.sds_dags import SDSDAG
from commons.pe.recon_utils import ReconUtils
from plugins.pe.java.java_kubernetes_operator import JavaKubernetesOperator
from plugins.pe.sds_redis_plugin.sds_redis_operator import SDSRedisDeleteKey

from manual_only_timetable import ManualOnlyTimetable

dag_gen_configs = Utils.read_data_from_shared_filesystem(AP.AUTOPARSER_DAG_GEN_FILE)


def create_dag(source: str, config: dict) -> SDSDAG:
    recon_utils = ReconUtils(conn_id=AP.RECON_REST_CONN_ID, source=source, recon_type="data-lake")
    SRDM_TABLE_NAME = config.get("srdm_table") if config.get("srdm_table", None) else source
    SDM_TABLE_NAME = config.get("sdm_model")
    with SDSDAG(
            dag_id=f"{source}-DATA-LAKE-RECON-dag",
            start_date=datetime(2022, 1, 1),
            timetable=ManualOnlyTimetable(),
            render_template_as_native_obj=True,
            catchup=False,
            is_paused_upon_creation=False,
            user_defined_macros={
                "json": json,
                "pendulum": pendulum,
                "os": os,
                "str": str,
                "get_recon_job_args": recon_utils.get_recon_job_args
            },
            params={
                "data_interval_start": "2023-01-01T10:00:00",
                "data_interval_end": "2023-01-01T12:00:00"
            },
            tags=["DATALAKE-RECON", "RECON", source.upper()] + config.get("tags", [])
    ) as dag:
        start_task = EmptyOperator(task_id="start")
        end_task = EmptyOperator(task_id="end")

        recon_models = [AP.RDM, AP.SRDM]
        if config.get("sdm_model"):
            recon_models.append(AP.SDM)

        recon_tasks = [
            JavaKubernetesOperator(
                task_id=f"{source}-recon-{recon_model.upper()}",
                namespace=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('namespace') }}}}",
                image=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('image') }}}}",
                init_container_image=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('init_container_image') }}}}",
                jar=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('jar') }}}}",
                class_name="ai.prevalent.recon.iceberg.IcebergReconciliation" if recon_model != AP.RDM else "ai.prevalent.recon.avro.AvroReconciliation",
                name=f"{source}-RECON-{recon_model}",
                env_from=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('env_from',[]) }}}}",
                environment_variables=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('environment_variables',{{}}) }}}}",
                labels=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('labels',{{}}) }}}}",
                annotations=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('annotations',{{}}) }}}}",
                job_args=f"{{{{ get_recon_job_args('{recon_model}', '{SRDM_TABLE_NAME if recon_model == AP.SRDM else SDM_TABLE_NAME}' ,pendulum.parse(dag_run.conf.get('data_interval_start')), pendulum.parse(dag_run.conf.get('data_interval_end')), ti.xcom_pull(key='avro_files', task_ids='check_data_exists_in_rdm', dag_id='{source}-AUTOPARSER-dag')) }}}}",
                container_resources_dict=f"{{{{var.json.{AP.DATALAKE_RECON_VARIABLE}.get('container_resources',{{}}) }}}}"
            )
            for recon_model in recon_models
        ]

        redis_clear_task = SDSRedisDeleteKey(
            task_id="redis_clear",
            key=f"{{{{ var.value.{AP.DATA_LAKE_RECON_REDIS_KEY_VARIABLE} }}}}",
            redis_conn_id="redis_default",
            trigger_rule="all_success"
        )
        eda = EdaReportUtils()
        run_eda_report = PythonOperator(
            task_id="run_eda_report",
            python_callable=eda.check_if_eda_must_run,
            op_kwargs={"data_source_feed_name": SRDM_TABLE_NAME,
                       "srdm_table": f"{{{{var.value.{AP.SRDM_SCHEMA_VARIABLE}}}}}" + f".{SRDM_TABLE_NAME}"},
            trigger_rule="all_success"
        )

        start_task >> recon_tasks >> redis_clear_task >> run_eda_report >> end_task

    return dag


for source_name, dag_gen_config in dag_gen_configs.items():
    globals()[source_name] = create_dag(source_name, dag_gen_config)
