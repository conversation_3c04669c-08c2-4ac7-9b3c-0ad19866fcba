import os
from datetime import datetime

from airflow.operators.python import PythonOperator
from airflow.operators.empty import EmptyOperator

from commons.pe.common_utils import Utils
from commons.pe.constants.airflow_constants import AutoParserConstants
from commons.pe.models.sds_dags import SDSDAG
from commons.pe.eda_report_utils import EdaReportUtils
from manual_only_timetable import ManualOnlyTimetable

from commons.pe.operator.spark_operator_factory import SparkOperatorFactory

with SDSDAG(
    dag_id="eda_report_generation",
    start_date=datetime(2021,1,1),
    timetable=ManualOnlyTimetable(),
    tags=["EDA", "DATA-SCIENCE", "SDS-STUDIO"],
    catchup=False,
    user_defined_macros={
        "os": os
    },
    params={
        "trigger_from": "manual",
        "srdm_table":"srdm.table_name",
        "partial_run":False,
        "entity_inventory_schema": "ei",
        "data_source_feed_name": "sample",
        "run_correlation": "false",
        "run_quality_check": "false",
        "correlation_threshold": "40",
        "filter_condition": ""
    }
) as dag:

    data_dictionary = Utils.read_data_from_shared_filesystem(relative_file_path=AutoParserConstants.EI_DATA_DICTIONARY_PATH)

    start = EmptyOperator(task_id="start_eda_report_generation")

    for entity, dictionary in data_dictionary.items():
        cleaned_entity_name = Utils.cleanup_name(entity)

        get_source_specific_fields = PythonOperator(
                task_id=f"prepare_{cleaned_entity_name}_configs",
                doc_md="This task fetches the source specific fields from the data dictionary for the entity specified "
                       "in the dag params and provides these fields as input for the EDA report generation job",
                python_callable=EdaReportUtils.get_eda_report_job_spec,
                op_kwargs={
                    "entity": entity,
                    "entity_inventory_schema":"{{params.get('entity_inventory_schema')}}",
                    "srdm_table": "{{params.get('srdm_table')}}",
                    "data_source_feed_name": "{{params.get('data_source_feed_name')}}",
                    "run_correlation": "{{params.get('run_correlation')}}",
                    "run_quality_check": "{{params.get('run_quality_check')}}",
                    "correlation_threshold": "{{params.get('correlation_threshold')}}",
                    "filter_condition": "{{params.get('filter_condition')}}",
                    "partial_run": "{{params.get('partial_run')}}"
                }
        )

        generate_eda_report = SparkOperatorFactory.get(
            task_id=f"generate_{cleaned_entity_name}_eda_report",
            from_conf=f"ti.xcom_pull(key='return_value', task_ids='prepare_{cleaned_entity_name}_configs')"
        )

        start >> get_source_specific_fields >> generate_eda_report
