{{- if .Values.podSecurityPolicy.create -}}
{{- $fullName := include "elasticsearch.uname" . -}}
{{- if .Capabilities.APIVersions.Has "policy/v1" -}}
apiVersion: policy/v1
{{- else}}
apiVersion: policy/v1beta1
{{- end }}
kind: PodSecurityPolicy
metadata:
  name: {{ default $fullName .Values.podSecurityPolicy.name | quote }}
  labels:
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    app: {{ $fullName | quote }}
spec:
{{ toYaml .Values.podSecurityPolicy.spec | indent 2 }}
{{- end -}}
