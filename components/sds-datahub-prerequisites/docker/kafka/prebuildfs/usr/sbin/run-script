#!/bin/sh
# Copyright Broadcom, Inc. All Rights Reserved.
# SPDX-License-Identifier: APACHE-2.0
set -u

if [ $# -eq 0 ]; then
    >&2 echo "No arguments provided"
    exit 1
fi

script=$1
exit_code="${2:-96}"
fail_if_not_present="${3:-n}"

if test -f "$script"; then
  sh $script

  if [ $? -ne 0 ]; then
    exit $((exit_code))
  fi
elif [ "$fail_if_not_present" = "y" ]; then
  >&2 echo "script not found: $script"
  exit 127
fi
