apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: {{ printf "%s-%s" (include "kube-prometheus-stack.fullname" .) "airflow-alert-rules" | trunc 63 | trimSuffix "-" }}
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
{{- if .Values.defaultRules.labels }}
{{ toYaml .Values.defaultRules.labels | indent 4 }}
{{- end }}
{{- if .Values.defaultRules.annotations }}
  annotations:
{{ toYaml .Values.defaultRules.annotations | indent 4 }}
{{- end }}
spec:
  groups:
  - name: airflow-alerts
    rules:
    - alert: AirflowDagImportError
      annotations:
        description: Airflow Import Error  LABELS = {{ ` {{ ` }} $labels {{ ` }} ` }}
        summary: Airflow Import Error (instance {{ ` {{ ` }} $labels.instance {{ ` }} ` }} )
      expr: increase(af_agg_dag_processing_import_errors[5m]) > 0
      for: 5m
      labels:
        severity: critical
    - alert: AirflowTaskInstanceFailure
      annotations:
        description: Airflow task instance failed  LABELS = {{ ` {{ ` }} $labels {{ ` }} ` }}
        summary: Airflow task instance failed (task_id {{ ` {{ ` }} $labels.task_id {{ ` }} ` }} )
      expr: ((sum(af_agg_ti_finish{state="failed"})by(dag_id,task_id) or vector(0))-(sum(af_agg_ti_finish{state="failed"} offset 5m)by(dag_id,task_id) or vector(0)))>0
      for: 5m
      labels:
        severity: critical
    - alert: AirflowDagRunFailure
      annotations:
        description: Airflow dag run failed  LABELS = {{ ` {{ ` }} $labels {{ ` }} ` }}
        summary: Airflow dag run failed (dag_id {{ ` {{ ` }} $labels.dag_id {{ ` }} ` }} )
      expr: sum(af_agg_dagrun_duration_failed)by(dag_id) > 0
      for: 5m
      labels:
        severity: critical
    - alert: AirflowQueueSizeExceeds20
      annotations:
        description: The total number of queued tasks in the Airflow cluster has been greater than {{ .Values.airflowAlerts.AirflowQueueSizeLimit | default "35"}} for more than 5 minutes.
        summary: The number of queued Airflow tasks exceeds {{ .Values.airflowAlerts.AirflowQueueSizeLimit | default "35"}}.
      expr: sum(af_agg_pool_queued_slots) > {{ .Values.airflowAlerts.AirflowQueueSizeLimit | default "35"}}
      for: 5m
      labels:
        severity: warning
    - alert: LongRunningDAG
      annotations:
        description: The Dag is running for long. LABELS = {{ ` {{ ` }} $labels {{ ` }} ` }}
        summary: DAG (dag_id {{ ` {{ ` }} $labels.dag_id {{ ` }} ` }} ) has been running for more than {{ .Values.airflowAlerts.MaxDAGdurationLimit | default "3600"}} seconds.
      expr: max_over_time(af_agg_dag_processing_duration[1h]) > {{ .Values.airflowAlerts.MaxDAGdurationLimit | default "3600"}}
      for: 5m
      labels:
        severity: warning
    
