apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: {{ printf "%s-%s" (include "kube-prometheus-stack.fullname" .) "sds-kubernetes-alert-rules" | trunc 63 | trimSuffix "-" }}
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
{{- if .Values.defaultRules.labels }}
{{ toYaml .Values.defaultRules.labels | indent 4 }}
{{- end }}
{{- if .Values.defaultRules.annotations }}
  annotations:
{{ toYaml .Values.defaultRules.annotations | indent 4 }}
{{- end }}
spec:
  groups:
  - name: sds-kubernetes-rules
    rules:
    - alert: HighPodCPUUsage
      expr: sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{namespace=~".*",pod=~".*"}) by (container) / sum(cluster:namespace:pod_cpu:active:kube_pod_container_resource_requests{namespace=~".*", pod=~".*"}) by (container) > 1
      for: 15m
      labels:
        severity: warning
      annotations:
        summary: "High CPU usage detected for pod {{`{{`}}$labels.namespace{{`}}`}}/{{`{{`}}$labels.pod{{`}}`}}"
        description: "The CPU usage for pod {{`{{`}}$labels.namespace{{`}}`}}/{{`{{`}}$labels.pod{{`}}`}} has been above 90% for the last 15 minutes."
    - alert: HighPodMemoryUsage
      expr: (sum by (pod, namespace) (container_memory_usage_bytes{image!="", container!="POD", container!=""}) / (sum by (pod, namespace) (kube_pod_container_resource_requests{resource="memory"}))) * 100 > 90
      for: 15m
      labels:
        severity: warning
      annotations:
        summary: "High memory usage detected for pod {{`{{`}}$labels.namespace{{`}}`}}/{{`{{`}}$labels.pod{{`}}`}}"
        description: "The memory usage for pod {{`{{`}}$labels.namespace{{`}}`}}/{{`{{`}}$labels.pod{{`}}`}} has been above 90% for the last 15 minutes."
    - alert: HighPodResourceUsage
      expr: sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{namespace=~".*",pod=~".*"}) by (container) / sum(cluster:namespace:pod_cpu:active:kube_pod_container_resource_requests{namespace=~".*", pod=~".*"}) by (container) > 1 or (sum by (pod, namespace) (container_memory_usage_bytes{image!="", container!="POD", container!=""}) > (sum by (pod, namespace) (kube_pod_container_resource_requests{resource="memory"})))
      for: 10m
      labels:
        severity: warning
      annotations:
        summary: "High resource usage detected for pod {{`{{`}}$labels.namespace{{`}}`}}/{{`{{`}}$labels.pod{{`}}`}}"
        description: "The CPU or memory usage for pod {{`{{`}}$labels.namespace{{`}}`}}/{{`{{`}}$labels.pod{{`}}`}} has been above the resource limits for the last 10 minutes."
    - alert: HighPodResourceUsage
      expr: sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{namespace=~".*",pod=~".*"}) by (container) / sum(cluster:namespace:pod_cpu:active:kube_pod_container_resource_limits{namespace=~".*", pod=~".*"}) by (container) > 1 or (sum by (pod, namespace) (container_memory_usage_bytes{image!="", container!="POD", container!=""}) > (sum by (pod, namespace) (kube_pod_container_resource_limits{resource="memory"})))
      for: 10m
      labels:
        severity: critical
      annotations:
        summary: "High resource usage detected for pod {{`{{`}}$labels.namespace{{`}}`}}/{{`{{`}}$labels.pod{{`}}`}}"
        description: "The CPU or memory usage for pod {{`{{`}}$labels.namespace{{`}}`}}/{{`{{`}}$labels.pod{{`}}`}} has been above the resource limits for the last 10 minutes."
    - alert: PodNotRunning
      expr: kube_pod_status_phase{phase!="Running"} == 1
      for: 10m
      labels:
        severity: critical
      annotations:
        summary: "Pod not running for more than 10 minutes {{`{{`}}$labels.namespace{{`}}`}}/{{`{{`}}$labels.pod{{`}}`}})"
        description: "Pod {{`{{`}}$labels.namespace{{`}}`}}/{{`{{`}}$labels.pod{{`}}`}} has not been in the 'Running' status for more than 10 minutes. Current status: {{`{{`}} $labels.phase {{`}}`}}"
    - alert: ContainerHighRestartRate
      expr: increase(kube_pod_container_status_restarts_total[10m]) > 2
      for: 10m
      labels:
        severity: critical
      annotations:
        summary: "Container restart rate high ({{`{{`}}$labels.namespace{{`}}`}}/{{`{{`}}$labels.pod{{`}}`}}/{{`{{`}}$labels.container{{`}}`}})"
        description: "Container {{`{{`}}$labels.container{{`}}`}} in pod {{`{{`}}$labels.namespace{{`}}`}}/{{`{{`}}$labels.pod{{`}}`}} has restarted more than once within the last 10 minutes."
    - alert: PersistantVolumeFillingUp
      expr: (kubelet_volume_stats_available_bytes{job="kubelet",metrics_path="/metrics",namespace=~".*"} / kubelet_volume_stats_capacity_bytes{job="kubelet",metrics_path="/metrics",namespace=~".*"}) < 0.10 and kubelet_volume_stats_used_bytes{job="kubelet",metrics_path="/metrics",namespace=~".*"} > 0 unless on (namespace, persistentvolumeclaim) kube_persistentvolumeclaim_access_mode{access_mode="ReadOnlyMany"} == 1 unless on (namespace, persistentvolumeclaim) kube_persistentvolumeclaim_labels{label_excluded_from_alerts="true"} == 1
      for: 10m
      labels:
        severity: critical
      annotations:
        summary: PersistentVolume is filling up.
        description: "The PersistentVolume claimed by {{`{{`}}$labels.persistentvolumeclaim{{`}}`}} in Namespace {{`{{`}}$labels.namespace{{`}}`}} is only {{`{{`}}$value | humanizePercentage{{`}}`}} free."
    - alert: RootVolumeFillingUp
      expr: (1 - (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"})) * 100 > 90
      for: 10m
      labels:
        severity: critical
      annotations:
        summary: RootVolume is filling up.
        description: "The root volume usage on node {{`{{`}}$labels.instance{{`}}`}} is above 90% for more than 10 minutes."
    - alert: ContainerNotRunning
      expr: kube_pod_container_status_running{namespace=~".*", pod=~".*"} == 0
      for: 10m
      labels:
        severity: critical
      annotations:
        summary: "Container not running for more than 10 minutes {{`{{`}}$labels.container{{`}}`}} in {{`{{`}}$labels.pod{{`}}`}})"
        description: "Container {{`{{`}}$labels.container{{`}}`}} in {{`{{`}}$labels.pod{{`}}`}} has not been in the 'Running' status for more than 10 minutes.."