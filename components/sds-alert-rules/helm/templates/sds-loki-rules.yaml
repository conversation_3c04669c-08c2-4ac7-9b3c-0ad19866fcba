apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: {{ printf "%s-%s" (include "kube-prometheus-stack.fullname" .) "sds-loki-alert-rules" | trunc 63 | trimSuffix "-" }}
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
{{- if .Values.defaultRules.labels }}
{{ toYaml .Values.defaultRules.labels | indent 4 }}
{{- end }}
{{- if .Values.defaultRules.annotations }}
  annotations:
{{ toYaml .Values.defaultRules.annotations | indent 4 }}
{{- end }}
spec:
  groups:
  - name: sds-loki-rules
    rules:
    - alert: LokiPanicEvents
      expr: increase(loki_panic_total[5m]) > 0
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "Panic event detected in Loki logs"
        description: "Loki has encountered one or more panic events in the last 5 minutes."
    - alert: LokiHighRequestDuration
      expr: histogram_quantile(0.95, sum(rate(loki_request_duration_seconds_bucket[5m])) by (le)) > {{ .Values.lokiAlerts.lokiMaxRequestDuration | default 30 }}
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "High Loki request latency detected"
        description: "95% of Loki requests are taking more than 30 seconds to respond."
    - alert: LokiMultipleCompactorsRunning
      expr: sum(loki_boltdb_shipper_compactor_running) > 1
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "Loki multiple compactors running"
        description: "There are more than one Loki compactor running, which could indicate a configuration issue or potential conflict."
    - alert: LokiNoCompactorsRunning
      expr: sum(loki_boltdb_shipper_compactor_running) == 0
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "No Loki compactor running"
        description: "No Loki compactor has been running for more than 5 minutes, which could indicate a potential issue in the compaction process."
    - alert: LokiHighChunkCreationRate
      expr: rate(loki_ingester_chunks_created_total[5m]) > {{ .Values.lokiAlerts.lokiMaxChunkRate | default 100 }}
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High chunk creation rate in Loki"
        description: "The rate of chunks created in the last 5 minutes exceeds 100 chunks per second."
    - alert: LokiChunkIngestionFlushingMismatch
      expr: sum(rate(loki_ingester_chunks_created_total[5m])) - sum(rate(loki_ingester_chunks_flushed_total[5m])) > 10
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "Mismatch between ingested and flushed chunks in Loki"
        description: "The rate of ingested chunks exceeds the rate of chunks flushed to disk by more than 10 chunks per second over the last 5 minutes."