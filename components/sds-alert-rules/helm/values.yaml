# Default values for sds-openalerts.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.




# serviceAccount:
#   # Specifies whether a service account should be created
#   create: true
#   # Annotations to add to the service account
#   annotations: {}
#   # The name of the service account to use.
#   # If not set and create is true, a name is generated using the fullname template
#   name: ""

# podAnnotations: {}

# podSecurityContext: {}
#   # fsGroup: 2000

# securityContext: {}



# nodeSelector: {}

# tolerations: []

# affinity: {}
defaultRules:
    labels: {}
airflowAlerts: {}
    # # The limit for the total number of queued tasks in the Airflow cluster
    # AirflowQueueSizeLimit: 
    # # The limit for the longest running DAG in seconds
    # MaxDAGdurationLimit:
lokiAlerts: {}
#   The maximum latency in loki requests seconds 
#   lokiMaxRequestDuration: 30
#   The limit for maximum no of chunks created per second in loki
#   lokiMaxChunkRate: 100
