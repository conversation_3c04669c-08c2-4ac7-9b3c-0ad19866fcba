datahub-mae-consumer
====================
A Helm chart for datahub-mae-consumer

Current chart version is `0.2.0`

## Chart Values

| Key                                                                         | Type                                                                                              | Default                            | Description                                                                                                                                                          |
|-----------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------|------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| affinity                                                                    | object                                                                                            | `{}`                               |                                                                                                                                                                      |
| extraLabels                                                                 | object                                                                                            | `{}`                               | Extra labels for deployment configuration                                                                                                                            |
| extraEnvs                                                                   | Extra [environment variables][] which will be appended to the `env:` definition for the container | `[]`                               |
| extraSidecars                                                               | list                                                                                              | `[]`                               | Add additional sidecar containers to the deployment pod(s)                                                                                                           |
| extraVolumes                                                                | Templatable string of additional `volumes` to be passed to the `tpl` function                     | ""                                 |
| extraVolumeMounts                                                           | Templatable string of additional `volumeMounts` to be passed to the `tpl` function                | ""                                 |
| fullnameOverride                                                            | string                                                                                            | `"datahub-mae-consumer"`           |                                                                                                                                                                      |
| global.datahub_analytics_enabled                                            | boolean                                                                                           | true                               |                                                                                                                                                                      |
| global.datahub.mae_consumer.port                                            | string                                                                                            | `"9091"`                           |                                                                                                                                                                      |
| global.datahub.mae_consumer.nodePort                                        | string                                                                                            | `"30002"`                          |                                                                                                                                                                      |
| global.elasticsearch.host                                                   | string                                                                                            | `"elasticsearch"`                  |                                                                                                                                                                      |
| global.elasticsearch.port                                                   | string                                                                                            | `"9200"`                           |                                                                                                                                                                      |
| global.kafka.bootstrap.server                                               | string                                                                                            | `"broker:9092"`                    |                                                                                                                                                                      |
| global.kafka.schemaregistry.url                                             | string                                                                                            | `"http://schema-registry:8081"`    |                                                                                                                                                                      |
| global.neo4j.host                                                           | string                                                                                            | `"neo4j:7474"`                     |                                                                                                                                                                      |
| global.neo4j.uri                                                            | string                                                                                            | `"bolt://neo4j"`                   |                                                                                                                                                                      |
| global.neo4j.database                                                       | string                                                                                            | `"graph.db"`                       | Neo4J database                                                                                                                                                       |
| global.neo4j.username                                                       | string                                                                                            | `"neo4j"`                          |                                                                                                                                                                      |
| global.neo4j.password.secretRef                                             | string                                                                                            | `"neo4j-secrets"`                  |                                                                                                                                                                      |
| global.neo4j.password.secretKey                                             | string                                                                                            | `"neo4j-password"`                 |                                                                                                                                                                      |
| global.hostAliases[0].hostnames[0]                                          | string                                                                                            | `"broker"`                         |                                                                                                                                                                      |
| global.hostAliases[0].hostnames[1]                                          | string                                                                                            | `"mysql"`                          |                                                                                                                                                                      |
| global.hostAliases[0].hostnames[2]                                          | string                                                                                            | `"elasticsearch"`                  |                                                                                                                                                                      |
| global.hostAliases[0].hostnames[3]                                          | string                                                                                            | `"neo4j"`                          |                                                                                                                                                                      |
| global.hostAliases[0].ip                                                    | string                                                                                            | `"*************"`                  |                                                                                                                                                                      |
| global.graph_service_impl                                                   | string                                                                                            | `elasticsearch`                    | One of `elasticsearch` or `neo4j`. Determines which backend to use for the GMS graph service. Elasticsearch is recommended for a simplified deployment.              |
| image.args                                                                  | list                                                                                              | `[]`                               | Override the image's args.  Used to configure custom startup or shutdown behavior                                                                                    |
| image.command                                                               | list                                                                                              | `[]`                               | Override the image's command.  Used to configure custom startup or shutdown behavior                                                                                 |
| image.pullPolicy                                                            | string                                                                                            | `"IfNotPresent"`                   |                                                                                                                                                                      |
| image.registry                                                              | string                                                                                            | ``                                 | Image registry override to be used by the job.                                                                                                                       |
| image.repository                                                            | string                                                                                            | `"acryldata/datahub-mae-consumer"` |                                                                                                                                                                      |
| image.tag                                                                   | string                                                                                            | `"head"`                           |                                                                                                                                                                      |
| imagePullSecrets                                                            | list                                                                                              | `[]`                               |                                                                                                                                                                      |
| ingress.annotations                                                         | object                                                                                            | `{}`                               |                                                                                                                                                                      |
| ingress.enabled                                                             | bool                                                                                              | `false`                            |                                                                                                                                                                      |
| ingress.hosts[0].host                                                       | string                                                                                            | `"chart-example.local"`            |                                                                                                                                                                      |
| ingress.hosts[0].paths                                                      | list                                                                                              | `[]`                               |                                                                                                                                                                      |
| ingress.tls                                                                 | list                                                                                              | `[]`                               |                                                                                                                                                                      |
| livenessProbe.initialDelaySeconds                                           | int                                                                                               | `60`                               |                                                                                                                                                                      |
| livenessProbe.periodSeconds                                                 | int                                                                                               | `30`                               |                                                                                                                                                                      |
| livenessProbe.failureThreshold                                              | int                                                                                               | `8`                                |                                                                                                                                                                      |
| nameOverride                                                                | string                                                                                            | `""`                               |                                                                                                                                                                      |
| nodeSelector                                                                | object                                                                                            | `{}`                               |                                                                                                                                                                      |
| podAnnotations                                                              | object                                                                                            | `{}`                               |                                                                                                                                                                      |
| podSecurityContext                                                          | object                                                                                            | `{}`                               |                                                                                                                                                                      |
| readinessProbe.initialDelaySeconds                                          | int                                                                                               | `60`                               |                                                                                                                                                                      |
| readinessProbe.periodSeconds                                                | int                                                                                               | `30`                               |                                                                                                                                                                      |
| readinessProbe.failureThreshold                                             | int                                                                                               | `8`                                |                                                                                                                                                                      |
| replicaCount                                                                | int                                                                                               | `1`                                |                                                                                                                                                                      |
| revisionHistoryLimit                                                        | int                                                                                               | `10`                               |                                                                                                                                                                      |
| resources                                                                   | object                                                                                            | `{}`                               |                                                                                                                                                                      |
| securityContext                                                             | object                                                                                            | `{}`                               |                                                                                                                                                                      |
| service.port                                                                | int                                                                                               | `80`                               |                                                                                                                                                                      |
| service.type                                                                | string                                                                                            | `"ClusterIP"`                      |                                                                                                                                                                      |
| serviceAccount.annotations                                                  | object                                                                                            | `{}`                               |                                                                                                                                                                      |
| serviceAccount.create                                                       | bool                                                                                              | `true`                             |                                                                                                                                                                      |
| serviceAccount.name                                                         | string                                                                                            | `nil`                              |                                                                                                                                                                      |
| serviceMonitor.create                                                       | bool                                                                                              | `false`                            | If set true and `global.datahub.monitoring.enablePrometheus` is set `true` it will create a ServiceMonitor resource                                                  |
| serviceMonitor.extraLabels                                                  | object                                                                                            | `{}`                               |                                                                                                                                                                      |
| tolerations                                                                 | list                                                                                              | `[]`                               |                                                                                                                                                                      |
| datahub.metadata_service_authentication.enabled                             | bool                                                                                              | `true`                             | Whether Metadata Service Authentication is enabled.                                                                                                                  |
| global.datahub.metadata_service_authentication.systemClientId               | string                                                                                            | `"__datahub_system"`               | The internal system id that is used to communicate with DataHub GMS. Required if metadata_service_authentication is 'true'.                                          |
| global.datahub.metadata_service_authentication.systemClientSecret.secretRef | string                                                                                            | `nil`                              | The reference to a secret containing the internal system secret that is used to communicate with DataHub GMS. Required if metadata_service_authentication is 'true'. |
| global.datahub.metadata_service_authentication.systemClientSecret.secretKey | string                                                                                            | `nil`                              | The key of a secret containing the internal system secret that is used to communicate with DataHub GMS. Required if metadata_service_authentication is 'true'.       |
