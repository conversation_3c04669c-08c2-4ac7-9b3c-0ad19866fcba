apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "datahub-mae-consumer.fullname" . }}
  labels:
    {{- include "datahub-mae-consumer.labels" . | nindent 4 }}
    {{- range $key, $val := .Values.extraLabels }}
    {{ $key }}: {{ $val | quote }}
    {{- end }}
spec:
  replicas: {{ .Values.replicaCount }}
  revisionHistoryLimit: {{ .Values.revisionHistoryLimit }}
  selector:
    matchLabels:
      {{- include "datahub-mae-consumer.selectorLabels" . | nindent 6 }}
  template:
    metadata:
    {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      labels:
        {{- include "datahub-mae-consumer.selectorLabels" . | nindent 8 }}
        {{- range $key, $value := .Values.global.podLabels }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
        {{- range $key, $value := .Values.extraPodLabels }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
    spec:
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
    {{- with .Values.global.hostAliases }}
      hostAliases:
    {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      serviceAccountName: {{ include "datahub-mae-consumer.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      volumes:
        {{- with .Values.global.credentialsAndCertsSecrets }}
        - name: datahub-certs-dir
          secret:
            defaultMode: 0444
            secretName: {{ .name }}
        {{- end }}
      {{- with .Values.extraVolumes }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.priorityClassName }}
      priorityClassName: "{{ .Values.priorityClassName }}"
      {{- end }}
      initContainers:
      {{- with .Values.extraInitContainers }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: {{ include "datahub.image" (dict "imageRegistry" .Values.global.imageRegistry "version" .Values.global.datahub.version "image" .Values.image) }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- if .Values.image.command }}
          command: {{ .Values.image.command | toRawJson }}
          {{- end }}
          {{- if .Values.image.args }}
          args: {{ .Values.image.args | toRawJson }}
          {{- end }}
          ports:
            - name: http
              containerPort: 9091
              protocol: TCP
          {{- if or .Values.global.datahub.monitoring.enablePrometheus .Values.global.datahub.monitoring.enableJMXPort }}
            - name: {{ .Values.global.datahub.monitoring.portName }}
              containerPort: 4318
              protocol: TCP
          {{- end }}
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: http
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: http
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          env:
            - name: PRE_PROCESS_HOOKS_UI_ENABLED
              value: {{ or .Values.global.datahub.reProcessUIEventHooks (not .Values.global.datahub.preProcessHooksUIEnabled) | quote }}
            - name: ENTITY_VERSIONING_ENABLED
              value: {{ .Values.global.datahub.entityVersioning.enabled | quote }}
            - name: SHOW_SEARCH_FILTERS_V2
              value: {{ .Values.global.datahub.search_and_browse.show_search_v2 | quote }}
            - name: SHOW_BROWSE_V2
              value: {{ .Values.global.datahub.search_and_browse.show_browse_v2 | quote }}
            - name: BACKFILL_BROWSE_PATHS_V2
              value: {{ .Values.global.datahub.search_and_browse.backfill_browse_v2 | quote }}
            {{- if .Values.global.datahub.systemUpdate.enabled }}
            - name: DATAHUB_REVISION
              value: {{ .Release.Revision | quote }}
            {{- end }}
            {{- if .Values.global.datahub.monitoring.enablePrometheus }}
            - name: ENABLE_PROMETHEUS
              value: "true"
            {{- end }}
            - name: MAE_CONSUMER_ENABLED
              value: "true"
            - name: PE_CONSUMER_ENABLED
              value: "true"
            - name: ENTITY_REGISTRY_CONFIG_PATH
              value: /datahub/datahub-mae-consumer/resources/entity-registry.yml
            - name: DATAHUB_GMS_HOST
              value: {{ (((.Values.datahub).gms).host | default ((.Values.global.datahub).gms).host) | default (printf "%s-%s" .Release.Name "datahub-gms") | trunc 63 | trimSuffix "-"}}
            - name: DATAHUB_GMS_PORT
              value: "{{ ((.Values.datahub).gms).port | default .Values.global.datahub.gms.port }}"
            - name: KAFKA_BOOTSTRAP_SERVER
              value: "{{ .Values.global.kafka.bootstrap.server }}"
            {{- with .Values.global.kafka.producer.compressionType }}
            - name: KAFKA_PRODUCER_COMPRESSION_TYPE
              value: "{{ . }}"
            {{- end }}
            {{- with .Values.global.kafka.producer.maxRequestSize }}
            - name: KAFKA_PRODUCER_MAX_REQUEST_SIZE
              value: {{ . | quote }}
            {{- end }}
            {{- with .Values.global.kafka.consumer.stopContainerOnDeserializationError }}
            - name: KAFKA_CONSUMER_STOP_ON_DESERIALIZATION_ERROR
              value: "{{ . }}"
            {{- end }}
            {{- with .Values.global.kafka.consumer.maxPartitionFetchBytes }}
            - name: KAFKA_CONSUMER_MAX_PARTITION_FETCH_BYTES
              value: {{ . | quote }}
            {{- end }}
            {{- if eq .Values.global.kafka.schemaregistry.type "INTERNAL" }}
            - name: KAFKA_SCHEMAREGISTRY_URL
              value: {{ printf "http://%s-%s:%s/schema-registry/api/" .Release.Name "datahub-gms" .Values.global.datahub.gms.port }}
            {{- else if eq .Values.global.kafka.schemaregistry.type "KAFKA" }}
            - name: KAFKA_SCHEMAREGISTRY_URL
              value: "{{ .Values.global.kafka.schemaregistry.url }}"
            {{- end }}
            {{- with .Values.global.kafka.schemaregistry.type }}
            - name: SCHEMA_REGISTRY_TYPE
              value: "{{ . }}"
            {{- end }}
            {{- with .Values.global.kafka.schemaregistry.glue }}
            - name: AWS_GLUE_SCHEMA_REGISTRY_REGION
              value: "{{ .region }}"
            {{- with .registry }}
            - name: AWS_GLUE_SCHEMA_REGISTRY_NAME
              value: "{{ . }}"
            {{- end }}
            {{- end }}
            - name: ELASTICSEARCH_HOST
              value: "{{ .Values.global.elasticsearch.host }}"
            - name: ELASTICSEARCH_PORT
              value: "{{ .Values.global.elasticsearch.port }}"
            - name: SKIP_ELASTICSEARCH_CHECK
              value: "{{ .Values.global.elasticsearch.skipcheck }}"
            {{- with .Values.global.elasticsearch.useSSL }}
            - name: ELASTICSEARCH_USE_SSL
              value: {{ . | quote }}
            {{- end }}
            {{- with .Values.global.elasticsearch.auth }}
            - name: ELASTICSEARCH_USERNAME
              value: {{ .username }}
            - name: ELASTICSEARCH_PASSWORD
              {{- if .password.value }}
              value: {{ .password.value | quote }}
              {{- else }}
              valueFrom:
                secretKeyRef:
                  name: "{{ .password.secretRef }}"
                  key: "{{ .password.secretKey }}"
              {{- end }}
            {{- end }}
            {{- with .Values.global.elasticsearch.indexPrefix }}
            - name: INDEX_PREFIX
              value: {{ . }}
            {{- end }}
            - name: GRAPH_SERVICE_IMPL
              value: {{ .Values.global.graph_service_impl }}
            {{- if eq .Values.global.graph_service_impl "neo4j" }}
            - name: NEO4J_HOST
              value: "{{ .Values.global.neo4j.host }}"
            - name: NEO4J_URI
              value: "{{ .Values.global.neo4j.uri }}"
            - name: NEO4J_DATABASE
              value: "{{ .Values.global.neo4j.database | default "graph.db" }}"
            - name: NEO4J_USERNAME
              value: "{{ .Values.global.neo4j.username }}"
            - name: NEO4J_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: "{{ .Values.global.neo4j.password.secretRef }}"
                  key: "{{ .Values.global.neo4j.password.secretKey }}"
            {{- end }}
            - name: DATAHUB_ANALYTICS_ENABLED
              value: "{{ .Values.global.datahub_analytics_enabled }}"
            {{- if .Values.global.datahub.metadata_service_authentication.enabled }}
            - name: METADATA_SERVICE_AUTH_ENABLED
              value: "true"
            - name: DATAHUB_SYSTEM_CLIENT_ID
              value: {{ .Values.global.datahub.metadata_service_authentication.systemClientId }}
            - name: DATAHUB_SYSTEM_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.global.datahub.metadata_service_authentication.systemClientSecret.secretRef }}
                  key: {{ .Values.global.datahub.metadata_service_authentication.systemClientSecret.secretKey }}
            {{- else }}
            - name: METADATA_SERVICE_AUTH_ENABLED
              value: "false"
            {{- end }}
            {{- if .Values.global.datahub.managed_ingestion.enabled }}
            - name: UI_INGESTION_ENABLED
              value: "true"
            {{- else }}
            - name: UI_INGESTION_ENABLED
              value: "false"
            {{- end }}
            {{- if .Values.global.datahub.managed_ingestion.defaultCliVersion }}
            - name: UI_INGESTION_DEFAULT_CLI_VERSION
              value: "{{ .Values.global.datahub.managed_ingestion.defaultCliVersion }}"
            {{- end }}
            {{- if .Values.global.springKafkaConfigurationOverrides }}
            {{- range $configName, $configValue := .Values.global.springKafkaConfigurationOverrides }}
            - name: SPRING_KAFKA_PROPERTIES_{{ $configName | replace "." "_" | upper }}
              value: {{ $configValue | quote }}
            {{- end }}
            {{- end }}
            {{- if .Values.global.credentialsAndCertsSecrets }}
            {{- range $envVarName, $envVarValue := .Values.global.credentialsAndCertsSecrets.secureEnv }}
            - name: SPRING_KAFKA_PROPERTIES_{{ $envVarName | replace "." "_" | upper }}
              valueFrom:
                secretKeyRef:
                  name: {{ $.Values.global.credentialsAndCertsSecrets.name }}
                  key: {{ $envVarValue }}
            {{- end }}
            {{- end }}
            {{- with .Values.global.kafka.topics }}
            - name: METADATA_AUDIT_EVENT_NAME
              value: {{ .metadata_audit_event_name }}
            - name: DATAHUB_USAGE_EVENT_NAME
              value: {{ .datahub_usage_event_name }}
            - name: METADATA_CHANGE_LOG_VERSIONED_TOPIC_NAME
              value: {{ .metadata_change_log_versioned_topic_name }}
            - name: METADATA_CHANGE_LOG_TIMESERIES_TOPIC_NAME
              value: {{ .metadata_change_log_timeseries_topic_name }}
            - name: PLATFORM_EVENT_TOPIC_NAME
              value: {{ .platform_event_topic_name }}
            - name: DATAHUB_UPGRADE_HISTORY_TOPIC_NAME
              value: {{ .datahub_upgrade_history_topic_name }}
            {{- end }}
            {{- if .Values.global.datahub.systemUpdate.enabled }}
            - name: DATAHUB_UPGRADE_HISTORY_KAFKA_CONSUMER_GROUP_ID
              value: {{ index .Values.global.kafka.consumer_groups.datahub_upgrade_history_kafka_consumer_group_id "mae-consumer" | default (printf "%s-%s" .Release.Name "duhe-consumer-job-client-mcl")  }}
            {{- end }}
            {{- with .Values.global.kafka.consumer_groups }}
            - name: DATAHUB_USAGE_EVENT_KAFKA_CONSUMER_GROUP_ID
              value: {{ .datahub_usage_event_kafka_consumer_group_id }}
            - name: METADATA_CHANGE_LOG_KAFKA_CONSUMER_GROUP_ID
              value: {{ .metadata_change_log_kafka_consumer_group_id }}
            - name: PLATFORM_EVENT_KAFKA_CONSUMER_GROUP_ID
              value: {{ .platform_event_kafka_consumer_group_id }}
            {{- end }}
            - name: ALWAYS_EMIT_CHANGE_LOG
              value: {{ .Values.global.datahub.alwaysEmitChangeLog | quote }}
            - name: GRAPH_SERVICE_DIFF_MODE_ENABLED
              value: {{ .Values.global.datahub.enableGraphDiffMode | quote }}
            {{- with .Values.global.kafka.metadataChangeLog.hooks.siblings }}
            - name: ENABLE_SIBLING_HOOK
              value: "{{ .enabled }}"
            - name: SIBLINGS_HOOK_CONSUMER_GROUP_SUFFIX
              value: "{{ .consumerGroupSuffix }}"
            {{- end }}
            {{- with .Values.global.kafka.metadataChangeLog.hooks.updateIndices }}
            - name: ENABLE_UPDATE_INDICES_HOOK
              value: "{{ .enabled }}"
            - name: UPDATE_INDICES_CONSUMER_GROUP_SUFFIX
              value: "{{ .consumerGroupSuffix }}"
            {{- end }}
            {{- with .Values.global.kafka.metadataChangeLog.hooks.ingestionScheduler }}
            - name: ENABLE_INGESTION_SCHEDULER_HOOK
              value: "{{ .enabled }}"
            - name: INGESTION_SCHEDULER_HOOK_CONSUMER_GROUP_SUFFIX
              value: "{{ .consumerGroupSuffix }}"
            {{- end }}
            {{- with .Values.global.kafka.metadataChangeLog.hooks.incidents }}
            - name: ENABLE_INCIDENTS_HOOK
              value: "{{ .enabled }}"
            - name: INCIDENTS_HOOK_CONSUMER_GROUP_SUFFIX
              value: "{{ .consumerGroupSuffix }}"
            {{- end }}
            {{- with .Values.global.kafka.metadataChangeLog.hooks.entityChangeEvents }}
            - name: ENABLE_ENTITY_CHANGE_EVENTS_HOOK
              value: "{{ .enabled }}"
            - name: ECE_CONSUMER_GROUP_SUFFIX
              value: "{{ .consumerGroupSuffix }}"
            {{- end }}
            {{- with .Values.global.kafka.metadataChangeLog.hooks.forms }}
            - name: FORMS_HOOK_ENABLED
              value: "{{ .enabled }}"
            - name: FORMS_HOOK_CONSUMER_GROUP_SUFFIX
              value: "{{ .consumerGroupSuffix }}"
            {{- end }}
          {{- with .Values.extraEnvs }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          volumeMounts:
          {{- with .Values.global.credentialsAndCertsSecrets }}
            - name: datahub-certs-dir
              mountPath: {{ .path | default "/mnt/certs" }}
          {{- end }}
          {{- with .Values.extraVolumeMounts }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
        {{- with .Values.extraSidecars }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with default .Values.global.nodeSelector .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with default .Values.global.tolerations .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
