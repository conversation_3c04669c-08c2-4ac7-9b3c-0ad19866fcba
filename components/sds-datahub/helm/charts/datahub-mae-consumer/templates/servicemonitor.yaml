{{- if and .Values.serviceMonitor.create .Values.global.datahub.monitoring.enablePrometheus -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ printf "%s-%s" .Release.Name "datahub-mae-consumer" }}
  labels:
    {{- include "datahub-mae-consumer.labels" . | nindent 4 }}
    {{- range $key, $val := .Values.serviceMonitor.extraLabels }}
    {{ $key }}: {{ $val | quote }}
    {{- end }}
  {{- with .Values.serviceMonitor.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  endpoints:
  - port: {{ .Values.global.datahub.monitoring.portName }}
    relabelings:
    - separator: /
      sourceLabels:
      - namespace
      - pod
      targetLabel: instance
  selector:
    matchLabels:
      app.kubernetes.io/managed-by: Helm
      app.kubernetes.io/name: datahub-mae-consumer
{{- end -}}