apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-%s" .Release.Name "datahub-mae-consumer" }}
  labels:
    {{- include "datahub-mae-consumer.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: {{ .Values.service.protocol }}
      name: {{ .Values.service.name }}
      {{- if eq .Values.service.type "NodePort" }}
      {{- with .Values.service.nodePort }}
      nodePort: {{ . }}
      {{- end }}
      {{- end }}
    {{- if .Values.global.datahub.monitoring.enablePrometheus }}
    - name: {{ .Values.global.datahub.monitoring.portName }}
      port: 4318
      targetPort: {{ .Values.global.datahub.monitoring.portName }}
      protocol: TCP
    {{- end }}
  selector:
    {{- include "datahub-mae-consumer.selectorLabels" . | nindent 4 }}
