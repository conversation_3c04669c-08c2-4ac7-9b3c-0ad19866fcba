# Default values for datahub-gms.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

hpa:
  # Can only be enabled if global.datahub_standalone_consumers_enabled
  enabled: false
  minReplicas: 1
  maxReplicas: 2
  behavior: {}
  targetCPUUtilizationPercentage: 100
  targetMemoryUtilizationPercentage:

revisionHistoryLimit: 10

image:
  repository: acryldata/datahub-gms
  pullPolicy: IfNotPresent
  tag:
  # Override the image's command & args with a new one.
  # This may be necessary for custom startup or shutdown behaviors
  command:
  args:

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

theme_v2:
  enabled: true
  default: false
  toggeable: true

terminationGracePeriodSeconds: 150

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name:

serviceMonitor:
  create: false
  extraLabels: {}

podAnnotations: {}
  # co.elastic.logs/enabled: "true"

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

priorityClassName:

service:
  type: ClusterIP # ClusterIP or NodePort
  port: "8080"
  targetPort: http
  protocol: TCP
  name: http
  # Annotations to add to the service, this will help in adding
  # Internal load balancer or various other annotation support in AWS
  annotations: {}
    # service.beta.kubernetes.io/aws-load-balancer-internal: "true"

ingress:
  # className: ""
  enabled: false
  extraLabels: {}
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []
      redirectPaths: []
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local
  pathType: ImplementationSpecific

# Extra labels for Deployment
extraLabels: {}
  # owner: myteam
  # environment: test

# Extra labels for the Pods
extraPodLabels: {}

# Extra environment variables
# This will be appended to the current 'env:' key. You can use any of the kubernetes env
# syntax here
extraEnvs: []
  # - name: MY_ENVIRONMENT_VAR
  #   value: the_value_goes_here

extraVolumes: []
  # - name: extras
  #   emptyDir: {}

extraVolumeMounts: []
  # - name: extras
  #   mountPath: /usr/share/extras
  #   readOnly: true

extraInitContainers: []

# Add extra sidecar containers to deployment pod
extraSidecars: []
  # - name: my-image-name
  #   image: my-image
  #   imagePullPolicy: Always

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

livenessProbe:
  initialDelaySeconds: 60
  periodSeconds: 30
  failureThreshold: 8

readinessProbe:
  initialDelaySeconds: 60
  periodSeconds: 30
  failureThreshold: 8

#This section is useful if we are installing this chart separately for testing
# helm install datahub-gms datahub-gms/
global:
  imageRegistry: "docker.io"

  datahub_analytics_enabled: true
  graph_service_impl: elasticsearch

  elasticsearch:
    host: "elasticsearch"
    port: "9200"
    skipcheck: "false"
    index:
      enableMappingsReindex: true
    search:
      maxTermBucketSize: 20
      exactMatch:
        exclusive: false
        withPrefix: true
        exactFactor: 2.0
        prefixFactor: 1.6
        caseSensitivityFactor: 0.7
        enableStructured: true
      graph:
        timeoutSeconds: 50
        batchSize: 1000
        maxResult: 10000

  kafka:
    bootstrap:
      server: "broker:9092"
    schemaregistry:
      # GMS Implementation - `url` configured based on component context
      type: INTERNAL
      # Confluent Kafka Implementation
      # type: KAFKA
      # url: "http://schema-registry:8081"
    ## Kafka producer and consumer settings
    #producer:
    #  compressionType: snappy
    #  maxRequestSize: "5242880"
    #consumer:
    #  maxPartitionFetchBytes: "5242880"

  neo4j:
    host: "neo4j:7474"
    uri: "bolt://neo4j"
    username: "neo4j"
    password:
      secretRef: "neo4j-secrets"
      secretKey: "neo4j-password"

  sql:
    datasource:
      host: "mysql:3306"
      url: "*************************************************************************"
      driver: "com.mysql.cj.jdbc.Driver"
      username: "datahub"
      password:
        secretRef: "mysql-secrets"
        secretKey: "mysql-password"

  datahub:
    version: head
    monitoring:
      enablePrometheus: false
      enableJMXPort: false
      portName: jmx
    gms:
      port: "8080"
      graphql:
        introspection:
          enabled: 'true' # v0.13.3+
    appVersion: "1.0"

    systemUpdate:
      ## The following options control settings for datahub-upgrade job which will
      ## managed ES indices and other update related work
      enabled: true

    managed_ingestion:
      enabled: true
      # defaultCliVersion: "X.X.X" --> Optional: Controls the acryl-datahub package version downloaded from PyPI.

    metadata_service_authentication:
      enabled: true
      # tokenService:
      #   signingKey:
      #    secretRef: <secret-ref>
      #    secretKey: <secret-key>
      #   salt:
      #    secretRef: <secret-ref>
      #    secretKey: <secret-key>
      systemClientId: "__datahub_system"
      # systemClientSecret:
      #  secretRef: <secret-ref>
      #  secretKey: <secret-key>
      restApi:
        authorization:
          # enables authorization of reads, writes, and deletes on REST APIs
          enabled: true
      view:
        authorization:
          # search/view authorization filters
          enabled: false
          # recommendation settings when search/view authorization is in effect
          recommendations:
            # whether to restrict Most Popular entities 
            peerGroupEnabled: true

    encryptionKey:
      secretRef: "encryption-key-secret"
      secretKey: "encryption-key-secret"

    enable_retention: false ## Set to true to enable retention on local DB

    ## Enables always emitting a MCL even when no changes are detected. Used for Time Based Lineage when no changes occur.
    alwaysEmitChangeLog: false

    ## Enables diff mode for graph writes, uses a different code path that produces a diff from previous to next to write relationships instead of wholesale deleting edges and reading
    enableGraphDiffMode: true

    ## Enable stricter URN validation logic
    strictUrnValidation: false

    cache:
      search:
        ## Enable general search caching
        enabled: false
        ## Configuration for the primary cahe
        primary:
          ttlSeconds: 600
          maxSize: 10000
        ## Configuration for homepage cache
        homepage:
          entityCounts:
            ttlSeconds: 600
        ## Lineage specific caching options
        lineage:
          ## Enables in-memory cache for searchAcrossLineage query
          enabled: false
          ttlSeconds: 86400
          lightningThreshold: 300

    metadataChangeProposal:
      consumer:
        batch:
          enabled: false

  hostAliases:
    - ip: "*************"
      hostnames:
        - "broker"
        - "mysql"
        - "elasticsearch"
        - "neo4j"
