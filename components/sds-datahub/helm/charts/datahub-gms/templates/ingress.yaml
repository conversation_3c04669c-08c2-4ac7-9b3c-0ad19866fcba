{{- if .Values.ingress.enabled -}}
{{- $fullName := printf "%s-%s" .Release.Name "datahub-gms"}}
{{- $svcPort := .Values.global.datahub.gms.port -}}
{{- $ingressApiVersion := include "datahub-gms.ingress.apiVersion" . -}}
{{- $ingressPathType := .Values.ingress.pathType -}}
apiVersion: {{ template "datahub-gms.ingress.apiVersion" . }}
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "datahub-gms.labels" . | nindent 4 }}
    {{- range $key, $val := .Values.ingress.extraLabels }}
    {{ $key }}: {{ $val }}
    {{- end }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
{{- if .Values.ingress.className }}
  ingressClassName: {{ .Values.ingress.className | quote }}
{{- end }}
{{- if .Values.ingress.tls }}
  tls:
  {{- range .Values.ingress.tls }}
    - hosts:
      {{- range .hosts }}
        - {{ . | quote }}
      {{- end }}
      secretName: {{ .secretName }}
  {{- end }}
{{- end }}
  rules:
  {{- range .Values.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
        {{- range .redirectPaths }}
          - path: {{ .path }}
            {{- if eq $ingressApiVersion "networking.k8s.io/v1" }}
            pathType: {{ $ingressPathType }}
            backend:
              service:
                name: {{ .name }}
                port:
                {{- if eq .port "use-annotation" }}
                  name: {{ .port }}
                {{- else }}
                  number: {{ .port }}
                {{- end }}
            {{- else }}
            backend:
              serviceName: {{ .name }}
              servicePort: {{ .port }}
            {{- end }}
        {{- end }}
        {{- range .paths }}
          - path: {{ . }}
            {{- if eq $ingressApiVersion "networking.k8s.io/v1" }}
            pathType: {{ $ingressPathType }}
            backend:
              service:
                name: {{ $fullName }}
                port:
                  number: {{ $svcPort }}
            {{- else }}
            backend:
              serviceName: {{ $fullName }}
              servicePort: {{ $svcPort }}
            {{- end }}
        {{- end }}
  {{- end }}
{{- end }}
