{{- if gt .Values.replicaCount 1.0 }}
apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-%s-%s" .Release.Name (regexReplaceAll "[^-a-z0-9]+" .Values.global.datahub.version "-") "hazelcast-svc" | trunc 63 | trimSuffix "-" }}
spec:
  clusterIP: None
  ports:
    - name: hazelcast
      port: 5701
      protocol: TCP
      targetPort: 5701
  selector:
    app.kubernetes.io/name: {{- include "datahub-gms.name" . | nindent 6 }}
  type: ClusterIP
{{- end }}
