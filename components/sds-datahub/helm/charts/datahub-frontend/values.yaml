# Default values for datahub-frontend.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

revisionHistoryLimit: 10

image:
  repository: acryldata/datahub-frontend-react
  tag:
  pullPolicy: IfNotPresent
  # Override the image's command & args with a new one.
  # This may be necessary for custom startup or shutdown behaviors
  command:
  args:

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

terminationGracePeriodSeconds: 150

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name:

podAnnotations: {}
  # co.elastic.logs/enabled: "true"

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

priorityClassName:

service:
  type: ClusterIP # ClusterIP or NodePort
  port: 9002
  targetPort: http
  containerPort: 9002
  protocol: TCP
  name: http
  # Annotations to add to the service, this will help in adding
  # Internal load balancer or various other annotation support in AWS
  annotations: {}
    # service.beta.kubernetes.io/aws-load-balancer-internal: "true"
  extraLabels: {}

serviceMonitor:
  create: false
  extraLabels: {}

ingress:
  # className: ""
  enabled: false
  extraLabels: {}
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []
      redirectPaths: []
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local
  pathType: ImplementationSpecific

auth:
  sessionTTLHours: "24"
# OIDC auth based on https://datahubproject.io/docs/authentication/guides/sso/configure-oidc-react
oidcAuthentication:
  enabled: false
  # provider: google/okta/azure/other  <- choose only one

  # clientId: your-client-id
  # clientSecret: your-client-secret
  # only needed if you would like to store the client secret in secret
  # clientSecretRef:
  #   secretRef: <secret-ref>
  #   secretKey: <secret-key>
  # only needed if provider is `other`
  # discoveryUri: https://my-keycloak.example.com/realms/master/.well-known/openid-configuration
  # only needed if provider is `okta`
  # oktaDomain: your-okta-domain.com

  # only needed if provider is `azure`
  # azureTenantId: your-azure-tenant-id
  # if needed, it should set meaningful defaults from provider
  # scope: "openid profile email"

  # The attribute that will contain the username used on the DataHub platform.
  # user_name_claim: "email"
  # A regex string used for extracting the username from the userNameClaim attribute.
  # user_name_claim_regex: "([^@]+)"

# Extra labels for Deployment
extraLabels: {}
  # owner: myteam
  # environment: test

# Extra labels for the Pods
extraPodLabels: {}

# Extra environment variables
# This will be appended to the current 'env:' key. You can use any of the kubernetes env
# syntax here
extraEnvs: []
  # - name: MY_ENVIRONMENT_VAR
  #   value: the_value_goes_here

extraVolumes: []
  # - name: extras
  #   emptyDir: {}

extraVolumeMounts: []
  # - name: extras
  #   mountPath: /usr/share/extras
  #   readOnly: true

extraInitContainers: []

# Add extra sidecar containers to deployment pod
extraSidecars: []
  # - name: my-image-name
  #   image: my-image
  #   imagePullPolicy: Always

lifecycle: {}
  # To add a new user to datahub in JAAS config without mounting the user.props file
  # postStart:
  #   exec:
  #     command: ["/bin/sh", "-c", "echo "test_user:password" >> datahub-frontend/conf/user.props"]

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 500m
  #   memory: 2Gi
  # requests:
  #   cpu: 100m
  #   memory: 1Gi

nodeSelector: {}

tolerations: []

affinity: {}

livenessProbe:
  initialDelaySeconds: 60
  periodSeconds: 30
  failureThreshold: 4

readinessProbe:
  initialDelaySeconds: 60
  periodSeconds: 30
  failureThreshold: 4

datahub:
  play:
    mem:
      buffer:
        size: "10MB"
global:
  imageRegistry: "docker.io"

  datahub_analytics_enabled: true

  elasticsearch:
    host: "elasticsearch"
    port: "9200"

  kafka:
    bootstrap:
      server: "broker:9092"

  datahub:
    version: head
    gms:
      port: "8080"
    monitoring:
      enablePrometheus: true
      portName: "jmx"
    appVersion: "1.0"
    frontend:
      validateSignUpEmail: true
    metadata_service_authentication:
      enabled: true
      systemClientId: "__datahub_system"
      # systemClientSecret:
      #  secretRef: <secret-ref>
      #  secretKey: <secret-key>
