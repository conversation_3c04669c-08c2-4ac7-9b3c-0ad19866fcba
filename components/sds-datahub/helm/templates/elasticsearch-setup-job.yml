{{- if and .Release.IsInstall .Values.elasticsearchSetupJob.enabled -}}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Release.Name }}-elasticsearch-setup-job
  labels:
    {{- include "datahub.labels" . | nindent 4 }}
  {{- with .Values.elasticsearchSetupJob.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  template:
    {{- if or .Values.global.podLabels .Values.elasticsearchSetupJob.podAnnotations }}
    metadata:
    {{- with .Values.elasticsearchSetupJob.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.global.podLabels }}
      labels:
        {{- range $key, $value := . }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
    {{- end }}
    {{- end }}
    spec:
      automountServiceAccountToken: false
    {{- with .Values.global.hostAliases }}
      hostAliases:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.elasticsearchSetupJob.serviceAccount }}
      serviceAccountName: {{ . }}
    {{- end }}
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      volumes:
      {{- with .Values.elasticsearchSetupJob.extraVolumes }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      restartPolicy: Never
      securityContext:
        {{- toYaml .Values.elasticsearchSetupJob.podSecurityContext | nindent 8 }}
      {{- with .Values.elasticsearchSetupJob.extraInitContainers }}
      initContainers:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: elasticsearch-setup-job
          image: {{ include "datahub.image" (dict "imageRegistry" .Values.global.imageRegistry "version" .Values.global.datahub.version "image" .Values.elasticsearchSetupJob.image) }}
          imagePullPolicy: {{ .Values.elasticsearchSetupJob.image.pullPolicy | default "IfNotPresent" }}
          {{- if .Values.elasticsearchSetupJob.image.command }}
          command: {{ .Values.elasticsearchSetupJob.image.command | toRawJson }}
          {{- end }}
          {{- if .Values.elasticsearchSetupJob.image.args }}
          args: {{ .Values.elasticsearchSetupJob.image.args | toRawJson }}
          {{- end }}
          env:
            - name: ELASTICSEARCH_HOST
              value: {{ .Values.global.elasticsearch.host | quote }}
            - name: ELASTICSEARCH_PORT
              value: {{ .Values.global.elasticsearch.port | quote }}
            - name: SKIP_ELASTICSEARCH_CHECK
              value: {{ .Values.global.elasticsearch.skipcheck | quote }}
            - name: ELASTICSEARCH_INSECURE
              value: {{ .Values.global.elasticsearch.insecure | quote }}
            {{- with .Values.global.elasticsearch.useSSL }}
            - name: ELASTICSEARCH_USE_SSL
              value: {{ . | quote }}
            {{- end }}
            {{- with .Values.global.elasticsearch.auth }}
            - name: ELASTICSEARCH_USERNAME
              value: {{ .username }}
            - name: ELASTICSEARCH_PASSWORD
              {{- if .password.value }}
              value: {{ .password.value | quote }}
              {{- else }}
              valueFrom:
                secretKeyRef:
                  name: "{{ .password.secretRef }}"
                  key: "{{ .password.secretKey }}"
              {{- end }}
            {{- end }}
            {{- with .Values.global.elasticsearch.indexPrefix }}
            - name: INDEX_PREFIX
              value: {{ . }}
            {{- end }}
            - name: DATAHUB_ANALYTICS_ENABLED
              value: {{ .Values.global.datahub_analytics_enabled | quote }}
          {{- with .Values.elasticsearchSetupJob.extraEnvs }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          securityContext:
            {{- toYaml .Values.elasticsearchSetupJob.securityContext | nindent 12 }}
          volumeMounts:
          {{- with .Values.elasticsearchSetupJob.extraVolumeMounts }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.elasticsearchSetupJob.resources | nindent 12 }}
        {{- with .Values.elasticsearchSetupJob.extraSidecars }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with default .Values.global.nodeSelector .Values.elasticsearchSetupJob.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.elasticsearchSetupJob.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with default .Values.global.tolerations .Values.elasticsearchSetupJob.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end -}}
