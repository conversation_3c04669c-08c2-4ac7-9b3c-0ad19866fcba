{{- if .Values.global.datahub.metadata_service_authentication.enabled -}}
{{- $secretRef := .Values.global.datahub.metadata_service_authentication.systemClientSecret.secretRef | required "secretRef required" -}}
{{- $secret := lookup "v1" "Secret" .Release.Namespace $secretRef -}}
{{- $data := $secret.data | default dict -}}
{{- with .Values.global.datahub.metadata_service_authentication.provisionSecrets }}

{{- if .enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $secretRef }}
  {{- with .annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{- if .autoGenerate }}
  system_client_secret: {{ get $data "system_client_secret" | default (randAlphaNum 32 | b64enc | quote) }}
  token_service_signing_key: {{ get $data "token_service_signing_key"| default (randAlphaNum 32 | b64enc | quote) }}
  token_service_salt: {{ get $data "token_service_salt" | default (randAlphaNum 32 | b64enc | quote) }}
  {{- else }}
  system_client_secret: {{ .secretValues.secret | b64enc | quote }}
  token_service_signing_key: {{ .secretValues.signingKey | b64enc | quote }}
  token_service_salt: {{ .secretValues.salt | b64enc | quote }}
  {{- end }}

{{- end }}
{{- end -}}
{{- end -}}
