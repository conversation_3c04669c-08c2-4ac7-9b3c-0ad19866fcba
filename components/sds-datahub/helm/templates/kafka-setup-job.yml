{{- if and .Release.IsInstall .Values.kafkaSetupJob.enabled -}}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Release.Name }}-kafka-setup-job
  labels:
    {{- include "datahub.labels" . | nindent 4 }}
  {{- with .Values.kafkaSetupJob.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  template:
    {{- if or .Values.global.podLabels .Values.kafkaSetupJob.podAnnotations }}
    metadata:
    {{- with .Values.kafkaSetupJob.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.global.podLabels }}
      labels:
        {{- range $key, $value := . }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
    {{- end }}
    {{- end }}
    spec:
      automountServiceAccountToken: false
    {{- with .Values.global.hostAliases }}
      hostAliases:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.kafkaSetupJob.serviceAccount }}
      serviceAccountName: {{ . }}
    {{- end }}
      restartPolicy: Never
      securityContext:
        {{- toYaml .Values.kafkaSetupJob.podSecurityContext | nindent 8 }}
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      volumes:
        {{- with .Values.global.credentialsAndCertsSecrets }}
        - name: datahub-certs-dir
          secret:
            defaultMode: 0444
            secretName: {{ .name }}
        {{- end }}
      {{- with .Values.kafkaSetupJob.extraVolumes }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.kafkaSetupJob.extraInitContainers }}
      initContainers:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: kafka-setup-job
          image: {{ include "datahub.image" (dict "imageRegistry" .Values.global.imageRegistry "version" .Values.global.datahub.version "image" .Values.kafkaSetupJob.image) }}
          imagePullPolicy: {{ .Values.kafkaSetupJob.image.pullPolicy | default "IfNotPresent" }}
          {{- if .Values.kafkaSetupJob.image.command }}
          command: {{ .Values.kafkaSetupJob.image.command | toRawJson }}
          {{- end }}
          {{- if .Values.kafkaSetupJob.image.args }}
          args: {{ .Values.kafkaSetupJob.image.args | toRawJson }}
          {{- end }}
          env:
            {{- if .Values.global.kafka.zookeeper.server }}
            - name: KAFKA_ZOOKEEPER_CONNECT
              value: {{ .Values.global.kafka.zookeeper.server | quote }}
            {{- end }}
            - name: KAFKA_BOOTSTRAP_SERVER
              value: {{ .Values.global.kafka.bootstrap.server | quote }}
            {{- with .Values.global.kafka.maxMessageBytes }}
            - name: MAX_MESSAGE_BYTES
              value: {{ . | quote }}
            {{- end }}
            {{- if eq .Values.global.kafka.schemaregistry.type "INTERNAL" }}
            - name: USE_CONFLUENT_SCHEMA_REGISTRY
              value: "false"
            {{- else if eq .Values.global.kafka.schemaregistry.type "KAFKA" }}
            - name: USE_CONFLUENT_SCHEMA_REGISTRY
              value: "true"
            {{- end }}
            {{- if .Values.global.springKafkaConfigurationOverrides }}
            {{- range $configName, $configValue := .Values.global.springKafkaConfigurationOverrides }}
            - name: KAFKA_PROPERTIES_{{ $configName | replace "." "_" | upper }}
              value: {{ $configValue | quote }}
            {{- end }}
            {{- end }}
            {{- if .Values.global.credentialsAndCertsSecrets }}
            {{- range $envVarName, $envVarValue := .Values.global.credentialsAndCertsSecrets.secureEnv }}
            - name: KAFKA_PROPERTIES_{{ $envVarName | replace "." "_" | upper }}
              valueFrom:
                secretKeyRef:
                  name: {{ $.Values.global.credentialsAndCertsSecrets.name }}
                  key: {{ $envVarValue }}
            {{- end }}
            {{- end }}
            {{- with .Values.global.kafka.topics }}
            - name: METADATA_CHANGE_EVENT_NAME
              value: {{ .metadata_change_event_name }}
            - name: FAILED_METADATA_CHANGE_EVENT_NAME
              value: {{ .failed_metadata_change_event_name }}
            - name: METADATA_AUDIT_EVENT_NAME
              value: {{ .metadata_audit_event_name }}
            - name: DATAHUB_USAGE_EVENT_NAME
              value: {{ .datahub_usage_event_name }}
            - name: METADATA_CHANGE_PROPOSAL_TOPIC_NAME
              value: {{ .metadata_change_proposal_topic_name }}
            - name: FAILED_METADATA_CHANGE_PROPOSAL_TOPIC_NAME
              value: {{ .failed_metadata_change_proposal_topic_name }}
            - name: METADATA_CHANGE_LOG_VERSIONED_TOPIC_NAME
              value: {{ .metadata_change_log_versioned_topic_name }}
            - name: METADATA_CHANGE_LOG_TIMESERIES_TOPIC_NAME
              value: {{ .metadata_change_log_timeseries_topic_name }}
            - name: PLATFORM_EVENT_TOPIC_NAME
              value: {{ .platform_event_topic_name }}
            - name: DATAHUB_UPGRADE_HISTORY_TOPIC_NAME
              value: {{ .datahub_upgrade_history_topic_name }}
            {{- end }}
            {{- with .Values.global.kafka.partitions }}
            - name: PARTITIONS
              value: {{ . | quote }}
            {{- end }}
            {{- with .Values.global.kafka.replicationFactor }}
            - name: REPLICATION_FACTOR
              value: {{ . | quote }}
            {{- end }}
          {{- with .Values.kafkaSetupJob.extraEnvs }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          securityContext:
            {{- toYaml .Values.kafkaSetupJob.securityContext | nindent 12 }}
          volumeMounts:
          {{- if .Values.global.credentialsAndCertsSecrets }}
            - name: datahub-certs-dir
              mountPath: {{ .Values.global.credentialsAndCertsSecrets.path | default "/mnt/certs" }}
          {{- end }}
          {{- with .Values.kafkaSetupJob.extraVolumeMounts }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.kafkaSetupJob.resources | nindent 12 }}
        {{- with .Values.kafkaSetupJob.extraSidecars }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with default .Values.global.nodeSelector .Values.kafkaSetupJob.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.kafkaSetupJob.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with default .Values.global.tolerations .Values.kafkaSetupJob.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end -}}
