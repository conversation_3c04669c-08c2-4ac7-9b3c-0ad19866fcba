{{- $secretRef := .Values.global.datahub.encryptionKey.secretRef | required "secretRef required" -}}
{{- $secretKey := .Values.global.datahub.encryptionKey.secretKey | required "secretKey required" -}}
{{- $secret := lookup "v1" "Secret" .Release.Namespace $secretRef -}}
{{- $data := $secret.data | default dict -}}
{{- with .Values.global.datahub.encryptionKey.provisionSecret }}

{{- if .enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $secretRef }}
  {{- with .annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{- if .autoGenerate }}
  encryption_key_secret: {{ get $data $secretKey | default (randAlphaNum 20 | b64enc | quote) }}
  {{- else }}
  encryption_key_secret: {{ .secretValues.encryptionKey | b64enc | quote }}
  {{- end }}

{{- end }}
{{- end -}}
