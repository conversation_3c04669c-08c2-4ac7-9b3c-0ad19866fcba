{{- if and .Release.IsInstall .Values.postgresqlSetupJob.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Release.Name }}-postgresql-setup-job
  labels:
    {{- include "datahub.labels" . | nindent 4 }}
  {{- with .Values.postgresqlSetupJob.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  template:
    {{- if or .Values.global.podLabels .Values.postgresqlSetupJob.podAnnotations }}
    metadata:
    {{- with .Values.postgresqlSetupJob.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.global.podLabels }}
      labels:
        {{- range $key, $value := . }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
    {{- end }}
    {{- end }}
    spec:
      automountServiceAccountToken: false
    {{- with .Values.global.hostAliases }}
      hostAliases:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.postgresqlSetupJob.serviceAccount }}
      serviceAccountName: {{ . }}
    {{- end }}
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      volumes:
      {{- with .Values.postgresqlSetupJob.extraVolumes }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      restartPolicy: Never
      securityContext:
        {{- toYaml .Values.postgresqlSetupJob.podSecurityContext | nindent 8 }}
      {{- with .Values.postgresqlSetupJob.extraInitContainers }}
      initContainers:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: postgresql-setup-job
          image: {{ include "datahub.image" (dict "imageRegistry" .Values.global.imageRegistry "version" .Values.global.datahub.version "image" .Values.postgresqlSetupJob.image) }}
          imagePullPolicy: {{ .Values.postgresqlSetupJob.image.pullPolicy | default "Always" }}
          {{- if .Values.postgresqlSetupJob.image.command }}
          command: {{ .Values.postgresqlSetupJob.image.command | toRawJson }}
          {{- end }}
          {{- if .Values.postgresqlSetupJob.image.args }}
          args: {{ .Values.postgresqlSetupJob.image.args | toRawJson }}
          {{- end }}
          env:
            - name: POSTGRES_USERNAME
              {{- $usernameValue := (.Values.postgresqlSetupJob).username | default .Values.global.sql.datasource.username }}
              {{- if and (kindIs "string" $usernameValue) $usernameValue }}
              value: {{ $usernameValue | quote }}
              {{- else }}
              valueFrom:
                secretKeyRef:
                  name: "{{ tpl (($usernameValue).secretRef | default .Values.global.sql.datasource.secretRef) . }}"
                  key: "{{ ($usernameValue.username).secretKey | default .Values.global.sql.datasource.username.secretKey }}"
              {{- end }}
            - name: POSTGRES_PASSWORD
              {{- $passwordValue := (.Values.postgresqlSetupJob.password).value | default .Values.global.sql.datasource.password.value }}
              {{- if $passwordValue }}
              value: {{ $passwordValue | quote }}
              {{- else }}
              valueFrom:
                secretKeyRef:
                  name: "{{ tpl ((.Values.postgresqlSetupJob).secretRef | default .Values.global.sql.datasource.secretRef) . }}"
                  key: "{{ (.Values.postgresqlSetupJob.password).secretKey | default .Values.global.sql.datasource.password.secretKey }}"
              {{- end }}
            - name: POSTGRES_HOST
              {{- $hostValue := (.Values.postgresqlSetupJob.host).value | default .Values.global.sql.datasource.host.value }}
              {{- if $hostValue }}
              value: {{ $hostValue | quote }}
              {{- else }}
              valueFrom:
                secretKeyRef:
                  name: "{{ tpl ((.Values.postgresqlSetupJob).secretRef | default .Values.global.sql.datasource.secretRef) . }}"
                  key: "{{ (.Values.postgresqlSetupJob.host).secretKey | default .Values.global.sql.datasource.host.secretKey }}"
              {{- end }}
            - name: POSTGRES_PORT
              {{- $portValue := (.Values.postgresqlSetupJob.port).value | default .Values.global.sql.datasource.port.value }}
              {{- if $portValue }}
              value: {{ $portValue | quote }}
              {{- else }}
              valueFrom:
                secretKeyRef:
                  name: "{{ tpl ((.Values.postgresqlSetupJob).secretRef | default .Values.global.sql.datasource.secretRef) . }}"
                  key: "{{ (.Values.postgresqlSetupJob.port).secretKey | default .Values.global.sql.datasource.port.secretKey }}"
              {{- end }}
          {{- with .Values.postgresqlSetupJob.extraEnvs }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          securityContext:
            {{- toYaml .Values.postgresqlSetupJob.securityContext | nindent 12 }}
          volumeMounts:
          {{- with .Values.postgresqlSetupJob.extraVolumeMounts }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.postgresqlSetupJob.resources | nindent 12 }}
        {{- with .Values.postgresqlSetupJob.extraSidecars }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with default .Values.global.nodeSelector .Values.postgresqlSetupJob.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.postgresqlSetupJob.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with default .Values.global.tolerations .Values.postgresqlSetupJob.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end -}}
