{{- if and .Release.IsInstall .Values.mysqlSetupJob.enabled -}}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Release.Name }}-mysql-setup-job
  labels:
    {{- include "datahub.labels" . | nindent 4 }}
  {{- with .Values.mysqlSetupJob.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  ttlSecondsAfterFinished: 60
  template:
    {{- if or .Values.global.podLabels .Values.mysqlSetupJob.podAnnotations }}
    metadata:
    {{- with .Values.mysqlSetupJob.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.global.podLabels }}
      labels:
        {{- range $key, $value := . }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
    {{- end }}
    {{- end }}
    spec:
    {{- with .Values.global.hostAliases }}
      hostAliases:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.mysqlSetupJob.serviceAccount }}
      serviceAccountName: {{ . }}
    {{- end }}
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      volumes:
      {{- with .Values.mysqlSetupJob.extraVolumes }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      restartPolicy: Never
      securityContext:
        {{- toYaml .Values.mysqlSetupJob.podSecurityContext | nindent 8 }}
      {{- with .Values.mysqlSetupJob.extraInitContainers }}
      initContainers:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: mysql-setup-job
          image: {{ include "datahub.image" (dict "imageRegistry" .Values.global.imageRegistry "version" .Values.global.datahub.version "image" .Values.mysqlSetupJob.image) }}
          imagePullPolicy: {{ .Values.mysqlSetupJob.image.pullPolicy | default "IfNotPresent" }}
          {{- if .Values.mysqlSetupJob.image.command }}
          command: {{ .Values.mysqlSetupJob.image.command | toRawJson }}
          {{- end }}
          {{- if .Values.mysqlSetupJob.image.args }}
          args: {{ .Values.mysqlSetupJob.image.args | toRawJson }}
          {{- end }}
          env:
            - name: MYSQL_USERNAME
              {{- $usernameValue := (.Values.mysqlSetupJob).username | default .Values.global.sql.datasource.username }}
              {{- if and (kindIs "string" $usernameValue) $usernameValue }}
              value: {{ $usernameValue | quote }}
              {{- else }}
              valueFrom:
                secretKeyRef:
                  name: "{{ ($usernameValue).secretRef | default .Values.global.sql.datasource.username.secretRef }}"
                  key: "{{ ($usernameValue).secretKey | default .Values.global.sql.datasource.username.secretKey }}"
              {{- end }}
            - name: MYSQL_PASSWORD
              {{- $passwordValue := (.Values.mysqlSetupJob.password).value | default .Values.global.sql.datasource.password.value }}
              {{- if $passwordValue }}
              value: {{ $passwordValue | quote }}
              {{- else }}
              valueFrom:
                secretKeyRef:
                  name: "{{ (.Values.mysqlSetupJob.password).secretRef | default .Values.global.sql.datasource.password.secretRef }}"
                  key: "{{ (.Values.mysqlSetupJob.password).secretKey | default .Values.global.sql.datasource.password.secretKey }}"
              {{- end }}
            - name: MYSQL_HOST
              value: {{ .Values.global.sql.datasource.hostForMysqlClient | quote }}
            - name: MYSQL_PORT
              value: {{ .Values.global.sql.datasource.port | quote }}
          {{- with .Values.mysqlSetupJob.extraEnvs }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          securityContext:
            {{- toYaml .Values.mysqlSetupJob.securityContext | nindent 12 }}
          volumeMounts:
          {{- with .Values.mysqlSetupJob.extraVolumeMounts }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.mysqlSetupJob.resources | nindent 12 }}
        {{- with .Values.mysqlSetupJob.extraSidecars }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with default .Values.global.nodeSelector .Values.mysqlSetupJob.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.mysqlSetupJob.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with default .Values.global.tolerations .Values.mysqlSetupJob.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end -}}
