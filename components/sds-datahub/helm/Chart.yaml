apiVersion: v2
appVersion: v1.0.0
dependencies:
- condition: datahub-gms.enabled
  name: datahub-gms
  repository: file://./subcharts/datahub-gms
  version: 0.2.179
- condition: datahub-frontend.enabled
  name: datahub-frontend
  repository: file://./subcharts/datahub-frontend
  version: 0.2.162
- condition: global.datahub_standalone_consumers_enabled
  name: datahub-mae-consumer
  repository: file://./subcharts/datahub-mae-consumer
  version: 0.2.166
- condition: global.datahub_standalone_consumers_enabled
  name: datahub-mce-consumer
  repository: file://./subcharts/datahub-mce-consumer
  version: 0.2.169
- condition: datahub-ingestion-cron.enabled
  name: datahub-ingestion-cron
  repository: file://./subcharts/datahub-ingestion-cron
  version: 0.2.146
- condition: acryl-datahub-actions.enabled
  name: acryl-datahub-actions
  repository: file://./subcharts/acryl-datahub-actions
  version: 0.2.150
description: A Helm chart for DataHub
maintainers:
- email: <EMAIL>
  name: DataHub
name: sds-datahub
type: application
version: 0.6.0
