# Values to start up datahub after starting up the datahub-datahub-bootstrap chart with "datahub-bootstrap" release name
# Copy this chart and change configuration as needed.

imagePullSecrets:
  - name: docker-secret

datahub-gms:
  enabled: true
  imagePullSecrets:
    - name: docker-secret
  image:
    # repository: acryldata/datahub-gms
    repository: prevalentai/datahub
    tag: "4-1-0-gms-1.0.0-bookworm-12.10-20250428-slim"
    # tag: "v0.11.0 # defaults to .global.datahub.version
    # Add custom command / arguments to this job.  Useful if you need a custom startup or shutdown script
    # to run
    # command: customCommand
    # args: []
  resources:
    limits:
      memory: 2Gi
    requests:
      cpu: 100m
      memory: 1Gi
  livenessProbe:
    initialDelaySeconds: 60
    periodSeconds: 30
    failureThreshold: 8
  readinessProbe:
    initialDelaySeconds: 120
    periodSeconds: 30
    failureThreshold: 8
  theme_v2:
    enabled: true
    default: false
    toggeable: true
  # Optionaly specify service type for datahub-gms: LoadBalancer, ClusterIP or NodePort, by default: LoadBalancer
  # service:
  #   type: ClusterIP
  # Optionally set a GMS specific SQL login (defaults to global login)
  # sql:
  #   datasource:
  #     username: "gms-login"
  #     password:
  #       secretRef: gms-secret
  #       secretKey: gms-password

datahub-frontend:
  enabled: true
  imagePullSecrets:
    - name: docker-secret
  image:
    # repository: acryldata/datahub-frontend-react
    repository: prevalentai/datahub
    tag: "4-1-0-frontend-1.0.0-bookworm-12.10-20250428-slim"
    # tag: "v0.11.0" # # defaults to .global.datahub.version
    # Add custom command / arguments to this job.  Useful if you need a custom startup or shutdown script
    # to run
    # command: customCommand
    # args: []
  resources:
    limits:
      memory: 1400Mi
    requests:
      cpu: 100m
      memory: 512Mi
  # Set up ingress to expose react front-end
  ingress:
    enabled: false
  defaultUserCredentials: {}
  service:
    extraLabels: {}
    # randomAdminPassword: true
    # You can also set specific passwords for default users
    # manualValues: |
    #   datahub:manualPassword
    #   initialViewer:manualPassword
  # Optionaly specify service type for datahub-frontend: LoadBalancer, ClusterIP or NodePort, by default: LoadBalancer
  # service:
  #   type: ClusterIP

acryl-datahub-actions:
  enabled: true
  imagePullSecrets:
    - name: docker-secret
  image:
    # repository: acryldata/datahub-actions
    # tag: "v0.2.0"
    repository: prevalentai/datahub
    tag: "4-1-0-actions-0.2.0-bookworm-12.10-20250428-slim"
    #pullPolicy: Always
    # Add custom command / arguments to this job.  Useful if you need a custom startup or shutdown script
    # to run
    # command: customCommand
    # args: []
  # mount the k8s secret as a volume in the container, each key name is mounted as a file on the mount path /etc/datahub/ingestion-secret-files
  # ingestionSecretFiles:
  #   name: ${K8S_SECRET_NAME}
  #   defaultMode: "0444"
  resources:
    limits:
      memory: 512Mi
    requests:
      cpu: 300m
      memory: 256Mi

datahub-mae-consumer:
  imagePullSecrets:
    - name: docker-secret
  image:
    #repository: acryldata/datahub-mae-consumer
    repository: prevalentai/datahub
    tag: "4-1-0-mae-consumer-1.0.0-bookworm-12.10-20250428-slim"
    # tag: "v0.11.0" # defaults to .global.datahub.version
    # Add custom command / arguments to this job.  Useful if you need a custom startup or shutdown script
    # to run
    # command: customCommand
    # args: []
  resources:
    limits:
      memory: 1536Mi
    requests:
      cpu: 100m
      memory: 256Mi

datahub-mce-consumer:
  imagePullSecrets:
    - name: docker-secret
  image:
    # repository: acryldata/datahub-mce-consumer
    repository: prevalentai/datahub
    tag: "4-1-0-mce-consumer-1.0.0-bookworm-12.10-20250428-slim"
    # tag: "v0.11.0" # defaults to .global.datahub.version
    # Add custom command / arguments to this job.  Useful if you need a custom startup or shutdown script
    # to run
    # command: customCommand
    # args: []
  resources:
    limits:
      memory: 1536Mi
    requests:
      cpu: 100m
      memory: 256Mi

datahub-ingestion-cron:
  enabled: false
  image:
    repository: acryldata/datahub-ingestion
    # tag: "v0.11.0" # defaults to .global.datahub.version

elasticsearchSetupJob:
  enabled: true
  image:
    # repository: acryldata/datahub-elasticsearch-setup
    repository: prevalentai/datahub
    tag: 4-1-0-elasticsearch-setup-bookworm-12.10-20250428-slim
    # tag: "v0.11.0" # defaults to .global.datahub.version
    # Add custom command / arguments to this job.  Useful if you need a custom startup or shutdown script
    # to run
    # command: customCommand
    # args: []
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 300m
      memory: 256Mi
  extraInitContainers: []
    # - name: wait-for-elasticsearch
    #   image: bitnami/kubectl
    #   args:
    #     - wait
    #     - pod/datahub-elasticsearch-master-0
    #     - --for=condition=ready
    #     - --timeout=120s
  podSecurityContext:
    fsGroup: 1000
  securityContext:
    runAsUser: 1000
  annotations:
    argocd.argoproj.io/hook: Sync
    argocd.argoproj.io/hook-delete-policy: HookSucceeded
    argocd.argoproj.io/sync-options: Force=true,Replace=true
    # This is what defines this resource as a hook. Without this line, the
    # job is considered part of the release.
    helm.sh/hook: pre-install,pre-upgrade
    helm.sh/hook-weight: "-5"
    helm.sh/hook-delete-policy: before-hook-creation
  podAnnotations:
    sidecar.istio.io/inject: "false"
  # If you want to use OpenSearch instead of ElasticSearch add the USE_AWS_ELASTICSEARCH environment variable below
  # extraEnvs:
  #   - name: USE_AWS_ELASTICSEARCH
  #     value: "true"
  # Add extra sidecar containers to job pod
  extraSidecars: []
    # - name: my-image-name
    #   image: my-image
    #   imagePullPolicy: Always

kafkaSetupJob:
  enabled: true
  image:
    # repository: acryldata/datahub-kafka-setup
    repository: prevalentai/datahub
    tag: "4-1-0-kafka-3.9.0-setup-bookworm-12.10-20250428-slim"
    # tag: "v0.11.0" # defaults to .global.datahub.version
    # Add custom command / arguments to this job.  Useful if you need a custom startup or shutdown script
    # to run
    # command: customCommand
    # args: []
  resources:
    limits:
      cpu: 500m
      memory: 1024Mi
    requests:
      cpu: 300m
      memory: 768Mi
  extraInitContainers: []
    # - name: wait-for-elasticsearch
    #   image: bitnami/kubectl
    #   args:
    #     - wait
    #     - pod/datahub-bootstrap-broker-0
    #     - --for=condition=ready
    #     - --timeout=120s
  podSecurityContext:
    fsGroup: 1000
  securityContext:
    runAsUser: 1000
  annotations:
    argocd.argoproj.io/hook: Sync
    argocd.argoproj.io/hook-delete-policy: HookSucceeded
    argocd.argoproj.io/sync-options: Force=true,Replace=true
    # This is what defines this resource as a hook. Without this line, the
    # job is considered part of the release.
    helm.sh/hook: pre-install,pre-upgrade
    helm.sh/hook-weight: "-5"
    helm.sh/hook-delete-policy: before-hook-creation
  podAnnotations: 
    sidecar.istio.io/inject: "false"
  # Add extra sidecar containers to job pod
  extraSidecars: []
    # - name: my-image-name
    #   image: my-image
    #   imagePullPolicy: Always

mysqlSetupJob:
  enabled: false
  image:
    # repository: acryldata/datahub-mysql-setup
    repository: prevalentai/datahub
    # tag: ""
    # tag: "v0.11.0" # defaults to .global.datahub.version
    # Add custom command / arguments to this job.  Useful if you need a custom startup or shutdown script
    # to run
    # command: customCommand
    # args: []
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 300m
      memory: 256Mi
  extraInitContainers: []
  podSecurityContext:
    fsGroup: 1000
  securityContext:
    runAsUser: 1000
  annotations:
    # This is what defines this resource as a hook. Without this line, the
    # job is considered part of the release.
    helm.sh/hook: pre-install,pre-upgrade
    helm.sh/hook-weight: "-5"
    helm.sh/hook-delete-policy: before-hook-creation
  podAnnotations: {}
  # Optionally set a set-up job specific login (defaults to global login)
  # username: "mysqlSetupJob-login"
  # password:
  #   secretRef: mysqlSetupJob-secret
  #   secretKey: mysqlSetupJob-password
  # Add extra sidecar containers to job pod
  extraSidecars: []
    # - name: my-image-name
    #   image: my-image
    #   imagePullPolicy: Always

postgresqlSetupJob:
  enabled: true
  image:
    repository: prevalentai/datahub
    tag: 4-1-0-postgresql15-setup-bookworm-12.10-20250428-slim
    #repository: acryldata/datahub-postgres-setup
    # tag: "v0.11.0" # defaults to .global.datahub.version
    # Add custom command / arguments to this job.  Useful if you need a custom startup or shutdown script
    # to run
    # command: customCommand
    # args: []
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 300m
      memory: 256Mi
  extraInitContainers: []
  podSecurityContext:
    fsGroup: 1000
  securityContext:
    runAsUser: 1000
  annotations:
    argocd.argoproj.io/hook: Sync
    argocd.argoproj.io/hook-delete-policy: HookSucceeded
    argocd.argoproj.io/sync-options: Force=true,Replace=true
    # This is what defines this resource as a hook. Without this line, the
    # job is considered part of the release.
    helm.sh/hook: pre-install,pre-upgrade
    helm.sh/hook-weight: "-5"
    helm.sh/hook-delete-policy: before-hook-creation
  podAnnotations:
    sidecar.istio.io/inject: "false"
  # Optionally set a set-up job specific login (defaults to global login)
  # username: "postgresqlSetupJob-login"
  # password:
  #   secretRef: postgresqlSetupJob-secret
  #   secretKey: postgresqlSetupJob-password
  # Add extra sidecar containers to job pod
  extraSidecars: []
    # - name: my-image-name
    #   image: my-image
    #   imagePullPolicy: Always
  # Optionally set a specific database using extraEnvs
  # extraEnvs: []
  #   - name: "DATAHUB_DB_NAME"
  #     value: "dh"

## No code data migration
datahubUpgrade:
  enabled: true
  image:
    # repository: acryldata/datahub-upgrade
    repository: prevalentai/datahub
    tag: "4-1-0-upgrade-1.0.0-bookworm-12.10-20250428-slim"
    # tag: "v0.11.0"  # defaults to .global.datahub.version
  batchSize: 1000
  batchDelayMs: 100
  podSecurityContext: {}
    # fsGroup: 1000
  securityContext: {}
    # runAsUser: 1000
  annotations:
    # This is what defines this resource as a hook. Without this line, the
    # job is considered part of the release.
    helm.sh/hook: post-install,post-upgrade
    helm.sh/hook-weight: "-2"
    helm.sh/hook-delete-policy: before-hook-creation
  podAnnotations: {}
  # Add extra sidecar containers to job pod
  extraSidecars: []
    # - name: my-image-name
    #   image: my-image
    #   imagePullPolicy: Always
  restoreIndices:
    image:
      # Add custom command / arguments to this job.  Useful if you need a custom startup or shutdown script
      # to run
      command:
      # Overrides the entire set of upgrade job arguments, for if you want to specify exactly whi
      args: []
    # Alternative for overriding specific args instead of changing the entire set and overriding defaults
    args:
      # Restore only rows with less than a certain epoch millisecond timestamp
      lePitEpochMs:
      # Restore only rows with greater than a certain epoch millisecond timestamp
      gePitEpochMs:
      # Used in conjunction with urnBasedPagination, resume from a particular urn
      lastUrn:
      # Used in conjunction with urnBasedPagination and lastUrn, resume from a particular urn for a particular aspect to avoid reprocessing certain aspects
      lastAspect:
      # Uses a key based paging strategy when scrolling through SQL rows instead of offset. Often faster for large sets of data
      urnBasedPagination:
      # Only restore for certain aspects, comma separated list
      aspectNames:
      # A SQL "like" statement that matches against the urn column
      urnLike:
      # Used with default paging, start from a specified offset
      startingOffset:
      # Specifies number of threads for processing pages of rows to restore
      numThreads:
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 300m
        memory: 256Mi
    # Schedule of CronJob when enabled
    schedule: "0 0 * * 0"
    # Add the concurrency Policy flexibility via values
    concurrencyPolicy: Allow
    # Add extra sidecar containers to job pod
    extraSidecars: []
      # - name: my-image-name
      #   image: my-image
      #   imagePullPolicy: Always
    # Optionally set a specific PostgreSQL database name using extraEnvs
    # extraEnvs:
    #   - name: "DATAHUB_DB_NAME"
    #    value: "dh"
  extraInitContainers: []

## Runs system update processes
## Includes: Elasticsearch Indices Creation/Reindex (See global.elasticsearch.index for additional configuration)
datahubSystemUpdate:
  image:
    # repository: acryldata/datahub-upgrade
    repository: prevalentai/datahub
    tag: "4-1-0-upgrade-1.0.0-bookworm-12.10-20250428-slim"
    # tag:
    # Add custom command / arguments to this job.  Useful if you need a custom startup or shutdown script
    # to run
    # command: customCommand
    # args: []
  podSecurityContext: {}
    # fsGroup: 1000
  securityContext: {}
    # runAsUser: 1000
  annotations:
    argocd.argoproj.io/hook: Sync
    argocd.argoproj.io/hook-delete-policy: HookSucceeded
    argocd.argoproj.io/sync-options: Force=true,Replace=true
    # This is what defines this resource as a hook. Without this line, the
    # job is considered part of the release.
    helm.sh/hook: pre-install,pre-upgrade
    helm.sh/hook-weight: "-4"
    helm.sh/hook-delete-policy: before-hook-creation
  # !! Requires version v0.13.0 or greater
  # Split the system update into 2 jobs, one that is blocking the rest of
  # the deployment and the other which is non-blocking. Once the blocking
  # steps are completed, the non-blocking job runs while the rest of the
  # system is starting.
  nonblocking:
    enabled: true
    # When mode = 'nonblocking' the nonblocking job should not include the above helm.sh/hook annotations
    annotations:
      argocd.argoproj.io/hook: Sync
      argocd.argoproj.io/hook-delete-policy: HookSucceeded
      argocd.argoproj.io/sync-options: Force=true,Replace=true
      # This is what defines this resource as a hook. Without this line, the
      # job is considered part of the release.
      helm.sh/hook: post-install,post-upgrade
      helm.sh/hook-delete-policy: before-hook-creation
    image:
      args:
      # Add custom command / arguments to this job.  Useful if you need a custom startup or shutdown script
      # to run
      # command: customCommand
      # args: []
  # Depends on v0.14.2 or greater
  bootstrapMCPs:
    default:
      value_configs:
        - "datahub.bootstrapMCPs.default.ingestion.version"
        - "datahub.bootstrapMCPs.default.schedule.timezone"
    datahubGC:
      # For information about this recipe https://datahubproject.io/docs/0.14.0/generated/ingestion/sources/datahubgc/#install-the-plugin
      # Generate values.schedule.interval with a jitter window using a generated config
      dailyCronWindow:
        startHour: 18
        endHour: 5
      # dynamic overrides, the output to each function is deepMerged with values
      values_generated_configs:
        - "datahub.systemUpdate.datahubGC.dailyCronWindow"
      # Environment variable containing the json value for the template mcp
      values_env: DATAHUB_GC_BOOTSTRAP_VALUES
      revision_env: DATAHUB_GC_BOOTSTRAP_REVISION
      # Base values for the template mcp
      values:
        ingestion:
          name: datahub-gc
          # # override global.datahub.managed_ingestion.defaultCliVersion
          # version: "0.14.1.7rc2"
        # schedule:
          # # override global.datahub.timezone
          # timezone: "UTC"
          # # override dailyCronWindow
          # interval: "0 1 * * *"
        cleanup_expired_tokens: "false"
        truncate_indices: "true"
        truncate_indices_retention_days: 30
        dataprocess_cleanup:
          retention_days: 30
          delete_empty_data_jobs: "true"
          delete_empty_data_flows: "true"
          hard_delete_entities: "false"
          keep_last_n: 10
        soft_deleted_entities_cleanup:
          retention_days: 30
  podAnnotations:
    sidecar.istio.io/inject: "false"
  resources:
    limits:
      cpu: 500m
      memory: 2Gi
    requests:
      cpu: 300m
      memory: 2Gi
  # Add extra sidecar containers to job pod
  extraSidecars: []
    # - name: my-image-name
    #   image: my-image
    #   imagePullPolicy: Always
  extraInitContainers: []

global:
  strict_mode: true
  graph_service_impl: elasticsearch
  datahub_analytics_enabled: true
  datahub_standalone_consumers_enabled: false
  imageRegistry: "docker.io"

  elasticsearch:
    host: "datahub-elasticsearch-master"
    # If you want to use OpenSearch instead of ElasticSearch use different hostname below
    # host: "opensearch-cluster-master"
    port: "9200"
    skipcheck: "false"
    insecure: "false"
    useSSL: "false"
    # If you want to specify index prefixes use indexPrefix
    # indexPrefix: "dh"

    ## The following section controls when and how reindexing of elasticsearch indices are performed
    index:
      ## Enable reindexing when mappings change based on the data model annotations
      enableMappingsReindex: true

      ## Enable reindexing when static index settings change.
      ## Dynamic settings which do not require reindexing are not affected
      ## Primarily this should be enabled when re-sharding is necessary for scaling/performance.
      enableSettingsReindex: true

      ## Index settings can be overridden for entity indices or other indices on an index by index basis
      ## Some index settings, such as # of shards, requires reindexing while others, i.e. replicas, do not
      ## The index settings options can be specified as a string or a map (which is converted to a string
      ## representation)
      ##
      ## Non-Entity indices do not require the prefix
      # settingsOverrides: '{"graph_service_v1":{"number_of_shards":"5"},"system_metadata_service_v1":{"number_of_shards":"5"}}'
      ## or
      # settingsOverrides:
      #   graph_service_v1:
      #     number_of_shards: '5'
      #   system_metadata_service_v1:
      #     number_of_shards: '6'
      #
      ## Entity indices do not require the prefix or suffix
      # entitySettingsOverrides: '{"dataset":{"number_of_shards":"10"}}'

      ## The amount of delay between indexing a document and having it returned in queries
      ## Increasing this value can improve performance when ingesting large amounts of data
      # refreshIntervalSeconds: 1

      ## The following options control settings for datahub-upgrade job when creating or reindexing indices
      upgrade:
        ## When reindexing is required, this option will clone the existing index as a backup
        ## The clone indices are not currently managed.
        cloneIndices: true

        ## Typically when reindexing the document counts between the original and destination indices should match.
        ## In some cases reindexing might not be able to proceed due to incompatibilities between a document in the
        ## orignal index and the new index's mappings. This document could be dropped and re-ingested or restored from
        ## the SQL database.
        ##
        ## This setting allows continuing if and only if the cloneIndices setting is also enabled which
        ## ensures a complete backup of the original index is preserved.
        allowDocCountMismatch: false

    ## Search related configuration
    search:
      ## Maximum terms in aggregations
      maxTermBucketSize: 20

      ## Configuration around exact matching for search
      exactMatch:
        ## if false will only apply weights, if true will exclude non-exact
        exclusive: false
        ## include prefix exact matches
        withPrefix: true
        ## boost multiplier when exact with case
        exactFactor: 2.0
        ## boost multiplier when exact prefix
        prefixFactor: 1.6
        ## stacked boost multiplier when case mismatch
        caseSensitivityFactor: 0.7
        ## enable exact match on structured search
        enableStructured: true

      ## Configuration for graph service dao
      graph:
        ## graph dao timeout seconds
        timeoutSeconds: 50
        ## graph dao batch size
        batchSize: 1000
        ## graph dao max result size
        maxResult: 10000

      custom:
        enabled: false
        # See documentation: https://datahubproject.io/docs/how/search/#customizing-search
        config:
          # Notes:
          #
          # First match wins
          #
          # queryRegex = Java regex syntax
          #
          # functionScores - See the following for function score syntax
          # https://www.elastic.co/guide/en/elasticsearch/reference/7.17/query-dsl-function-score-query.html

          queryConfigurations:
            # Select *
            - queryRegex: '[*]|'
              simpleQuery: false
              prefixMatchQuery: false
              exactMatchQuery: false
              functionScore:
                functions:
                  - filter:
                      term:
                        deprecated:
                          value: true
                    weight: 0.5
                score_mode: multiply
                boost_mode: multiply

            # Criteria for exact-match only
            # Contains quoted or contains underscore then use exact match query
            - queryRegex: >-
                ["'].+["']|\S+_\S+
              simpleQuery: false
              prefixMatchQuery: true
              exactMatchQuery: true
              functionScore:
                functions:
                  - filter:
                      term:
                        deprecated:
                          value: true
                    weight: 0.5
                score_mode: multiply
                boost_mode: multiply
            # default
            - queryRegex: .*
              simpleQuery: true
              prefixMatchQuery: true
              exactMatchQuery: true
              functionScore:
                functions:
                  - filter:
                      term:
                        deprecated:
                          value: true
                    weight: 0.5
                score_mode: multiply
                boost_mode: multiply

  kafka:
    bootstrap:
      server: "datahub-bootstrap-kafka:9092"
    zookeeper:
      server: "datahub-bootstrap-zookeeper:2181"
    # This section defines the names for the kafka topics that DataHub depends on, at a global level. Do not override this config
    # at a sub-chart level.
    topics:
      metadata_change_event_name: "MetadataChangeEvent_v4"
      failed_metadata_change_event_name: "FailedMetadataChangeEvent_v4"
      metadata_audit_event_name: "MetadataAuditEvent_v4"
      datahub_usage_event_name: "DataHubUsageEvent_v1"
      metadata_change_proposal_topic_name: "MetadataChangeProposal_v1"
      failed_metadata_change_proposal_topic_name: "FailedMetadataChangeProposal_v1"
      metadata_change_log_versioned_topic_name: "MetadataChangeLog_Versioned_v1"
      metadata_change_log_timeseries_topic_name: "MetadataChangeLog_Timeseries_v1"
      platform_event_topic_name: "PlatformEvent_v1"
      datahub_upgrade_history_topic_name: "DataHubUpgradeHistory_v1"
    consumer_groups:
      datahub_upgrade_history_kafka_consumer_group_id: {}
      #   gms: "<<release-name>>-duhe-consumer-job-client-gms"
      #   mae-consumer: "<<release-name>>-duhe-consumer-job-client-mcl"
      #   mce-consumer: "<<release-name>>-duhe-consumer-job-client-mcp"
      datahub_actions_ingestion_executor_consumer_group_id: "ingestion_executor"
      datahub_actions_slack_consumer_group_id: "datahub_slack_action"
      datahub_actions_teams_consumer_group_id: "datahub_teams_action"
      datahub_usage_event_kafka_consumer_group_id: "datahub-usage-event-consumer-job-client"
      metadata_change_log_kafka_consumer_group_id: "generic-mae-consumer-job-client"
      platform_event_kafka_consumer_group_id: "generic-platform-event-job-client"
      metadata_change_event_kafka_consumer_group_id: "mce-consumer-job-client"
      metadata_change_proposal_kafka_consumer_group_id: "generic-mce-consumer-job-client"
    metadataChangeLog:
      hooks:
        siblings:
          enabled: true
          consumerGroupSuffix: ''
        updateIndices:
          enabled: true
          consumerGroupSuffix: ''
        ingestionScheduler:
          enabled: true
          consumerGroupSuffix: ''
        incidents:
          enabled: true
          consumerGroupSuffix: ''
        entityChangeEvents:
          enabled: true
          consumerGroupSuffix: ''
        forms:
          enabled: true
          consumerGroupSuffix: ''
    maxMessageBytes: "5242880"  # 5MB
    producer:
      compressionType: none
      maxRequestSize: "5242880"  # 5MB
    consumer:
      maxPartitionFetchBytes: "5242880"  # 5MB
      stopContainerOnDeserializationError: true
    ## For AWS MSK set this to a number larger than 1
    # partitions: 3
    # replicationFactor: 3
    schemaregistry:
      # GMS Implementation - `url` configured based on component context
      type: INTERNAL
      # Confluent Kafka Implementation
      # type: KAFKA
      # url: "http://datahub-bootstrap-cp-schema-registry:8081"

      # Glue Implementation - `url` not applicable
      # type: AWS_GLUE
      # glue:
      #   region: us-east-1
      #   registry: datahub

  neo4j:
    host: "datahub-bootstrap-neo4j:7474"
    uri: "bolt://datahub-bootstrap-neo4j"
    username: "neo4j"
    password:
      secretRef: neo4j-secrets
      secretKey: neo4j-password
    # --------------OR----------------
    # value: password

  sql:
    datasource:
      # host: "datahub-bootstrap-mysql:3306"
      hostForMysqlClient: "datahub-bootstrap-mysql"
      # port: "3306"
      # url: "**************************************************************************************************************************************************************"
      # driver: "com.mysql.cj.jdbc.Driver"
      # username: "root"
      # password:
      #   secretRef: mysql-secrets
      #   secretKey: mysql-root-password
      # --------------OR----------------
      # value: password

      # Use below for usage of PostgreSQL instead of MySQL
      # host: "product-df-dte-pgdb.cbcutnh1kf4m.ap-south-1.rds.amazonaws.com:7632"
      # hostForpostgresqlClient: "product-df-dte-pgdb.cbcutnh1kf4m.ap-south-1.rds.amazonaws.com"
      # port: "7632"
      # url: "********************************************************************************************"
      # driver: "org.postgresql.Driver"
      # username: "datahub"
      # password:
      #   secretRef: datahub-secrets
      #   secretKey: datahubPsqlPassword
      driver: "org.postgresql.Driver"
      jdbcClient: "postgresql"
      secretRef: "external-secret-vault-{{ .Values.global.DEPLOY_NAMESPACE }}"
      host:
        secretKey: postgresHostName
      port:
        secretKey: postgresPort
      database:
        secretKey: datahubDbName
      username:
        secretKey: datahubDbUsername
      password:
        secretKey: datahubDbpassword
      # --------------OR----------------
      #   value: password

      # If you want to use specific PostgreSQL database use extraEnvs
      # extraEnvs:
      #  - name: "DATAHUB_DB_NAME"
      #    value: "dh"

      ## Use below for usage of Google Cloud SQL MySQL instance instead of the self hosted MySQL,
      ## make sure you have deployed gcloud-sqlproxy as prerequisite
      # host: "datahub-bootstrap-gcloud-sqlproxy:3306"
      # hostForMysqlClient: "datahub-bootstrap-gcloud-sqlproxy"
      # port: "3306"
      # url: "************************************************************************************************************************************************************************"
      # driver: "com.mysql.cj.jdbc.Driver"
      # username: "root"
      # password:
      #   secretRef: mysql-secrets
      #   secretKey: mysql-root-password
      # --------------OR----------------
      #   value: password

  datahub:
    version: v1.0.0
    gms:
      protocol: "http"
      port: "8080"
      nodePort: "30001"

    # Used for scheduled tasks
    timezone: "UTC"

    frontend:
      validateSignUpEmail: true

    monitoring:
      enablePrometheus: true
      # Set a custom name for the monitoring port
      portName: jmx

    mae_consumer:
      port: "9091"
      nodePort: "30002"

    appVersion: "1.0.0"
    systemUpdate:
      ## The following options control settings for datahub-upgrade job which will
      ## managed ES indices and other update related work
      enabled: true

    encryptionKey:
      secretRef: "datahub-encryption-secrets"
      secretKey: "encryption_key_secret"
      # Set to false if you'd like to provide your own secret.
      provisionSecret:
        enabled: true
        autoGenerate: true
        annotations: {}
      # Only specify if autoGenerate set to false
      #  secretValues:
      #    encryptionKey: <encryption key value>

    managed_ingestion:
      enabled: true
      defaultCliVersion: "1.0.0"

    metadata_service_authentication:
      enabled: false
      systemClientId: "__datahub_system"
      systemClientSecret:
        secretRef: "datahub-auth-secrets"
        secretKey: "system_client_secret"
      tokenService:
        signingKey:
          secretRef: "datahub-auth-secrets"
          secretKey: "token_service_signing_key"
        salt:
          secretRef: "datahub-auth-secrets"
          secretKey: "token_service_salt"
      # Set to false if you'd like to provide your own auth secrets
      provisionSecrets:
        enabled: true
        autoGenerate: true
        annotations: {}
      # Only specify if autoGenerate set to false
      #  secretValues:
      #    secret: <secret value>
      #    signingKey: <signing key value>
      #    salt: <salt value>

    ## Enables always emitting a MCL even when no changes are detected. Used for Time Based Lineage when no changes occur.
    alwaysEmitChangeLog: false

    ## Enables diff mode for graph writes, uses a different code path that produces a diff from previous to next to write relationships instead of wholesale deleting edges and reading
    enableGraphDiffMode: true

    ## Enable stricter URN validation logic
    strictUrnValidation: false

    ## Values specific to the unified search and browse feature.
    search_and_browse:
      show_search_v2: true  # If on, show the new search filters experience as of v0.10.5
      show_browse_v2: true  # If on, show the new browse experience as of v0.10.5
      backfill_browse_v2: true  # If on, run the backfill upgrade job that generates default browse paths for relevant entities

    ## v0.15.0+
    metadataChangeProposal:
      consumer:
        batch:
          enabled: false


    ## v0.13.4+
    mcp:
      throttle:
        # updateIntervalMs: 60000
        mceConsumer:  # v0.14.2+
          enabled: false
        apiRequests:  # v0.14.2+
          enabled: false
        ## Versioned MCL topic
        versioned:
          ## Whether to throttle MCP processing based on MCL backlog
          enabled: true
        #  threshold: 4000
        #  maxAttempts: 1000
        #  initialIntervalMs: 100
        #  multiplier: 10
        #  maxIntervalMs:  30000
        # Timeseries MCL topic
        timeseries:
          ## Whether to throttle MCP processing based on MCL backlog
          enabled: false
        #  threshold: 4000
        #  maxAttempts: 1000
        #  initialIntervalMs: 100
        #  multiplier: 10
        #  maxIntervalMs: 30000

    ## v0.15.0+
    entityVersioning:
      enabled: false

    ## Enables a fast path for processing events sourced from the UI, updates indices synchronously for requests originating from GraphQL
    preProcessHooksUIEnabled: true
    ## Reprocess UI events at MAE Consumer, normally if preprocess is enabled this is not required.
    reProcessUIEventHooks: false

#  hostAliases:
#    - ip: "*************"
#      hostnames:
#        - "broker"
#        - "mysql"
#        - "postgresql"
#        - "elasticsearch"
#        - "neo4j"

## Add below to enable SSL for kafka
#  credentialsAndCertsSecrets:
#    name: datahub-certs
#    path: /mnt/datahub/certs
#    secureEnv:
#      ssl.key.password: datahub.linkedin.com.KeyPass
#      ssl.keystore.password: datahub.linkedin.com.KeyStorePass
#      ssl.truststore.password: datahub.linkedin.com.TrustStorePass
#      kafkastore.ssl.truststore.password: datahub.linkedin.com.TrustStorePass
#
#  springKafkaConfigurationOverrides:
#    ssl.keystore.location: /mnt/datahub/certs/datahub.linkedin.com.keystore.jks
#    ssl.truststore.location: /mnt/datahub/certs/datahub.linkedin.com.truststore.jks
#    kafkastore.ssl.truststore.location: /mnt/datahub/certs/datahub.linkedin.com.truststore.jks
#    security.protocol: SSL
#    kafkastore.security.protocol: SSL
#    ssl.keystore.type: JKS
#    ssl.truststore.type: JKS
#    ssl.protocol: TLS
#    ssl.endpoint.identification.algorithm:
#    basic.auth.credentials.source: USER_INFO
#    basic.auth.user.info: