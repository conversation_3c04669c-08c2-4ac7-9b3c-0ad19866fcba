# Default values for acryl-datahub-actions.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
replicaCount: 1

image:
  repository: acryldata/datahub-actions
  tag: "v0.1.1"
  pullPolicy: IfNotPresent
  # Override the image's command & args with a new one.
  # This may be necessary for custom startup or shutdown behaviors
  command:
  args:

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

terminationGracePeriodSeconds: 150

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name:

serviceMonitor:
  create: false
  extraLabels: {}

podAnnotations: {}

podSecurityContext: {}

securityContext: {}

priorityClassName:

service:
  type: ClusterIP
  port: 9093

# Extra labels for Deployment
extraLabels: {}
  # owner: myteam
  # environment: test

# Extra labels for the Pods
extraPodLabels: {}

# Extra environment variables
# This will be appended to the current 'env:' key. You can use any of the kubernetes env
# syntax here
extraEnvs: []

extraVolumes: []

extraVolumeMounts: []

extraInitContainers: []

# Add extra sidecar containers to deployment pod
extraSidecars: []
  # - name: my-image-name
  #   image: my-image
  #   imagePullPolicy: Always

resources: {}

nodeSelector: {}

tolerations: []

affinity: {}

actions:
  kafkaAutoOffsetPolicy: "latest"
  # Configure a custom executor id that will be set as the EXECUTOR_ID environment variable
  # executorId: ""

# mount the k8s secret as a volume in the container, each key name is mounted as a file on the mount path /etc/datahub/ingestion-secret-files
# ingestionSecretFiles:
#   name: ${K8S_SECRET_NAME}
#   defaultMode: "0444"

debug:
  # Set enabled to true will set the DATAHUB_DEBUG env var to true
  enabled: false

global:
  imageRegistry: "docker.io"

  kafka:
    bootstrap:
      server: "broker:9092"
    schemaregistry:
      url: "https://prerequisites-cp-schema-registry:8081"

  datahub:
    gms:
      port: "8080"
    monitoring:
      enablePrometheus: true
      portName: "jmx"
    metadata_service_authentication:
      enabled: true
      systemClientId: "__datahub_system"
      # systemClientSecret:
      #  secretRef: <secret-ref>
      #  secretKey: <secret-key>
