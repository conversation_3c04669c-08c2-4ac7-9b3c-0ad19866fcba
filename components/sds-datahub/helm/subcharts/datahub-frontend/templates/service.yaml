apiVersion: v1
kind: Service
metadata:
  name: {{ include "datahub-frontend.fullname" . }}
  labels:
    {{- include "datahub-frontend.labels" . | nindent 4 }}
    {{- range $key, $val := .Values.service.extraLabels }}
    {{ $key }}: {{ $val | quote }}
    {{- end }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: {{ .Values.service.protocol }}
      name: {{ .Values.service.name }}
      {{- if eq .Values.service.type "NodePort" }}
      {{- with .Values.service.nodePort }}
      nodePort: {{ . }}
      {{- end }}
      {{- end }}
    {{- if .Values.global.datahub.monitoring.enablePrometheus }}
    - name: {{ .Values.global.datahub.monitoring.portName }}
      port: 4318
      targetPort: {{ .Values.global.datahub.monitoring.portName }}
      protocol: TCP
    {{- end }}
  selector:
    {{- include "datahub-frontend.selectorLabels" . | nindent 4 }}
