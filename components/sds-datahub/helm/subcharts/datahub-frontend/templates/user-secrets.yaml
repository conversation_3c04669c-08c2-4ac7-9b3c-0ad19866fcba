{{- if .Values.defaultUserCredentials }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-user-secret" .Release.Name }}
  labels:
    {{- include "datahub-frontend.labels" . | nindent 4 }}
type: Opaque
data:
  user.props:
    {{- if .Values.defaultUserCredentials.randomAdminPassword }}
      {{ printf "datahub:%s" (randAlphaNum 32) | b64enc | quote }}
    {{- else if .Values.defaultUserCredentials.manualValues }}
      {{ .Values.defaultUserCredentials.manualValues | b64enc | quote }}
    {{- end }}
{{- end -}}