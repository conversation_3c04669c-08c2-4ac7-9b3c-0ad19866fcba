# Default values for datahub-ingestion-cron.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

image:
  repository: acryldata/datahub-ingestion
  tag:
  pullPolicy: IfNotPresent

imagePullSecrets: []

podSecurityContext: {}
# fsGroup: 2000

crons: {}
  #### Example data with externally managed config map
  #hive:
    ## Daily at midnight (we may want to offset this to not conflict with other processes)
    #schedule: "0 0 * * *"

    #recipe:
    #  configmapName:
    #  fileName:

    ## Command to be executed
    #command: "datahub ingest -c <recipe.fileName>"

  # Example data with helm managed config map
  # mysql:
  #   schedule: "0 0 0 0 0"
  #   recipe:
  #     configmapName: datahub-mysql-ingestion
  #     fileName: mysql.yaml
  #     # Example mysql -> datahub source recipe
  #     fileContent:
  #       source:
  #         type: mysql
  #         config:
  #           # Coordinates
  #           host_port: localhost:3306
  #           database: dbname
  #           # Credentials
  #           username: root
  #           password: example
  #       sink:
  #         type: datahub-rest
  #         config:
  #           server: http://localhost:8080

    # Command to be executed
#    command: "datahub ingest -c <recipe.fileName>"

    ## Deployment pod host aliases
    ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
    ##
    #hostAliases: []

    ## Environment variables.
    #env: {}

    ## Environment variables from Secret resources.
    #envFromSecrets: {}

    ## Additional primary volume mounts
    ##
    #extraVolumeMounts:
    #- name: configmap-volume
    #  mountPath: config.yml
    #  subPath: config.yml

    ## Additional primary volumes
    ##
    #extraVolumes:
    #- name: configmap-volume
    #  configMap:
    #    name: crawler-config

    ## Add your own init container or uncomment and modify the given example.
    ##
    #extraInitContainers: {}

    ## Add extra sidecar containers to job pod
    ##
    # extraSidecars: []
    # - name: my-image-name
    #  image: my-image
    #  imagePullPolicy: Always

    ## If you want to specify your own service account, set its name like so.
    ##
    #serviceAccountName: "my-cron-service"

    ## If you want to specify container level security
    #securityContext:
    #  capabilities:
    #    drop:
    #      - ALL
    #  readOnlyRootFilesystem: true
    #  runAsNonRoot: true
    #  runAsUser: 1000

    ## Add your own pod annotations.
    ##
    #podAnnotations: {}

    ## Add your own restartPolicy
    ##
    #restartPolicy:

    ## Add your own concurrencyPolicy
    ##
    #concurrencyPolicy:

    ## Add your own failedJobsHistoryLimit
    ##
    #failedJobsHistoryLimit:

    ## Add your own successfulJobsHistoryLimit
    ##
    #successfulJobsHistoryLimit:

    ## Add your own backoffLimit
    ##
    #backoffLimit:

    ## Node Selector parameters for job pod
    ##
    #nodeSelector: {}
    #  region: us-west-1
    #  disk: ssd

    ## Affinity settings for job pod
    ##
    #affinity : {}

    ## Toleration settings  for job pod
    ##
    #tolerations: []

    # Set the cron job to suspended state
    ##
    #suspend: true

global:
  imageRegistry: "docker.io"

  datahub:
    version: head
