{{- $baseName := include "datahub-ingestion-cron.fullname" .}}
{{- $labels := include "datahub-ingestion-cron.labels" .}}
{{- range $jobName, $val := .Values.crons }}
{{- $defaultCommand := printf "datahub ingest -c /etc/recipe/%s" $val.recipe.fileName }}
apiVersion: {{ include "datahub-ingestion-cron.cronjob.apiVersion" $}}
kind: CronJob
metadata:
  name: "{{ $baseName }}-{{ $jobName }}"
  labels: {{- $labels | nindent 4 }}
spec:
  schedule: {{ default "0 0 * * *" .schedule | quote }}
  concurrencyPolicy: {{ default "Allow" .concurrencyPolicy }}
  successfulJobsHistoryLimit: {{ default 3 .successfulJobsHistoryLimit }}
  failedJobsHistoryLimit: {{ default 1 .failedJobsHistoryLimit }}
  suspend: {{ default false .suspend }}
  jobTemplate:
    spec:
      backoffLimit: {{ default 6 $val.backoffLimit }}
      template:
        {{- with $val.podAnnotations }}
        metadata:
          annotations:
            {{- toYaml . | nindent 12 }}
        {{- end }}
        spec:
        {{- with $.Values.imagePullSecrets }}
          imagePullSecrets:
            {{- toYaml . | nindent 12 }}
        {{- end }}
        {{- if .extraInitContainers }}
          initContainers:
          {{- toYaml .extraInitContainers | nindent 12 }}
          {{- end }}
        {{- if .hostAliases }}
          hostAliases:
          {{- toYaml .hostAliases | nindent 12 }}
        {{- end }}
        {{- if .serviceAccountName }}
          serviceAccountName: {{ .serviceAccountName }}
        {{- end }}
          securityContext:
            {{- toYaml $.Values.podSecurityContext | nindent 12 }}
          containers:
          - name: {{ $jobName }}-crawler
            image: {{ include "datahub.image" (dict "imageRegistry" $.Values.global.imageRegistry "version" $.Values.global.datahub.version "image" $.Values.image) }}
            imagePullPolicy: {{ $.Values.image.pullPolicy }}
            volumeMounts:
              - name: recipe
                mountPath: /etc/recipe
            {{- if .extraVolumeMounts }}
              {{- toYaml .extraVolumeMounts | nindent 14 }}
            {{- end }}
            command: ["/bin/sh", "-c", {{ default $defaultCommand .command }}]
            {{- if .securityContext }}
            securityContext:
              {{- toYaml .securityContext | nindent 14 }}
            {{- end }}
            env:
              {{- if .env }}
              {{- range $key,$value := .env }}
              - name: {{ $key | quote }}
                value: {{ $value | quote }}
              {{- end }}
              {{- end }}
              {{- if .envFromSecrets }}
              {{- range $key,$value := .envFromSecrets }}
              - name: {{ $key | quote }}
                valueFrom:
                  secretKeyRef:
                    name: {{ $value.secret | quote }}
                    key: {{ $value.key | quote }}
              {{- end }}
              {{- end }}
            {{- if .extraSidecars }}
              {{- toYaml .extraSidecars | nindent 10 }}
            {{- end }}
          restartPolicy: {{ default "OnFailure" .restartPolicy }}
          {{- with default $.Values.global.nodeSelector .nodeSelector }}
          nodeSelector:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- if .affinity }}
          affinity:
            {{- toYaml .affinity | nindent 12 }}
          {{- end }}
          {{- with default $.Values.global.tolerations .tolerations }}
          tolerations:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          volumes:
            - name: recipe
              configMap:
                name: {{ required "A valid .recipe.configmapName entry is required!" $val.recipe.configmapName }}
          {{- if .extraVolumes }}
            {{- toYaml .extraVolumes | nindent 12 }}
          {{- end }}
---
{{- end }}
