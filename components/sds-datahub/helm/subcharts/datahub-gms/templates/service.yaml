apiVersion: v1
kind: Service
metadata:
  name: {{ include "datahub-gms.fullname" . }}
  labels:
    {{- include "datahub-gms.labels" . | nindent 4 }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: {{ .Values.service.protocol }}
      name: {{ .Values.service.name }}
      {{- if eq .Values.service.type "NodePort" }}
      {{- with .Values.service.nodePort }}
      nodePort: {{ . }}
      {{- end }}
      {{- end }}
    {{- if .Values.global.datahub.monitoring.enablePrometheus }}
    - name: {{ .Values.global.datahub.monitoring.portName }}
      port: 4318
      targetPort: {{ .Values.global.datahub.monitoring.portName }}
      protocol: TCP
    {{- end }}
  selector:
    {{- include "datahub-gms.selectorLabels" . | nindent 4 }}
