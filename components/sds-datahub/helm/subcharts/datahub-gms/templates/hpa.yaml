{{- if .Values.hpa.enabled }}
{{- $apiVersion := include "datahub-gms.hpa.apiVersion" . -}}
apiVersion: {{ $apiVersion }}
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "datahub-gms.fullname" . }}
  labels:
    {{- include "datahub-gms.labels" . | nindent 4 }}
    {{- range $key, $val := .Values.extraLabels }}
    {{ $key }}: {{ $val | quote }}
    {{- end }}
spec:
  {{- if and (.Values.global.strict_mode) (.Values.hpa.enabled) (.Values.global.datahub.managed_ingestion.enabled) (not .Values.global.datahub_standalone_consumers_enabled) }}
  {{- fail "\nHPA cannot be used in non standalone mode. Please enable standalone consumers if you wish to run multiple GMS instances and managed ingestion." }}
  {{- else }}
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "datahub-gms.fullname" . }}
  minReplicas: {{ .Values.hpa.minReplicas }}
  maxReplicas: {{ .Values.hpa.maxReplicas }}
  {{- with .Values.hpa.behavior }}
  behavior:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  metrics:
  {{- with .Values.hpa.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        {{- if (eq $apiVersion "autoscaling/v2") }}
        target:
          type: Utilization
          averageUtilization: {{ . }}
        {{- else }}
        targetAverageUtilization: {{ . }}
        {{- end }}
  {{- end }}
  {{- with .Values.hpa.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        {{- if (eq $apiVersion "autoscaling/v2") }}
        target:
          type: Utilization
          averageUtilization: {{ . }}
        {{- else }}
        targetAverageUtilization: {{ . }}
        {{- end }}
  {{- end }}
{{- end }}
{{- end }}
