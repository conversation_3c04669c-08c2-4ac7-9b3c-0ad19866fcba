# Default values for datahub-mae-consumer.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

revisionHistoryLimit: 10

image:
  repository: acryldata/datahub-mae-consumer
  pullPolicy: IfNotPresent
  tag:
  # Override the image's command & args with a new one.
  # This may be necessary for custom startup or shutdown behaviors
  command:
  args:

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

terminationGracePeriodSeconds: 150

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name:

serviceMonitor:
  create: false
  extraLabels: {}

podAnnotations: {}
  # co.elastic.logs/enabled: "true"

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

priorityClassName:

service:
  type: ClusterIP # NodePort
  port: "9091"
  targetPort: http
  protocol: TCP
  name: http

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

# Extra labels for Deployment
extraLabels: {}
  # owner: myteam
  # environment: test

# Extra labels for the Pods
extraPodLabels: {}

# Extra environment variables
# This will be appended to the current 'env:' key. You can use any of the kubernetes env
# syntax here
extraEnvs: []
  # - name: MY_ENVIRONMENT_VAR
  #   value: the_value_goes_here

extraVolumes: []
  # - name: extras
  #   emptyDir: {}

extraVolumeMounts: []
  # - name: extras
  #   mountPath: /usr/share/extras
  #   readOnly: true

extraInitContainers: []

# Add extra sidecar containers to deployment pod
extraSidecars: []
  # - name: my-image-name
  #   image: my-image
  #   imagePullPolicy: Always

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

livenessProbe:
  initialDelaySeconds: 60
  periodSeconds: 30
  failureThreshold: 8

readinessProbe:
  initialDelaySeconds: 60
  periodSeconds: 30
  failureThreshold: 8

global:
  imageRegistry: "docker.io"

  graph_service_impl: elasticsearch
  datahub_analytics_enabled: true

  elasticsearch:
    host: "elasticsearch"
    port: "9200"
    skipcheck: "false"

  kafka:
    bootstrap:
      server: "broker:9092"
    schemaregistry:
      url: "https://schema-registry:8081"
    ## Kafka producer and consumer settings
    #producer:
    #  compressionType: snappy
    #  maxRequestSize: 5242880
    #consumer:
    #  maxPartitionFetchBytes: 5242880

  neo4j:
    host: "neo4j:7474"
    uri: "bolt://neo4j"
    username: "neo4j"
    password:
      secretRef: "neo4j-secrets"
      secretKey: "neo4j-password"

  datahub:
    version: head
    gms:
      port: "8080"
    monitoring:
      enablePrometheus: false
      portName: jmx

    systemUpdate:
      ## The following options control settings for datahub-upgrade job which will
      ## managed ES indices and other update related work
      enabled: true

    mae_consumer:
      port: "9091"

    managed_ingestion:
      enabled: true

    metadata_service_authentication:
      enabled: true
      systemClientId: "__datahub_system"
      # systemClientSecret:
      #  secretRef: <secret-ref>
      #  secretKey: <secret-key>

    ## Enables always emitting a MCL even when no changes are detected. Used for Time Based Lineage when no changes occur.
    alwaysEmitChangeLog: false

    ## Enables diff mode for graph writes, uses a different code path that produces a diff from previous to next to write relationships instead of wholesale deleting edges and reading
    enableGraphDiffMode: true

  hostAliases:
    - ip: "*************"
      hostnames:
        - "broker"
        - "mysql"
        - "elasticsearch"
        - "neo4j"
