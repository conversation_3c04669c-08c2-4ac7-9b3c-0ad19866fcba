# Default values for datahub-mce-consumer.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

revisionHistoryLimit: 10

image:
  repository: acryldata/datahub-mce-consumer
  pullPolicy: IfNotPresent
  tag:
  # Override the image's command & args with a new one.
  # This may be necessary for custom startup or shutdown behaviors
  command:
  args:

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

terminationGracePeriodSeconds: 150

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name:


podAnnotations: {}
  # co.elastic.logs/enabled: "true"

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

priorityClassName:

service:
  type: ClusterIP # NodePort
  port: "9090"
  targetPort: http
  protocol: TCP
  name: http

serviceMonitor:
  create: false
  extraLabels: {}

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

# Extra labels for Deployment
extraLabels: {}
  # owner: myteam
  # environment: test

# Extra labels for the Pods
extraPodLabels: {}

# Extra environment variables
# This will be appended to the current 'env:' key. You can use any of the kubernetes env
# syntax here
extraEnvs: []
  # - name: MY_ENVIRONMENT_VAR
  #   value: the_value_goes_here

extraVolumes: []
  # - name: extras
  #   emptyDir: {}

extraVolumeMounts: []
  # - name: extras
  #   mountPath: /usr/share/extras
  #   readOnly: true

extraInitContainers: []

# Add extra sidecar containers to deployment pod
extraSidecars: []
  # - name: my-image-name
  #   image: my-image
  #   imagePullPolicy: Always

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

livenessProbe:
  initialDelaySeconds: 60
  periodSeconds: 30
  failureThreshold: 4

readinessProbe:
  initialDelaySeconds: 60
  periodSeconds: 30
  failureThreshold: 4

global:
  imageRegistry: "docker.io"

  elasticsearch:
    host: "elasticsearch-master"

  kafka:
    bootstrap:
      server: "broker:9092"
    schemaregistry:
      url: "http://schema-registry:8081"
    # Kafka producer and consumer settings
    #producer:
    #  compressionType: snappy
    #  maxRequestSize: "5242880"
    #consumer:
    #  maxPartitionFetchBytes: "5242880"

  datahub:
    version: head
    monitoring:
      enablePrometheus: false
      portName: jmx
    gms:
      port: "8080"
    metadata_service_authentication:
      enabled: true
      systemClientId: "__datahub_system"
      # systemClientSecret:
      #  secretRef: <secret-ref>
      #  secretKey: <secret-key>

    systemUpdate:
      ## The following options control settings for datahub-upgrade job which will
      ## managed ES indices and other update related work
      enabled: true

  sql:
    datasource:
      host: "mysql:3306"
      url: "*************************************************************************"
      driver: "com.mysql.cj.jdbc.Driver"
      username: "datahub"
      password:
        secretRef: "mysql-secrets"
        secretKey: "mysql-password"

    ## Enables always emitting a MCL even when no changes are detected. Used for Time Based Lineage when no changes occur.
    alwaysEmitChangeLog: false

    ## Enables diff mode for graph writes, uses a different code path that produces a diff from previous to next to write relationships instead of wholesale deleting edges and reading
    enableGraphDiffMode: true

    ## Enable stricter URN validation logic
    strictUrnValidation: false

  hostAliases:
    - ip: "*************"
      hostnames:
        - "broker"
        - "mysql"
        - "elasticsearch"
        - "neo4j"
