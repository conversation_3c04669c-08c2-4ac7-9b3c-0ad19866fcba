# Values to start up datahub after starting up the datahub-prerequisites chart with "prerequisites" release name
# Copy this chart and change configuration as needed.
datahub-gms:
  enabled: true
  image:
    repository: acryldata/datahub-gms
    # tag: "v0.11.0 # defaults to .global.datahub.version


datahub-frontend:
  enabled: true
  image:
    repository: acryldata/datahub-frontend-react
    # tag: "v0.11.0 # defaults to .global.datahub.version

  # Set up ingress to expose react front-end
  ingress:
    enabled: false
  defaultUserCredentials: {}
  #  randomAdminPassword: true
  #  # You can also set specific passwords for default users
  #  # manualValues: |
  #  #   datahub:manualPassword
  #  #   initialViewer:manualPassword

acryl-datahub-actions:
  enabled: true
  image:
    repository: acryldata/datahub-actions
    tag: "v0.0.7"
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 300m
      memory: 256Mi

elasticsearchSetupJob:
  enabled: true
  image:
    repository: acryldata/datahub-elasticsearch-setup
    # tag: "v0.11.0 # defaults to .global.datahub.version


kafkaSetupJob:
  enabled: true
  image:
    repository: acryldata/datahub-kafka-setup
    # tag: "v0.11.0 # defaults to .global.datahub.version


mysqlSetupJob:
  enabled: true
  image:
    repository: acryldata/datahub-mysql-setup
    # tag: "v0.11.0 # defaults to .global.datahub.version


datahubUpgrade:
  enabled: true
  image:
    repository: acryldata/datahub-upgrade
    # tag: "v0.11.0 # defaults to .global.datahub.version


datahub-ingestion-cron:
  enabled: false
  image:
    repository: acryldata/datahub-ingestion
    # tag: "v0.11.0 # defaults to .global.datahub.version


global:
  graph_service_impl: neo4j

  elasticsearch:
    host: "elasticsearch-master"
    port: "9200"

  kafka:
    bootstrap:
      server: "prerequisites-kafka:9092"
    zookeeper:
      server: "prerequisites-zookeeper:2181"
    schemaregistry:
      url: "http://prerequisites-cp-schema-registry:8081"



  datahub:
    version: v0.11.0
    gms:
      port: "8080"
    mae_consumer:
      port: "9091"
    appVersion: "1.0"

    managed_ingestion:
      enabled: true
      defaultCliVersion: "0.11.0"
