# Defining environment
ARG BASE_IMAGE

# Defining custom repo urls for use in enterprise environments. Re-used between stages below.
ARG ALPINE_REPO_URL=http://dl-cdn.alpinelinux.org/alpine
ARG GITHUB_REPO_URL=https://github.com
ARG MAVEN_CENTRAL_REPO_URL=https://repo1.maven.org/maven2

FROM golang:1-alpine3.21 AS binary

# Re-declaring arg from above to make it available in this stage (will inherit default value)
ARG ALPINE_REPO_URL

ENV DOCKERIZE_VERSION=v0.9.3
WORKDIR /go/src/github.com/jwilder

# Optionally set corporate mirror for apk
RUN if [ "${ALPINE_REPO_URL}" != "https://dl-cdn.alpinelinux.org/alpine" ] ; then sed -i "s#http.*://dl-cdn.alpinelinux.org/alpine#${ALPINE_REPO_URL}#g" /etc/apk/repositories ; fi

RUN apk --no-cache --update add openssl git tar curl

WORKDIR /go/src/github.com/jwilder/dockerize

RUN go install github.com/jwilder/dockerize@$DOCKERIZE_VERSION

FROM ${BASE_IMAGE} AS base

# Re-declaring args from above to make them available in this stage (will inherit default values)
ARG GITHUB_REPO_URL
ARG MAVEN_CENTRAL_REPO_URL

# Install required dependencies
ENV JMX_VERSION=0.20.0
RUN apt-get update && apt-get install -y --no-install-recommends \
    wget \
    libsnappy1v5 \
    ca-certificates && \
    mkdir -p /tmp && \
    wget --no-verbose ${GITHUB_REPO_URL}/open-telemetry/opentelemetry-java-instrumentation/releases/download/v2.15.0/opentelemetry-javaagent.jar && \
    wget --no-verbose ${MAVEN_CENTRAL_REPO_URL}/io/prometheus/jmx/jmx_prometheus_javaagent/${JMX_VERSION}/jmx_prometheus_javaagent-${JMX_VERSION}.jar -O jmx_prometheus_javaagent.jar \
    && cp /usr/lib/jvm/java-17-amazon-corretto/lib/security/cacerts /tmp/kafka.client.truststore.jks \
    && apt-get purge -y --auto-remove wget \
    && apt-get clean \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /var/cache/apt/*

COPY --from=binary /go/bin/dockerize /usr/local/bin

ENV LD_LIBRARY_PATH="/lib:/lib64"


FROM base AS prod-install

COPY --from=acryldata/datahub-mae-consumer:v1.0.0 /datahub /datahub
RUN chmod +x /datahub/datahub-mae-consumer/scripts/start.sh

FROM prod-install AS final

RUN groupadd --system datahub && useradd --system --gid datahub --home-dir /home/<USER>/home/<USER>
USER datahub

ENV JMX_OPTS=""
ENV JAVA_OPTS=""
ENV OTEL_EXPORTER_OTLP_MAX_PAYLOAD_SIZE=4194304 \
    OTEL_EXPORTER_OTLP_HTTP_HTTP2_MAX_FRAME_SIZE=16777215 \
    OTEL_EXPORTER_OTLP_HTTP_COMPRESSION=gzip \
    OTEL_EXPORTER_OTLP_TRACES_COMPRESSION=gzip

EXPOSE 9090

HEALTHCHECK --start-period=2m --retries=4 CMD curl --fail http://localhost:9091/actuator/health || exit 1

CMD /datahub/datahub-mae-consumer/scripts/start.sh
