ARG BASE_IMAGE

# Defining custom repo urls for use in enterprise environments. Re-used between stages below.
ARG GITHUB_REPO_URL=https://github.com
ARG MAVEN_CENTRAL_REPO_URL=https://repo1.maven.org/maven2

# Build dockerize binary in a Debian-based builder stage
FROM golang:1-bookworm AS binary

ENV DOCKERIZE_VERSION=v0.9.3
WORKDIR /go/src/github.com/jwilder

RUN apt-get update && apt-get install -y --no-install-recommends \
    git ca-certificates && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /go/src/github.com/jwilder/dockerize

RUN go install github.com/jwilder/dockerize@$DOCKERIZE_VERSION

# Base image using specified Debian image
FROM $BASE_IMAGE AS base

ENV JMX_VERSION=0.20.0

# Re-declaring args from above to make them available in this stage (will inherit default values)
ARG GITHUB_REPO_URL
ARG MAVEN_CENTRAL_REPO_URL

# Install required dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    wget \
    ca-certificates \
    libsnappy1v5 \
    && wget --no-verbose ${GITHUB_REPO_URL}/open-telemetry/opentelemetry-java-instrumentation/releases/download/v2.15.0/opentelemetry-javaagent.jar \
    && wget --no-verbose ${MAVEN_CENTRAL_REPO_URL}/io/prometheus/jmx/jmx_prometheus_javaagent/${JMX_VERSION}/jmx_prometheus_javaagent-${JMX_VERSION}.jar -O jmx_prometheus_javaagent.jar \
    && cp /usr/lib/jvm/java-17-amazon-corretto/lib/security/cacerts /tmp/kafka.client.truststore.jks \
    && apt-get purge -y --auto-remove wget \
    && apt-get clean \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /var/cache/apt/*


COPY --from=binary /go/bin/dockerize /usr/local/bin

ENV LD_LIBRARY_PATH="/lib:/lib64"

FROM acryldata/datahub-gms:v1.0.0 as datahub-copy

FROM base AS prod-install
COPY --from=datahub-copy /datahub /datahub
RUN chmod +x /datahub/datahub-gms/scripts/start.sh

FROM prod-install AS final

RUN mkdir -p /etc/datahub/plugins/auth/resources /datahub

RUN groupadd --system datahub && useradd --system --gid datahub --home-dir /home/<USER>/home/<USER>
RUN chown -R datahub:datahub /etc/datahub /datahub
USER datahub

ENV JMX_OPTS=""
ENV JAVA_OPTS=""
ENV OTEL_EXPORTER_OTLP_MAX_PAYLOAD_SIZE=4194304 \
    OTEL_EXPORTER_OTLP_HTTP_HTTP2_MAX_FRAME_SIZE=8388608 \
    OTEL_EXPORTER_OTLP_HTTP_COMPRESSION=gzip \
    OTEL_EXPORTER_OTLP_TRACES_COMPRESSION=gzip

EXPOSE 8080

HEALTHCHECK --start-period=2m --retries=4 CMD curl --fail http://localhost:8080/health || exit 1

CMD /datahub/datahub-gms/scripts/start.sh
