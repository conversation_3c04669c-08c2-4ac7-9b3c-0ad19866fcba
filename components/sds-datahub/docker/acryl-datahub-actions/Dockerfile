#Custom created one by <PERSON><PERSON> due to unavailability of dockerfile in the repository for version 0.2.0
ARG BASE_IMAGE

FROM $BASE_IMAGE AS python-base

ARG TARGETARCH

ARG GITHUB_REPO_URL=https://github.com
ARG DEBIAN_REPO_URL=https://deb.debian.org/debian

ARG PIP_MIRROR_URL=https://pypi.python.org/simple
RUN if [ "${PIP_MIRROR_URL}" != "https://pypi.python.org/simple" ] ; then pip config set global.index-url ${PIP_MIRROR_URL} ; fi
ENV UV_INDEX_URL=${PIP_MIRROR_URL}

RUN apt-get update && apt-get upgrade -y \
    && apt-get --no-install-recommends install -y -qq \
    lsb-release \
    libldap2-dev \
    libsasl2-dev \
    libsasl2-modules \
    libaio1 \
    libsasl2-modules-gssapi-mit \
    krb5-user \
    krb5-config \
    libkrb5-dev \
    librdkafka-dev \
    git \
    wget \
    curl \
    zip \
    unzip \
    ldap-utils \
    unixodbc \
    libodbc2 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && python -m pip install --no-cache --upgrade pip 'uv>=0.1.10' wheel setuptools

COPY --from=powerman/dockerize:0.19 /usr/local/bin/dockerize /usr/local/bin
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

RUN addgroup --gid 1000 datahub && \
    adduser --disabled-password --uid 1000 --gid 1000 --home /datahub-ingestion datahub 

USER datahub

ENV REQUESTS_CA_BUNDLE=/etc/ssl/certs/ca-certificates.crt

ENV VIRTUAL_ENV=/datahub-ingestion/.venv

ENV PATH=/datahub-ingestion/.venv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

RUN python3 -m venv $VIRTUAL_ENV && \
    uv pip install --no-cache --upgrade pip setuptools wheel

FROM python-base AS ingestion-base-full

USER 0

RUN export TARGETARCH=${TARGETARCH:-amd64} && \
    apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get --no-install-recommends install -y -qq default-jre-headless wget unzip && \
    rm -rf /var/lib/apt/lists/* && \
    mkdir -p /opt/oracle && \
    cd /opt/oracle && \
    if [ "$TARGETARCH" = "amd64" ]; then \
        wget --secure-protocol=TLSv1_2 --no-verbose -c https://download.oracle.com/otn_software/linux/instantclient/2115000/instantclient-basic-linux.x64-*********.0dbru.zip && \
        unzip instantclient-basic-linux.x64-*********.0dbru.zip && \
        rm instantclient-basic-linux.x64-*********.0dbru.zip && \
        echo "/opt/oracle/instantclient_21_15" > /etc/ld.so.conf.d/oracle-instantclient.conf; \
    elif [ "$TARGETARCH" = "arm64" ]; then \
        wget --secure-protocol=TLSv1_2 --no-verbose -c https://download.oracle.com/otn_software/linux/instantclient/1923000/instantclient-basic-linux.arm64-*********.0dbru.zip && \
        unzip instantclient-basic-linux.arm64-*********.0dbru.zip && \
        rm instantclient-basic-linux.arm64-*********.0dbru.zip && \
        echo "/opt/oracle/instantclient_19_23" > /etc/ld.so.conf.d/oracle-instantclient.conf; \
    else \
        echo "Unsupported architecture: $TARGETARCH" && exit 1; \
    fi && \
    ldconfig

USER datahub

FROM ingestion-base-full AS final

USER root

COPY --chown=root:root --chmod=755 ./start.sh /start_datahub_actions.sh

COPY --chown=root:root --chmod=755 ./readiness-check.sh /readiness-check.sh

RUN chmod a+x /start_datahub_actions.sh && \
    mkdir -p /etc/datahub/actions && \
    mkdir -p /tmp/datahub/logs/actions/system && \
    chown -R datahub:datahub /etc/datahub /tmp/datahub && \
    apt-get update && \
    apt-get --no-install-recommends install -y -qq default-jre && \
    apt-get clean && \
    rm -rf /var/lib/{apt,dpkg,cache,log}/


COPY --from=acryldata/datahub-actions:v0.2.0 /actions-src /actions-src
COPY --from=acryldata/datahub-actions:v0.2.0 /etc/datahub/actions /etc/datahub/actions

# COPY --chown=root:root --chmod=755 ./datahub-actions /actions-src
# COPY --chown=root:root --chmod=755 ./config /etc/datahub/actions/system/conf

USER datahub

WORKDIR /actions-src

RUN UV_LINK_MODE=copy uv pip install -e ".[all]"

WORKDIR /datahub-ingestion

RUN find . -name "htrace-core4-4.1.0-incubating.jar" -exec rm "{}" \;

ENTRYPOINT []

CMD dockerize -wait ${DATAHUB_GMS_PROTOCOL:-http}://$DATAHUB_GMS_HOST:$DATAHUB_GMS_PORT/health -timeout 240s /start_datahub_actions.sh