# Defining environment
ARG APP_ENV=prod
ARG BASE_IMAGE

FROM ${BASE_IMAGE} AS base

# Configurable repositories
ARG GITHUB_REPO_URL=https://github.com
ARG MAVEN_CENTRAL_REPO_URL=https://repo1.maven.org/maven2
ARG TARGETARCH=amd64

# Create datahub user and group
RUN groupadd --system datahub && \
    useradd --system --gid datahub --home-dir /home/<USER>

# Install required dependencies
ENV JMX_VERSION=0.20.0
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    curl \
    wget \
    ca-certificates \
    libsnappy1v5 && \
    # Download OpenTelemetry and JMX agents
    wget --secure-protocol=TLSv1_2 ${GITHUB_REPO_URL}/open-telemetry/opentelemetry-java-instrumentation/releases/download/v2.15.0/opentelemetry-javaagent.jar -O opentelemetry-javaagent.jar && \
    wget --secure-protocol=TLSv1_2 ${MAVEN_CENTRAL_REPO_URL}/io/prometheus/jmx/jmx_prometheus_javaagent/${JMX_VERSION}/jmx_prometheus_javaagent-${JMX_VERSION}.jar -O jmx_prometheus_javaagent.jar \
    # Clean up
    && apt-get purge -y --auto-remove wget \
    && apt-get clean \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /var/cache/apt/*

# Set library path compatible with Debian
ENV LD_LIBRARY_PATH="/lib:/usr/lib:/lib64:/usr/lib64"

FROM acryldata/datahub-frontend-react:v1.0.0 AS datahub-copy

FROM base AS prod-install

COPY --from=datahub-copy --chown=root:root --chmod=755 /datahub-frontend /datahub-frontend
COPY --from=datahub-copy --chown=root:root --chmod=755 /start.sh /start.sh
RUN sed -i '1s|^#!/bin/sh|#!/bin/bash|' /start.sh

FROM base AS dev-install
# Dummy stage for development. Assumes code is built on your machine and mounted to this image.
# See this excellent thread https://github.com/docker/cli/issues/1134
VOLUME [ "/datahub-frontend" ]

FROM ${APP_ENV}-install AS final
USER datahub

ENV OTEL_EXPORTER_OTLP_MAX_PAYLOAD_SIZE=4194304 \
    OTEL_EXPORTER_OTLP_HTTP_HTTP2_MAX_FRAME_SIZE=16777215 \
    OTEL_EXPORTER_OTLP_HTTP_COMPRESSION=gzip \
    OTEL_EXPORTER_OTLP_TRACES_COMPRESSION=gzip

ARG SERVER_PORT=9002
ENV SERVER_PORT=$SERVER_PORT
RUN echo $SERVER_PORT
EXPOSE $SERVER_PORT

HEALTHCHECK --start-period=2m --retries=4 CMD curl --fail http://localhost:$SERVER_PORT/admin || exit 1
CMD ./start.sh
