ARG KAFKA_DOCKER_VERSION=7.9.1
ARG BASE_IMAGE
# Defining custom repo urls for use in enterprise environments. Re-used between stages below.
ARG GITHUB_REPO_URL=https://github.com
ARG MAVEN_CENTRAL_REPO_URL=https://repo1.maven.org/maven2
ARG APACHE_DOWNLOAD_URL=null

# Using AS a base image because to get the needed jars for confluent utils
FROM confluentinc/cp-base-new:$KAFKA_DOCKER_VERSION AS confluent_base

ARG MAVEN_CENTRAL_REPO_URL
ARG SNAKEYAML_VERSION="2.0"

RUN rm /usr/share/java/cp-base-new/snakeyaml-*.jar \
  && wget -P /usr/share/java/cp-base-new $MAVEN_CENTRAL_REPO_URL/org/yaml/snakeyaml/$SNAKEYAML_VERSION/snakeyaml-$SNAKEYAML_VERSION.jar

# Based on https://github.com/blacktop's kafka build
FROM $BASE_IMAGE

ARG APACHE_DOWNLOAD_URL
ARG GITHUB_REPO_URL

ENV KAFKA_VERSION=3.9.0
ENV SCALA_VERSION=2.13

LABEL name="kafka" version=${KAFKA_VERSION}

# Install required packages
# Split into runtime dependencies and build-time dependencies
RUN apt-get update && \
    # Install runtime dependencies
    apt-get install -y --no-install-recommends \
    python3 \
    curl \
    ca-certificates \
    jq \
    # Install build-time dependencies
    git \
    gcc \
    libc6-dev \
    libffi-dev \
    zip \
    wget

# Create directory and download Kafka
RUN mkdir -p /opt

# Determine mirror and download Kafka
RUN if [ "${APACHE_DOWNLOAD_URL}" != "null" ] ; then \
        mirror="${APACHE_DOWNLOAD_URL}/" ; \
    else \
        mirror=$(curl --stderr /dev/null https://www.apache.org/dyn/closer.cgi\?as_json\=1 | jq -r '.preferred') ; \
    fi && \
    echo "Downloading AMD64 version of Kafka" && \
    curl -sSL "${mirror}kafka/${KAFKA_VERSION}/kafka_${SCALA_VERSION}-${KAFKA_VERSION}.tgz" | tar -xzf - -C /opt

# Move and setup Kafka
RUN mv /opt/kafka_${SCALA_VERSION}-${KAFKA_VERSION} /opt/kafka && \
    useradd --no-create-home --shell /usr/sbin/nologin kafka && \
    chown -R kafka: /opt/kafka

# Cleanup build dependencies and cache
RUN apt-get purge -y --auto-remove git gcc libc6-dev libffi-dev zip wget && \
    apt-get clean && \
    apt-get autoremove -y && \
    rm -rf /var/lib/apt/lists/* && \
    rm -rf /var/cache/apt/* && \
    rm -rf /tmp/* /var/tmp/*

ENV PATH=/sbin:/opt/kafka/bin/:$PATH

WORKDIR /opt/kafka

# Check directory contents for debugging purposes
RUN ls -la
COPY --from=confluent_base /usr/share/java/cp-base-new/ /usr/share/java/cp-base-new/
COPY --from=confluent_base /etc/cp-base-new/log4j.properties /etc/cp-base-new/log4j.properties

ADD --chown=root:root --chmod=755 ${GITHUB_REPO_URL}/aws/aws-msk-iam-auth/releases/download/v2.3.0/aws-msk-iam-auth-2.3.0-all.jar /usr/share/java/cp-base-new
ADD --chown=root:root --chmod=755 ${GITHUB_REPO_URL}/aws/aws-msk-iam-auth/releases/download/v2.3.0/aws-msk-iam-auth-2.3.0-all.jar /opt/kafka/libs

ENV METADATA_AUDIT_EVENT_NAME="MetadataAuditEvent_v4"
ENV METADATA_CHANGE_EVENT_NAME="MetadataChangeEvent_v4"
ENV FAILED_METADATA_CHANGE_EVENT_NAME="FailedMetadataChangeEvent_v4"
ENV DATAHUB_USAGE_EVENT_NAME="DataHubUsageEvent_v1"
ENV METADATA_CHANGE_LOG_VERSIONED_TOPIC_NAME="MetadataChangeLog_Versioned_v1"
ENV METADATA_CHANGE_LOG_TIMESERIES_TOPIC_NAME="MetadataChangeLog_Timeseries_v1"
ENV METADATA_CHANGE_PROPOSAL_TOPIC_NAME="MetadataChangeProposal_v1"
ENV FAILED_METADATA_CHANGE_PROPOSAL_TOPIC_NAME="FailedMetadataChangeProposal_v1"
ENV PLATFORM_EVENT_TOPIC_NAME="PlatformEvent_v1"
ENV DATAHUB_UPGRADE_HISTORY_TOPIC_NAME="DataHubUpgradeHistory_v1"
ENV USE_CONFLUENT_SCHEMA_REGISTRY="TRUE"

COPY ./kafka-setup.sh ./kafka-setup.sh
COPY ./kafka-config.sh ./kafka-config.sh
COPY ./kafka-topic-workers.sh ./kafka-topic-workers.sh
COPY ./kafka-ready.sh ./kafka-ready.sh
COPY ./env_to_properties.py ./env_to_properties.py

RUN chmod +x ./kafka-setup.sh ./kafka-topic-workers.sh ./kafka-ready.sh

CMD ./kafka-setup.sh