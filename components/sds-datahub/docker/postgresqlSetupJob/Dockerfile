ARG BASE_IMAGE

FROM $BASE_IMAGE

# Promote ARGs to ENV for availability inside RUN blocks
ARG USERNAME="dfuser"
ARG TARGETARCH
ARG DOCKERIZE_VERSION=v0.9.3

RUN set -ex && \
    groupadd -r -g 1000 "${USERNAME}" && \
    useradd -r -u 1000 -m -s /bin/bash -g "${USERNAME}" "${USERNAME}" && \
    apt-get update && \
    apt-get --no-install-recommends install -y postgresql-client wget ca-certificates && \
    if [ "$TARGETARCH" = "amd64" ]; then \
        echo "Installing for AMD64 architecture"; \
        wget --secure-protocol=TLSv1_2 -q -O - https://github.com/jwilder/dockerize/releases/download/${DOCKERIZE_VERSION}/dockerize-linux-amd64-${DOCKERIZE_VERSION}.tar.gz | tar xz -C /usr/local/bin; \
    elif [ "$TARGETARCH" = "arm64" ]; then \
        echo "Installing for ARM64 architecture"; \
        wget --secure-protocol=TLSv1_2 -q -O - https://github.com/jwilder/dockerize/releases/download/${DOCKERIZE_VERSION}/dockerize-linux-arm64-${DOCKERIZE_VERSION}.tar.gz | tar xz -C /usr/local/bin; \
    else \
        echo "Unsupported architecture: $TARGETARCH"; \
        exit 1; \
    fi && \
    apt-get autoremove -yqq --purge wget && \
    rm -rf /var/lib/apt/lists/*

COPY ./init.sql /init.sql
COPY ./init.sh /init.sh
RUN chmod 755 /init.sh

CMD dockerize -wait tcp://${POSTGRES_HOST}:${POSTGRES_PORT} -timeout 240s /init.sh