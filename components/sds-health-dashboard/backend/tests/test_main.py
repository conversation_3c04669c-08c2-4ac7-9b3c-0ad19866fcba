"""
Unit tests for the main FastAPI application.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch, AsyncMock
import json
from datetime import datetime

from main import app
from models import HealthCheckResult, HealthStatus, CheckType, HealthCheckSummary


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    return TestClient(app)


@pytest.fixture
def mock_health_service():
    """Mock health check service."""
    with patch('main.health_service') as mock:
        yield mock


@pytest.fixture
def sample_health_check_results():
    """Sample health check results for testing."""
    return [
        HealthCheckResult(
            check_id="test_env_airflow_url",
            check_type=CheckType.URL_CHECK,
            component_name="airflow",
            environment="test_env",
            status=HealthStatus.HEALTHY,
            message="URL check successful (HTTP 200)",
            response_time_ms=150.5,
            timestamp=datetime.utcnow(),
            details={"status_code": 200, "url": "http://test.com/health"}
        ),
        HealthCheckResult(
            check_id="test_env_druid_connection",
            check_type=CheckType.K8S_SERVICE_CONNECTION,
            component_name="druid",
            environment="test_env",
            status=HealthStatus.UNHEALTHY,
            message="Service not reachable at port 8088",
            response_time_ms=5000.0,
            timestamp=datetime.utcnow(),
            error_details="Connection timeout"
        )
    ]


class TestMainEndpoints:
    """Test main application endpoints."""
    
    def test_root_endpoint(self, client):
        """Test the root endpoint."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert data["service"] == "SDS Health Check Dashboard API"
        assert data["version"] == "1.0.0"
        assert data["status"] == "running"
        assert "timestamp" in data
    
    def test_health_endpoint(self, client):
        """Test the health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
    
    def test_environments_endpoint(self, client):
        """Test the environments endpoint."""
        response = client.get("/environments")
        assert response.status_code == 200
        data = response.json()
        assert "environments" in data
        assert isinstance(data["environments"], list)
        # Should include some of the default environments
        assert "devops" in data["environments"]
        assert "pe-dev" in data["environments"]


class TestHealthCheckEndpoints:
    """Test health check related endpoints."""
    
    def test_get_all_health_checks_success(self, client, mock_health_service, sample_health_check_results):
        """Test successful retrieval of all health checks."""
        mock_health_service.execute_all_checks = AsyncMock(return_value=sample_health_check_results)
        
        response = client.get("/health-checks")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["component_name"] == "airflow"
        assert data[0]["status"] == "healthy"
        assert data[1]["component_name"] == "druid"
        assert data[1]["status"] == "unhealthy"
    
    def test_get_all_health_checks_with_environment_filter(self, client, mock_health_service, sample_health_check_results):
        """Test health checks with environment filter."""
        mock_health_service.execute_all_checks = AsyncMock(return_value=sample_health_check_results)
        
        response = client.get("/health-checks?environment=test_env")
        assert response.status_code == 200
        mock_health_service.execute_all_checks.assert_called_once_with("test_env")
    
    def test_get_all_health_checks_force_refresh(self, client, mock_health_service, sample_health_check_results):
        """Test health checks with force refresh."""
        mock_health_service.execute_all_checks = AsyncMock(return_value=sample_health_check_results)
        
        response = client.get("/health-checks?force_refresh=true")
        assert response.status_code == 200
        # Should bypass cache and call the service
        mock_health_service.execute_all_checks.assert_called_once()
    
    def test_get_health_summary_success(self, client, mock_health_service, sample_health_check_results):
        """Test successful retrieval of health summary."""
        mock_health_service.execute_all_checks = AsyncMock(return_value=sample_health_check_results)
        
        response = client.get("/health-checks/summary")
        assert response.status_code == 200
        data = response.json()
        assert data["total_checks"] == 2
        assert data["healthy_count"] == 1
        assert data["unhealthy_count"] == 1
        assert data["warning_count"] == 0
        assert "last_updated" in data
    
    def test_get_environment_health_checks_success(self, client, mock_health_service, sample_health_check_results):
        """Test successful retrieval of environment-specific health checks."""
        mock_health_service.execute_all_checks = AsyncMock(return_value=sample_health_check_results)
        
        response = client.get("/health-checks/devops")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        mock_health_service.execute_all_checks.assert_called_once_with("devops")
    
    def test_get_environment_health_checks_invalid_environment(self, client):
        """Test health checks for invalid environment."""
        response = client.get("/health-checks/invalid_env")
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"]
    
    def test_refresh_health_checks(self, client):
        """Test manual refresh of health checks."""
        response = client.post("/health-checks/refresh")
        assert response.status_code == 200
        data = response.json()
        assert "refresh initiated" in data["message"]
    
    def test_get_environment_config_success(self, client):
        """Test successful retrieval of environment configuration."""
        response = client.get("/health-checks/config/devops")
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "devops"
        assert data["namespace"] == "sds-devops"
    
    def test_get_environment_config_invalid_environment(self, client):
        """Test environment config for invalid environment."""
        response = client.get("/health-checks/config/invalid_env")
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"]


class TestErrorHandling:
    """Test error handling scenarios."""
    
    def test_health_checks_service_error(self, client, mock_health_service):
        """Test handling of service errors during health checks."""
        mock_health_service.execute_all_checks = AsyncMock(side_effect=Exception("Service error"))
        
        response = client.get("/health-checks")
        assert response.status_code == 500
        data = response.json()
        assert "Health check execution failed" in data["detail"]
    
    def test_health_summary_service_error(self, client, mock_health_service):
        """Test handling of service errors during summary generation."""
        mock_health_service.execute_all_checks = AsyncMock(side_effect=Exception("Service error"))
        
        response = client.get("/health-checks/summary")
        assert response.status_code == 500
        data = response.json()
        assert "Summary generation failed" in data["detail"]


class TestCaching:
    """Test caching behavior."""
    
    def test_cache_behavior(self, client, mock_health_service, sample_health_check_results):
        """Test that results are cached appropriately."""
        mock_health_service.execute_all_checks = AsyncMock(return_value=sample_health_check_results)
        
        # First request
        response1 = client.get("/health-checks")
        assert response1.status_code == 200
        
        # Second request should use cache (within cache duration)
        response2 = client.get("/health-checks")
        assert response2.status_code == 200
        
        # Service should only be called once due to caching
        assert mock_health_service.execute_all_checks.call_count == 1
    
    def test_cache_bypass_with_force_refresh(self, client, mock_health_service, sample_health_check_results):
        """Test that force refresh bypasses cache."""
        mock_health_service.execute_all_checks = AsyncMock(return_value=sample_health_check_results)
        
        # First request
        response1 = client.get("/health-checks")
        assert response1.status_code == 200
        
        # Second request with force refresh should bypass cache
        response2 = client.get("/health-checks?force_refresh=true")
        assert response2.status_code == 200
        
        # Service should be called twice
        assert mock_health_service.execute_all_checks.call_count == 2


if __name__ == "__main__":
    pytest.main([__file__])
