"""
Unit tests for health check implementations.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock, MagicMock
import aiohttp
from datetime import datetime
import subprocess

from health_checks_extended import ExtendedHealthCheckService
from models import HealthStatus, CheckType, ServiceConfig, URLConfig, DatabaseConfig
from config import EnvironmentConfig


@pytest.fixture
def health_service():
    """Create a health check service instance for testing."""
    service = ExtendedHealthCheckService()
    service.session = Mock()
    service.k8s_client = Mock()
    service.k8s_apps_client = Mock()
    return service


@pytest.fixture
def sample_environment_config():
    """Sample environment configuration for testing."""
    return EnvironmentConfig(
        name="test_env",
        namespace="sds-test",
        domain_url="https://test.sds.local",
        services=[
            ServiceConfig(
                name="airflow-webserver",
                namespace="sds-test",
                service_name="airflow-webserver.sds-test.svc.cluster.local",
                port=8080,
                health_endpoint="orchestration/health",
                expected_replicas=1,
                labels={"app": "airflow2", "component": "webserver"}
            )
        ],
        databases=[
            DatabaseConfig(
                component_name="airflow",
                secret_name="airflow-secret-vault",
                namespace="sds-test",
                database_type="postgres"
            )
        ],
        urls=[
            URLConfig(
                name="Airflow",
                url="https://test.sds.local/orchestration/health",
                expected_status_codes=[200],
                timeout_seconds=10
            )
        ]
    )


class TestURLHealthChecks:
    """Test URL health check implementations."""
    
    @pytest.mark.asyncio
    async def test_url_check_success(self, health_service, sample_environment_config):
        """Test successful URL health check."""
        # Mock successful HTTP response
        mock_response = Mock()
        mock_response.status = 200
        mock_response.__aenter__ = AsyncMock(return_value=mock_response)
        mock_response.__aexit__ = AsyncMock(return_value=None)
        
        health_service.session.get = Mock(return_value=mock_response)
        
        results = await health_service._execute_url_checks(sample_environment_config)
        
        assert len(results) == 1
        result = results[0]
        assert result.status == HealthStatus.HEALTHY
        assert result.component_name == "Airflow"
        assert result.check_type == CheckType.URL_CHECK
        assert "HTTP 200" in result.message
        assert result.response_time_ms is not None
    
    @pytest.mark.asyncio
    async def test_url_check_failure(self, health_service, sample_environment_config):
        """Test failed URL health check."""
        # Mock failed HTTP response
        mock_response = Mock()
        mock_response.status = 500
        mock_response.__aenter__ = AsyncMock(return_value=mock_response)
        mock_response.__aexit__ = AsyncMock(return_value=None)
        
        health_service.session.get = Mock(return_value=mock_response)
        
        results = await health_service._execute_url_checks(sample_environment_config)
        
        assert len(results) == 1
        result = results[0]
        assert result.status == HealthStatus.UNHEALTHY
        assert "HTTP 500" in result.message
    
    @pytest.mark.asyncio
    async def test_url_check_timeout(self, health_service, sample_environment_config):
        """Test URL health check timeout."""
        # Mock timeout exception
        health_service.session.get = Mock(side_effect=aiohttp.ClientTimeout())
        
        results = await health_service._execute_url_checks(sample_environment_config)
        
        assert len(results) == 1
        result = results[0]
        assert result.status == HealthStatus.UNHEALTHY
        assert "timed out" in result.message.lower()


class TestServiceHealthChecks:
    """Test Kubernetes service health check implementations."""
    
    @pytest.mark.asyncio
    async def test_service_health_check_success(self, health_service, sample_environment_config):
        """Test successful service health check."""
        with patch('subprocess.run') as mock_run:
            # Mock successful kubectl command
            mock_run.return_value = Mock(
                returncode=0,
                stdout="200\n",
                stderr=""
            )
            
            results = await health_service._execute_service_health_checks(sample_environment_config)
            
            assert len(results) == 1
            result = results[0]
            assert result.status == HealthStatus.HEALTHY
            assert result.check_type == CheckType.K8S_SERVICE_HEALTH
            assert "HTTP 200" in result.message
    
    @pytest.mark.asyncio
    async def test_service_health_check_failure(self, health_service, sample_environment_config):
        """Test failed service health check."""
        with patch('subprocess.run') as mock_run:
            # Mock failed kubectl command
            mock_run.return_value = Mock(
                returncode=1,
                stdout="",
                stderr="Connection refused"
            )
            
            results = await health_service._execute_service_health_checks(sample_environment_config)
            
            assert len(results) == 1
            result = results[0]
            assert result.status == HealthStatus.UNHEALTHY
            assert "failed" in result.message.lower()


class TestPodReplicaChecks:
    """Test pod replica check implementations."""
    
    @pytest.mark.asyncio
    async def test_pod_replica_check_success(self, health_service, sample_environment_config):
        """Test successful pod replica check."""
        # Mock Kubernetes API response
        mock_pods = Mock()
        mock_pods.items = [Mock(), Mock()]  # 2 pods
        health_service.k8s_client.list_pod_for_all_namespaces = Mock(return_value=mock_pods)
        
        results = await health_service._execute_pod_replica_checks(sample_environment_config)
        
        assert len(results) == 1
        result = results[0]
        assert result.status == HealthStatus.HEALTHY
        assert result.check_type == CheckType.POD_REPLICA_CHECK
        assert "2/1" in result.message  # 2 actual, 1 expected
    
    @pytest.mark.asyncio
    async def test_pod_replica_check_insufficient(self, health_service, sample_environment_config):
        """Test pod replica check with insufficient replicas."""
        # Mock Kubernetes API response with no pods
        mock_pods = Mock()
        mock_pods.items = []  # 0 pods
        health_service.k8s_client.list_pod_for_all_namespaces = Mock(return_value=mock_pods)
        
        results = await health_service._execute_pod_replica_checks(sample_environment_config)
        
        assert len(results) == 1
        result = results[0]
        assert result.status == HealthStatus.UNHEALTHY
        assert "Insufficient replicas" in result.message


class TestDatabaseChecks:
    """Test database connectivity check implementations."""
    
    @pytest.mark.asyncio
    async def test_database_check_airflow_success(self, health_service, sample_environment_config):
        """Test successful Airflow database check."""
        # Mock Kubernetes secret
        mock_secret = Mock()
        mock_secret.data = {
            "connection": "************************************************"  # base64 encoded connection string
        }
        health_service.k8s_client.read_namespaced_secret = Mock(return_value=mock_secret)
        
        results = await health_service._execute_database_checks(sample_environment_config)
        
        assert len(results) == 1
        result = results[0]
        assert result.status == HealthStatus.HEALTHY
        assert result.check_type == CheckType.DATABASE_CONNECTIVITY
        assert result.component_name == "airflow"
    
    @pytest.mark.asyncio
    async def test_database_check_secret_not_found(self, health_service, sample_environment_config):
        """Test database check when secret is not found."""
        from kubernetes.client.rest import ApiException
        
        # Mock Kubernetes API exception
        health_service.k8s_client.read_namespaced_secret = Mock(
            side_effect=ApiException(status=404, reason="Not Found")
        )
        
        results = await health_service._execute_database_checks(sample_environment_config)
        
        assert len(results) == 1
        result = results[0]
        assert result.status == HealthStatus.UNHEALTHY
        assert "failed" in result.message.lower()


class TestSecretChecks:
    """Test secret existence check implementations."""
    
    @pytest.mark.asyncio
    async def test_secret_check_success(self, health_service, sample_environment_config):
        """Test successful secret existence check."""
        # Mock Kubernetes secret
        mock_secret = Mock()
        health_service.k8s_client.read_namespaced_secret = Mock(return_value=mock_secret)
        
        results = await health_service._execute_secret_checks(sample_environment_config)
        
        assert len(results) == 1
        result = results[0]
        assert result.status == HealthStatus.HEALTHY
        assert result.check_type == CheckType.SECRET_EXISTS
    
    @pytest.mark.asyncio
    async def test_secret_check_not_found(self, health_service, sample_environment_config):
        """Test secret check when secret doesn't exist."""
        from kubernetes.client.rest import ApiException
        
        # Mock Kubernetes API exception for not found
        health_service.k8s_client.read_namespaced_secret = Mock(
            side_effect=ApiException(status=404, reason="Not Found")
        )
        
        results = await health_service._execute_secret_checks(sample_environment_config)
        
        assert len(results) == 1
        result = results[0]
        assert result.status == HealthStatus.UNHEALTHY
        assert "does not exist" in result.message


class TestPVCChecks:
    """Test PVC status check implementations."""
    
    @pytest.mark.asyncio
    async def test_pvc_check_success(self, health_service, sample_environment_config):
        """Test successful PVC status check."""
        # Mock PVC with bound status
        mock_pvc = Mock()
        mock_pvc.metadata.name = "test-pvc"
        mock_pvc.status.phase = "Bound"
        
        mock_pvcs = Mock()
        mock_pvcs.items = [mock_pvc]
        
        health_service.k8s_client.list_namespaced_persistent_volume_claim = Mock(return_value=mock_pvcs)
        
        results = await health_service._execute_pvc_checks(sample_environment_config)
        
        assert len(results) == 1
        result = results[0]
        assert result.status == HealthStatus.HEALTHY
        assert result.check_type == CheckType.PVC_STATUS
        assert "bound" in result.message.lower()
    
    @pytest.mark.asyncio
    async def test_pvc_check_not_bound(self, health_service, sample_environment_config):
        """Test PVC check when PVC is not bound."""
        # Mock PVC with pending status
        mock_pvc = Mock()
        mock_pvc.metadata.name = "test-pvc"
        mock_pvc.status.phase = "Pending"
        
        mock_pvcs = Mock()
        mock_pvcs.items = [mock_pvc]
        
        health_service.k8s_client.list_namespaced_persistent_volume_claim = Mock(return_value=mock_pvcs)
        
        results = await health_service._execute_pvc_checks(sample_environment_config)
        
        assert len(results) == 1
        result = results[0]
        assert result.status == HealthStatus.UNHEALTHY
        assert "not bound" in result.message.lower()


if __name__ == "__main__":
    pytest.main([__file__])
