apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "sds-health-dashboard.fullname" . }}-config
  labels:
    {{- include "sds-health-dashboard.labels" . | nindent 4 }}
data:
  config.yaml: |
    environments:
      {{- range $name, $config := .Values.config.environments }}
      {{ $name }}:
        name: {{ $name }}
        namespace: {{ $config.namespace }}
        {{- if $config.domain_url }}
        domain_url: {{ $config.domain_url }}
        {{- end }}
        enabled: {{ $config.enabled }}
        services: []
        databases: []
        urls: []
      {{- end }}
    global_timeout_seconds: {{ .Values.config.global_timeout_seconds }}
    cache_duration_seconds: {{ .Values.config.cache_duration_seconds }}
    max_concurrent_checks: {{ .Values.config.max_concurrent_checks }}
    retry_attempts: {{ .Values.config.retry_attempts }}
    retry_delay_seconds: {{ .Values.config.retry_delay_seconds }}
