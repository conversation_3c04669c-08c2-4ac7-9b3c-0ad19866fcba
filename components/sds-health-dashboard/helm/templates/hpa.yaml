{{- if .Values.autoscaling.backend.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "sds-health-dashboard.fullname" . }}-backend
  labels:
    {{- include "sds-health-dashboard.labels" . | nindent 4 }}
    app.kubernetes.io/component: backend
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "sds-health-dashboard.fullname" . }}-backend
  minReplicas: {{ .Values.autoscaling.backend.minReplicas }}
  maxReplicas: {{ .Values.autoscaling.backend.maxReplicas }}
  metrics:
    {{- if .Values.autoscaling.backend.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.backend.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.autoscaling.backend.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.backend.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
---
{{- if .Values.autoscaling.frontend.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "sds-health-dashboard.fullname" . }}-frontend
  labels:
    {{- include "sds-health-dashboard.labels" . | nindent 4 }}
    app.kubernetes.io/component: frontend
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "sds-health-dashboard.fullname" . }}-frontend
  minReplicas: {{ .Values.autoscaling.frontend.minReplicas }}
  maxReplicas: {{ .Values.autoscaling.frontend.maxReplicas }}
  metrics:
    {{- if .Values.autoscaling.frontend.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.frontend.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.autoscaling.frontend.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.frontend.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
