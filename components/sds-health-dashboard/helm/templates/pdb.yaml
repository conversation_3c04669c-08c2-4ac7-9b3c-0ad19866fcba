{{- if .Values.podDisruptionBudget.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "sds-health-dashboard.fullname" . }}-backend
  labels:
    {{- include "sds-health-dashboard.labels" . | nindent 4 }}
    app.kubernetes.io/component: backend
spec:
  minAvailable: {{ .Values.podDisruptionBudget.minAvailable }}
  selector:
    matchLabels:
      {{- include "sds-health-dashboard.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: backend
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "sds-health-dashboard.fullname" . }}-frontend
  labels:
    {{- include "sds-health-dashboard.labels" . | nindent 4 }}
    app.kubernetes.io/component: frontend
spec:
  minAvailable: {{ .Values.podDisruptionBudget.minAvailable }}
  selector:
    matchLabels:
      {{- include "sds-health-dashboard.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: frontend
{{- end }}
