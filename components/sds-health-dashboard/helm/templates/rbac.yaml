{{- if .Values.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "sds-health-dashboard.fullname" . }}
  labels:
    {{- include "sds-health-dashboard.labels" . | nindent 4 }}
rules:
  {{- toYaml .Values.rbac.rules | nindent 2 }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "sds-health-dashboard.fullname" . }}
  labels:
    {{- include "sds-health-dashboard.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "sds-health-dashboard.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "sds-health-dashboard.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
{{- end }}
