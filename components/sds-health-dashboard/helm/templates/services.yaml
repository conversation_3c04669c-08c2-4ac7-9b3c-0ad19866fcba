{{- if .Values.backend.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "sds-health-dashboard.fullname" . }}-backend
  labels:
    {{- include "sds-health-dashboard.labels" . | nindent 4 }}
    app.kubernetes.io/component: backend
  {{- with .Values.backend.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.backend.service.type }}
  ports:
    - port: {{ .Values.backend.service.port }}
      targetPort: {{ .Values.backend.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "sds-health-dashboard.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: backend
{{- end }}
---
{{- if .Values.frontend.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "sds-health-dashboard.fullname" . }}-frontend
  labels:
    {{- include "sds-health-dashboard.labels" . | nindent 4 }}
    app.kubernetes.io/component: frontend
  {{- with .Values.frontend.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.frontend.service.type }}
  ports:
    - port: {{ .Values.frontend.service.port }}
      targetPort: {{ .Values.frontend.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "sds-health-dashboard.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: frontend
{{- end }}
