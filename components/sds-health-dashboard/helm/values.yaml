# Default values for sds-health-dashboard
# This is a YAML-formatted file.

global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: ""

nameOverride: ""
fullnameOverride: ""

# Backend configuration
backend:
  enabled: true
  image:
    repository: prevalentai/sds-health-dashboard-backend
    tag: "1.0.0"
    pullPolicy: IfNotPresent
  
  replicaCount: 2
  
  service:
    type: ClusterIP
    port: 8000
    targetPort: 8000
    annotations: {}
  
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 256Mi
  
  nodeSelector: {}
  tolerations: []
  affinity: {}
  
  podAnnotations: {}
  podSecurityContext: {}
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop:
        - ALL
  
  livenessProbe:
    httpGet:
      path: /health
      port: 8000
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  
  readinessProbe:
    httpGet:
      path: /health
      port: 8000
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3
  
  env:
    - name: HEALTH_DASHBOARD_CONFIG
      value: "/etc/health-dashboard/config.yaml"
    - name: LOG_LEVEL
      value: "INFO"

# Frontend configuration
frontend:
  enabled: true
  image:
    repository: prevalentai/sds-health-dashboard-frontend
    tag: "1.0.0"
    pullPolicy: IfNotPresent
  
  replicaCount: 2
  
  service:
    type: ClusterIP
    port: 80
    targetPort: 80
    annotations: {}
  
  resources:
    limits:
      cpu: 200m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 128Mi
  
  nodeSelector: {}
  tolerations: []
  affinity: {}
  
  podAnnotations: {}
  podSecurityContext: {}
  securityContext:
    runAsNonRoot: true
    runAsUser: 101
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop:
        - ALL
  
  livenessProbe:
    httpGet:
      path: /health
      port: 80
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  
  readinessProbe:
    httpGet:
      path: /health
      port: 80
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3

# Ingress configuration
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
  hosts:
    - host: health-dashboard.sds.local
      paths:
        - path: /
          pathType: Prefix
          service:
            name: sds-health-dashboard-frontend
            port: 80
  tls:
    - secretName: sds-health-dashboard-tls
      hosts:
        - health-dashboard.sds.local

# Service Account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# RBAC
rbac:
  create: true
  rules:
    - apiGroups: [""]
      resources: ["pods", "services", "secrets", "persistentvolumeclaims"]
      verbs: ["get", "list", "watch"]
    - apiGroups: ["apps"]
      resources: ["deployments", "replicasets"]
      verbs: ["get", "list", "watch"]

# Configuration
config:
  # Environment-specific configurations
  environments:
    devops:
      namespace: "sds-devops"
      domain_url: ""
      enabled: true
    pe-dev:
      namespace: "sds-pe-dev"
      domain_url: ""
      enabled: true
    pe-qaregress:
      namespace: "sds-pe-qaregress"
      domain_url: ""
      enabled: true
    ttv:
      namespace: "sds-ttv"
      domain_url: ""
      enabled: true
    sol-ttv:
      namespace: "sds-sol-ttv"
      domain_url: ""
      enabled: true
    aws-ite:
      namespace: "sds-aws-ite"
      domain_url: ""
      enabled: true
    aws-fte:
      namespace: "sds-aws-fte"
      domain_url: ""
      enabled: true
    aws-dte:
      namespace: "sds-aws-dte"
      domain_url: ""
      enabled: true
  
  # Global settings
  global_timeout_seconds: 30
  cache_duration_seconds: 120
  max_concurrent_checks: 10
  retry_attempts: 2
  retry_delay_seconds: 5

# Monitoring
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    interval: 30s
    scrapeTimeout: 10s
    labels: {}

# Autoscaling
autoscaling:
  backend:
    enabled: false
    minReplicas: 2
    maxReplicas: 10
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  
  frontend:
    enabled: false
    minReplicas: 2
    maxReplicas: 10
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

# Pod Disruption Budget
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# Network Policy
networkPolicy:
  enabled: false
  ingress: []
  egress: []
