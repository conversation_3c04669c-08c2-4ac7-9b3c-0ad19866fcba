# SDS Health Dashboard Testing Guide

This guide covers testing strategies and procedures for the SDS Health Check Dashboard.

## Testing Strategy

The testing approach includes:
- Unit tests for individual components
- Integration tests for API endpoints
- End-to-end tests for complete workflows
- Performance tests for scalability
- Security tests for vulnerabilities

## Backend Testing

### Running Unit Tests

```bash
cd components/sds-health-dashboard/backend

# Install test dependencies
pip install -r requirements.txt

# Run all tests
pytest

# Run with coverage
pytest --cov=. --cov-report=html

# Run specific test file
pytest tests/test_main.py

# Run with verbose output
pytest -v

# Run only fast tests (exclude slow/integration tests)
pytest -m "not slow"
```

### Test Structure

```
backend/tests/
├── test_main.py              # FastAPI endpoint tests
├── test_health_checks.py     # Health check logic tests
├── test_config.py            # Configuration tests
├── conftest.py               # Shared test fixtures
└── integration/
    ├── test_k8s_integration.py
    └── test_api_integration.py
```

### Writing New Tests

Example test for a new health check:

```python
import pytest
from unittest.mock import Mock, AsyncMock
from health_checks_extended import ExtendedHealthCheckService
from models import HealthStatus, CheckType

@pytest.mark.asyncio
async def test_new_health_check(health_service, sample_environment_config):
    """Test new health check implementation."""
    # Mock external dependencies
    health_service.k8s_client.some_method = Mock(return_value=mock_response)
    
    # Execute the health check
    results = await health_service._execute_new_check(sample_environment_config)
    
    # Assertions
    assert len(results) == 1
    result = results[0]
    assert result.status == HealthStatus.HEALTHY
    assert result.check_type == CheckType.NEW_CHECK_TYPE
    assert "expected message" in result.message
```

### Integration Testing

```bash
# Start test environment
docker-compose -f docker-compose.test.yml up -d

# Run integration tests
pytest tests/integration/ -v

# Cleanup
docker-compose -f docker-compose.test.yml down
```

## Frontend Testing

### Running Unit Tests

```bash
cd components/sds-health-dashboard/frontend

# Install dependencies
npm install

# Run all tests
npm test

# Run tests with coverage
npm test -- --coverage

# Run tests in watch mode
npm test -- --watch

# Run specific test file
npm test -- HealthStatusIcon.test.tsx
```

### Test Structure

```
frontend/src/
├── components/
│   ├── __tests__/
│   │   ├── HealthStatusIcon.test.tsx
│   │   ├── HealthCheckCard.test.tsx
│   │   ├── DashboardSummary.test.tsx
│   │   └── HealthDashboard.test.tsx
│   └── ...
├── services/
│   └── __tests__/
│       └── api.test.ts
└── utils/
    └── __tests__/
        └── helpers.test.ts
```

### Writing Component Tests

Example test for a React component:

```typescript
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import NewComponent from '../NewComponent';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('NewComponent', () => {
  test('renders correctly with props', () => {
    renderWithQueryClient(<NewComponent prop1="value1" />);
    
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });

  test('handles user interactions', async () => {
    renderWithQueryClient(<NewComponent />);
    
    const button = screen.getByRole('button', { name: /click me/i });
    fireEvent.click(button);
    
    await waitFor(() => {
      expect(screen.getByText('Updated Text')).toBeInTheDocument();
    });
  });
});
```

### API Service Testing

```typescript
import { HealthDashboardAPI } from '../api';
import { rest } from 'msw';
import { setupServer } from 'msw/node';

const server = setupServer(
  rest.get('/api/health-checks', (req, res, ctx) => {
    return res(ctx.json([
      {
        check_id: 'test_check',
        component_name: 'test_component',
        status: 'healthy',
        // ... other properties
      }
    ]));
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

describe('HealthDashboardAPI', () => {
  test('fetches health checks successfully', async () => {
    const result = await HealthDashboardAPI.getAllHealthChecks();
    
    expect(result).toHaveLength(1);
    expect(result[0].component_name).toBe('test_component');
  });
});
```

## End-to-End Testing

### Cypress Setup

```bash
cd components/sds-health-dashboard/frontend

# Install Cypress
npm install --save-dev cypress

# Open Cypress
npx cypress open

# Run headless
npx cypress run
```

### E2E Test Example

```typescript
// cypress/integration/dashboard.spec.ts
describe('Health Dashboard', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('displays health check summary', () => {
    cy.get('[data-testid="health-summary"]').should('be.visible');
    cy.get('[data-testid="total-checks"]').should('contain.text', '10');
  });

  it('filters health checks by environment', () => {
    cy.get('[data-testid="environment-filter"]').click();
    cy.get('[data-value="pe-dev"]').click();
    
    cy.get('[data-testid="health-check-card"]')
      .should('have.length.greaterThan', 0)
      .each(($card) => {
        cy.wrap($card).should('contain.text', 'pe-dev');
      });
  });

  it('refreshes health checks manually', () => {
    cy.intercept('GET', '/api/health-checks*', { fixture: 'health-checks.json' }).as('getHealthChecks');
    
    cy.get('[data-testid="refresh-button"]').click();
    
    cy.wait('@getHealthChecks');
    cy.get('[data-testid="success-message"]').should('be.visible');
  });
});
```

## Performance Testing

### Load Testing with Artillery

```bash
# Install Artillery
npm install -g artillery

# Create test configuration
cat > load-test.yml << EOF
config:
  target: 'http://localhost:8000'
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: "Health Check API"
    requests:
      - get:
          url: "/health-checks"
      - get:
          url: "/health-checks/summary"
EOF

# Run load test
artillery run load-test.yml
```

### Memory and CPU Profiling

```bash
# Backend profiling
cd components/sds-health-dashboard/backend

# Install profiling tools
pip install memory-profiler py-spy

# Profile memory usage
python -m memory_profiler main.py

# Profile CPU usage
py-spy record -o profile.svg -- python main.py
```

## Security Testing

### Dependency Scanning

```bash
# Backend security scan
cd components/sds-health-dashboard/backend
pip install safety
safety check

# Frontend security scan
cd components/sds-health-dashboard/frontend
npm audit
npm audit fix
```

### Container Security Scanning

```bash
# Scan backend image
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image prevalentai/sds-health-dashboard-backend:1.0.0

# Scan frontend image
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image prevalentai/sds-health-dashboard-frontend:1.0.0
```

### API Security Testing

```bash
# Install OWASP ZAP
docker pull owasp/zap2docker-stable

# Run security scan
docker run -t owasp/zap2docker-stable zap-baseline.py \
  -t http://localhost:8000
```

## Continuous Integration

### GitHub Actions Workflow

```yaml
# .github/workflows/test.yml
name: Test

on: [push, pull_request]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          cd components/sds-health-dashboard/backend
          pip install -r requirements.txt
      - name: Run tests
        run: |
          cd components/sds-health-dashboard/backend
          pytest --cov=. --cov-report=xml
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: |
          cd components/sds-health-dashboard/frontend
          npm ci
      - name: Run tests
        run: |
          cd components/sds-health-dashboard/frontend
          npm test -- --coverage --watchAll=false
```

## Test Data Management

### Mock Data

Create realistic test data:

```python
# tests/fixtures/health_checks.py
SAMPLE_HEALTH_CHECKS = [
    {
        "check_id": "pe-dev_airflow_url",
        "check_type": "url_check",
        "component_name": "airflow",
        "environment": "pe-dev",
        "status": "healthy",
        "message": "URL check successful (HTTP 200)",
        "response_time_ms": 150.5,
        "timestamp": "2024-01-15T10:30:00Z"
    },
    # ... more test data
]
```

### Database Seeding

For integration tests that require specific data states:

```python
@pytest.fixture
def seed_test_data():
    """Seed test environment with known data."""
    # Setup test data
    yield
    # Cleanup test data
```

## Quality Gates

### Coverage Requirements

- Backend: Minimum 80% code coverage
- Frontend: Minimum 75% code coverage
- Critical paths: 95% coverage

### Performance Benchmarks

- API response time: < 500ms (95th percentile)
- Frontend load time: < 2 seconds
- Memory usage: < 512MB per backend pod

### Security Requirements

- No high/critical vulnerabilities in dependencies
- Container images pass security scans
- API endpoints protected against common attacks

## Troubleshooting Tests

### Common Issues

1. **Flaky tests**: Use proper async/await patterns and timeouts
2. **Mock issues**: Ensure mocks are reset between tests
3. **Environment differences**: Use consistent test environments
4. **Timing issues**: Use proper waiting mechanisms

### Debug Commands

```bash
# Run single test with debug output
pytest tests/test_main.py::test_specific_function -v -s

# Run frontend tests with debug info
npm test -- --verbose --no-cache

# Check test coverage gaps
pytest --cov=. --cov-report=html
open htmlcov/index.html
```
