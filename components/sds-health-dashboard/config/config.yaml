# SDS Health Dashboard Configuration
# This file contains the configuration for local development and testing

environments:
  local-dev:
    name: "local-dev"
    namespace: "default"
    domain_url: "http://localhost:8080"
    enabled: true
    services: []
    databases: []
    urls:
      - name: "Backend Health"
        url: "http://localhost:8000/health"
        expected_status_codes: [200]
        timeout_seconds: 5
      - name: "Frontend Health"
        url: "http://localhost:3000/health"
        expected_status_codes: [200]
        timeout_seconds: 5

  mock-env:
    name: "mock-env"
    namespace: "sds-mock"
    domain_url: "https://mock.sds.local"
    enabled: true
    services:
      - name: "mock-airflow"
        namespace: "sds-mock"
        service_name: "airflow-webserver.sds-mock.svc.cluster.local"
        port: 8080
        health_endpoint: "orchestration/health"
        expected_replicas: 1
        labels:
          app: "airflow2"
          component: "webserver"
    databases:
      - component_name: "airflow"
        secret_name: "airflow-secret-vault"
        namespace: "sds-mock"
        database_type: "postgres"
    urls:
      - name: "Mock Airflow"
        url: "https://mock.sds.local/orchestration/health"
        expected_status_codes: [200]
        timeout_seconds: 10

# Global configuration
global_timeout_seconds: 30
cache_duration_seconds: 120
max_concurrent_checks: 10
retry_attempts: 2
retry_delay_seconds: 5
