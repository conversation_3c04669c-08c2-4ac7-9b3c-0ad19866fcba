# SDS Health Dashboard Deployment Guide

This guide provides comprehensive instructions for deploying the SDS Health Check Dashboard in different environments.

## Prerequisites

- Kubernetes cluster with appropriate RBAC permissions
- Helm 3.x installed
- Docker registry access for pushing images
- kube<PERSON><PERSON> configured for target cluster

## Building and Pushing Images

### Backend Image

```bash
cd components/sds-health-dashboard/backend
docker build -t prevalentai/sds-health-dashboard-backend:1.0.0 .
docker push prevalentai/sds-health-dashboard-backend:1.0.0
```

### Frontend Image

```bash
cd components/sds-health-dashboard/frontend
docker build -t prevalentai/sds-health-dashboard-frontend:1.0.0 .
docker push prevalentai/sds-health-dashboard-frontend:1.0.0
```

## Helm Deployment

### Basic Deployment

```bash
cd components/sds-health-dashboard/helm
helm install sds-health-dashboard . \
  --namespace sds-monitoring \
  --create-namespace
```

### Environment-Specific Deployment

Create environment-specific values files:

#### Development Environment (`values-dev.yaml`)

```yaml
config:
  environments:
    pe-dev:
      namespace: "sds-pe-dev"
      domain_url: "https://dev.sds.example.com"
      enabled: true

ingress:
  hosts:
    - host: health-dashboard-dev.sds.example.com
      paths:
        - path: /
          pathType: Prefix
          service:
            name: sds-health-dashboard-frontend
            port: 80

backend:
  replicaCount: 1
  resources:
    requests:
      cpu: 50m
      memory: 128Mi
    limits:
      cpu: 200m
      memory: 256Mi

frontend:
  replicaCount: 1
  resources:
    requests:
      cpu: 25m
      memory: 64Mi
    limits:
      cpu: 100m
      memory: 128Mi
```

#### Production Environment (`values-prod.yaml`)

```yaml
config:
  environments:
    aws-ite:
      namespace: "sds-aws-ite"
      domain_url: "https://ite.sds.example.com"
      enabled: true
    aws-fte:
      namespace: "sds-aws-fte"
      domain_url: "https://fte.sds.example.com"
      enabled: true

ingress:
  hosts:
    - host: health-dashboard.sds.example.com
      paths:
        - path: /
          pathType: Prefix
          service:
            name: sds-health-dashboard-frontend
            port: 80
  tls:
    - secretName: sds-health-dashboard-tls
      hosts:
        - health-dashboard.sds.example.com

backend:
  replicaCount: 3
  resources:
    requests:
      cpu: 100m
      memory: 256Mi
    limits:
      cpu: 500m
      memory: 512Mi

frontend:
  replicaCount: 3
  resources:
    requests:
      cpu: 50m
      memory: 128Mi
    limits:
      cpu: 200m
      memory: 256Mi

autoscaling:
  backend:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
  frontend:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70

monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
```

Deploy with environment-specific values:

```bash
# Development
helm install sds-health-dashboard . \
  --namespace sds-monitoring \
  --create-namespace \
  -f values-dev.yaml

# Production
helm install sds-health-dashboard . \
  --namespace sds-monitoring \
  --create-namespace \
  -f values-prod.yaml
```

## Configuration

### Environment Configuration

The dashboard supports multiple environment configurations. Each environment should be configured with:

- `namespace`: Kubernetes namespace for the environment
- `domain_url`: Base URL for external health checks
- `enabled`: Whether to include this environment in health checks

### RBAC Configuration

The dashboard requires the following Kubernetes permissions:

```yaml
rules:
  - apiGroups: [""]
    resources: ["pods", "services", "secrets", "persistentvolumeclaims"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["apps"]
    resources: ["deployments", "replicasets"]
    verbs: ["get", "list", "watch"]
```

### Network Policies

If using network policies, ensure the following traffic is allowed:

```yaml
# Allow frontend to backend communication
- from:
  - podSelector:
      matchLabels:
        app.kubernetes.io/component: frontend
  to:
  - podSelector:
      matchLabels:
        app.kubernetes.io/component: backend
  ports:
  - protocol: TCP
    port: 8000

# Allow backend to access Kubernetes API
- from:
  - podSelector:
      matchLabels:
        app.kubernetes.io/component: backend
  to: []
  ports:
  - protocol: TCP
    port: 443
```

## Monitoring and Observability

### Prometheus Metrics

The backend exposes metrics at `/metrics` endpoint. Enable ServiceMonitor for automatic scraping:

```yaml
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    interval: 30s
    scrapeTimeout: 10s
```

### Health Checks

Both frontend and backend expose health check endpoints:

- Backend: `GET /health`
- Frontend: `GET /health`

### Logging

Configure log levels using environment variables:

```yaml
backend:
  env:
    - name: LOG_LEVEL
      value: "INFO"  # DEBUG, INFO, WARNING, ERROR
```

## Troubleshooting

### Common Issues

1. **Backend can't access Kubernetes API**
   - Check RBAC permissions
   - Verify service account is correctly configured
   - Ensure network policies allow API access

2. **Frontend can't reach backend**
   - Check service configuration
   - Verify ingress routing
   - Check network policies

3. **Health checks failing**
   - Verify environment configurations
   - Check domain URLs are accessible
   - Ensure kubectl is available in backend pods

4. **High memory usage**
   - Adjust cache settings
   - Reduce concurrent check limits
   - Increase resource limits

### Debug Commands

```bash
# Check pod status
kubectl get pods -n sds-monitoring -l app.kubernetes.io/name=sds-health-dashboard

# View backend logs
kubectl logs -n sds-monitoring deployment/sds-health-dashboard-backend

# View frontend logs
kubectl logs -n sds-monitoring deployment/sds-health-dashboard-frontend

# Check service endpoints
kubectl get endpoints -n sds-monitoring

# Test backend health
kubectl port-forward -n sds-monitoring svc/sds-health-dashboard-backend 8000:8000
curl http://localhost:8000/health

# Test frontend health
kubectl port-forward -n sds-monitoring svc/sds-health-dashboard-frontend 8080:80
curl http://localhost:8080/health
```

## Upgrading

### Helm Upgrade

```bash
helm upgrade sds-health-dashboard . \
  --namespace sds-monitoring \
  -f values-prod.yaml
```

### Rolling Back

```bash
# List releases
helm history sds-health-dashboard -n sds-monitoring

# Rollback to previous version
helm rollback sds-health-dashboard 1 -n sds-monitoring
```

## Security Considerations

1. **Use non-root containers**: Both images run as non-root users
2. **Read-only root filesystem**: Containers use read-only root filesystems
3. **Network policies**: Implement network policies to restrict traffic
4. **RBAC**: Use minimal required permissions
5. **TLS**: Enable TLS for ingress traffic
6. **Image scanning**: Scan images for vulnerabilities before deployment

## Performance Tuning

### Backend Optimization

```yaml
backend:
  env:
    - name: MAX_CONCURRENT_CHECKS
      value: "20"
    - name: CACHE_DURATION_SECONDS
      value: "300"
    - name: GLOBAL_TIMEOUT_SECONDS
      value: "30"
```

### Frontend Optimization

```yaml
frontend:
  resources:
    requests:
      cpu: 50m
      memory: 128Mi
    limits:
      cpu: 200m
      memory: 256Mi
```

### Database Considerations

The dashboard is stateless and doesn't require a database. All data is fetched in real-time from the Kubernetes API and external services.
