/**
 * Unit tests for HealthStatusIcon component
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import HealthStatusIcon from '../HealthStatusIcon';
import { HealthStatus } from '../../types';

describe('HealthStatusIcon', () => {
  test('renders healthy status icon', () => {
    render(<HealthStatusIcon status={HealthStatus.HEALTHY} />);
    
    const icon = screen.getByRole('img', { hidden: true });
    expect(icon).toBeInTheDocument();
    
    // Check tooltip
    const tooltip = screen.getByLabelText('Healthy');
    expect(tooltip).toBeInTheDocument();
  });

  test('renders unhealthy status icon', () => {
    render(<HealthStatusIcon status={HealthStatus.UNHEALTHY} />);
    
    const icon = screen.getByRole('img', { hidden: true });
    expect(icon).toBeInTheDocument();
    
    // Check tooltip
    const tooltip = screen.getByLabelText('Unhealthy');
    expect(tooltip).toBeInTheDocument();
  });

  test('renders warning status icon', () => {
    render(<HealthStatusIcon status={HealthStatus.WARNING} />);
    
    const icon = screen.getByRole('img', { hidden: true });
    expect(icon).toBeInTheDocument();
    
    // Check tooltip
    const tooltip = screen.getByLabelText('Warning');
    expect(tooltip).toBeInTheDocument();
  });

  test('renders unknown status icon', () => {
    render(<HealthStatusIcon status={HealthStatus.UNKNOWN} />);
    
    const icon = screen.getByRole('img', { hidden: true });
    expect(icon).toBeInTheDocument();
    
    // Check tooltip
    const tooltip = screen.getByLabelText('Unknown');
    expect(tooltip).toBeInTheDocument();
  });

  test('renders without tooltip when showTooltip is false', () => {
    render(<HealthStatusIcon status={HealthStatus.HEALTHY} showTooltip={false} />);
    
    const icon = screen.getByRole('img', { hidden: true });
    expect(icon).toBeInTheDocument();
    
    // Tooltip should not be present
    expect(screen.queryByLabelText('Healthy')).not.toBeInTheDocument();
  });

  test('applies correct size styling', () => {
    const { rerender } = render(<HealthStatusIcon status={HealthStatus.HEALTHY} size="small" />);
    
    let icon = screen.getByRole('img', { hidden: true });
    expect(icon.parentElement).toHaveStyle({ fontSize: '1rem' });
    
    rerender(<HealthStatusIcon status={HealthStatus.HEALTHY} size="large" />);
    icon = screen.getByRole('img', { hidden: true });
    expect(icon.parentElement).toHaveStyle({ fontSize: '2rem' });
    
    rerender(<HealthStatusIcon status={HealthStatus.HEALTHY} size="medium" />);
    icon = screen.getByRole('img', { hidden: true });
    expect(icon.parentElement).toHaveStyle({ fontSize: '1.5rem' });
  });
});
