/**
 * Unit tests for DashboardSummary component
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import DashboardSummary from '../DashboardSummary';
import { HealthCheckSummary } from '../../types';

const mockSummary: HealthCheckSummary = {
  total_checks: 10,
  healthy_count: 7,
  unhealthy_count: 2,
  warning_count: 1,
  last_updated: '2024-01-15T10:30:00Z',
  environment: 'test-env'
};

describe('DashboardSummary', () => {
  test('renders summary with correct statistics', () => {
    render(<DashboardSummary summary={mockSummary} />);
    
    // Check overall health percentage (7/10 = 70%)
    expect(screen.getByText('70%')).toBeInTheDocument();
    
    // Check individual counts
    expect(screen.getByText('10')).toBeInTheDocument(); // Total checks
    expect(screen.getByText('7')).toBeInTheDocument();  // Healthy
    expect(screen.getByText('2')).toBeInTheDocument();  // Unhealthy
    expect(screen.getByText('1')).toBeInTheDocument();  // Warning
    
    // Check labels
    expect(screen.getByText('Total Checks')).toBeInTheDocument();
    expect(screen.getByText('Healthy')).toBeInTheDocument();
    expect(screen.getByText('Unhealthy')).toBeInTheDocument();
    expect(screen.getByText('Warnings')).toBeInTheDocument();
  });

  test('displays environment chip when environment is provided', () => {
    render(<DashboardSummary summary={mockSummary} />);
    
    expect(screen.getByText('Environment: test-env')).toBeInTheDocument();
  });

  test('does not display environment chip when environment is not provided', () => {
    const summaryWithoutEnv = { ...mockSummary, environment: undefined };
    render(<DashboardSummary summary={summaryWithoutEnv} />);
    
    expect(screen.queryByText(/Environment:/)).not.toBeInTheDocument();
  });

  test('displays formatted last updated timestamp', () => {
    render(<DashboardSummary summary={mockSummary} />);
    
    // Should display formatted date
    expect(screen.getByText(/Last updated:/)).toBeInTheDocument();
  });

  test('shows loading state when loading prop is true', () => {
    render(<DashboardSummary summary={mockSummary} loading={true} />);
    
    // Should show progress indicators instead of content
    const progressBars = screen.getAllByRole('progressbar');
    expect(progressBars.length).toBeGreaterThan(0);
  });

  test('calculates health percentage correctly for edge cases', () => {
    // Test 100% healthy
    const perfectSummary: HealthCheckSummary = {
      total_checks: 5,
      healthy_count: 5,
      unhealthy_count: 0,
      warning_count: 0,
      last_updated: '2024-01-15T10:30:00Z'
    };
    
    const { rerender } = render(<DashboardSummary summary={perfectSummary} />);
    expect(screen.getByText('100%')).toBeInTheDocument();
    
    // Test 0% healthy
    const failedSummary: HealthCheckSummary = {
      total_checks: 3,
      healthy_count: 0,
      unhealthy_count: 3,
      warning_count: 0,
      last_updated: '2024-01-15T10:30:00Z'
    };
    
    rerender(<DashboardSummary summary={failedSummary} />);
    expect(screen.getByText('0%')).toBeInTheDocument();
    
    // Test no checks
    const emptySummary: HealthCheckSummary = {
      total_checks: 0,
      healthy_count: 0,
      unhealthy_count: 0,
      warning_count: 0,
      last_updated: '2024-01-15T10:30:00Z'
    };
    
    rerender(<DashboardSummary summary={emptySummary} />);
    expect(screen.getByText('0%')).toBeInTheDocument();
  });

  test('applies correct color based on health percentage', () => {
    // Test success color (>= 90%)
    const excellentSummary: HealthCheckSummary = {
      total_checks: 10,
      healthy_count: 9,
      unhealthy_count: 1,
      warning_count: 0,
      last_updated: '2024-01-15T10:30:00Z'
    };
    
    const { rerender } = render(<DashboardSummary summary={excellentSummary} />);
    const percentage = screen.getByText('90%');
    expect(percentage).toHaveClass('MuiTypography-root');
    
    // Test warning color (70-89%)
    const warningLevelSummary: HealthCheckSummary = {
      total_checks: 10,
      healthy_count: 8,
      unhealthy_count: 2,
      warning_count: 0,
      last_updated: '2024-01-15T10:30:00Z'
    };
    
    rerender(<DashboardSummary summary={warningLevelSummary} />);
    expect(screen.getByText('80%')).toBeInTheDocument();
    
    // Test error color (< 70%)
    const poorSummary: HealthCheckSummary = {
      total_checks: 10,
      healthy_count: 6,
      unhealthy_count: 4,
      warning_count: 0,
      last_updated: '2024-01-15T10:30:00Z'
    };
    
    rerender(<DashboardSummary summary={poorSummary} />);
    expect(screen.getByText('60%')).toBeInTheDocument();
  });
});
