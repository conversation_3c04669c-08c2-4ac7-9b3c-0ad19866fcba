const set = require('regenerate')(0x37F, 0x384, 0x386, 0x38C, 0x1DBF, 0x1F59, 0x1F5B, 0x1F5D, 0x2126, 0xAB65, 0x101A0);
set.addRange(0x370, 0x373).addRange(0x375, 0x377).addRange(0x37A, 0x37D).addRange(0x388, 0x38A).addRange(0x38E, 0x3A1).addRange(0x3A3, 0x3E1).addRange(0x3F0, 0x3FF).addRange(0x1D26, 0x1D2A).addRange(0x1D5D, 0x1D61).addRange(0x1D66, 0x1D6A).addRange(0x1F00, 0x1F15).addRange(0x1F18, 0x1F1D).addRange(0x1F20, 0x1F45).addRange(0x1F48, 0x1F4D).addRange(0x1F50, 0x1F57).addRange(0x1F5F, 0x1F7D).addRange(0x1F80, 0x1FB4).addRange(0x1FB6, 0x1FC4).addRange(0x1FC6, 0x1FD3).addRange(0x1FD6, 0x1FDB).addRange(0x1FDD, 0x1FEF).addRange(0x1FF2, 0x1FF4).addRange(0x1FF6, 0x1FFE).addRange(0x10140, 0x1018E).addRange(0x1D200, 0x1D245);
exports.characters = set;
