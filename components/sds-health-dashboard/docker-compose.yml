version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - LOG_LEVEL=DEBUG
      - HEALTH_DASHBOARD_CONFIG=/app/config/config.yaml
    volumes:
      - ./config:/app/config:ro
      - ./backend:/app:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - health-dashboard

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - health-dashboard

  # Development backend with hot reload
  backend-dev:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: dev-install
    ports:
      - "8001:8000"
    environment:
      - LOG_LEVEL=DEBUG
      - HEALTH_DASHBOARD_CONFIG=/app/config/config.yaml
    volumes:
      - ./config:/app/config:ro
      - ./backend:/app
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    networks:
      - health-dashboard
    profiles:
      - dev

  # Development frontend with hot reload
  frontend-dev:
    image: node:18-alpine
    working_dir: /app
    ports:
      - "3001:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8001
      - CHOKIDAR_USEPOLLING=true
    volumes:
      - ./frontend:/app
      - /app/node_modules
    command: sh -c "npm install && npm start"
    networks:
      - health-dashboard
    profiles:
      - dev

  # Nginx reverse proxy for development
  nginx:
    image: nginx:alpine
    ports:
      - "8080:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - backend
      - frontend
    networks:
      - health-dashboard
    profiles:
      - proxy

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - health-dashboard
    profiles:
      - cache

  # Prometheus for monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - health-dashboard
    profiles:
      - monitoring

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3002:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - health-dashboard
    profiles:
      - monitoring

networks:
  health-dashboard:
    driver: bridge

volumes:
  redis_data:
  prometheus_data:
  grafana_data:
