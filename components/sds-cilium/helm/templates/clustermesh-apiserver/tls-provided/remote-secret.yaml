{{- if and .Values.clustermesh.useAPIServer (not .Values.clustermesh.apiserver.tls.auto.enabled) }}
{{- if .Values.clustermesh.apiserver.tls.enableSecrets }}
apiVersion: v1
kind: Secret
metadata:
  name: clustermesh-apiserver-remote-cert
  namespace: {{ .Release.Namespace }}
  {{- with .Values.clustermesh.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  ca.crt: {{ .Values.tls.ca.cert }}
  tls.crt: {{ .Values.clustermesh.apiserver.tls.remote.cert | required "missing clustermesh.apiserver.tls.remote.cert" }}
  tls.key: {{ .Values.clustermesh.apiserver.tls.remote.key  | required "missing clustermesh.apiserver.tls.remote.key"  }}
{{- end }}
{{- end }}
