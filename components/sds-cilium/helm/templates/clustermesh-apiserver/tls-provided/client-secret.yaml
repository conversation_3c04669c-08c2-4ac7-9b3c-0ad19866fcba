{{- if and .Values.externalWorkloads.enabled (not .Values.clustermesh.apiserver.tls.auto.enabled) }}
{{- if .Values.clustermesh.apiserver.tls.enableSecrets }}
apiVersion: v1
kind: Secret
metadata:
  name: clustermesh-apiserver-client-cert
  namespace: {{ .Release.Namespace }}
  {{- with .Values.clustermesh.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  ca.crt: {{ .Values.tls.ca.cert }}
  tls.crt: {{ .Values.clustermesh.apiserver.tls.client.cert | required "missing clustermesh.apiserver.tls.client.cert" }}
  tls.key: {{ .Values.clustermesh.apiserver.tls.client.key  | required "missing clustermesh.apiserver.tls.client.key" }}
{{- end }}
{{- end }}
