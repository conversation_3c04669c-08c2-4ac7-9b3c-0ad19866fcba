{{- if and (or .Values.externalWorkloads.enabled .Values.clustermesh.useAPIServer) (not .Values.clustermesh.apiserver.tls.auto.enabled) }}
{{- if .Values.clustermesh.apiserver.tls.enableSecrets }}
apiVersion: v1
kind: Secret
metadata:
  name: clustermesh-apiserver-server-cert
  namespace: {{ .Release.Namespace }}
  {{- with .Values.clustermesh.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  ca.crt: {{ .Values.tls.ca.cert }}
  tls.crt: {{ .Values.clustermesh.apiserver.tls.server.cert | required "missing clustermesh.apiserver.tls.server.cert" }}
  tls.key: {{ .Values.clustermesh.apiserver.tls.server.key  | required "missing clustermesh.apiserver.tls.server.key"  }}
{{- end }}
{{- end }}
