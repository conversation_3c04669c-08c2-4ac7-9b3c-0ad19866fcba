{{- if and (or .Values.externalWorkloads.enabled .Values.clustermesh.useAPIServer) (not .Values.clustermesh.apiserver.tls.auto.enabled) }}
{{- if .Values.clustermesh.apiserver.tls.enableSecrets }}
apiVersion: v1
kind: Secret
metadata:
  name: clustermesh-apiserver-admin-cert
  namespace: {{ .Release.Namespace }}
  {{- with .Values.clustermesh.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  ca.crt: {{ .Values.tls.ca.cert }}
  tls.crt: {{ .Values.clustermesh.apiserver.tls.admin.cert | required "missing clustermesh.apiserver.tls.admin.cert" }}
  tls.key: {{ .Values.clustermesh.apiserver.tls.admin.key  | required "missing clustermesh.apiserver.tls.admin.key"  }}
{{- end }}
{{- end }}
