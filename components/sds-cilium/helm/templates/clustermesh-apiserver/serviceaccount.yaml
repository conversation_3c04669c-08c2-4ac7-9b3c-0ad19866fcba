{{- if and (or .Values.externalWorkloads.enabled  .Values.clustermesh.useAPIServer) .Values.serviceAccounts.clustermeshApiserver.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.serviceAccounts.clustermeshApiserver.name | quote }}
  namespace: {{ .Release.Namespace }}
  {{- if or .Values.serviceAccounts.clustermeshApiserver.annotations .Values.clustermesh.annotations }}
  annotations:
    {{- with .Values.clustermesh.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.serviceAccounts.clustermeshApiserver.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
{{- end }}
