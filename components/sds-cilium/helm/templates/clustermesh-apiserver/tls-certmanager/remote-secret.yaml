{{- if and .Values.clustermesh.useAPIServer .Values.clustermesh.apiserver.tls.auto.enabled (eq .Values.clustermesh.apiserver.tls.auto.method "certmanager") }}
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: clustermesh-apiserver-remote-cert
  namespace: {{ .Release.Namespace }}
  {{- with .Values.clustermesh.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  issuerRef:
    {{- toYaml .Values.clustermesh.apiserver.tls.auto.certManagerIssuerRef | nindent 4 }}
  secretName: clustermesh-apiserver-remote-cert
  commonName: {{ include "clustermesh-apiserver-generate-certs.remote-common-name" . }}
  duration: {{ printf "%dh0m0s" (mul .Values.clustermesh.apiserver.tls.auto.certValidityDuration 24) }}
{{- end }}
