{{- if and (or .Values.externalWorkloads.enabled .Values.clustermesh.useAPIServer) .Values.clustermesh.apiserver.tls.auto.enabled (eq .Values.clustermesh.apiserver.tls.auto.method "certmanager") }}
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: clustermesh-apiserver-server-cert
  namespace: {{ .Release.Namespace }}
  {{- with .Values.clustermesh.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  issuerRef:
    {{- toYaml .Values.clustermesh.apiserver.tls.auto.certManagerIssuerRef | nindent 4 }}
  secretName: clustermesh-apiserver-server-cert
  commonName: clustermesh-apiserver.cilium.io
  dnsNames:
  - clustermesh-apiserver.cilium.io
  - "*.mesh.cilium.io"
  - "clustermesh-apiserver.{{ .Release.Namespace }}.svc"
  {{- range $dns := .Values.clustermesh.apiserver.tls.server.extraDnsNames }}
  - {{ $dns | quote }}
  {{- end }}
  ipAddresses:
  - "127.0.0.1"
  - "::1"
  {{- range $ip := .Values.clustermesh.apiserver.tls.server.extraIpAddresses }}
  - {{ $ip | quote }}
  {{- end }}
  duration: {{ printf "%dh0m0s" (mul .Values.clustermesh.apiserver.tls.auto.certValidityDuration 24) }}
{{- end }}
