{{- if and (or .Values.externalWorkloads.enabled .Values.clustermesh.useAPIServer) .Values.clustermesh.apiserver.tls.auto.enabled (eq .Values.clustermesh.apiserver.tls.auto.method "helm") }}
{{- $_ := include "cilium.ca.setup" . -}}
{{- $cn := "clustermesh-apiserver.cilium.io" }}
{{- $ip := concat (list "127.0.0.1" "::1") .Values.clustermesh.apiserver.tls.server.extraIpAddresses }}
{{- $dns := concat (list $cn "*.mesh.cilium.io" (printf "clustermesh-apiserver.%s.svc" .Release.Namespace)) .Values.clustermesh.apiserver.tls.server.extraDnsNames }}
{{- $cert := genSignedCert $cn $ip $dns (.Values.clustermesh.apiserver.tls.auto.certValidityDuration | int) .commonCA -}}
---
apiVersion: v1
kind: Secret
metadata:
  name: clustermesh-apiserver-server-cert
  namespace: {{ .Release.Namespace }}
  {{- with .Values.clustermesh.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  ca.crt:  {{ .commonCA.Cert | b64enc }}
  tls.crt: {{ $cert.Cert | b64enc }}
  tls.key: {{ $cert.Key  | b64enc }}
{{- end }}
