{{- if and .Values.externalWorkloads.enabled .Values.clustermesh.apiserver.tls.auto.enabled (eq .Values.clustermesh.apiserver.tls.auto.method "helm") }}
{{- $_ := include "cilium.ca.setup" . -}}
{{- $cn := "externalworkload" }}
{{- $cert := genSignedCert $cn nil nil (.Values.clustermesh.apiserver.tls.auto.certValidityDuration | int) .commonCA -}}
---
apiVersion: v1
kind: Secret
metadata:
  name: clustermesh-apiserver-client-cert
  namespace: {{ .Release.Namespace }}
  {{- with .Values.clustermesh.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  ca.crt:  {{ .commonCA.Cert | b64enc }}
  tls.crt: {{ $cert.Cert | b64enc }}
  tls.key: {{ $cert.Key  | b64enc }}
{{- end }}
