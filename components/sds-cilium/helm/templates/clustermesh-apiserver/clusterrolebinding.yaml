{{- if and (or .Values.externalWorkloads.enabled .Values.clustermesh.useAPIServer) .Values.serviceAccounts.clustermeshApiserver.create .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: clustermesh-apiserver
  labels:
    app.kubernetes.io/part-of: cilium
  {{- with .Values.clustermesh.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: clustermesh-apiserver
subjects:
- kind: ServiceAccount
  name: {{ .Values.serviceAccounts.clustermeshApiserver.name | quote }}
  namespace: {{ .Release.Namespace }}
{{- end }}
