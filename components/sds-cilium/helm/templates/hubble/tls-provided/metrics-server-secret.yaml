{{- if and .Values.agent .Values.hubble.enabled .Values.hubble.metrics.tls.enabled (not .Values.hubble.tls.auto.enabled) (not .Values.hubble.metrics.tls.server.existingSecret) }}
apiVersion: v1
kind: Secret
metadata:
  name: hubble-metrics-server-certs
  namespace: {{ .Release.Namespace }}
  {{- with .Values.hubble.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  ca.crt:  {{ .Values.tls.ca.cert }}
  tls.crt: {{ .Values.hubble.metrics.tls.server.cert | required "missing hubble.metrics.tls.server.cert" }}
  tls.key: {{ .Values.hubble.metrics.tls.server.key  | required "missing hubble.metrics.tls.server.key"  }}
{{- end }}
