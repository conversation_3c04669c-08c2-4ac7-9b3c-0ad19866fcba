{{- if and .Values.agent .Values.hubble.enabled .Values.hubble.tls.enabled (not .Values.hubble.tls.auto.enabled) (not .Values.hubble.tls.server.existingSecret) }}
apiVersion: v1
kind: Secret
metadata:
  name: hubble-server-certs
  namespace: {{ .Release.Namespace }}
  {{- with .Values.hubble.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  ca.crt:  {{ .Values.tls.ca.cert }}
  tls.crt: {{ .Values.hubble.tls.server.cert | required "missing hubble.tls.server.cert" }}
  tls.key: {{ .Values.hubble.tls.server.key  | required "missing hubble.tls.server.key"  }}
{{- end }}
