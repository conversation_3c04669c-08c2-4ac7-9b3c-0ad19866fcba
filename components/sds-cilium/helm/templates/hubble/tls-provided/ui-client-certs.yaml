{{- if and .Values.hubble.enabled .Values.hubble.tls.enabled (not .Values.hubble.tls.auto.enabled) .Values.hubble.ui.enabled .Values.hubble.relay.enabled .Values.hubble.relay.tls.server.enabled (not .Values.hubble.ui.tls.client.existingSecret) }}
apiVersion: v1
kind: Secret
metadata:
  name: hubble-ui-client-certs
  namespace: {{ .Release.Namespace }}
  {{- with .Values.hubble.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  ca.crt:  {{ .Values.tls.ca.cert }}
  tls.crt: {{ .Values.hubble.ui.tls.client.cert | required "missing hubble.ui.tls.client.cert" }}
  tls.key: {{ .Values.hubble.ui.tls.client.key  | required "missing hubble.ui.tls.client.key"  }}
{{- end }}
