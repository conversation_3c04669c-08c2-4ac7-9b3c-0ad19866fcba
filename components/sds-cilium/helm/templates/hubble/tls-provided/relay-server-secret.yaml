{{- if and .Values.hubble.enabled .Values.hubble.tls.enabled (not .Values.hubble.tls.auto.enabled) .Values.hubble.relay.enabled .Values.hubble.relay.tls.server.enabled (not .Values.hubble.relay.tls.server.existingSecret) }}
apiVersion: v1
kind: Secret
metadata:
  name: hubble-relay-server-certs
  namespace: {{ .Release.Namespace }}
  {{- with .Values.hubble.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  ca.crt:  {{ .Values.tls.ca.cert }}
  tls.crt: {{ .Values.hubble.relay.tls.server.cert | required "missing hubble.relay.tls.server.cert" }}
  tls.key: {{ .Values.hubble.relay.tls.server.key  | required "missing hubble.relay.tls.server.key"  }}
{{- end }}
