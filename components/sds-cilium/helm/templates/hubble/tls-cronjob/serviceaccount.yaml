{{- if and .Values.hubble.enabled .Values.hubble.tls.enabled .Values.hubble.tls.auto.enabled (eq .Values.hubble.tls.auto.method "cronJob") .Values.serviceAccounts.hubblecertgen.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.serviceAccounts.hubblecertgen.name | quote }}
  namespace: {{ .Release.Namespace }}
  {{- if or .Values.serviceAccounts.hubblecertgen.annotations .Values.hubble.annotations }}
  annotations:
    {{- with .Values.hubble.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.serviceAccounts.hubblecertgen.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
{{- end }}
