{{- if and .Values.hubble.enabled .Values.hubble.tls.enabled .Values.hubble.tls.auto.enabled (eq .Values.hubble.tls.auto.method "cronJob") }}
---
apiVersion: batch/v1
kind: Job
metadata:
  name: hubble-generate-certs
  namespace: {{ .Release.Namespace }}
  labels:
    k8s-app: hubble-generate-certs
    app.kubernetes.io/name: hubble-generate-certs
    app.kubernetes.io/part-of: cilium
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    {{- with .Values.certgen.annotations.job }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.hubble.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
{{ include "hubble-generate-certs.job.spec" . }}
{{- end }}
