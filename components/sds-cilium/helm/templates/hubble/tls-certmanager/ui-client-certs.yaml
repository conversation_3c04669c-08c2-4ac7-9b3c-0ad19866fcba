{{- if and .Values.hubble.enabled .Values.hubble.tls.enabled .Values.hubble.tls.auto.enabled (eq .Values.hubble.tls.auto.method "certmanager") .Values.hubble.ui.enabled .Values.hubble.relay.enabled .Values.hubble.relay.tls.server.enabled }}
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: hubble-ui-client-certs
  namespace: {{ .Release.Namespace }}
  {{- with .Values.hubble.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  issuerRef:
    {{- toYaml .Values.hubble.tls.auto.certManagerIssuerRef | nindent 4 }}
  secretName: hubble-ui-client-certs
  commonName: "*.hubble-ui.cilium.io"
  dnsNames:
  - "*.hubble-ui.cilium.io"
  duration: {{ printf "%dh0m0s" (mul .Values.hubble.tls.auto.certValidityDuration 24) }}
  privateKey:
    rotationPolicy: Always
  isCA: false
  usages:
    - signing
    - key encipherment
    - client auth
{{- end }}
