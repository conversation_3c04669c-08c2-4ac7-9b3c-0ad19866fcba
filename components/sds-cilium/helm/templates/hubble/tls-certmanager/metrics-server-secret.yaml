{{- if and .Values.hubble.enabled .Values.hubble.metrics.tls.enabled .Values.hubble.tls.auto.enabled (eq .Values.hubble.tls.auto.method "certmanager") }}
{{- $cn := list (.Values.cluster.name | replace "." "-") "hubble-metrics.cilium.io" | join "." }}
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: hubble-metrics-server-certs
  namespace: {{ .Release.Namespace }}
  {{- with .Values.hubble.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  issuerRef:
    {{- toYaml .Values.hubble.tls.auto.certManagerIssuerRef | nindent 4 }}
  secretName: hubble-metrics-server-certs
  commonName: {{ $cn | quote }}
  dnsNames:
  - {{ $cn | quote }}
  {{- range $dns := .Values.hubble.metrics.tls.server.extraDnsNames }}
  - {{ $dns | quote }}
  {{- end }}
  {{- if .Values.hubble.metrics.tls.server.extraIpAddresses }}
  ipAddresses:
  {{- range $ip := .Values.hubble.metrics.tls.server.extraIpAddresses }}
  - {{ $ip | quote }}
  {{- end }}
  {{- end }}
  duration: {{ printf "%dh0m0s" (mul .Values.hubble.tls.auto.certValidityDuration 24) }}
  privateKey:
    rotationPolicy: Always
  isCA: false
  usages:
    - signing
    - key encipherment
    - server auth
{{- end }}
