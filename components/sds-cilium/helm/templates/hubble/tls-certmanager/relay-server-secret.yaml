{{- if and .Values.hubble.enabled .Values.hubble.tls.enabled .Values.hubble.tls.auto.enabled (eq .Values.hubble.tls.auto.method "certmanager") .Values.hubble.relay.enabled .Values.hubble.relay.tls.server.enabled }}
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: hubble-relay-server-certs
  namespace: {{ .Release.Namespace }}
  {{- with .Values.hubble.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  issuerRef:
    {{- toYaml .Values.hubble.tls.auto.certManagerIssuerRef | nindent 4 }}
  secretName: hubble-relay-server-certs
  commonName: "*.hubble-relay.cilium.io"
  dnsNames:
  - "*.hubble-relay.cilium.io"
  {{- range $dns := .Values.hubble.relay.tls.server.extraDnsNames }}
  - {{ $dns | quote }}
  {{- end }}
  {{- if .Values.hubble.relay.tls.server.extraIpAddresses }}
  ipAddresses:
  {{- range $ip := .Values.hubble.relay.tls.server.extraIpAddresses }}
  - {{ $ip | quote }}
  {{- end }}
  {{- end }}
  duration: {{ printf "%dh0m0s" (mul .Values.hubble.tls.auto.certValidityDuration 24) }}
  privateKey:
    rotationPolicy: Always
  isCA: false
  usages:
    - signing
    - key encipherment
    - server auth
{{- end }}
