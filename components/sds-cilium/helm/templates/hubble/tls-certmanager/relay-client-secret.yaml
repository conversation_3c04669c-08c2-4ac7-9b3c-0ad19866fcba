{{- if and .Values.hubble.enabled .Values.hubble.tls.enabled .Values.hubble.tls.auto.enabled (eq .Values.hubble.tls.auto.method "certmanager") .Values.hubble.relay.enabled }}
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: hubble-relay-client-certs
  namespace: {{ .Release.Namespace }}
  {{- with .Values.hubble.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  issuerRef:
    {{- toYaml .Values.hubble.tls.auto.certManagerIssuerRef | nindent 4 }}
  secretName: hubble-relay-client-certs
  commonName: "*.hubble-relay.cilium.io"
  dnsNames:
  - "*.hubble-relay.cilium.io"
  duration: {{ printf "%dh0m0s" (mul .Values.hubble.tls.auto.certValidityDuration 24) }}
  privateKey:
    rotationPolicy: Always
  isCA: false
  usages:
    - signing
    - key encipherment
    - client auth
{{- end }}
