{{- if and .Values.hubble.enabled .Values.hubble.tls.enabled .Values.hubble.tls.auto.enabled (eq .Values.hubble.tls.auto.method "helm") .Values.hubble.relay.enabled }}
{{- $_ := include "cilium.ca.setup" . -}}
{{- $cn := "*.hubble-relay.cilium.io" }}
{{- $dns := list $cn }}
{{- $cert := genSignedCert $cn nil $dns (.Values.hubble.tls.auto.certValidityDuration | int) .commonCA -}}
---
apiVersion: v1
kind: Secret
metadata:
  name: hubble-relay-client-certs
  namespace: {{ .Release.Namespace }}
  {{- with .Values.hubble.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  ca.crt:  {{ .commonCA.Cert | b64enc }}
  tls.crt: {{ $cert.Cert | b64enc }}
  tls.key: {{ $cert.Key  | b64enc }}
{{- end }}
