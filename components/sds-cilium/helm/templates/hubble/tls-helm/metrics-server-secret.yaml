{{- if and .Values.agent .Values.hubble.enabled .Values.hubble.metrics.tls.enabled .Values.hubble.tls.auto.enabled (eq .Values.hubble.tls.auto.method "helm") }}
{{- $_ := include "cilium.ca.setup" . -}}
{{- $cn := list (.Values.cluster.name | replace "." "-") "hubble-metrics.cilium.io" | join "." }}
{{- $ip := .Values.hubble.metrics.tls.server.extraIpAddresses }}
{{- $dns := prepend .Values.hubble.metrics.tls.server.extraDnsNames $cn }}
{{- $cert := genSignedCert $cn $ip $dns (.Values.hubble.tls.auto.certValidityDuration | int) .commonCA -}}
---
apiVersion: v1
kind: Secret
metadata:
  name: hubble-metrics-server-certs
  namespace: {{ .Release.Namespace }}
  {{- with .Values.hubble.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  ca.crt:  {{ .commonCA.Cert | b64enc }}
  tls.crt: {{ $cert.Cert | b64enc }}
  tls.key: {{ $cert.Key  | b64enc }}
{{- end }}
