{{- if .Values.hubble.metrics.dashboards.enabled }}
{{- $files := .Files.Glob "files/hubble/dashboards/*.json" }}
{{- range $path, $fileContents := $files }}
{{- $dashboardName := regexReplaceAll "(^.*/)(.*)\\.json$" $path "${2}" }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $dashboardName | trunc 63 | trimSuffix "-" }}
  namespace: {{ $.Values.hubble.metrics.dashboards.namespace | default $.Release.Namespace }}
  labels:
    k8s-app: hubble
    app.kubernetes.io/name: hubble
    app.kubernetes.io/part-of: cilium
    {{- if $.Values.hubble.metrics.dashboards.label }}
    {{ $.Values.hubble.metrics.dashboards.label }}: {{ ternary $.Values.hubble.metrics.dashboards.labelValue "1" (not (empty $.Values.hubble.metrics.dashboards.labelValue)) | quote }}
    {{- end }}
  {{- if or $.Values.hubble.metrics.dashboards.annotations $.Values.hubble.annotations }}
  annotations:
    {{- with $.Values.hubble.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with $.Values.hubble.metrics.dashboards.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
data:
  {{ $dashboardName }}.json: {{ $.Files.Get $path | toJson }}
{{- end }}
{{- end }}
