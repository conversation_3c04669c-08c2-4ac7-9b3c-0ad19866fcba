{{- if and .Values.preflight.enabled .Values.serviceAccounts.preflight.create .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cilium-pre-flight
  {{- with .Values.preflight.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    app.kubernetes.io/part-of: cilium
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cilium-pre-flight
subjects:
- kind: ServiceAccount
  name: {{ .Values.serviceAccounts.preflight.name | quote }} 
  namespace: {{ .Release.Namespace }}
{{- end }}
