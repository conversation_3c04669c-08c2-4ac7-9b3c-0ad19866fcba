{{- $envoyDS := eq (include "envoyDaemonSetEnabled" .) "true" -}}
{{- if and $envoyDS (not .Values.preflight.enabled) .Values.envoy.prometheus.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: cilium-envoy
  namespace: {{ .Release.Namespace }}
  {{- if or (not .Values.envoy.prometheus.serviceMonitor.enabled) .Values.envoy.annotations }}
  annotations:
  {{- if not .Values.envoy.prometheus.serviceMonitor.enabled }}
    prometheus.io/scrape: "true"
    prometheus.io/port: {{ .Values.envoy.prometheus.port | quote }}
  {{- end }}
  {{- with .Values.envoy.annotations }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- end }}
  labels:
    k8s-app: cilium-envoy
    app.kubernetes.io/name: cilium-envoy
    app.kubernetes.io/part-of: cilium
    io.cilium/app: proxy
spec:
  clusterIP: None
  type: ClusterIP
  selector:
    k8s-app: cilium-envoy
  ports:
  - name: envoy-metrics
    port: {{ .Values.envoy.prometheus.port }}
    protocol: TCP
    targetPort: envoy-metrics
{{- end }}
