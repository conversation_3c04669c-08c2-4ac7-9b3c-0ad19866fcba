{{- if and .Values.hubble.enabled .Values.hubble.relay.enabled .Values.hubble.relay.prometheus.enabled .Values.hubble.relay.prometheus.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: hubble-relay
  namespace: {{ .Values.hubble.relay.prometheus.serviceMonitor.namespace | default .Release.Namespace }}
  labels:
    {{- with .Values.hubble.relay.prometheus.serviceMonitor.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- if or .Values.hubble.relay.prometheus.serviceMonitor.annotations .Values.hubble.relay.annotations }}
  annotations:
    {{- with .Values.hubble.relay.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.hubble.relay.prometheus.serviceMonitor.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
spec:
  selector:
    matchLabels:
      k8s-app: hubble-relay
  namespaceSelector:
    matchNames:
    - {{ .Release.Namespace }}
  endpoints:
  - port: metrics
    interval: {{ .Values.hubble.relay.prometheus.serviceMonitor.interval | quote }}
    path: /metrics
    {{- with .Values.hubble.relay.prometheus.serviceMonitor.relabelings }}
    relabelings:
    {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.hubble.relay.prometheus.serviceMonitor.metricRelabelings }}
    metricRelabelings:
    {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end }}
