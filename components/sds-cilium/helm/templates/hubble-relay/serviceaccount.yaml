{{- if and .Values.hubble.enabled .Values.hubble.relay.enabled .Values.serviceAccounts.relay.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.serviceAccounts.relay.name | quote }}
  namespace: {{ .Release.Namespace }}
  {{- if or .Values.serviceAccounts.relay.annotations .Values.hubble.relay.annotations }}
  annotations:
    {{- with .Values.hubble.relay.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.serviceAccounts.relay.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
automountServiceAccountToken: {{ .Values.serviceAccounts.relay.automount }}
{{- end }}
