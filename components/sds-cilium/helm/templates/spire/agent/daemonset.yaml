{{- if and .Values.authentication.mutual.spire.enabled .Values.authentication.mutual.spire.install.enabled -}}
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: spire-agent
  namespace: {{ .Values.authentication.mutual.spire.install.namespace }}
  {{- if or .Values.authentication.mutual.spire.install.server.annotations .Values.authentication.mutual.spire.annotations }}
  annotations:
    {{- with .Values.authentication.mutual.spire.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.authentication.mutual.spire.install.server.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
  labels:
    app: spire-agent
  {{- with .Values.authentication.mutual.spire.install.agent.labels }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels:
      app: spire-agent
  template:
    metadata:
      namespace: {{ .Values.authentication.mutual.spire.install.namespace }}
      labels:
        app: spire-agent
        {{- with .Values.authentication.mutual.spire.install.agent.labels }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      hostPID: true
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      serviceAccountName: {{ .Values.authentication.mutual.spire.install.agent.serviceAccount.name }}
      {{- with .Values.authentication.mutual.spire.install.agent.podSecurityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      initContainers:
        - name: init
          image: {{ include "cilium.image" .Values.authentication.mutual.spire.install.initImage | quote }}
          imagePullPolicy: {{ .Values.authentication.mutual.spire.install.initImage.pullPolicy }}
          command:
            - /bin/sh
            - -c
            - |
              {{- tpl (.Files.Get "files/spire/wait-for-spire.bash") . | nindent 14 }}
      containers:
        - name: spire-agent
          {{- if eq (typeOf .Values.authentication.mutual.spire.install.agent.image) "string" }}
          image: {{ .Values.authentication.mutual.spire.install.agent.image }}
          {{- else }}
          image: {{ include "cilium.image" .Values.authentication.mutual.spire.install.agent.image | quote }}
          imagePullPolicy: {{ .Values.authentication.mutual.spire.install.agent.image.pullPolicy }}
          {{- end }}
          args: ["-config", "/run/spire/config/agent.conf"]
          {{- with .Values.authentication.mutual.spire.install.agent.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: spire-config
              mountPath: /run/spire/config
              readOnly: true
            - name: spire-bundle
              mountPath: /run/spire/bundle
            - name: spire-agent-socket
              mountPath: /run/spire/sockets
              readOnly: false
            - name: spire-agent
              mountPath: /var/run/secrets/tokens
          env:
            - name: MY_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
          livenessProbe:
            httpGet:
              path: /live
              port: 4251
            failureThreshold: 2
            initialDelaySeconds: 15
            periodSeconds: 60
            timeoutSeconds: 3
          readinessProbe:
            httpGet:
              path: /ready
              port: 4251
            initialDelaySeconds: 5
            periodSeconds: 5
      {{- with .Values.authentication.mutual.spire.install.agent.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.authentication.mutual.spire.install.agent.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      tolerations:
        {{- with .Values.authentication.mutual.spire.install.agent.tolerations }}
          {{- toYaml . | trim | nindent 8 }}
        {{- end }}
        - key:  {{ .Values.agentNotReadyTaintKey | default "node.cilium.io/agent-not-ready" }}
          effect: NoSchedule
      volumes:
        - name: spire-config
          configMap:
            name: spire-agent
        - name: spire-bundle
          configMap:
            name: spire-bundle
        - name: spire-agent-socket
          hostPath:
            path: /run/spire/sockets
            type: DirectoryOrCreate
        - name: spire-agent
          projected:
            sources:
            - serviceAccountToken:
                path: spire-agent
                expirationSeconds: 600
                audience: spire-server
{{- end }}
