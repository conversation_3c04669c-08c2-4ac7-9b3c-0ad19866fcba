{{- if and .Values.authentication.mutual.spire.enabled .Values.authentication.mutual.spire.install.enabled .Values.authentication.mutual.spire.install.server.serviceAccount.create -}}
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ .Values.authentication.mutual.spire.install.server.serviceAccount.name }}
  namespace: {{ .Values.authentication.mutual.spire.install.namespace }}
  {{- with .Values.authentication.mutual.spire.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
rules:
# Role (namespace scoped) to be able to push certificate bundles to a configmap
- apiGroups: [ "" ]
  resources: [ "configmaps" ]
  verbs: [ "patch", "get", "list" ]
{{- end }}
