{{- if and .Values.authentication.mutual.spire.enabled .Values.authentication.mutual.spire.install.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: spire-server
  namespace: {{ .Values.authentication.mutual.spire.install.namespace }}
  {{- with .Values.authentication.mutual.spire.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
data:
  server.conf: |
    server {
      bind_address = "0.0.0.0"
      bind_port = "8081"
      socket_path = "/tmp/spire-server/private/api.sock"
      trust_domain = {{ .Values.authentication.mutual.spire.trustDomain | quote }}
      data_dir = "/run/spire/data"
      log_level = "INFO"
      ca_key_type = {{ .Values.authentication.mutual.spire.install.server.ca.keyType | quote }}

      ca_subject = {
        country = [{{ .Values.authentication.mutual.spire.install.server.ca.subject.country | quote }}],
        organization = [{{ .Values.authentication.mutual.spire.install.server.ca.subject.organization | quote }}],
        common_name = {{ .Values.authentication.mutual.spire.install.server.ca.subject.commonName | quote }},
      }

      admin_ids = [
        "spiffe://{{ .Values.authentication.mutual.spire.trustDomain }}/cilium-operator",
      ]
    }

    plugins {
      DataStore "sql" {
        plugin_data {
          database_type = "sqlite3"
          connection_string = "/run/spire/data/datastore.sqlite3"
        }
      }

      NodeAttestor "k8s_psat" {
        plugin_data {
          clusters = {
            {{ .Values.cluster.name | quote }} = {
              use_token_review_api_validation = true
              service_account_allow_list = ["{{ .Values.authentication.mutual.spire.install.namespace}}:{{ .Values.authentication.mutual.spire.install.agent.serviceAccount.name }}"]
            }
          }
        }
      }

      KeyManager "disk" {
        plugin_data {
          keys_path = "/run/spire/data/keys.json"
        }
      }

      Notifier "k8sbundle" {
        plugin_data {
          namespace = {{ .Values.authentication.mutual.spire.install.namespace | quote }}
        }
      }
    }

    health_checks {
      listener_enabled = true
      bind_address = "0.0.0.0"
      bind_port = "8080"
      live_path = "/live"
      ready_path = "/ready"
    }
{{- end }}
