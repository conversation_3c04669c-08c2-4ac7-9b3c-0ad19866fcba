{{- if and .Values.authentication.mutual.spire.enabled .Values.authentication.mutual.spire.install.enabled .Values.authentication.mutual.spire.install.server.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.authentication.mutual.spire.install.server.serviceAccount.name }}
  namespace: {{ .Values.authentication.mutual.spire.install.namespace }}
  {{- with .Values.authentication.mutual.spire.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
