{{- $envoyDS := eq (include "envoyDaemonSetEnabled" .) "true" -}}
{{- if and .Values.agent (not .Values.preflight.enabled) .Values.prometheus.enabled }}
{{- if .Values.prometheus.serviceMonitor.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: cilium-agent
  namespace: {{ .Release.Namespace }}
  {{- with .Values.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    k8s-app: cilium
    app.kubernetes.io/name: cilium-agent
    app.kubernetes.io/part-of: cilium
spec:
  clusterIP: None
  type: ClusterIP
  selector:
    k8s-app: cilium
  ports:
  - name: metrics
    port: {{ .Values.prometheus.port }}
    protocol: TCP
    targetPort: prometheus
  {{- if not $envoyDS }}
  - name: envoy-metrics
    port: {{ .Values.envoy.prometheus.port }}
    protocol: TCP
    targetPort: envoy-metrics
  {{- end }}
{{- else if and .Values.envoy.prometheus.enabled (not $envoyDS) }}
apiVersion: v1
kind: Service
metadata:
  name: cilium-agent
  namespace: {{ .Release.Namespace }}
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: {{ .Values.envoy.prometheus.port | quote }}
  labels:
    k8s-app: cilium
    app.kubernetes.io/name: cilium-agent
    app.kubernetes.io/part-of: cilium
spec:
  clusterIP: None
  type: ClusterIP
  selector:
    k8s-app: cilium
  ports:
  - name: envoy-metrics
    port: {{ .Values.envoy.prometheus.port }}
    protocol: TCP
    targetPort: envoy-metrics
{{- end }}
{{- end }}
