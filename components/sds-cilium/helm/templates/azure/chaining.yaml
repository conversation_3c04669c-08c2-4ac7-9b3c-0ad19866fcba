{{- $cloudProvider := tpl .Values.cloudProvider . -}}
{{- if eq $cloudProvider "azure" }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: cni-configuration
  namespace: kube-system
data:
  cni-config: |-
    {{- if .Values.azureOverlayEnabled }}
    {
      "cniVersion": "0.3.0",
      "name": "azure",
      "plugins": [
        {
          "type": "azure-vnet",
          "mode": "transparent",
          "ipsToRouteViaHost": [
            "*************"
          ],
          "executionMode": "v4swift",
          "ipam": {
            "mode": "v4overlay",
            "type": "azure-cns"
          },
          "dns": {},
          "runtimeConfig": {
            "dns": {}
          },
          "windowsSettings": {}
        },
        {
          "type": "portmap",
          "capabilities": {
            "portMappings": true
          },
          "snat": true
        },
        {
          "type": "cilium-cni",
          "chaining-mode": "generic-veth",
          "enable-debug": true,
          "log-file": "/var/run/cilium/cilium-cni.log"
        }
      ]
    }
    {{- else }}
    {
      "cniVersion": "0.3.0",
      "name": "azure",
      "plugins": [
        {
          "type": "azure-vnet",
          "mode": "transparent",
          "ipsToRouteViaHost": ["*************"],
          "ipam": {
            "type": "azure-vnet-ipam"
          }
        },
        {
          "type": "portmap",
          "capabilities": {
            "portMappings": true
          },
          "snat": true
        },
        {
          "type": "cilium-cni",
          "chaining-mode": "generic-veth",
          "enable-debug": true,
          "log-file": "/var/run/cilium/cilium-cni.log"
        }
      ]
    }
    {{- end }}
{{- end }}
