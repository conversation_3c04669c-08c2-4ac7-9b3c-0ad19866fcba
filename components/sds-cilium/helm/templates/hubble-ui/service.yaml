{{- if and (or .Values.hubble.enabled .Values.hubble.ui.standalone.enabled) .Values.hubble.ui.enabled }}
kind: Service
apiVersion: v1
metadata:
  name: hubble-ui
  namespace: {{ .Release.Namespace }}
  {{- if or .Values.hubble.ui.service.annotations  .Values.hubble.ui.annotations }}
  annotations:
    {{- with .Values.hubble.ui.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.hubble.ui.service.annotations  }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
  labels:
    k8s-app: hubble-ui
    app.kubernetes.io/name: hubble-ui
    app.kubernetes.io/part-of: cilium
spec:
  type: {{ .Values.hubble.ui.service.type | quote }}
  selector:
    k8s-app: hubble-ui
  ports:
    - name: http
      port: 80
      targetPort: 8081
      {{- if and (eq "NodePort" .Values.hubble.ui.service.type) .Values.hubble.ui.service.nodePort }}
      nodePort: {{ .Values.hubble.ui.service.nodePort }}
      {{- end }}
{{- end }}
