{{- if and (or .Values.hubble.enabled .Values.hubble.ui.standalone.enabled) .Values.hubble.ui.enabled .Values.hubble.ui.podDisruptionBudget.enabled }}
{{- $component := .Values.hubble.ui.podDisruptionBudget }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: hubble-ui
  namespace: {{ .Release.Namespace }}
  {{- with .Values.hubble.ui.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    k8s-app: hubble-ui
    app.kubernetes.io/name: hubble-ui
    app.kubernetes.io/part-of: cilium
spec:
  {{- with $component.maxUnavailable }}
  maxUnavailable: {{ . }}
  {{- end }}
  {{- with $component.minAvailable }}
  minAvailable: {{ . }}
  {{- end }}
  selector:
    matchLabels:
      k8s-app: hubble-ui
{{- end }}
