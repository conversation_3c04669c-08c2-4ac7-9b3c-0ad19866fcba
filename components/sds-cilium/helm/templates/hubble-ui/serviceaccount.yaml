{{- if and (or .Values.hubble.enabled .Values.hubble.ui.standalone.enabled) .Values.hubble.ui.enabled .Values.serviceAccounts.ui.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.serviceAccounts.ui.name | quote }}
  namespace: {{ .Release.Namespace }}
  {{- if or .Values.serviceAccounts.ui.annotations .Values.hubble.ui.annotations }}
  annotations:
    {{- with .Values.hubble.ui.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.serviceAccounts.ui.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
{{- end }}
