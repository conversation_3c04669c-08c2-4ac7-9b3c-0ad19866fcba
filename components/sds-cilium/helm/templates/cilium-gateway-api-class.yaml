{{- if .Values.gatewayAPI.enabled -}}
{{- if ( or (eq (.Values.gatewayAPI.gatewayClass.create | toString) "true") (and (.Capabilities.APIVersions.Has "gateway.networking.k8s.io/v1/GatewayClass") (eq (.Values.gatewayAPI.gatewayClass.create | toString) "auto"))) }}
apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: cilium
spec:
  controllerName: io.cilium/gateway-controller
  description: The default Cilium GatewayClass
{{- end}}
{{- end}}
