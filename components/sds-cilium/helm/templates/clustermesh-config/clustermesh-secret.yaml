{{- if .Values.clustermesh.config.enabled }}
---
apiVersion: v1
kind: Secret
metadata:
  name: cilium-clustermesh
  namespace: {{ .Release.Namespace }}
  {{- with .Values.clustermesh.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
data:
  {{- $kvstoremesh := and .Values.clustermesh.useAPIServer .Values.clustermesh.apiserver.kvstoremesh.enabled }}
  {{- $override := ternary (printf "https://clustermesh-apiserver.%s.svc:2379" .Release.Namespace) "" $kvstoremesh }}
  {{- range .Values.clustermesh.config.clusters }}
  {{ .name }}: {{ include "clustermesh-config-generate-etcd-cfg" (list . $.Values.clustermesh.config.domain $override) | b64enc }}
  {{- /* The parenthesis around .tls are required, since it can be null: https://stackoverflow.com/a/68807258 */}}
  {{- if and (eq $override "") (.tls).cert (.tls).key }}
  {{- if .tls.caCert }}
  {{ .name }}.etcd-client-ca.crt: {{ .tls.caCert }}
  {{- end }}
  {{ .name }}.etcd-client.key: {{ .tls.key }}
  {{ .name }}.etcd-client.crt: {{ .tls.cert }}
  {{- end }}
  {{- end }}
{{- end }}
