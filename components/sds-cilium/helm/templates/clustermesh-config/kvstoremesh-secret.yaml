{{- if and .Values.clustermesh.useAPIServer .Values.clustermesh.config.enabled .Values.clustermesh.apiserver.kvstoremesh.enabled }}
---
apiVersion: v1
kind: Secret
metadata:
  name: cilium-kvstoremesh
  namespace: {{ .Release.Namespace }}
  {{- with .Values.clustermesh.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
data:
  {{- range .Values.clustermesh.config.clusters }}
  {{ .name }}: {{ include "clustermesh-config-generate-etcd-cfg" (list . $.Values.clustermesh.config.domain "") | b64enc }}
  {{- /* The parenthesis around .tls are required, since it can be null: https://stackoverflow.com/a/68807258 */}}
  {{- if and (.tls).cert (.tls).key }}
  {{- if .tls.caCert }}
  {{ .name }}.etcd-client-ca.crt: {{ .tls.caCert }}
  {{- end }}
  {{ .name }}.etcd-client.key: {{ .tls.key }}
  {{ .name }}.etcd-client.crt: {{ .tls.cert }}
  {{- end }}
  {{- end }}
{{- end }}
