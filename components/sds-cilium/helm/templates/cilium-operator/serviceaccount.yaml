{{- if and .Values.operator.enabled .Values.serviceAccounts.operator.create }}
{{- if and .Values.eni.enabled .Values.eni.iamRole }}
  {{ $_ := set .Values.serviceAccounts.operator.annotations "eks.amazonaws.com/role-arn" .Values.eni.iamRole }}
{{- end}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.serviceAccounts.operator.name | quote }}
  namespace: {{ .Release.Namespace }}
  {{- if or .Values.serviceAccounts.operator.annotations .Values.operator.annotations }}
  annotations:
    {{- with .Values.operator.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.serviceAccounts.operator.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
{{- end }}
