{{- if and .Values.operator.enabled .Values.operator.podDisruptionBudget.enabled }}
{{- $component := .Values.operator.podDisruptionBudget }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: cilium-operator
  namespace: {{ .Release.Namespace }}
  {{- with .Values.operator.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    io.cilium/app: operator
    name: cilium-operator
    app.kubernetes.io/name: cilium-operator
    app.kubernetes.io/part-of: cilium
spec:
  {{- with $component.maxUnavailable }}
  maxUnavailable: {{ . }}
  {{- end }}
  {{- with $component.minAvailable }}
  minAvailable: {{ . }}
  {{- end }}
  selector:
    matchLabels:
      io.cilium/app: operator
      name: cilium-operator
{{- end }}
