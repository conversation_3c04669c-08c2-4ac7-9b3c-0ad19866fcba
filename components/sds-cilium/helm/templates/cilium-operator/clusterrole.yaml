{{- if and .Values.operator.enabled .Values.serviceAccounts.operator.create .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cilium-operator
  {{- with .Values.operator.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    app.kubernetes.io/part-of: cilium
rules:
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - get
  - list
  - watch
{{- if hasKey .Values "disableEndpointCRD" }}
{{- if not .Values.disableEndpointCRD }}
{{- if (and .Values.operator.unmanagedPodWatcher.restart (ne (.Values.operator.unmanagedPodWatcher.intervalSeconds | int64) 0 ) ) }}
  # to automatically delete [core|kube]dns pods so that are starting to being
  # managed by Cilium
  - delete
{{- end }}
{{- end }}
{{- end }}
- apiGroups:
  - ""
  resources:
  - configmaps
  resourceNames:
  - cilium-config
  verbs:
   # allow patching of the configmap to set annotations
  - patch
{{- if or .Values.operator.removeNodeTaints .Values.operator.setNodeNetworkStatus (include "hasDuration" .Values.operator.endpointGCInterval) }}
- apiGroups:
  - ""
  resources:
  - nodes
  verbs:
  - list
  - watch
{{- end }}
{{- if or .Values.operator.removeNodeTaints .Values.operator.setNodeNetworkStatus }}
- apiGroups:
  - ""
  resources:
{{- if .Values.operator.removeNodeTaints }}
  # To remove node taints
  - nodes
{{- end }}
{{- if .Values.operator.setNodeNetworkStatus }}
  # To set NetworkUnavailable false on startup
  - nodes/status
{{- end }}
  verbs:
  - patch
{{- end }}
- apiGroups:
  - discovery.k8s.io
  resources:
  - endpointslices
  verbs:
{{- if .Values.clustermesh.enableEndpointSliceSynchronization }}
  - create
  - update
  - delete
  - deletecollection
{{- end }}
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  # to perform LB IP allocation for BGP
  - services/status
  verbs:
  - update
  - patch
- apiGroups:
  - ""
  resources:
  # to check apiserver connectivity
  - namespaces
{{- if or .Values.ingressController.enabled .Values.gatewayAPI.enabled  }}
  - secrets
{{- end }}
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  # to perform the translation of a CNP that contains `ToGroup` to its endpoints
  - services
  - endpoints
  verbs:
  - get
  - list
  - watch
{{- if or .Values.ingressController.enabled .Values.gatewayAPI.enabled }}
  - create
  - update
  - delete
  - patch
{{- end }}
{{- if .Values.clustermesh.enableEndpointSliceSynchronization }}
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - patch
  - create
{{- end }}
- apiGroups:
  - cilium.io
  resources:
  - ciliumnetworkpolicies
  - ciliumclusterwidenetworkpolicies
  verbs:
  # Create auto-generated CNPs and CCNPs from Policies that have 'toGroups'
  - create
  - update
  - deletecollection
  # To update the status of the CNPs and CCNPs
  - patch
  - get
  - list
  - watch
- apiGroups:
  - cilium.io
  resources:
  - ciliumnetworkpolicies/status
  - ciliumclusterwidenetworkpolicies/status
  verbs:
  # Update the auto-generated CNPs and CCNPs status.
  - patch
  - update
- apiGroups:
  - cilium.io
  resources:
  - ciliumendpoints
  - ciliumidentities
  verbs:
  # To perform garbage collection of such resources
  - delete
  - list
  - watch
- apiGroups:
  - cilium.io
  resources:
  - ciliumidentities
  verbs:
  # To synchronize garbage collection of such resources
  - update
- apiGroups:
  - cilium.io
  resources:
  - ciliumnodes
  verbs:
  - create
  - update
  - get
  - list
  - watch
{{- if include "hasDuration" .Values.operator.nodeGCInterval }}
    # To perform CiliumNode garbage collector
  - delete
{{- end }}
- apiGroups:
  - cilium.io
  resources:
  - ciliumnodes/status
  verbs:
  - update
- apiGroups:
  - cilium.io
  resources:
  - ciliumendpointslices
  - ciliumenvoyconfigs
  - ciliumbgppeerconfigs
  - ciliumbgpadvertisements
  - ciliumbgpnodeconfigs
  verbs:
  - create
  - update
  - get
  - list
  - watch
  - delete
  - patch
- apiGroups:
  - apiextensions.k8s.io
  resources:
  - customresourcedefinitions
  verbs:
  - create
  - get
  - list
  - watch
- apiGroups:
  - apiextensions.k8s.io
  resources:
  - customresourcedefinitions
  verbs:
  - update
  resourceNames:
  - ciliumloadbalancerippools.cilium.io
  - ciliumbgppeeringpolicies.cilium.io
  - ciliumbgpclusterconfigs.cilium.io
  - ciliumbgppeerconfigs.cilium.io
  - ciliumbgpadvertisements.cilium.io
  - ciliumbgpnodeconfigs.cilium.io
  - ciliumbgpnodeconfigoverrides.cilium.io
  - ciliumclusterwideenvoyconfigs.cilium.io
  - ciliumclusterwidenetworkpolicies.cilium.io
  - ciliumegressgatewaypolicies.cilium.io
  - ciliumendpoints.cilium.io
  - ciliumendpointslices.cilium.io
  - ciliumenvoyconfigs.cilium.io
  - ciliumexternalworkloads.cilium.io
  - ciliumidentities.cilium.io
  - ciliumlocalredirectpolicies.cilium.io
  - ciliumnetworkpolicies.cilium.io
  - ciliumnodes.cilium.io
  - ciliumnodeconfigs.cilium.io
  - ciliumcidrgroups.cilium.io
  - ciliuml2announcementpolicies.cilium.io
  - ciliumpodippools.cilium.io
- apiGroups:
  - cilium.io
  resources:
  - ciliumloadbalancerippools
  - ciliumpodippools
  - ciliumbgppeeringpolicies
  - ciliumbgpclusterconfigs
  - ciliumbgpnodeconfigoverrides
  verbs:
  - get
  - list
  - watch
- apiGroups:
    - cilium.io
  resources:
    - ciliumpodippools
  verbs:
    - create
- apiGroups:
  - cilium.io
  resources:
  - ciliumloadbalancerippools/status
  verbs:
  - patch
# For cilium-operator running in HA mode.
#
# Cilium operator running in HA mode requires the use of ResourceLock for Leader Election
# between multiple running instances.
# The preferred way of doing this is to use LeasesResourceLock as edits to Leases are less
# common and fewer objects in the cluster watch "all Leases".
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - create
  - get
  - update
{{- if .Values.ingressController.enabled }}
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses
  - ingressclasses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses/status # To update ingress status with load balancer IP.
  verbs:
  - update
{{- end }}
{{- if .Values.gatewayAPI.enabled }}
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - gatewayclasses
  - gateways
  - tlsroutes
  - httproutes
  - grpcroutes
  - referencegrants
  - referencepolicies
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - gatewayclasses/status
  - gateways/status
  - httproutes/status
  - grpcroutes/status
  - tlsroutes/status
  verbs:
  - update
  - patch
{{- end }}
{{- if or .Values.gatewayAPI.enabled .Values.clustermesh.enableMCSAPISupport }}
- apiGroups:
  - multicluster.x-k8s.io
  resources:
  - serviceimports
  verbs:
  - get
  - list
  - watch
{{- if .Values.clustermesh.enableMCSAPISupport }}
  - create
  - update
  - patch
  - delete
{{- end }}
{{- end }}
{{- if .Values.clustermesh.enableMCSAPISupport }}
- apiGroups:
  - multicluster.x-k8s.io
  resources:
  - serviceexports
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - create
  - update
  - patch
  - delete
{{- end }}
{{- end }}
