{{- if .Values.operator.enabled }}
{{- if .Values.azure.enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: cilium-azure
  namespace: {{ .Release.Namespace }}
  {{- with .Values.operator.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: Opaque
data:
  AZURE_CLIENT_ID: {{ default "" .Values.azure.clientID | b64enc | quote }}
  AZURE_CLIENT_SECRET: {{ default "" .Values.azure.clientSecret | b64enc | quote }}
{{- end }}
{{- end }}
