{{- if or
  (and (or .Values.externalWorkloads.enabled .Values.clustermesh.useAPIServer) .Values.clustermesh.apiserver.tls.auto.enabled (eq .Values.clustermesh.apiserver.tls.auto.method "helm"))
  (and (or .Values.agent .Values.hubble.relay.enabled .Values.hubble.ui.enabled) .Values.hubble.enabled .Values.hubble.tls.enabled .Values.hubble.tls.auto.enabled (eq .Values.hubble.tls.auto.method "helm"))
  (and .Values.tls.ca.key .Values.tls.ca.cert)
-}}

{{- $_ := include "cilium.ca.setup" . -}}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ .commonCASecretName }}
  namespace: {{ .Release.Namespace }}
data:
  ca.crt: {{ .commonCA.Cert | b64enc }}
  ca.key: {{ .commonCA.Key  | b64enc }}
{{- end }}
