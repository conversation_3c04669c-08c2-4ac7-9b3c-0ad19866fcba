{{- $secretNamespaces := dict -}}
{{- range $cfg := tuple .Values.ingressController .Values.gatewayAPI .Values.envoyConfig .Values.bgpControlPlane -}}
{{- if and $cfg.enabled $cfg.secretsNamespace.create $cfg.secretsNamespace.name -}}
{{- $_ := set $secretNamespaces $cfg.secretsNamespace.name 1 -}}
{{- end -}}
{{- end -}}

{{- range $name, $_ := $secretNamespaces }}
---
apiVersion: v1
kind: Namespace
metadata:
  name: {{ $name | quote }}
{{- end}}
