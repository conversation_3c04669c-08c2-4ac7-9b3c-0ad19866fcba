{{- if and .Values.nodeinit.enabled .Values.serviceAccounts.nodeinit.enabled  .Values.serviceAccounts.nodeinit.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.serviceAccounts.nodeinit.name | quote }}
  namespace: {{ .Release.Namespace }}
  {{- if or .Values.serviceAccounts.nodeinit.annotations .Values.nodeinit.annotations }}
  annotations:
    {{- with .Values.nodeinit.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.serviceAccounts.nodeinit.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
{{- end }}
