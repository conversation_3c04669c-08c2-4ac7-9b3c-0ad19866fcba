annotations:
  artifacthub.io/crds: "- kind: CiliumNetworkPolicy\n  version: v2\n  name: ciliumnetworkpolicies.cilium.io\n
    \ displayName: Cilium Network Policy\n  description: |\n    Cilium Network Policies
    provide additional functionality beyond what\n    is provided by standard Kubernetes
    NetworkPolicy such as the ability\n    to allow traffic based on FQDNs, or to
    filter at Layer 7.\n- kind: CiliumClusterwideNetworkPolicy\n  version: v2\n  name:
    ciliumclusterwidenetworkpolicies.cilium.io\n  displayName: Cilium Clusterwide
    Network Policy\n  description: |\n    Cilium Clusterwide Network Policies support
    configuring network traffic\n    policiies across the entire cluster, including
    applying node firewalls.\n- kind: CiliumExternalWorkload\n  version: v2\n  name:
    ciliumexternalworkloads.cilium.io\n  displayName: Cilium External Workload\n  description:
    |\n    Cilium External Workload supports configuring the ability for external\n
    \   non-Kubernetes workloads to join the cluster.\n- kind: CiliumLocalRedirectPolicy\n
    \ version: v2\n  name: ciliumlocalredirectpolicies.cilium.io\n  displayName: Cilium
    Local Redirect Policy\n  description: |\n    Cilium Local Redirect Policy allows
    local redirects to be configured\n    within a node to support use cases like
    Node-Local DNS or KIAM.\n- kind: CiliumNode\n  version: v2\n  name: ciliumnodes.cilium.io\n
    \ displayName: Cilium Node\n  description: |\n    Cilium Node represents a node
    managed by Cilium. It contains a\n    specification to control various node specific
    configuration aspects\n    and a status section to represent the status of the
    node.\n- kind: CiliumIdentity\n  version: v2\n  name: ciliumidentities.cilium.io\n
    \ displayName: Cilium Identity\n  description: |\n    Cilium Identity allows introspection
    into security identities that\n    Cilium allocates which identify sets of labels
    that are assigned to\n    individual endpoints in the cluster.\n- kind: CiliumEndpoint\n
    \ version: v2\n  name: ciliumendpoints.cilium.io\n  displayName: Cilium Endpoint\n
    \ description: |\n    Cilium Endpoint represents the status of individual pods
    or nodes in\n    the cluster which are managed by Cilium, including enforcement
    status,\n    IP addressing and whether the networking is successfully operational.\n-
    kind: CiliumEndpointSlice\n  version: v2alpha1\n  name: ciliumendpointslices.cilium.io\n
    \ displayName: Cilium Endpoint Slice\n  description: |\n    Cilium Endpoint Slice
    represents the status of groups of pods or nodes\n    in the cluster which are
    managed by Cilium, including enforcement status,\n    IP addressing and whether
    the networking is successfully operational.\n- kind: CiliumEgressGatewayPolicy\n
    \ version: v2\n  name: ciliumegressgatewaypolicies.cilium.io\n  displayName: Cilium
    Egress Gateway Policy\n  description: |\n    Cilium Egress Gateway Policy provides
    control over the way that traffic\n    leaves the cluster and which source addresses
    to use for that traffic.\n- kind: CiliumClusterwideEnvoyConfig\n  version: v2\n
    \ name: ciliumclusterwideenvoyconfigs.cilium.io\n  displayName: Cilium Clusterwide
    Envoy Config\n  description: |\n    Cilium Clusterwide Envoy Config specifies
    Envoy resources and K8s service mappings\n    to be provisioned into Cilium host
    proxy instances in cluster context.\n- kind: CiliumEnvoyConfig\n  version: v2\n
    \ name: ciliumenvoyconfigs.cilium.io\n  displayName: Cilium Envoy Config\n  description:
    |\n    Cilium Envoy Config specifies Envoy resources and K8s service mappings\n
    \   to be provisioned into Cilium host proxy instances in namespace context.\n-
    kind: CiliumBGPPeeringPolicy\n  version: v2alpha1\n  name: ciliumbgppeeringpolicies.cilium.io\n
    \ displayName: Cilium BGP Peering Policy\n  description: |\n    Cilium BGP Peering
    Policy instructs Cilium to create specific BGP peering\n    configurations.\n-
    kind: CiliumBGPClusterConfig\n  version: v2alpha1\n  name: ciliumbgpclusterconfigs.cilium.io\n
    \ displayName: Cilium BGP Cluster Config\n  description: |\n    Cilium BGP Cluster
    Config instructs Cilium operator to create specific BGP cluster\n    configurations.\n-
    kind: CiliumBGPPeerConfig\n  version: v2alpha1\n  name: ciliumbgppeerconfigs.cilium.io\n
    \ displayName: Cilium BGP Peer Config\n  description: |\n    CiliumBGPPeerConfig
    is a common set of BGP peer configurations. It can be referenced \n    by multiple
    peers from CiliumBGPClusterConfig.\n- kind: CiliumBGPAdvertisement\n  version:
    v2alpha1\n  name: ciliumbgpadvertisements.cilium.io\n  displayName: Cilium BGP
    Advertisement\n  description: |\n    CiliumBGPAdvertisement is used to define
    source of BGP advertisement as well as BGP attributes \n    to be advertised with
    those prefixes.\n- kind: CiliumBGPNodeConfig\n  version: v2alpha1\n  name: ciliumbgpnodeconfigs.cilium.io\n
    \ displayName: Cilium BGP Node Config\n  description: |\n    CiliumBGPNodeConfig
    is read only node specific BGP configuration. It is constructed by Cilium operator.\n
    \   It will also contain node local BGP state information.\n- kind: CiliumBGPNodeConfigOverride\n
    \ version: v2alpha1\n  name: ciliumbgpnodeconfigoverrides.cilium.io\n  displayName:
    Cilium BGP Node Config Override\n  description: |\n    CiliumBGPNodeConfigOverride
    can be used to override node specific BGP configuration.\n- kind: CiliumLoadBalancerIPPool\n
    \ version: v2alpha1\n  name: ciliumloadbalancerippools.cilium.io\n  displayName:
    Cilium Load Balancer IP Pool\n  description: |\n    Defining a Cilium Load Balancer
    IP Pool instructs Cilium to assign IPs to LoadBalancer Services.\n- kind: CiliumNodeConfig\n
    \ version: v2alpha1\n  name: ciliumnodeconfigs.cilium.io\n  displayName: Cilium
    Node Configuration\n  description: |\n    CiliumNodeConfig is a list of configuration
    key-value pairs. It is applied to\n    nodes indicated by a label selector.\n-
    kind: CiliumCIDRGroup\n  version: v2alpha1\n  name: ciliumcidrgroups.cilium.io\n
    \ displayName: Cilium CIDR Group\n  description: |\n    CiliumCIDRGroup is a list
    of CIDRs that can be referenced as a single entity from CiliumNetworkPolicies.\n-
    kind: CiliumL2AnnouncementPolicy\n  version: v2alpha1\n  name: ciliuml2announcementpolicies.cilium.io\n
    \ displayName: Cilium L2 Announcement Policy\n  description: |\n    CiliumL2AnnouncementPolicy
    is a policy which determines which service IPs will be announced to\n    the local
    area network, by which nodes, and via which interfaces.\n- kind: CiliumPodIPPool\n
    \ version: v2alpha1\n  name: ciliumpodippools.cilium.io\n  displayName: Cilium
    Pod IP Pool\n  description: |\n    CiliumPodIPPool defines an IP pool that can
    be used for pooled IPAM (i.e. the multi-pool IPAM mode).\n"
apiVersion: v2
appVersion: 1.16.6
description: eBPF-based Networking, Security, and Observability
home: https://cilium.io/
icon: https://cdn.jsdelivr.net/gh/cilium/cilium@main/Documentation/images/logo-solo.svg
keywords:
- BPF
- eBPF
- Kubernetes
- Networking
- Security
- Observability
- Troubleshooting
kubeVersion: '>= 1.21.0-0'
name: sds-cilium
sources:
- https://github.com/cilium/cilium
version: 1.16.6
