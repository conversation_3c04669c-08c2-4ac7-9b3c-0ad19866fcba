cni:
  exclusive: false
  chainingMode: generic-veth
  configMap: cni-configuration
  customConf: true
  
loadBalancer:
  mode: snat

socketLB:
  enabled: true
  terminatePodConnections: true

ipam:
  mode: delegated-plugin

bpfClockProbe: true
enableIPv6Masquerade: false
enableRuntimeDeviceDetection: false
sessionAffinity: true

bpf:
  enableTCX: false

extraConfig:
  local-router-ipv4: "************"
  enable-endpoint-routes: "true"
  disable-cnp-status-updates: "true"
  disable-embedded-dns-proxy: "false"
  disable-endpoint-crd: "false"
  enable-standalone-dns-proxy: "false"

envoy:
  enabled: false

routingMode: native

enableIPv4Masquerade: false

endpointHealthChecking:
  enabled: false

azureOverlayEnabled: true

operator:
  unmanagedPodWatcher:
    intervalSeconds: 0

# cni:
#   chainingMode: "generic-veth"
#   customConf: true
#   configMap: cni-configuration
