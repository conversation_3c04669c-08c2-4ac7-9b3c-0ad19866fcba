policies: 
  castAI:
    enabled: true
  provider: "{{  .Values.global.CLOUD_SERVICE_PROVIDER  }}"
  aws:
    AWS_REGION: "{{  .Values.global.AWS_REGION  }}"
    LOGS_BUCKET: "{{  .Values.global.S3_LOGS_BUCKET_NAME  }}"
    DATALAKE_BUCKET: "{{  .Values.global.S3_DATALAKE_BUCKET_NAME  }}"
    APPS_BUCKET: "{{  .Values.global.S3_APPS_BUCKET_NAME  }}"
    AWS_VPC_CIDRS: "{{  .Values.global.VPC_CIDRS  }}"
  #Add extra policies here. Even we can use helm templating fuctions here. Eg:. Please note {{/* */}} in helm is a comment
  # extraPolicies: |-
  #   {{- if .Values.hubble.relay.enabled -}}
  #   apiVersion: cilium.io/v2
  #   kind: CiliumNetworkPolicy
  #   metadata:
  #     name: hubble-ui-tony
  #     namespace: kube-system
  #   spec:
  #     endpointSelector:
  #       matchLabels:
  #         io.cilium.k8s.policy.serviceaccount: hubble-ui
  #     egress:
  #     - toEntities:
  #       - kube-apiserver
  #     - toEndpoints:
  #       - matchLabels:
  #           io.cilium.k8s.policy.serviceaccount: hubble-relay
  #   {{- end }}
  azure:
    PSQL_HOSTNAME: "{{  .Values.global.PSQL_HOSTNAME  }}"
    AZURE_STORAGE_ACCOUNT_NAME:  "{{  .Values.global.AZURE_STORAGE_ACCOUNT_NAME  }}"
  extraPolicies: