{{- if and (.Values.policies.castAI.enabled) (lookup "v1" "Namespace" "" "castai-agent") }}
#To CastAI for agent
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: castai-agent-access
  namespace: castai-agent
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: castai-agent
      k8s:io.kubernetes.pod.namespace: kube-system
  egress:
  - toEndpoints:
      - matchLabels:
          io.kubernetes.pod.namespace: kube-system
          k8s-app: kube-dns
    toPorts:
      - ports:
          - port: "53"
            protocol: UDP
        rules:
          dns:
            - matchPattern: "*"
  - toFQDNs:
      - matchPattern: api.cast.ai
      - matchPattern: grpc.cast.ai
      - matchPattern: api-grpc.cast.ai
      - matchPattern: api.eu.cast.ai
      - matchPattern: grpc.eu.cast.ai
      - matchPattern: api-grpc.eu.cast.ai
    toPorts:
      - ports:
          - port: "443"
---
#To CastAI for controller
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: castai-controller-access
  namespace: castai-agent
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: castai-cluster-controller
      k8s:io.kubernetes.pod.namespace: kube-system
  egress:
  - toEndpoints:
      - matchLabels:
          io.kubernetes.pod.namespace: kube-system
          k8s-app: kube-dns
    toPorts:
      - ports:
          - port: "53"
            protocol: UDP
        rules:
          dns:
            - matchPattern: "*"
  - toFQDNs:
      - matchPattern: api.cast.ai
      - matchPattern: grpc.cast.ai
      - matchPattern: api-grpc.cast.ai
      - matchPattern: api.eu.cast.ai
      - matchPattern: grpc.eu.cast.ai
      - matchPattern: api-grpc.eu.cast.ai
    toPorts:
      - ports:
          - port: "443"
---
#To CastAI for evictor
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: castai-evictor-access
  namespace: castai-agent
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: castai-evictor
      k8s:io.kubernetes.pod.namespace: kube-system
  egress:
  - toEndpoints:
      - matchLabels:
          io.kubernetes.pod.namespace: kube-system
          k8s-app: kube-dns
    toPorts:
      - ports:
          - port: "53"
            protocol: UDP
        rules:
          dns:
            - matchPattern: "*"
  - toFQDNs:
      - matchPattern: api.cast.ai
      - matchPattern: grpc.cast.ai
      - matchPattern: api-grpc.cast.ai
      - matchPattern: api.eu.cast.ai
      - matchPattern: grpc.eu.cast.ai
      - matchPattern: api-grpc.eu.cast.ai
    toPorts:
      - ports:
        - port: "443"
---
#To CastAI for workload autoscaler
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: castai-workload-autoscaler-access
  namespace: castai-agent
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: castai-workload-autoscaler
      k8s:io.kubernetes.pod.namespace: kube-system
  egress:
  - toEndpoints:
      - matchLabels:
          io.kubernetes.pod.namespace: kube-system
          k8s-app: kube-dns
    toPorts:
      - ports:
          - port: "53"
            protocol: UDP
        rules:
          dns:
            - matchPattern: "*"
  - toFQDNs:
      - matchPattern: api.cast.ai
      - matchPattern: grpc.cast.ai
      - matchPattern: api-grpc.cast.ai
      - matchPattern: api.eu.cast.ai
      - matchPattern: grpc.eu.cast.ai
      - matchPattern: api-grpc.eu.cast.ai
    toPorts:
      - ports:
          - port: "443"
{{- end }}
