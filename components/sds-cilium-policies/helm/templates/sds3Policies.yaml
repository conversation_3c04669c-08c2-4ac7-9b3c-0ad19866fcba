apiVersion: cilium.io/v2
kind: CiliumClusterwideNetworkPolicy
metadata:
  name: sds3-extra-policies
spec:
  endpointSelector: {}
  egress:
  - toEndpoints:
      - matchLabels:
          io.kubernetes.pod.namespace: kube-system
          k8s-app: kube-dns
    toPorts:
      - ports:
          - port: "53"
            protocol: UDP
        rules:
          dns:
            - matchPattern: "*"
#To datalake S3 bucket
  - toFQDNs:
  {{- if eq ( tpl .Values.policies.provider . ) "aws" }}
    # SDS S3 Buckets
    - matchPattern: "s3-r-w.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    - matchPattern: "s3.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    - matchPattern: "{{ ( tpl .Values.policies.aws.DATALAKE_BUCKET . ) }}.s3.amazonaws.com"
    - matchPattern: "{{ ( tpl .Values.policies.aws.DATALAKE_BUCKET . ) }}.s3.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    - matchPattern: "{{ ( tpl .Values.policies.aws.DATALAKE_BUCKET . ) }}.s3.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com.cluster.local"
    - matchPattern: "{{ ( tpl .Values.policies.aws.LOGS_BUCKET . ) }}.s3.amazonaws.com"
    - matchPattern: "{{ ( tpl .Values.policies.aws.LOGS_BUCKET . ) }}.s3.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    - matchPattern: "{{ ( tpl .Values.policies.aws.LOGS_BUCKET . ) }}.s3.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com.cluster.local"
    - matchPattern: "{{ ( tpl .Values.policies.aws.APPS_BUCKET . ) }}.s3.amazonaws.com"
    - matchPattern: "{{ ( tpl .Values.policies.aws.APPS_BUCKET . ) }}.s3.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    - matchPattern: "{{ ( tpl .Values.policies.aws.APPS_BUCKET . ) }}.s3.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com.cluster.local"
    # AWS STS
    - matchPattern: "sts.amazonaws.com"
    - matchPattern: "sts.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    # AWS EC2
    - matchPattern: "ec2.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    # For Secrets CSI controllers
    - matchPattern: "secretsmanager.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    # For ALB Controller pods
    - matchPattern: "elasticloadbalancing.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    - matchPattern: "shield.us-east-1.amazonaws.com"
    - matchPattern: "wafv2.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    - matchPattern: "waf-regional.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    # For SNS
    - matchPattern: "sns.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    - matchPattern: "eks.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    - matchPattern: "eks-proxy.eks.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    - matchPattern: "ecs.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    - matchPattern: "elasticmapreduce.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    - matchPattern: "config.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    - matchPattern: "securityhub.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
    - matchPattern: "elasticfilesystem.{{ ( tpl .Values.policies.aws.AWS_REGION . ) }}.amazonaws.com"
  {{- end }}
  {{- if eq ( tpl .Values.policies.provider . ) "azure" }}
    - matchPattern: "{{ ( tpl .Values.policies.azure.PSQL_HOSTNAME . ) }}"
    - matchPattern: "{{ ( tpl .Values.policies.azure.AZURE_STORAGE_ACCOUNT_NAME . ) }}.dfs.core.windows.net"
    - matchPattern: "{{ ( tpl .Values.policies.azure.AZURE_STORAGE_ACCOUNT_NAME . ) }}.blob.core.windows.net"
  {{- end }}
    # For Nifi registry to Github
    - matchPattern: github.com
    # For Prometheus
    - matchPattern: raw.githubusercontent.com
    - matchPattern: grafana.com
    # For downloading prometheus plugins
    - matchPattern: storage.googleapis.com
    # To JFrog
    - matchPattern: prevalentai.jfrog.io
    - matchPattern: jfrog-prod-use1-shared-virginia-main.s3.amazonaws.com
    # For management API and Studio
    - matchPattern: api.github.com
    - matchPattern: pypi.org
    - matchPattern: files.pythonhosted.org
    - matchPattern: googlechromelabs.github.io
    - matchPattern: management.azure.com
    - matchPattern: prevalentindia.webhook.office.com
    - matchPattern: smtp.office365.com
    toPorts:
    - ports:
        - port: "443"
