annotations:
  artifacthub.io/license: Apache-2.0
  artifacthub.io/prerelease: "false"
  artifacthub.io/signKey: |
    fingerprint: 1020CF3C033D4F35BAE1C19E1226061C665DF13E
    url: https://cert-manager.io/public-keys/cert-manager-keyring-2021-09-20-1020CF3C033D4F35BAE1C19E1226061C665DF13E.gpg
apiVersion: v1
appVersion: v1.13.1
description: A Helm chart for cert-manager
home: https://github.com/cert-manager/cert-manager
icon: https://raw.githubusercontent.com/cert-manager/cert-manager/d53c0b9270f8cd90d908460d69502694e1838f5f/logo/logo-small.png
keywords:
- cert-manager
- kube-lego
- letsencrypt
- tls
kubeVersion: '>= 1.22.0-0'
maintainers:
- email: <EMAIL>
  name: cert-manager-maintainers
  url: https://cert-manager.io
name: sds-cert-manager
sources:
- https://github.com/cert-manager/cert-manager
version: 3.4.0-2
