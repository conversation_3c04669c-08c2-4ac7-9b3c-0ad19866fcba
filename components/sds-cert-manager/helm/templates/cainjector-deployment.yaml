{{- if .Values.cainjector.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "cainjector.fullname" . }}
  namespace: {{ include "cert-manager.namespace" . }}
  labels:
    app: {{ include "cainjector.name" . }}
    app.kubernetes.io/name: {{ include "cainjector.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "cainjector"
    {{- include "labels" . | nindent 4 }}
  {{- with .Values.cainjector.deploymentAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.cainjector.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "cainjector.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
      app.kubernetes.io/component: "cainjector"
  {{- with .Values.cainjector.strategy }}
  strategy:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  template:
    metadata:
      labels:
        app: {{ include "cainjector.name" . }}
        app.kubernetes.io/name: {{ include "cainjector.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        app.kubernetes.io/component: "cainjector"
        {{- include "labels" . | nindent 8 }}
        {{- with .Values.cainjector.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.cainjector.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      serviceAccountName: {{ template "cainjector.serviceAccountName" . }}
      {{- if hasKey .Values.cainjector "automountServiceAccountToken" }}
      automountServiceAccountToken: {{ .Values.cainjector.automountServiceAccountToken }}
      {{- end }}
      enableServiceLinks: {{ .Values.cainjector.enableServiceLinks }}
      {{- with .Values.global.priorityClassName }}
      priorityClassName: {{ . | quote }}
      {{- end }}
      {{- with .Values.cainjector.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}-cainjector
          {{- with .Values.cainjector.image }}
          image: "{{- if .registry -}}{{ .registry }}/{{- end -}}{{ .repository }}{{- if (.digest) -}} @{{ .digest }}{{- else -}}:{{ default $.Chart.AppVersion .tag }} {{- end -}}"
          {{- end }}
          imagePullPolicy: {{ .Values.cainjector.image.pullPolicy }}
          args:
          {{- if .Values.global.logLevel }}
          - --v={{ .Values.global.logLevel }}
          {{- end }}
          {{- with .Values.global.leaderElection }}
          - --leader-election-namespace={{ .namespace }}
          {{- if .leaseDuration }}
          - --leader-election-lease-duration={{ .leaseDuration }}
          {{- end }}
          {{- if .renewDeadline }}
          - --leader-election-renew-deadline={{ .renewDeadline }}
          {{- end }}
          {{- if .retryPeriod }}
          - --leader-election-retry-period={{ .retryPeriod }}
          {{- end }}
          {{- end }}
          {{- with .Values.cainjector.extraArgs }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
          env:
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          {{- with .Values.cainjector.containerSecurityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.cainjector.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.cainjector.volumeMounts }}
          volumeMounts:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.cainjector.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.cainjector.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.cainjector.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with  .Values.cainjector.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.cainjector.volumes }}
      volumes:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
