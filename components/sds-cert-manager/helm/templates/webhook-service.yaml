apiVersion: v1
kind: Service
metadata:
  name: {{ template "webhook.fullname" . }}
  namespace: {{ include "cert-manager.namespace" . }}
{{- with .Values.webhook.serviceAnnotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
  labels:
    app: {{ include "webhook.name" . }}
    app.kubernetes.io/name: {{ include "webhook.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "webhook"
    {{- include "labels" . | nindent 4 }}
    {{- with .Values.webhook.serviceLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  type: {{ .Values.webhook.serviceType }}
  {{- with .Values.webhook.loadBalancerIP }}
  loadBalancerIP: {{ . }}
  {{- end }}
  ports:
  - name: https
    port: 443
    protocol: TCP
    targetPort: "https"
  selector:
    app.kubernetes.io/name: {{ include "webhook.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "webhook"
