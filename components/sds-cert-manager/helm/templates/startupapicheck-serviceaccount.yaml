{{- if .Values.startupapicheck.enabled }}
{{- if .Values.startupapicheck.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.startupapicheck.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ template "startupapicheck.serviceAccountName" . }}
  namespace: {{ include "cert-manager.namespace" . }}
  {{- with .Values.startupapicheck.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    app: {{ include "startupapicheck.name" . }}
    app.kubernetes.io/name: {{ include "startupapicheck.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "startupapicheck"
    {{- include "labels" . | nindent 4 }}
    {{- with .Values.startupapicheck.serviceAccount.labels }}
      {{ toYaml . | nindent 4 }}
    {{- end }}
{{- with .Values.global.imagePullSecrets }}
imagePullSecrets:
  {{- toYaml . | nindent 2 }}
{{- end }}
{{- end }}
{{- end }}
