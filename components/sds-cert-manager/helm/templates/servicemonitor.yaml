{{- if and .Values.prometheus.enabled .Values.prometheus.servicemonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "cert-manager.fullname" . }}
{{- if .Values.prometheus.servicemonitor.namespace }}
  namespace: {{ .Values.prometheus.servicemonitor.namespace }}
{{- else }}
  namespace: {{ include "cert-manager.namespace" . }}
{{- end }}
  labels:
    app: {{ include "cert-manager.name" . }}
    app.kubernetes.io/name: {{ include "cert-manager.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "controller"
    {{- include "labels" . | nindent 4 }}
    prometheus: {{ .Values.prometheus.servicemonitor.prometheusInstance }}
    {{- with .Values.prometheus.servicemonitor.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
{{- if .Values.prometheus.servicemonitor.annotations }}
  annotations:
    {{- with .Values.prometheus.servicemonitor.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end }}
spec:
  jobLabel: {{ template "cert-manager.fullname" . }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ template "cert-manager.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
      app.kubernetes.io/component: "controller"
{{- if .Values.prometheus.servicemonitor.namespace }}
  namespaceSelector:
    matchNames:
      - {{ include "cert-manager.namespace" . }}
{{- end }}
  endpoints:
  - targetPort: {{ .Values.prometheus.servicemonitor.targetPort }}
    path: {{ .Values.prometheus.servicemonitor.path }}
    interval: {{ .Values.prometheus.servicemonitor.interval }}
    scrapeTimeout: {{ .Values.prometheus.servicemonitor.scrapeTimeout }}
    honorLabels: {{ .Values.prometheus.servicemonitor.honorLabels }}
    {{- with .Values.prometheus.servicemonitor.endpointAdditionalProperties }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end }}
