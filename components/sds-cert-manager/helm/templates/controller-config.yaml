{{- if .Values.config -}}
  {{- if not .Values.config.apiVersion -}}
    {{- fail "config.apiVersion must be set" -}}
  {{- end -}}

  {{- if not .Values.config.kind -}}
    {{- fail "config.kind must be set" -}}
  {{- end -}}
{{- end -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "cert-manager.fullname" . }}
  namespace: {{ include "cert-manager.namespace" . }}
  labels:
    app: {{ include "cert-manager.name" . }}
    app.kubernetes.io/name: {{ include "cert-manager.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "controller"
    {{- include "labels" . | nindent 4 }}
data:
  {{- if .Values.config }}
  config.yaml: |
    {{ .Values.config | toYaml | nindent 4 }}
  {{- end }}
