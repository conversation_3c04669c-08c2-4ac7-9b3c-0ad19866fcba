apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "cert-manager.fullname" . }}
  namespace: {{ include "cert-manager.namespace" . }}
  labels:
    app: {{ template "cert-manager.name" . }}
    app.kubernetes.io/name: {{ template "cert-manager.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "controller"
    {{- include "labels" . | nindent 4 }}
  {{- with .Values.deploymentAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ template "cert-manager.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
      app.kubernetes.io/component: "controller"
  {{- with .Values.strategy }}
  strategy:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  template:
    metadata:
      labels:
        app: {{ template "cert-manager.name" . }}
        app.kubernetes.io/name: {{ template "cert-manager.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        app.kubernetes.io/component: "controller"
        {{- include "labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if and .Values.prometheus.enabled (not .Values.prometheus.servicemonitor.enabled) }}
      {{- if not .Values.podAnnotations }}
      annotations:
      {{- end }}
        prometheus.io/path: "/metrics"
        prometheus.io/scrape: 'true'
        prometheus.io/port: '9402'
      {{- end }}
    spec:
      serviceAccountName: {{ template "cert-manager.serviceAccountName" . }}
      {{- if hasKey .Values "automountServiceAccountToken" }}
      automountServiceAccountToken: {{ .Values.automountServiceAccountToken }}
      {{- end }}
      enableServiceLinks: {{ .Values.enableServiceLinks }}
      {{- with .Values.global.priorityClassName }}
      priorityClassName: {{ . | quote }}
      {{- end }}
      {{- with .Values.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if or .Values.volumes .Values.config}}
      volumes:
        {{- if .Values.config }} 
        - name: config 
          configMap: 
            name: {{ include "cert-manager.fullname" . }} 
        {{- end }}
        {{ with .Values.volumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}-controller
          {{- with .Values.image }}
          image: "{{- if .registry -}}{{ .registry }}/{{- end -}}{{ .repository }}{{- if (.digest) -}} @{{ .digest }}{{- else -}}:{{ default $.Chart.AppVersion .tag }} {{- end -}}"
          {{- end }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          args:
          {{- if .Values.global.logLevel }}
          - --v={{ .Values.global.logLevel }}
          {{- end }}
          {{- if .Values.config }}
          - --config=/var/cert-manager/config/config.yaml
          {{- end }}
          {{- $config := default .Values.config "" }}
          {{- if .Values.clusterResourceNamespace }}
          - --cluster-resource-namespace={{ .Values.clusterResourceNamespace }}
          {{- else }}
          - --cluster-resource-namespace=$(POD_NAMESPACE)
          {{- end }}
          {{- with .Values.global.leaderElection }}
          - --leader-election-namespace={{ .namespace }}
          {{- if .leaseDuration }}
          - --leader-election-lease-duration={{ .leaseDuration }}
          {{- end }}
          {{- if .renewDeadline }}
          - --leader-election-renew-deadline={{ .renewDeadline }}
          {{- end }}
          {{- if .retryPeriod }}
          - --leader-election-retry-period={{ .retryPeriod }}
          {{- end }}
          {{- end }}
          {{- with .Values.acmesolver.image }}
          - --acme-http01-solver-image={{- if .registry -}}{{ .registry }}/{{- end -}}{{ .repository }}{{- if (.digest) -}} @{{ .digest }}{{- else -}}:{{ default $.Chart.AppVersion .tag }} {{- end -}}
          {{- end }}
          {{- with .Values.extraArgs }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
          {{- with .Values.ingressShim }}
          {{- if .defaultIssuerName }}
          - --default-issuer-name={{ .defaultIssuerName }}
          {{- end }}
          {{- if .defaultIssuerKind }}
          - --default-issuer-kind={{ .defaultIssuerKind }}
          {{- end }}
          {{- if .defaultIssuerGroup }}
          - --default-issuer-group={{ .defaultIssuerGroup }}
          {{- end }}
          {{- end }}
          {{- if .Values.featureGates }}
          - --feature-gates={{ .Values.featureGates }}
          {{- end }}
          {{- if .Values.maxConcurrentChallenges }}
          - --max-concurrent-challenges={{ .Values.maxConcurrentChallenges }}
          {{- end }}
          {{- if .Values.enableCertificateOwnerRef }}
          - --enable-certificate-owner-ref=true
          {{- end }}
          {{- if .Values.dns01RecursiveNameserversOnly }}
          - --dns01-recursive-nameservers-only=true
          {{- end }}
          {{- with .Values.dns01RecursiveNameservers }}
          - --dns01-recursive-nameservers={{ . }}
          {{- end }}
          ports:
          - containerPort: 9402
            name: http-metrics
            protocol: TCP
          - containerPort: 9403
            name: http-healthz
            protocol: TCP
          {{- with .Values.containerSecurityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- if or .Values.config .Values.volumeMounts }}
          volumeMounts:
            {{- if .Values.config}} 
            - name: config 
              mountPath: /var/cert-manager/config
            {{- end }}
            {{- with .Values.volumeMounts }} 
            {{- toYaml . | nindent 12 }}
            {{- end }}
          {{- end }}
          env:
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          {{- with .Values.extraEnv }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
          {{- with .Values.http_proxy }}
          - name: HTTP_PROXY
            value: {{ . }}
          {{- end }}
          {{- with .Values.https_proxy }}
          - name: HTTPS_PROXY
            value: {{ . }}
          {{- end }}
          {{- with .Values.no_proxy }}
          - name: NO_PROXY
            value: {{ . }}
          {{- end }}
          {{- with .Values.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}

          {{- with .Values.livenessProbe }}
          {{- if .enabled }}
          # LivenessProbe settings are based on those used for the Kubernetes
          # controller-manager. See:
          # https://github.com/kubernetes/kubernetes/blob/806b30170c61a38fedd54cc9ede4cd6275a1ad3b/cmd/kubeadm/app/util/staticpod/utils.go#L241-L245
          livenessProbe:
            httpGet:
              port: http-healthz
              path: /livez
              scheme: HTTP
            initialDelaySeconds: {{ .initialDelaySeconds }}
            periodSeconds: {{ .periodSeconds }}
            timeoutSeconds: {{ .timeoutSeconds }}
            successThreshold: {{ .successThreshold }}
            failureThreshold: {{ .failureThreshold }}
          {{- end }}
          {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with  .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.podDnsPolicy }}
      dnsPolicy: {{ . }}
      {{- end }}
      {{- with .Values.podDnsConfig }}
      dnsConfig:
        {{- toYaml . | nindent 8 }}
      {{- end }}
