{{- if .Values.cainjector.enabled }}
{{- if .Values.cainjector.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.cainjector.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ template "cainjector.serviceAccountName" . }}
  namespace: {{ include "cert-manager.namespace" . }}
  {{- with .Values.cainjector.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    app: {{ include "cainjector.name" . }}
    app.kubernetes.io/name: {{ include "cainjector.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "cainjector"
    {{- include "labels" . | nindent 4 }}
    {{- with .Values.cainjector.serviceAccount.labels }}
      {{ toYaml . | nindent 4 }}
    {{- end }}
{{- with .Values.global.imagePullSecrets }}
imagePullSecrets:
  {{- toYaml . | nindent 2 }}
{{- end }}
{{- end }}
{{- end }}
