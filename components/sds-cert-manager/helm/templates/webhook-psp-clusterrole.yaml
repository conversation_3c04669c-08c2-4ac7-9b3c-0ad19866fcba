{{- if .Values.global.podSecurityPolicy.enabled }}
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ template "webhook.fullname" . }}-psp
  labels:
    app: {{ include "webhook.name" . }}
    app.kubernetes.io/name: {{ include "webhook.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "webhook"
    {{- include "labels" . | nindent 4 }}
rules:
- apiGroups: ['policy']
  resources: ['podsecuritypolicies']
  verbs:     ['use']
  resourceNames:
  - {{ template "webhook.fullname" . }}
{{- end }}
