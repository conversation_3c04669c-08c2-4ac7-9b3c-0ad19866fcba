apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: {{ include "webhook.fullname" . }}
  labels:
    app: {{ include "webhook.name" . }}
    app.kubernetes.io/name: {{ include "webhook.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: "webhook"
    {{- include "labels" . | nindent 4 }}
  annotations:
    cert-manager.io/inject-ca-from-secret: {{ printf "%s/%s-ca" (include "cert-manager.namespace" .) (include "webhook.fullname" .) | quote}}
    {{- with .Values.webhook.validatingWebhookConfigurationAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
webhooks:
  - name: webhook.cert-manager.io
    namespaceSelector:
      matchExpressions:
      - key: "cert-manager.io/disable-validation"
        operator: "NotIn"
        values:
        - "true"
    rules:
      - apiGroups:
          - "cert-manager.io"
          - "acme.cert-manager.io"
        apiVersions:
          - "v1"
        operations:
          - CREATE
          - UPDATE
        resources:
          - "*/*"
    admissionReviewVersions: ["v1"]
    # This webhook only accepts v1 cert-manager resources.
    # Equivalent matchPolicy ensures that non-v1 resource requests are sent to
    # this webhook (after the resources have been converted to v1).
    matchPolicy: Equivalent
    timeoutSeconds: {{ .Values.webhook.timeoutSeconds }}
    failurePolicy: Fail
    sideEffects: None
    clientConfig:
      {{- if .Values.webhook.url.host }}
      url: https://{{ .Values.webhook.url.host }}/validate
      {{- else }}
      service:
        name: {{ template "webhook.fullname" . }}
        namespace: {{ include "cert-manager.namespace" . }}
        path: /validate
      {{- end }}
