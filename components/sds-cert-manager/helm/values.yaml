# Default values for cert-manager.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  # Reference to one or more secrets to be used when pulling images
  # ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
  imagePullSecrets:
    - name: "docker-secret"

  # Labels to apply to all resources
  # Please note that this does not add labels to the resources created dynamically by the controllers.
  # For these resources, you have to add the labels in the template in the cert-manager custom resource:
  # eg. podTemplate/ ingressTemplate in ACMEChallengeSolverHTTP01Ingress
  #    ref: https://cert-manager.io/docs/reference/api-docs/#acme.cert-manager.io/v1.ACMEChallengeSolverHTTP01Ingress
  # eg. secretTemplate in CertificateSpec
  #    ref: https://cert-manager.io/docs/reference/api-docs/#cert-manager.io/v1.CertificateSpec
  commonLabels: {}
  # team_name: dev

  # Optional priority class to be used for the cert-manager pods
  priorityClassName: ""
  rbac:
    create: true
    # Aggregate ClusterRoles to Kubernetes default user-facing roles. Ref: https://kubernetes.io/docs/reference/access-authn-authz/rbac/#user-facing-roles
    aggregateClusterRoles: true

  podSecurityPolicy:
    enabled: false
    useAppArmor: true

  # Set the verbosity of cert-manager. Range of 0 - 6 with 6 being the most verbose.
  logLevel: 2

  leaderElection:
    # Override the namespace used for the leader election lease
    namespace: "kube-system"

    # The duration that non-leader candidates will wait after observing a
    # leadership renewal until attempting to acquire leadership of a led but
    # unrenewed leader slot. This is effectively the maximum duration that a
    # leader can be stopped before it is replaced by another candidate.
    # leaseDuration: 60s

    # The interval between attempts by the acting master to renew a leadership
    # slot before it stops leading. This must be less than or equal to the
    # lease duration.
    # renewDeadline: 40s

    # The duration the clients should wait between attempting acquisition and
    # renewal of a leadership.
    # retryPeriod: 15s

installCRDs: false

replicaCount: 1

strategy: {}
  # type: RollingUpdate
  # rollingUpdate:
  #   maxSurge: 0
  #   maxUnavailable: 1

podDisruptionBudget:
  enabled: false

  # minAvailable and maxUnavailable can either be set to an integer (e.g. 1)
  # or a percentage value (e.g. 25%)
  # if neither minAvailable or maxUnavailable is set, we default to `minAvailable: 1`
  # minAvailable: 1
  # maxUnavailable: 1

# Comma separated list of feature gates that should be enabled on the
# controller pod.
featureGates: ""

# The maximum number of challenges that can be scheduled as 'processing' at once
maxConcurrentChallenges: 60

image:
  repository: prevalentai/cert-manager-controller
  # You can manage a registry with
  # registry: quay.io
  # repository: jetstack/cert-manager-controller

  # Override the image tag to deploy by setting this variable.
  # If no value is set, the chart's appVersion will be used.
  tag: v1.13.1

  # Setting a digest will override any tag
  # digest: sha256:0e072dddd1f7f8fc8909a2ca6f65e76c5f0d2fcfb8be47935ae3457e8bbceb20
  pullPolicy: IfNotPresent

# Override the namespace used to store DNS provider credentials etc. for ClusterIssuer
# resources. By default, the same namespace as cert-manager is deployed within is
# used. This namespace will not be automatically created by the Helm chart.
clusterResourceNamespace: ""

# This namespace allows you to define where the services will be installed into
# if not set then they will use the namespace of the release
# This is helpful when installing cert manager as a chart dependency (sub chart)
namespace: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  # name: ""
  # Optional additional annotations to add to the controller's ServiceAccount
  # annotations: {}
  # Automount API credentials for a Service Account.
  # Optional additional labels to add to the controller's ServiceAccount
  # labels: {}
  automountServiceAccountToken: true

# Automounting API credentials for a particular pod
# automountServiceAccountToken: true

# When this flag is enabled, secrets will be automatically removed when the certificate resource is deleted
enableCertificateOwnerRef: false

# Used to configure options for the controller pod.
# This allows setting options that'd usually be provided via flags.
# An APIVersion and Kind must be specified in your values.yaml file.
# Flags will override options that are set here.
config:
#   apiVersion: controller.config.cert-manager.io/v1alpha1
#   kind: ControllerConfiguration
#   logging:
#    verbosity: 2
#    format: text
#   leaderElectionConfig:
#    namespace: kube-system
#   kubernetesAPIQPS: 9000
#   kubernetesAPIBurst: 9000
#   numberOfConcurrentWorkers: 200
#   featureGates:
#     additionalCertificateOutputFormats: true
#     experimentalCertificateSigningRequestControllers: true
#     experimentalGatewayAPISupport: true
#     serverSideApply: true
#     literalCertificateSubject: true
#     useCertificateRequestBasicConstraints: true

# Setting Nameservers for DNS01 Self Check
# See: https://cert-manager.io/docs/configuration/acme/dns01/#setting-nameservers-for-dns01-self-check

# Comma separated string with host and port of the recursive nameservers cert-manager should query
dns01RecursiveNameservers: ""

# Forces cert-manager to only use the recursive nameservers for verification.
# Enabling this option could cause the DNS01 self check to take longer due to caching performed by the recursive nameservers
dns01RecursiveNameserversOnly: false

# Additional command line flags to pass to cert-manager controller binary.
# To see all available flags run docker run quay.io/jetstack/cert-manager-controller:<version> --help
extraArgs: []
  # Use this flag to enable or disable arbitrary controllers, for example, disable the CertificiateRequests approver
  # - --controllers=*,-certificaterequests-approver

extraEnv: []
# - name: SOME_VAR
#   value: 'some value'

resources:
  limits:
    cpu: 200m
    memory: 128Mi
  requests:
    cpu: 100m
    memory: 64Mi

# Pod Security Context
# ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
securityContext:
  runAsNonRoot: true
  seccompProfile:
    type: RuntimeDefault

# Container Security Context to be set on the controller component container
# ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
containerSecurityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true


volumes: []

volumeMounts: []

# Optional additional annotations to add to the controller Deployment
# deploymentAnnotations: {}

# Optional additional annotations to add to the controller Pods
# podAnnotations: {}

podLabels: {}

# Optional annotations to add to the controller Service
# serviceAnnotations: {}

# Optional additional labels to add to the controller Service
# serviceLabels: {}

# Optional DNS settings, useful if you have a public and private DNS zone for
# the same domain on Route 53. What follows is an example of ensuring
# cert-manager can access an ingress or DNS TXT records at all times.
# NOTE: This requires Kubernetes 1.10 or `CustomPodDNS` feature gate enabled for
# the cluster to work.
# podDnsPolicy: "None"
# podDnsConfig:
#   nameservers:
#     - "1.1.1.1"
#     - "8.8.8.8"

nodeSelector:
  kubernetes.io/os: linux

ingressShim: {}
  # defaultIssuerName: ""
  # defaultIssuerKind: ""
  # defaultIssuerGroup: ""

prometheus:
  enabled: true
  servicemonitor:
    enabled: false
    prometheusInstance: default
    targetPort: 9402
    path: /metrics
    interval: 60s
    scrapeTimeout: 30s
    labels: {}
    annotations: {}
    honorLabels: false
    endpointAdditionalProperties: {}

# Use these variables to configure the HTTP_PROXY environment variables
# http_proxy: "http://proxy:8080"
# https_proxy: "https://proxy:8080"
# no_proxy: 127.0.0.1,localhost

# A Kubernetes Affinty, if required; see https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#affinity-v1-core
# for example:
#   affinity:
#     nodeAffinity:
#      requiredDuringSchedulingIgnoredDuringExecution:
#        nodeSelectorTerms:
#        - matchExpressions:
#          - key: foo.bar.com/role
#            operator: In
#            values:
#            - master
affinity: {}

# A list of Kubernetes Tolerations, if required; see https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#toleration-v1-core
# for example:
#   tolerations:
#   - key: foo.bar.com/role
#     operator: Equal
#     value: master
#     effect: NoSchedule
tolerations: []

# A list of Kubernetes TopologySpreadConstraints, if required; see https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#topologyspreadconstraint-v1-core
# for example:
#   topologySpreadConstraints:
#   - maxSkew: 2
#     topologyKey: topology.kubernetes.io/zone
#     whenUnsatisfiable: ScheduleAnyway
#     labelSelector:
#       matchLabels:
#         app.kubernetes.io/instance: cert-manager
#         app.kubernetes.io/component: controller
topologySpreadConstraints: []

# LivenessProbe settings for the controller container of the controller Pod.
#
# Disabled by default, because the controller has a leader election mechanism
# which should cause it to exit if it is unable to renew its leader election
# record.
# LivenessProbe durations and thresholds are based on those used for the Kubernetes
# controller-manager. See:
# https://github.com/kubernetes/kubernetes/blob/806b30170c61a38fedd54cc9ede4cd6275a1ad3b/cmd/kubeadm/app/util/staticpod/utils.go#L241-L245
livenessProbe:
  enabled: false
  initialDelaySeconds: 10
  periodSeconds: 10
  timeoutSeconds: 15
  successThreshold: 1
  failureThreshold: 8

# enableServiceLinks indicates whether information about services should be
# injected into pod's environment variables, matching the syntax of Docker
# links.
enableServiceLinks: false

webhook:
  replicaCount: 1
  timeoutSeconds: 10

  # Used to configure options for the webhook pod.
  # This allows setting options that'd usually be provided via flags.
  # An APIVersion and Kind must be specified in your values.yaml file.
  # Flags will override options that are set here.
  config:
    # apiVersion: webhook.config.cert-manager.io/v1alpha1
    # kind: WebhookConfiguration

    # The port that the webhook should listen on for requests.
    # In GKE private clusters, by default kubernetes apiservers are allowed to
    # talk to the cluster nodes only on 443 and 10250. so configuring
    # securePort: 10250, will work out of the box without needing to add firewall
    # rules or requiring NET_BIND_SERVICE capabilities to bind port numbers <1000.
    # This should be uncommented and set as a default by the chart once we graduate
    # the apiVersion of WebhookConfiguration past v1alpha1.
    # securePort: 10250

  strategy: {}
    # type: RollingUpdate
    # rollingUpdate:
    #   maxSurge: 0
    #   maxUnavailable: 1

  # Pod Security Context to be set on the webhook component Pod
  # ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault

  podDisruptionBudget:
    enabled: false

    # minAvailable and maxUnavailable can either be set to an integer (e.g. 1)
    # or a percentage value (e.g. 25%)
    # if neither minAvailable or maxUnavailable is set, we default to `minAvailable: 1`
    # minAvailable: 1
    # maxUnavailable: 1

  # Container Security Context to be set on the webhook component container
  # ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
  containerSecurityContext:
    allowPrivilegeEscalation: false
    capabilities:
      drop:
      - ALL
    # readOnlyRootFilesystem: true
    # runAsNonRoot: true

  # Optional additional annotations to add to the webhook Deployment
  # deploymentAnnotations: {}

  # Optional additional annotations to add to the webhook Pods
  # podAnnotations: {}

  # Optional additional annotations to add to the webhook Service
  # serviceAnnotations: {}

  # Optional additional annotations to add to the webhook MutatingWebhookConfiguration
  # mutatingWebhookConfigurationAnnotations: {}

  # Optional additional annotations to add to the webhook ValidatingWebhookConfiguration
  # validatingWebhookConfigurationAnnotations: {}

  # Additional command line flags to pass to cert-manager webhook binary.
  # To see all available flags run docker run quay.io/jetstack/cert-manager-webhook:<version> --help
  extraArgs: []
  # Path to a file containing a WebhookConfiguration object used to configure the webhook
  # - --config=<path-to-config-file>

  # Comma separated list of feature gates that should be enabled on the
  # webhook pod.
  featureGates: ""

  resources:
    limits:
      cpu: 200m
      memory: 128Mi
    requests:
      cpu: 100m
      memory: 64Mi

  ## Liveness and readiness probe values
  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle/#container-probes
  ##
  livenessProbe:
    failureThreshold: 3
    initialDelaySeconds: 60
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  readinessProbe:
    failureThreshold: 3
    initialDelaySeconds: 5
    periodSeconds: 5
    successThreshold: 1
    timeoutSeconds: 1

  nodeSelector:
    kubernetes.io/os: linux

  affinity: {}

  tolerations: []

  topologySpreadConstraints: []

  # Optional additional labels to add to the Webhook Pods
  podLabels: {}

  # Optional additional labels to add to the Webhook Service
  serviceLabels: {}

  image:
    repository: prevalentai/cert-manager-webhook
    # You can manage a registry with
    # registry: quay.io
    # repository: jetstack/cert-manager-webhook

    # Override the image tag to deploy by setting this variable.
    # If no value is set, the chart's appVersion will be used.
    tag: v1.13.1

    # Setting a digest will override any tag
    # digest: sha256:0e072dddd1f7f8fc8909a2ca6f65e76c5f0d2fcfb8be47935ae3457e8bbceb20

    pullPolicy: IfNotPresent

  serviceAccount:
    # Specifies whether a service account should be created
    create: true
    # The name of the service account to use.
    # If not set and create is true, a name is generated using the fullname template
    # name: ""
    # Optional additional annotations to add to the controller's ServiceAccount
    # annotations: {}
    # Optional additional labels to add to the webhook's ServiceAccount
    # labels: {}
    # Automount API credentials for a Service Account.
    automountServiceAccountToken: true

  # Automounting API credentials for a particular pod
  # automountServiceAccountToken: true

  # The port that the webhook should listen on for requests.
  # In GKE private clusters, by default kubernetes apiservers are allowed to
  # talk to the cluster nodes only on 443 and 10250. so configuring
  # securePort: 10250, will work out of the box without needing to add firewall
  # rules or requiring NET_BIND_SERVICE capabilities to bind port numbers <1000
  securePort: 10250

  # Specifies if the webhook should be started in hostNetwork mode.
  #
  # Required for use in some managed kubernetes clusters (such as AWS EKS) with custom
  # CNI (such as calico), because control-plane managed by AWS cannot communicate
  # with pods' IP CIDR and admission webhooks are not working
  #
  # Since the default port for the webhook conflicts with kubelet on the host
  # network, `webhook.securePort` should be changed to an available port if
  # running in hostNetwork mode.
  hostNetwork: false

  # Specifies how the service should be handled. Useful if you want to expose the
  # webhook to outside of the cluster. In some cases, the control plane cannot
  # reach internal services.
  serviceType: ClusterIP
  # loadBalancerIP:

  # Overrides the mutating webhook and validating webhook so they reach the webhook
  # service using the `url` field instead of a service.
  url: {}
    # host:

  # Enables default network policies for webhooks.
  networkPolicy:
    enabled: false
    ingress:
    - from:
      - ipBlock:
          cidr: 0.0.0.0/0
    egress:
    - ports:
      - port: 80
        protocol: TCP
      - port: 443
        protocol: TCP
      - port: 53
        protocol: TCP
      - port: 53
        protocol: UDP
      # On OpenShift and OKD, the Kubernetes API server listens on
      # port 6443.
      - port: 6443
        protocol: TCP
      to:
      - ipBlock:
          cidr: 0.0.0.0/0

  volumes: []
  volumeMounts: []

  # enableServiceLinks indicates whether information about services should be
  # injected into pod's environment variables, matching the syntax of Docker
  # links.
  enableServiceLinks: false

cainjector:
  enabled: true
  replicaCount: 1

  strategy: {}
    # type: RollingUpdate
    # rollingUpdate:
    #   maxSurge: 0
    #   maxUnavailable: 1

  # Pod Security Context to be set on the cainjector component Pod
  # ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault

  podDisruptionBudget:
    enabled: false

    # minAvailable and maxUnavailable can either be set to an integer (e.g. 1)
    # or a percentage value (e.g. 25%)
    # if neither minAvailable or maxUnavailable is set, we default to `minAvailable: 1`
    # minAvailable: 1
    # maxUnavailable: 1

  # Container Security Context to be set on the cainjector component container
  # ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
  containerSecurityContext:
    allowPrivilegeEscalation: false
    capabilities:
      drop:
      - ALL
    # readOnlyRootFilesystem: true
    # runAsNonRoot: true


  # Optional additional annotations to add to the cainjector Deployment
  # deploymentAnnotations: {}

  # Optional additional annotations to add to the cainjector Pods
  # podAnnotations: {}

  # Additional command line flags to pass to cert-manager cainjector binary.
  # To see all available flags run docker run quay.io/jetstack/cert-manager-cainjector:<version> --help
  extraArgs: []
  # Enable profiling for cainjector
  # - --enable-profiling=true

  resources:
    limits:
      cpu: 200m
      memory: 128Mi
    requests:
      cpu: 100m
      memory: 64Mi

  nodeSelector:
    kubernetes.io/os: linux

  affinity: {}

  tolerations: []

  topologySpreadConstraints: []

  # Optional additional labels to add to the CA Injector Pods
  podLabels: {}

  image:
    repository: prevalentai/cert-manager-cainjector
    # You can manage a registry with
    # registry: quay.io
    # repository: jetstack/cert-manager-cainjector

    # Override the image tag to deploy by setting this variable.
    # If no value is set, the chart's appVersion will be used.
    tag: v1.13.1

    # Setting a digest will override any tag
    # digest: sha256:0e072dddd1f7f8fc8909a2ca6f65e76c5f0d2fcfb8be47935ae3457e8bbceb20

    pullPolicy: IfNotPresent

  serviceAccount:
    # Specifies whether a service account should be created
    create: true
    # The name of the service account to use.
    # If not set and create is true, a name is generated using the fullname template
    # name: ""
    # Optional additional annotations to add to the controller's ServiceAccount
    # annotations: {}
    # Automount API credentials for a Service Account.
    # Optional additional labels to add to the cainjector's ServiceAccount
    # labels: {}
    automountServiceAccountToken: true

  # Automounting API credentials for a particular pod
  # automountServiceAccountToken: true

  volumes: []
  volumeMounts: []

  # enableServiceLinks indicates whether information about services should be
  # injected into pod's environment variables, matching the syntax of Docker
  # links.
  enableServiceLinks: false

acmesolver:
  image:
    repository: quay.io/jetstack/cert-manager-acmesolver
    # You can manage a registry with
    # registry: quay.io
    # repository: jetstack/cert-manager-acmesolver

    # Override the image tag to deploy by setting this variable.
    # If no value is set, the chart's appVersion will be used.
    # tag: canary

    # Setting a digest will override any tag
    # digest: sha256:0e072dddd1f7f8fc8909a2ca6f65e76c5f0d2fcfb8be47935ae3457e8bbceb20

# This startupapicheck is a Helm post-install hook that waits for the webhook
# endpoints to become available.
# The check is implemented using a Kubernetes Job- if you are injecting mesh
# sidecar proxies into cert-manager pods, you probably want to ensure that they
# are not injected into this Job's pod. Otherwise the installation may time out
# due to the Job never being completed because the sidecar proxy does not exit.
# See https://github.com/cert-manager/cert-manager/pull/4414 for context.
startupapicheck:
  enabled: true

  # Pod Security Context to be set on the startupapicheck component Pod
  # ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
  securityContext:
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault

  # Container Security Context to be set on the controller component container
  # ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
  containerSecurityContext:
    allowPrivilegeEscalation: false
    capabilities:
      drop:
      - ALL
    # readOnlyRootFilesystem: true
    # runAsNonRoot: true

  # Timeout for 'kubectl check api' command
  timeout: 1m

  # Job backoffLimit
  backoffLimit: 4

  # Optional additional annotations to add to the startupapicheck Job
  jobAnnotations:
    helm.sh/hook: post-install
    helm.sh/hook-weight: "1"
    helm.sh/hook-delete-policy: before-hook-creation,hook-succeeded

  # Optional additional annotations to add to the startupapicheck Pods
  podAnnotations:
    sidecar.istio.io/inject: "false"

  # Additional command line flags to pass to startupapicheck binary.
  # To see all available flags run docker run quay.io/jetstack/cert-manager-ctl:<version> --help
  extraArgs: []

  resources: {}
    # requests:
    #   cpu: 10m
    #   memory: 32Mi

  nodeSelector:
    kubernetes.io/os: linux

  affinity: {}

  tolerations: []

  # Optional additional labels to add to the startupapicheck Pods
  podLabels: {}

  image:
    repository: prevalentai/cert-manager-ctl
    # You can manage a registry with
    # registry: quay.io
    # repository: jetstack/cert-manager-ctl

    # Override the image tag to deploy by setting this variable.
    # If no value is set, the chart's appVersion will be used.
    tag: v1.13.1

    # Setting a digest will override any tag
    # digest: sha256:0e072dddd1f7f8fc8909a2ca6f65e76c5f0d2fcfb8be47935ae3457e8bbceb20

    pullPolicy: IfNotPresent

  rbac:
    # annotations for the startup API Check job RBAC and PSP resources
    annotations:
      helm.sh/hook: post-install
      helm.sh/hook-weight: "-5"
      helm.sh/hook-delete-policy: before-hook-creation,hook-succeeded

  # Automounting API credentials for a particular pod
  # automountServiceAccountToken: true

  serviceAccount:
    # Specifies whether a service account should be created
    create: true

    # The name of the service account to use.
    # If not set and create is true, a name is generated using the fullname template
    # name: ""

    # Optional additional annotations to add to the Job's ServiceAccount
    annotations:
      helm.sh/hook: post-install
      helm.sh/hook-weight: "-5"
      helm.sh/hook-delete-policy: before-hook-creation,hook-succeeded

    # Automount API credentials for a Service Account.
    automountServiceAccountToken: true

    # Optional additional labels to add to the startupapicheck's ServiceAccount
    # labels: {}

  volumes: []
  volumeMounts: []

  # enableServiceLinks indicates whether information about services should be
  # injected into pod's environment variables, matching the syntax of Docker
  # links.
  enableServiceLinks: false
